package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据权限树形结构响应VO类
 * 按模块分组展示数据权限，类似菜单权限的树形结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataPermissionTreeResponseVO", description = "数据权限树形结构响应")
public class DataPermissionTreeResponseVO {

    @ApiModelProperty(value = "模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long moduleId;

    @ApiModelProperty(value = "模块名称", example = "用户数据模块")
    private String moduleName;

    @ApiModelProperty(value = "模块标识", example = "user_data_module")
    private String moduleIdentifier;

    @ApiModelProperty(value = "模块拥有的操作权限类型", example = "[1, 2, 3]",
                     notes = "1-新增，2-修改，3-删除，4-查看")
    private List<Integer> operateTypes;

    @ApiModelProperty(value = "模块下的数据权限列表")
    private List<DataPermissionTreeItemVO> children;

    /**
     * 数据权限树形节点VO类
     */
    @Data
    @ApiModel(value = "DataPermissionTreeItemVO", description = "数据权限树形节点")
    public static class DataPermissionTreeItemVO {

        @ApiModelProperty(value = "数据权限ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long dataId;

        @ApiModelProperty(value = "数据权限名称", example = "用户基础数据")
        private String dataName;

        @ApiModelProperty(value = "数据标识", example = "user_basic_data")
        private String dataIdentifier;

        @ApiModelProperty(value = "数据类型", example = "1")
        private Integer dataType;

        @ApiModelProperty(value = "排序序号", example = "1")
        private Integer orderInfo;

        @ApiModelProperty(value = "是否禁用", example = "false")
        private Boolean isDisable;

        @ApiModelProperty(value = "数据权限的操作权限类型", example = "[1, 2, 3]",
                         notes = "1-新增，2-修改，3-删除，4-查看")
        private List<Integer> operateTypes;
    }
}
