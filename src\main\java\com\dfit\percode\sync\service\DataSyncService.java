package com.dfit.percode.sync.service;

import com.dfit.percode.sync.entity.ExternalDepartment;
import com.dfit.percode.sync.entity.DepartmentChild;
import com.dfit.percode.sync.entity.ExternalEmployee;
import com.dfit.percode.sync.entity.ExternalEmployeePosition;
import com.dfit.percode.sync.entity.ExternalEmployeeTitle;
import com.dfit.percode.sync.entity.ExternalEmployeeSystem;
import com.dfit.percode.entity.TOrgStructure;
import com.dfit.percode.entity.TUser;
import com.dfit.percode.mapper.TOrgStructureMapper;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.mapper.EmployeeExtendedMapper;
import com.dfit.percode.util.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.dfit.percode.sync.dto.EmployeePositionBatch;
import com.dfit.percode.sync.dto.EmployeeTitleBatch;
import com.dfit.percode.sync.dto.EmployeeSystemBatch;

/**
 * 数据同步服务
 * 负责从外部系统同步部门和员工数据到当前系统
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@Slf4j
public class DataSyncService {

    @Autowired
    private ExternalDataService externalDataService;

    @Autowired
    private TOrgStructureMapper orgStructureMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private EmployeeExtendedMapper employeeExtendedMapper;

    @Autowired
    private SnowflakeIdGenerator snowflakeIdGenerator;

    // 部门代码到内部ID的映射缓存
    private Map<String, Long> orgCodeToIdMap = new HashMap<>();

    /**
     * 执行完整的数据同步
     * 先同步部门，再同步员工
     */
    @Transactional
    public void performFullSync() {
        log.info("开始执行完整数据同步");

        try {
            // 第一步：同步部门数据
            syncDepartments();

            // 第二步：同步员工数据（包含扩展数据）
            syncEmployees();

            // 第三步：根据主岗位更新员工的部门归属
            updateEmployeeDepartmentAffiliation();

            log.info("完整数据同步执行成功");
        } catch (Exception e) {
            log.error("数据同步执行失败", e);
            throw new RuntimeException("数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行完整的数据同步（指定时间范围）
     * 先同步部门，再同步员工
     *
     * @param startDate 开始时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDate 结束时间字符串（格式：yyyy-MM-dd HH:mm:ss）
     */
    @Transactional
    public void performFullSync(String startDate, String endDate) {
        log.info("开始执行完整数据同步，时间范围: {} - {}", startDate, endDate);

        try {
            // 解析时间字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startDate, formatter);
            LocalDateTime end = LocalDateTime.parse(endDate, formatter);

            // 第一步：同步部门数据
            syncDepartments(start, end);

            // 第二步：同步员工数据（包含扩展数据）
            syncEmployees(start, end);

            // 第三步：根据主岗位更新员工的部门归属
            updateEmployeeDepartmentAffiliation();

            log.info("完整数据同步执行成功，时间范围: {} - {}", startDate, endDate);
        } catch (Exception e) {
            log.error("数据同步执行失败，时间范围: {} - {}", startDate, endDate, e);
            throw new RuntimeException("数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 执行增量数据同步
     * 只同步指定时间范围内变更的数据，提高同步效率
     * 适用于定时任务的日常增量同步
     *
     * @param startDate 开始时间 (格式: yyyy-MM-dd HH:mm:ss)
     * @param endDate 结束时间 (格式: yyyy-MM-dd HH:mm:ss)
     */
    @Transactional
    public void performIncrementalSync(String startDate, String endDate) {
        log.info("开始执行增量数据同步，时间范围: {} - {}", startDate, endDate);

        try {
            // 解析时间字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startDate, formatter);
            LocalDateTime end = LocalDateTime.parse(endDate, formatter);

            // 增量同步策略：只同步变更的数据
            log.info("采用增量同步策略，只处理时间范围内的变更数据");

            // 第一步：增量同步部门数据
            syncDepartments(start, end);

            // 第二步：增量同步员工数据（包含扩展数据）
            syncEmployees(start, end);

            // 第三步：根据主岗位更新员工的部门归属
            // 注意：这一步会处理所有用户，确保新同步的数据能正确关联
            updateEmployeeDepartmentAffiliation();

            // 第四步：关联可能的孤儿记录
            linkOrphanRecords();

            log.info("增量数据同步执行成功，时间范围: {} - {}", startDate, endDate);
        } catch (Exception e) {
            log.error("增量数据同步执行失败，时间范围: {} - {}", startDate, endDate, e);
            throw new RuntimeException("增量数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步部门数据
     * 从外部系统获取部门数据并转换为内部格式
     */
    @Transactional
    public void syncDepartments() {
        log.info("开始同步部门数据");

        try {
            // 从外部系统获取部门数据
            List<ExternalDepartment> externalDepartments = externalDataService.getAllDepartments();
            log.info("从外部系统获取到 {} 个部门", externalDepartments.size());

            // 清空映射缓存
            orgCodeToIdMap.clear();

            // 批量转换部门数据
            List<TOrgStructure> departmentBatch = new ArrayList<>();
            int departmentChildCount = 0;

            for (ExternalDepartment extDept : externalDepartments) {
                TOrgStructure internalDept = convertToInternalDepartment(extDept);
                departmentBatch.add(internalDept);

                // 更新映射缓存
                orgCodeToIdMap.put(extDept.getOrgCode(), internalDept.getId());

                // 同步部门子表数据（children数组）
                if (extDept.getChildren() != null && !extDept.getChildren().isEmpty()) {
                    log.info("开始同步部门子表数据，部门 {} 有 {} 个子记录", extDept.getOrgCode(), extDept.getChildren().size());
                    for (DepartmentChild child : extDept.getChildren()) {
                        syncDepartmentChild(internalDept.getId(), child);
                        departmentChildCount++;
                    }
                    log.info("部门子表数据同步完成，共处理 {} 条记录到 t_department_child 表", extDept.getChildren().size());
                }
            }

            // 批量保存部门数据
            if (!departmentBatch.isEmpty()) {
                log.info("开始批量插入 {} 个部门到 t_org_structure 表", departmentBatch.size());
                employeeExtendedMapper.batchInsertDepartments(departmentBatch);
                log.info("批量插入部门数据完成");
            }

            int departmentCount = departmentBatch.size();

            log.info("部门数据同步汇总: 主表(t_org_structure)={} 条, 子表(t_department_child)={} 条", departmentCount, departmentChildCount);

            log.info("部门数据同步完成，共处理 {} 个部门", externalDepartments.size());
        } catch (Exception e) {
            log.error("部门数据同步失败", e);
            throw new RuntimeException("部门数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步部门数据（指定时间范围）
     * 从外部系统获取指定时间范围的部门数据并转换为内部格式
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    @Transactional
    public void syncDepartments(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("开始同步部门数据，时间范围: {} - {}", startDate, endDate);

        try {
            // 显示同步策略建议
            String strategyAdvice = externalDataService.getSyncStrategyAdvice(startDate, endDate, "department");
            log.info("同步策略: {}", strategyAdvice);

            // 从外部系统获取指定时间范围的部门数据
            List<ExternalDepartment> externalDepartments = externalDataService.getDepartments(startDate, endDate);
            log.info("从外部系统获取到 {} 个部门", externalDepartments.size());

            // 清空映射缓存
            orgCodeToIdMap.clear();

            // 批量转换部门数据
            List<TOrgStructure> departmentBatch = new ArrayList<>();
            int departmentChildCount = 0;

            for (ExternalDepartment extDept : externalDepartments) {
                TOrgStructure internalDept = convertToInternalDepartment(extDept);
                departmentBatch.add(internalDept);

                // 更新映射缓存
                orgCodeToIdMap.put(extDept.getOrgCode(), internalDept.getId());

                // 同步部门子表数据（children数组）
                if (extDept.getChildren() != null && !extDept.getChildren().isEmpty()) {
                    log.info("开始同步部门子表数据，部门 {} 有 {} 个子记录", extDept.getOrgCode(), extDept.getChildren().size());
                    for (DepartmentChild child : extDept.getChildren()) {
                        syncDepartmentChild(internalDept.getId(), child);
                        departmentChildCount++;
                    }
                    log.info("部门子表数据同步完成，共处理 {} 条记录到 t_department_child 表", extDept.getChildren().size());
                }
            }

            // 批量保存部门数据
            if (!departmentBatch.isEmpty()) {
                log.info("开始批量插入 {} 个部门到 t_org_structure 表", departmentBatch.size());
                employeeExtendedMapper.batchInsertDepartments(departmentBatch);
                log.info("批量插入部门数据完成");
            }

            int departmentCount = departmentBatch.size();

            log.info("部门数据同步汇总: 主表(t_org_structure)={} 条, 子表(t_department_child)={} 条", departmentCount, departmentChildCount);

            log.info("部门数据同步完成，共处理 {} 个部门", externalDepartments.size());
        } catch (Exception e) {
            log.error("部门数据同步失败", e);
            throw new RuntimeException("部门数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步员工数据
     * 从外部系统获取员工数据并转换为内部格式
     */
    @Transactional
    public void syncEmployees() {
        log.info("开始同步员工数据");

        try {
            // 从外部系统获取员工数据
            List<ExternalEmployee> externalEmployees = externalDataService.getAllEmployees();
            log.info("从外部系统获取到 {} 个员工", externalEmployees.size());

            // 转换并保存员工数据（包括扩展数据）
            int employeeCount = 0;
            int positionCount = 0;
            int titleCount = 0;
            int systemCount = 0;

            for (ExternalEmployee extEmp : externalEmployees) {
                log.info("正在处理员工: mdmId={}, employeeName={}, employeeCode={}",
                        extEmp.getMdmId(), extEmp.getEmployeeName(), extEmp.getEmployeeCode());

                // 1. 保存员工基本数据
                TUser internalUser = convertToInternalUser(extEmp);
                saveOrUpdateUser(internalUser);
                employeeCount++;

                log.info("员工数据已保存到 t_user 表: id={}, userName={}, account={}",
                        internalUser.getId(), internalUser.getUserName(), internalUser.getAccount());

                // 2. 同时处理扩展数据（来自同一次API调用）
                // 同步员工岗位信息
                if (extEmp.getPositions() != null && !extEmp.getPositions().isEmpty()) {
                    log.info("员工 {} 有 {} 个岗位信息", extEmp.getMdmId(), extEmp.getPositions().size());
                    for (ExternalEmployeePosition position : extEmp.getPositions()) {
                        syncEmployeePosition(extEmp.getMdmId(), position);
                        positionCount++;
                    }
                }

                // 同步员工职称信息
                if (extEmp.getTitles() != null && !extEmp.getTitles().isEmpty()) {
                    log.info("员工 {} 有 {} 个职称信息", extEmp.getMdmId(), extEmp.getTitles().size());
                    for (ExternalEmployeeTitle title : extEmp.getTitles()) {
                        syncEmployeeTitle(extEmp.getMdmId(), title);
                        titleCount++;
                    }
                }

                // 同步员工系统标识信息
                if (extEmp.getSystems() != null && !extEmp.getSystems().isEmpty()) {
                    log.info("员工 {} 有 {} 个系统标识信息", extEmp.getMdmId(), extEmp.getSystems().size());
                    for (ExternalEmployeeSystem system : extEmp.getSystems()) {
                        syncEmployeeSystem(extEmp.getMdmId(), system);
                        systemCount++;
                    }
                }
            }

            log.info("员工数据同步汇总: t_user={} 条, t_employee_position={} 条, t_employee_title={} 条, t_employee_system={} 条",
                    employeeCount, positionCount, titleCount, systemCount);

            // 尝试关联孤儿记录
            linkOrphanRecords();

            log.info("员工数据同步完成，共处理 {} 个员工及其扩展数据", externalEmployees.size());
        } catch (Exception e) {
            log.error("员工数据同步失败", e);
            throw new RuntimeException("员工数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步员工数据（指定时间范围）
     * 从外部系统获取指定时间范围的员工数据并转换为内部格式
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    @Transactional
    public void syncEmployees(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("开始同步员工数据，时间范围: {} - {}", startDate, endDate);

        try {
            // 显示同步策略建议
            String strategyAdvice = externalDataService.getSyncStrategyAdvice(startDate, endDate, "employee");
            log.info("同步策略: {}", strategyAdvice);

            // 从外部系统获取指定时间范围的员工数据
            List<ExternalEmployee> externalEmployees = externalDataService.getEmployees(startDate, endDate);
            log.info("从外部系统获取到 {} 个员工", externalEmployees.size());

            // 转换并保存员工数据（包括扩展数据）
            int employeeCount = 0;
            int positionCount = 0;
            int titleCount = 0;
            int systemCount = 0;

            // 使用批量处理优化性能
            if (externalEmployees.size() > 100) {
                log.info("数据量较大({} 个员工)，使用批量处理模式", externalEmployees.size());
                int[] counts = batchSyncEmployeesOptimized(externalEmployees);
                employeeCount = counts[0];
                positionCount = counts[1];
                titleCount = counts[2];
                systemCount = counts[3];
            } else {
                log.info("数据量较小({} 个员工)，使用逐条处理模式", externalEmployees.size());

                for (ExternalEmployee extEmp : externalEmployees) {
                    log.info("正在处理员工: mdmId={}, employeeName={}, employeeCode={}",
                            extEmp.getMdmId(), extEmp.getEmployeeName(), extEmp.getEmployeeCode());

                    // 1. 保存员工基本数据
                    TUser internalUser = convertToInternalUser(extEmp);
                    saveOrUpdateUser(internalUser);
                    employeeCount++;

                    log.info("员工数据已保存到 t_user 表: id={}, userName={}, account={}",
                            internalUser.getId(), internalUser.getUserName(), internalUser.getAccount());

                    // 2. 同时处理扩展数据（来自同一次API调用）
                    // 同步员工岗位信息
                    if (extEmp.getPositions() != null && !extEmp.getPositions().isEmpty()) {
                        log.info("员工 {} 有 {} 个岗位信息", extEmp.getMdmId(), extEmp.getPositions().size());
                        for (ExternalEmployeePosition position : extEmp.getPositions()) {
                            syncEmployeePosition(extEmp.getMdmId(), position);
                            positionCount++;
                        }
                    }

                    // 同步员工职称信息
                    if (extEmp.getTitles() != null && !extEmp.getTitles().isEmpty()) {
                        log.info("员工 {} 有 {} 个职称信息", extEmp.getMdmId(), extEmp.getTitles().size());
                        for (ExternalEmployeeTitle title : extEmp.getTitles()) {
                            syncEmployeeTitle(extEmp.getMdmId(), title);
                            titleCount++;
                        }
                    }

                    // 同步员工系统标识信息
                    if (extEmp.getSystems() != null && !extEmp.getSystems().isEmpty()) {
                        log.info("员工 {} 有 {} 个系统标识信息", extEmp.getMdmId(), extEmp.getSystems().size());
                        for (ExternalEmployeeSystem system : extEmp.getSystems()) {
                            syncEmployeeSystem(extEmp.getMdmId(), system);
                            systemCount++;
                        }
                    }
                }
            }

            log.info("员工数据同步汇总: t_user={} 条, t_employee_position={} 条, t_employee_title={} 条, t_employee_system={} 条",
                    employeeCount, positionCount, titleCount, systemCount);

            // 尝试关联孤儿记录
            linkOrphanRecords();

            log.info("员工数据同步完成，共处理 {} 个员工及其扩展数据", externalEmployees.size());
        } catch (Exception e) {
            log.error("员工数据同步失败", e);
            throw new RuntimeException("员工数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步员工扩展数据
     * 包括岗位信息、职称信息、系统标识信息
     */
    @Transactional
    public void syncEmployeeExtendedData() {
        log.info("开始同步员工扩展数据");

        try {
            // 重新获取员工数据（包含扩展信息）
            List<ExternalEmployee> externalEmployees = externalDataService.getAllEmployees();
            log.info("开始处理 {} 个员工的扩展数据", externalEmployees.size());

            int positionCount = 0;
            int titleCount = 0;
            int systemCount = 0;

            for (ExternalEmployee extEmp : externalEmployees) {
                log.info("正在处理员工扩展数据: mdmId={}, employeeName={}", extEmp.getMdmId(), extEmp.getEmployeeName());

                // 同步员工岗位信息
                if (extEmp.getPositions() != null && !extEmp.getPositions().isEmpty()) {
                    log.info("员工 {} 有 {} 个岗位信息", extEmp.getMdmId(), extEmp.getPositions().size());
                    for (ExternalEmployeePosition position : extEmp.getPositions()) {
                        syncEmployeePosition(extEmp.getMdmId(), position);
                        positionCount++; // 现在所有数据都会保存，所以直接计数
                    }
                }

                // 同步员工职称信息
                if (extEmp.getTitles() != null && !extEmp.getTitles().isEmpty()) {
                    log.info("员工 {} 有 {} 个职称信息", extEmp.getMdmId(), extEmp.getTitles().size());
                    for (ExternalEmployeeTitle title : extEmp.getTitles()) {
                        syncEmployeeTitle(extEmp.getMdmId(), title);
                        titleCount++; // 现在所有数据都会保存，所以直接计数
                    }
                }

                // 同步员工系统标识信息
                if (extEmp.getSystems() != null && !extEmp.getSystems().isEmpty()) {
                    log.info("员工 {} 有 {} 个系统标识信息", extEmp.getMdmId(), extEmp.getSystems().size());
                    for (ExternalEmployeeSystem system : extEmp.getSystems()) {
                        syncEmployeeSystem(extEmp.getMdmId(), system);
                        systemCount++; // 现在所有数据都会保存，所以直接计数
                    }
                }
            }

            log.info("员工扩展数据同步汇总: t_employee_position={} 条, t_employee_title={} 条, t_employee_system={} 条",
                    positionCount, titleCount, systemCount);

            // 尝试关联孤儿记录
            linkOrphanRecords();
        } catch (Exception e) {
            log.error("员工扩展数据同步失败", e);
            throw new RuntimeException("员工扩展数据同步失败: " + e.getMessage(), e);
        }
    }

    /**
     * 关联孤儿记录
     * 将没有user_id的扩展数据通过mdm_id关联到对应的用户
     */
    @Transactional
    public void linkOrphanRecords() {
        log.info("开始关联孤儿记录");

        try {
            int linkedPositions = 0;
            int linkedTitles = 0;
            int linkedSystems = 0;

            // 关联员工岗位表的孤儿记录
            linkedPositions = employeeExtendedMapper.linkOrphanPositionRecords();
            log.info("关联员工岗位孤儿记录: {} 条", linkedPositions);

            // 关联员工职称表的孤儿记录
            linkedTitles = employeeExtendedMapper.linkOrphanTitleRecords();
            log.info("关联员工职称孤儿记录: {} 条", linkedTitles);

            // 关联员工系统标识表的孤儿记录
            linkedSystems = employeeExtendedMapper.linkOrphanSystemRecords();
            log.info("关联员工系统标识孤儿记录: {} 条", linkedSystems);

            int totalLinked = linkedPositions + linkedTitles + linkedSystems;
            log.info("孤儿记录关联汇总: 岗位表={} 条, 职称表={} 条, 系统表={} 条, 总计={} 条",
                    linkedPositions, linkedTitles, linkedSystems, totalLinked);
        } catch (Exception e) {
            log.error("关联孤儿记录失败", e);
            throw new RuntimeException("关联孤儿记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 清理重复的员工扩展数据
     */
    @Transactional
    public void cleanDuplicateEmployeeData() {
        log.info("开始清理重复的员工扩展数据");

        try {
            // 清理重复的岗位数据
            int duplicatePositions = employeeExtendedMapper.cleanDuplicatePositions();
            log.info("清理重复岗位数据: {} 条", duplicatePositions);

            // 清理重复的职称数据
            int duplicateTitles = employeeExtendedMapper.cleanDuplicateTitles();
            log.info("清理重复职称数据: {} 条", duplicateTitles);

            // 清理重复的系统数据
            int duplicateSystems = employeeExtendedMapper.cleanDuplicateSystems();
            log.info("清理重复系统数据: {} 条", duplicateSystems);

            log.info("员工扩展数据清理完成: 岗位={}, 职称={}, 系统={}",
                    duplicatePositions, duplicateTitles, duplicateSystems);

        } catch (Exception e) {
            log.error("清理重复员工扩展数据失败", e);
            throw e;
        }
    }

    /**
     * 根据主岗位更新员工的部门归属
     */
    @Transactional
    public void updateEmployeeDepartmentAffiliation() {
        log.info("开始更新员工部门归属");

        try {
            // 0. 先查询基础数据统计信息
            Map<String, Object> positionStats = employeeExtendedMapper.getPositionStatistics();
            log.info("员工岗位数据统计: 总岗位={}, 有org_code={}, 无org_code={}, 主岗位={}, 有user_id={}, 无user_id={}",
                    positionStats.get("total_positions"),
                    positionStats.get("has_org_code"),
                    positionStats.get("no_org_code"),
                    positionStats.get("primary_positions"),
                    positionStats.get("has_user_id"),
                    positionStats.get("no_user_id"));

            Map<String, Object> userStats = employeeExtendedMapper.getUserStatistics();
            log.info("用户数据统计: 总用户={}, 无部门归属={}",
                    userStats.get("total_users"),
                    userStats.get("no_department"));

            // 查看岗位数据样本
            List<Map<String, Object>> positionSamples = employeeExtendedMapper.getPositionSamples();
            log.info("岗位数据样本（前10条）:");
            for (Map<String, Object> sample : positionSamples) {
                log.info("  user_id={}, org_code={}, is_primary={}, employee_mdm_id={}",
                        sample.get("user_id"), sample.get("org_code"),
                        sample.get("is_primary"), sample.get("employee_mdm_id"));
            }

            // 测试JOIN查询
            List<Map<String, Object>> joinSamples = employeeExtendedMapper.testJoinQuery();
            log.info("JOIN查询测试结果（前10条）:");
            for (Map<String, Object> sample : joinSamples) {
                log.info("  userId={}, userName={}, orgCode={}",
                        sample.get("userId"), sample.get("userName"), sample.get("orgCode"));
            }

            // 1. 查询所有需要更新部门归属的用户
            List<Map<String, Object>> usersNeedUpdate = employeeExtendedMapper.findUsersNeedDepartmentUpdate();
            log.info("找到 {} 个需要更新部门归属的用户", usersNeedUpdate.size());

            int updateCount = 0;
            int notFoundCount = 0;
            int nullOrgCodeCount = 0;

            for (Map<String, Object> userInfo : usersNeedUpdate) {
                Long userId = (Long) userInfo.get("userId");
                String orgCode = (String) userInfo.get("orgCode");
                String userName = (String) userInfo.get("userName");
                String mdmId = (String) userInfo.get("mdmId");

                log.debug("正在更新用户部门归属: userId={}, userName={}, mdmId={}, orgCode={}",
                         userId, userName, mdmId, orgCode);

                // 检查org_code是否为空
                if (orgCode == null || orgCode.trim().isEmpty()) {
                    nullOrgCodeCount++;
                    log.warn("用户主岗位的org_code为空: userId={}, userName={}, mdmId={}", userId, userName, mdmId);
                    continue;
                }

                // 2. 根据org_code找到对应的内部部门ID
                Long departmentId = employeeExtendedMapper.findDepartmentIdByOrgCode(orgCode);

                if (departmentId != null) {
                    // 3. 更新t_user表的organ_affiliation字段
                    employeeExtendedMapper.updateUserDepartmentAffiliation(userId, departmentId);
                    updateCount++;
                    log.info("成功更新用户 {} ({}) 的部门归属为部门ID: {}", userName, userId, departmentId);
                } else {
                    notFoundCount++;
                    log.warn("未找到组织代码 {} 对应的部门，用户: {} ({})", orgCode, userName, userId);
                }
            }

            log.info("员工部门归属更新汇总: 成功更新={} 个, 未找到部门={} 个, org_code为空={} 个",
                    updateCount, notFoundCount, nullOrgCodeCount);
        } catch (Exception e) {
            log.error("员工部门归属更新失败", e);
            throw new RuntimeException("员工部门归属更新失败: " + e.getMessage(), e);
        }
    }

    /**
     * 同步单个员工岗位信息
     * 支持保存孤儿记录（没有对应用户的扩展数据）
     */
    private void syncEmployeePosition(String employeeMdmId, ExternalEmployeePosition position) {
        try {
            // 1. 根据MDM ID查找用户ID（允许为空）
            Long userId = employeeExtendedMapper.findUserIdByMdmId(employeeMdmId);

            if (userId == null) {
                log.warn("未找到MDM ID为 {} 的用户，将保存为孤儿记录", employeeMdmId);
            }

            // 2. 插入员工岗位信息（userId可以为null）
            Long positionId = snowflakeIdGenerator.generateId();
            employeeExtendedMapper.insertEmployeePosition(
                positionId,
                userId, // 可以为null
                position.getGuid(),
                position.getEmployeeMdmId(),
                position.getPositionCode(),
                position.getOrgCode(),
                position.getDepartmentCode(),
                position.getIsPrimary(),
                position.getStatus(),
                position.getIsActive(),
                position.getPositionDetailCode(),
                position.getId(), // 外部系统ID
                "SYNCED",
                LocalDateTime.now()
            );

            if (userId != null) {
                log.info("员工岗位数据已保存到 t_employee_position 表: userId={}, positionCode={}, orgCode={}, isPrimary={}",
                        userId, position.getPositionCode(), position.getOrgCode(), position.getIsPrimary());
            } else {
                log.info("员工岗位数据已保存为孤儿记录到 t_employee_position 表: mdmId={}, positionCode={}, orgCode={}, isPrimary={}",
                        employeeMdmId, position.getPositionCode(), position.getOrgCode(), position.getIsPrimary());
            }
        } catch (Exception e) {
            log.error("同步员工岗位失败: {} - {}", employeeMdmId, position.getPositionCode(), e);
            throw e;
        }
    }

    /**
     * 同步单个员工职称信息
     * 支持保存孤儿记录（没有对应用户的扩展数据）
     */
    private void syncEmployeeTitle(String employeeMdmId, ExternalEmployeeTitle title) {
        try {
            // 1. 根据MDM ID查找用户ID（允许为空）
            Long userId = employeeExtendedMapper.findUserIdByMdmId(employeeMdmId);

            if (userId == null) {
                log.warn("未找到MDM ID为 {} 的用户，将保存为孤儿记录", employeeMdmId);
            }

            // 2. 插入员工职称信息（userId可以为null）
            Long titleId = snowflakeIdGenerator.generateId();
            employeeExtendedMapper.insertEmployeeTitle(
                titleId,
                userId, // 可以为null
                title.getGuid(),
                title.getEmployeeMdmId(),
                title.getTitleCode(),
                title.getTitleType(),
                title.getTitleLevel(),
                title.getTitleName(),
                title.getStatus(),
                title.getTitleCategory(),
                null, // 外部系统ID（如果有的话）
                "SYNCED",
                LocalDateTime.now()
            );

            if (userId != null) {
                log.info("员工职称数据已保存到 t_employee_title 表: userId={}, titleName={}, titleType={}",
                        userId, title.getTitleName(), title.getTitleType());
            } else {
                log.info("员工职称数据已保存为孤儿记录到 t_employee_title 表: mdmId={}, titleName={}, titleType={}",
                        employeeMdmId, title.getTitleName(), title.getTitleType());
            }
        } catch (Exception e) {
            log.error("同步员工职称失败: {} - {}", employeeMdmId, title.getTitleName(), e);
            throw e;
        }
    }

    /**
     * 同步单个员工系统标识信息
     * 支持保存孤儿记录（没有对应用户的扩展数据）
     */
    private void syncEmployeeSystem(String employeeMdmId, ExternalEmployeeSystem system) {
        try {
            // 1. 根据MDM ID查找用户ID（允许为空）
            Long userId = employeeExtendedMapper.findUserIdByMdmId(employeeMdmId);

            if (userId == null) {
                log.warn("未找到MDM ID为 {} 的用户，将保存为孤儿记录", employeeMdmId);
            }

            // 2. 插入员工系统标识信息（userId可以为null）
            Long systemId = snowflakeIdGenerator.generateId();
            employeeExtendedMapper.insertEmployeeSystem(
                systemId,
                userId, // 可以为null
                system.getGuid(),
                system.getEmployeeMdmId(),
                system.getSystemCode(),
                system.getSystemDataId(),
                system.getOrgCode(),
                system.getDepartmentCode(),
                system.getEmployeeCode(),
                system.getLoginAccount(),
                null, // 外部系统ID（如果有的话）
                "SYNCED",
                LocalDateTime.now()
            );

            if (userId != null) {
                log.info("员工系统标识数据已保存到 t_employee_system 表: userId={}, systemCode={}, loginAccount={}",
                        userId, system.getSystemCode(), system.getLoginAccount());
            } else {
                log.info("员工系统标识数据已保存为孤儿记录到 t_employee_system 表: mdmId={}, systemCode={}, loginAccount={}",
                        employeeMdmId, system.getSystemCode(), system.getLoginAccount());
            }
        } catch (Exception e) {
            log.error("同步员工系统标识失败: {} - {}", employeeMdmId, system.getSystemCode(), e);
            throw e;
        }
    }

    /**
     * 同步单个部门子表信息
     */
    private void syncDepartmentChild(Long deptId, DepartmentChild child) {
        try {
            // 1. 生成ID
            Long childId = snowflakeIdGenerator.generateId();

            // 2. 插入到t_department_child表
            employeeExtendedMapper.insertDepartmentChild(
                childId,
                deptId,
                child.getGuid(),
                child.getDeptUuid(),
                child.getSourceSystem(),
                child.getSourceDataNm(),
                child.getUdef1(),
                child.getUdef2(),
                child.getUdef3(),
                child.getUdef4(),
                child.getUdef5(),
                child.getUdef6(),
                null, // 外部系统ID（如果有的话）
                "SYNCED",
                LocalDateTime.now()
            );

            log.info("部门子表数据已保存到 t_department_child 表: deptId={}, guid={}, sourceSystem={}",
                    deptId, child.getGuid(), child.getSourceSystem());
        } catch (Exception e) {
            log.error("同步部门子表信息失败: {} - {}", deptId, child.getGuid(), e);
            throw e;
        }
    }

    /**
     * 将外部部门数据转换为内部部门格式
     */
    private TOrgStructure convertToInternalDepartment(ExternalDepartment extDept) {
        TOrgStructure internalDept = new TOrgStructure();

        // 基础字段映射
        internalDept.setId(snowflakeIdGenerator.generateId());
        internalDept.setOrganName(extDept.getOrgName());

        // 处理父部门ID - 需要通过parent_code查找对应的内部ID
        if (extDept.getParentCode() != null && !extDept.getParentCode().isEmpty()) {
            Long parentId = orgCodeToIdMap.get(extDept.getParentCode());
            internalDept.setPreId(parentId);
        }

        // 设置默认值
        internalDept.setOrderInfo(1);
        internalDept.setIsDel(false);
        internalDept.setCreateTime(LocalDateTime.now());
        internalDept.setModifyTime(LocalDateTime.now());

        // 设置数据来源为数据同步
        internalDept.setDataSource(2); // 2表示数据同步

        // 注意：扩展字段（deptUuid、orgCode等）现在通过部门扩展表存储
        // 不再直接存储在t_org_structure表中，避免表结构冲突
        // 同步相关字段也不再存储在t_org_structure表中

        return internalDept;
    }

    /**
     * 将外部员工数据转换为内部用户格式
     * 注意：由于表结构简化，只保留核心字段，扩展字段通过员工扩展表存储
     */
    private TUser convertToInternalUser(ExternalEmployee extEmp) {
        TUser internalUser = new TUser();

        // 基础字段映射（只使用核心字段）
        internalUser.setId(snowflakeIdGenerator.generateId());
        internalUser.setUserName(extEmp.getEmployeeName());
        internalUser.setAccount(extEmp.getAccount());

        // 注意：部门关联现在需要通过employee_position表处理
        // 这里暂时不设置organ_affiliation，会在syncEmployeeExtendedData()和updateEmployeeDepartmentAffiliation()中处理

        // 状态转换：A=在职 -> false(未禁用), 其他 -> true(禁用)
        internalUser.setIsDisable(!"A".equals(extEmp.getStatus()));

        // 设置默认值
        internalUser.setIsDel(false);
        internalUser.setCreateTime(LocalDateTime.now());
        internalUser.setModifyTime(LocalDateTime.now());

        // 设置默认密码
        internalUser.setPassword("123456");

        // 注意：扩展字段（mdmId, employeeCode等）现在通过员工扩展表存储
        // 不再直接存储在t_user表中，避免表结构冲突
        // 同步相关字段也不再存储在t_user表中

        return internalUser;
    }

    /**
     * 保存或更新部门数据
     * 支持重复执行，避免主键冲突
     */
    private void saveOrUpdateDepartment(TOrgStructure department) {
        try {
            // 先尝试插入
            orgStructureMapper.insert(department);
            log.debug("保存部门成功: {}", department.getOrganName());
        } catch (Exception e) {
            // 如果是主键冲突，尝试更新
            if (e.getMessage().contains("duplicate key") || e.getMessage().contains("Duplicate entry")) {
                try {
                    log.warn("部门数据已存在，尝试更新: {}", department.getOrganName());
                    // 更新时间戳
                    department.setModifyTime(LocalDateTime.now());
                    orgStructureMapper.updateById(department);
                    log.info("更新部门成功: {}", department.getOrganName());
                } catch (Exception updateEx) {
                    log.error("更新部门失败: {}", department.getOrganName(), updateEx);
                    throw updateEx;
                }
            } else {
                log.error("保存部门失败: {}", department.getOrganName(), e);
                throw e;
            }
        }
    }

    /**
     * 保存或更新用户数据
     * 注意：由于表结构简化，暂时禁用基于mdm_id的去重逻辑
     * 改为基于account字段进行简单去重
     */
    private void saveOrUpdateUser(TUser user) {
        try {
            // 1. 基于账号进行简单去重检查
            if (user.getAccount() != null && !user.getAccount().trim().isEmpty()) {
                int existsCount = userMapper.checkAccountExists(user.getAccount());
                if (existsCount > 0) {
                    log.warn("账号已存在，跳过用户创建: account={}, userName={}", user.getAccount(), user.getUserName());
                    return;
                }
            }

            // 2. 直接插入新用户
            userMapper.insert(user);
            log.debug("保存新用户成功: account={}, userName={}", user.getAccount(), user.getUserName());

        } catch (Exception e) {
            log.error("保存用户失败: account={}, userName={}", user.getAccount(), user.getUserName(), e);
            throw e;
        }
    }

    // 注意：由于表结构简化，以下方法暂时不可用
    // cleanDuplicateUsersByMdmId() 和 updateExistingUser() 方法
    // 需要扩展字段支持才能正常工作

    /**
     * 批量同步员工数据（性能优化版本）
     * 适用于大数据量的同步场景
     *
     * @param externalEmployees 外部员工数据列表
     * @return 统计数组 [employeeCount, positionCount, titleCount, systemCount]
     */
    private int[] batchSyncEmployeesOptimized(List<ExternalEmployee> externalEmployees) {
        log.info("开始批量同步员工数据，总数量: {}", externalEmployees.size());

        // 1. 批量保存员工基本数据
        log.info("第1步：批量保存员工基本数据");
        List<TUser> users = new ArrayList<>();
        for (ExternalEmployee extEmp : externalEmployees) {
            TUser internalUser = convertToInternalUser(extEmp);
            users.add(internalUser);
        }

        // 分批保存用户数据（避免单次插入过多）
        int batchSize = 500;
        int employeeCount = 0;
        for (int i = 0; i < users.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, users.size());
            List<TUser> batch = users.subList(i, endIndex);
            for (TUser user : batch) {
                saveOrUpdateUser(user);
                employeeCount++;
            }
            log.info("已保存员工批次: {}/{}", endIndex, users.size());
        }

        // 2. 批量查询用户ID映射
        log.info("第2步：建立MDM ID到用户ID的映射");
        List<String> mdmIds = externalEmployees.stream()
                .map(ExternalEmployee::getMdmId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<Map<String, Object>> userMappings = employeeExtendedMapper.batchFindUserIdsByMdmIds(mdmIds);
        Map<String, Long> mdmIdToUserIdMap = userMappings.stream()
                .collect(Collectors.toMap(
                    map -> (String) map.get("mdm_id"),
                    map -> (Long) map.get("id")
                ));

        // 3. 批量准备扩展数据
        log.info("第3步：准备扩展数据批量插入");
        List<EmployeePositionBatch> positionBatches = new ArrayList<>();
        List<EmployeeTitleBatch> titleBatches = new ArrayList<>();
        List<EmployeeSystemBatch> systemBatches = new ArrayList<>();

        LocalDateTime now = LocalDateTime.now();

        for (ExternalEmployee extEmp : externalEmployees) {
            Long userId = mdmIdToUserIdMap.get(extEmp.getMdmId());

            // 准备岗位数据
            if (extEmp.getPositions() != null) {
                for (ExternalEmployeePosition position : extEmp.getPositions()) {
                    positionBatches.add(new EmployeePositionBatch(
                        snowflakeIdGenerator.generateId(),
                        userId,
                        position.getGuid(),
                        position.getEmployeeMdmId(),
                        position.getPositionCode(),
                        position.getOrgCode(),
                        position.getDepartmentCode(),
                        position.getIsPrimary(),
                        position.getStatus(),
                        position.getIsActive(),
                        position.getPositionDetailCode(),
                        position.getId(),
                        "SYNCED",
                        now
                    ));
                }
            }

            // 准备职称数据
            if (extEmp.getTitles() != null) {
                for (ExternalEmployeeTitle title : extEmp.getTitles()) {
                    titleBatches.add(new EmployeeTitleBatch(
                        snowflakeIdGenerator.generateId(),
                        userId,
                        title.getGuid(),
                        title.getEmployeeMdmId(),
                        title.getTitleCode(),
                        title.getTitleType(),
                        title.getTitleLevel(),
                        title.getTitleName(),
                        title.getStatus(),
                        title.getTitleCategory(),
                        null,
                        "SYNCED",
                        now
                    ));
                }
            }

            // 准备系统标识数据
            if (extEmp.getSystems() != null) {
                for (ExternalEmployeeSystem system : extEmp.getSystems()) {
                    systemBatches.add(new EmployeeSystemBatch(
                        snowflakeIdGenerator.generateId(),
                        userId,
                        system.getGuid(),
                        system.getEmployeeMdmId(),
                        system.getSystemCode(),
                        system.getSystemDataId(),
                        system.getOrgCode(),
                        system.getDepartmentCode(),
                        system.getEmployeeCode(),
                        system.getLoginAccount(),
                        null,
                        "SYNCED",
                        now
                    ));
                }
            }
        }

        // 4. 批量插入扩展数据
        log.info("第4步：批量插入扩展数据");
        int positionCount = batchInsertPositions(positionBatches);
        int titleCount = batchInsertTitles(titleBatches);
        int systemCount = batchInsertSystems(systemBatches);

        log.info("批量同步完成: 员工={}, 岗位={}, 职称={}, 系统={}",
                employeeCount, positionCount, titleCount, systemCount);

        return new int[]{employeeCount, positionCount, titleCount, systemCount};
    }

    /**
     * 批量插入员工岗位数据
     */
    private int batchInsertPositions(List<EmployeePositionBatch> positionBatches) {
        if (positionBatches.isEmpty()) {
            return 0;
        }

        int batchSize = 1000;
        int totalCount = 0;

        for (int i = 0; i < positionBatches.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, positionBatches.size());
            List<EmployeePositionBatch> batch = positionBatches.subList(i, endIndex);

            try {
                employeeExtendedMapper.batchInsertEmployeePositions(batch);
                totalCount += batch.size();
                log.info("批量插入岗位数据: {}/{}", endIndex, positionBatches.size());
            } catch (Exception e) {
                log.error("批量插入岗位数据失败: 批次 {}-{}", i, endIndex, e);
                // 降级为逐条插入
                for (EmployeePositionBatch position : batch) {
                    try {
                        employeeExtendedMapper.insertEmployeePosition(
                            position.getId(),
                            position.getUserId(),
                            position.getGuid(),
                            position.getEmployeeMdmId(),
                            position.getPositionCode(),
                            position.getOrgCode(),
                            position.getDepartmentCode(),
                            position.getIsPrimary(),
                            position.getStatus(),
                            position.getIsActive(),
                            position.getPositionDetailCode(),
                            position.getExternalId(),
                            position.getSyncStatus(),
                            position.getLastSyncTime()
                        );
                        totalCount++;
                    } catch (Exception ex) {
                        log.error("插入单条岗位数据失败: {}", position.getEmployeeMdmId(), ex);
                    }
                }
            }
        }

        return totalCount;
    }

    /**
     * 批量插入员工职称数据
     */
    private int batchInsertTitles(List<EmployeeTitleBatch> titleBatches) {
        if (titleBatches.isEmpty()) {
            return 0;
        }

        int batchSize = 1000;
        int totalCount = 0;

        for (int i = 0; i < titleBatches.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, titleBatches.size());
            List<EmployeeTitleBatch> batch = titleBatches.subList(i, endIndex);

            try {
                employeeExtendedMapper.batchInsertEmployeeTitles(batch);
                totalCount += batch.size();
                log.info("批量插入职称数据: {}/{}", endIndex, titleBatches.size());
            } catch (Exception e) {
                log.error("批量插入职称数据失败: 批次 {}-{}", i, endIndex, e);
                // 降级为逐条插入
                for (EmployeeTitleBatch title : batch) {
                    try {
                        employeeExtendedMapper.insertEmployeeTitle(
                            title.getId(),
                            title.getUserId(),
                            title.getGuid(),
                            title.getEmployeeMdmId(),
                            title.getTitleCode(),
                            title.getTitleType(),
                            title.getTitleLevel(),
                            title.getTitleName(),
                            title.getStatus(),
                            title.getTitleCategory(),
                            title.getExternalId(),
                            title.getSyncStatus(),
                            title.getLastSyncTime()
                        );
                        totalCount++;
                    } catch (Exception ex) {
                        log.error("插入单条职称数据失败: {}", title.getEmployeeMdmId(), ex);
                    }
                }
            }
        }

        return totalCount;
    }

    /**
     * 批量插入员工系统标识数据
     */
    private int batchInsertSystems(List<EmployeeSystemBatch> systemBatches) {
        if (systemBatches.isEmpty()) {
            return 0;
        }

        int batchSize = 1000;
        int totalCount = 0;

        for (int i = 0; i < systemBatches.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, systemBatches.size());
            List<EmployeeSystemBatch> batch = systemBatches.subList(i, endIndex);

            try {
                employeeExtendedMapper.batchInsertEmployeeSystems(batch);
                totalCount += batch.size();
                log.info("批量插入系统标识数据: {}/{}", endIndex, systemBatches.size());
            } catch (Exception e) {
                log.error("批量插入系统标识数据失败: 批次 {}-{}", i, endIndex, e);
                // 降级为逐条插入
                for (EmployeeSystemBatch system : batch) {
                    try {
                        employeeExtendedMapper.insertEmployeeSystem(
                            system.getId(),
                            system.getUserId(),
                            system.getGuid(),
                            system.getEmployeeMdmId(),
                            system.getSystemCode(),
                            system.getSystemDataId(),
                            system.getOrgCode(),
                            system.getDepartmentCode(),
                            system.getEmployeeCode(),
                            system.getLoginAccount(),
                            system.getExternalId(),
                            system.getSyncStatus(),
                            system.getLastSyncTime()
                        );
                        totalCount++;
                    } catch (Exception ex) {
                        log.error("插入单条系统标识数据失败: {}", system.getEmployeeMdmId(), ex);
                    }
                }
            }
        }

        return totalCount;
    }
}
