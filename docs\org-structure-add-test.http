### 新增部门接口测试

### 1. 新增根部门
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "总公司",
  "preId": null,
  "orderInfo": 1
}

### 2. 新增子部门（指定父部门ID）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "技术部",
  "preId": 1001,
  "orderInfo": 1
}

### 3. 新增子部门（不指定排序序号，使用默认值）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "市场部",
  "preId": 1001
}

### 4. 新增三级部门
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "前端开发组",
  "preId": 1002,
  "orderInfo": 1
}

### 5. 新增同级部门（测试排序）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "后端开发组",
  "preId": 1002,
  "orderInfo": 2
}

### 6. 测试重复名称（应该失败）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "技术部",
  "preId": 1001,
  "orderInfo": 3
}

### 7. 测试不存在的父部门（应该失败）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "测试部门",
  "preId": 99999,
  "orderInfo": 1
}

### 8. 测试空名称（应该失败）
POST http://localhost:8080/t-org-structure/add
Content-Type: application/json

{
  "organName": "",
  "preId": 1001,
  "orderInfo": 1
}
