package com.dfit.percode.service.impl;

import com.dfit.percode.config.SuperAdminConfig;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.service.IUserPermissionService;
import com.dfit.percode.service.SuperAdminPermissionService;
import com.dfit.percode.vo.RoleInfoVO;
import com.dfit.percode.vo.UserPermissionsResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 用户权限服务实现类
 * 封装用户权限查询逻辑，支持菜单和按钮权限分离
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class UserPermissionServiceImpl implements IUserPermissionService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SuperAdminConfig superAdminConfig;

    @Autowired
    private SuperAdminPermissionService superAdminPermissionService;

    /**
     * 根据用户ID查询用户权限（支持权限类型筛选）
     * 实现权限查询链路：用户 → 角色 → 权限数据
     * 支持菜单、按钮和数据权限的分离查询
     *
     * @param userId           用户ID
     * @param moduleIdentifier 模块标识符，可选
     * @param moduleType       权限类型，可选值：menu（菜单）、data（数据）、all（全部）
     * @return 用户权限数据
     */
    @Override
    public UserPermissionsResponseVO getUserPermissions(Long userId, String moduleIdentifier, String moduleType) {
        log.info("开始查询用户权限，用户ID: {}, 模块标识: {}, 权限类型: {}", userId, moduleIdentifier, moduleType);
        long startTime = System.currentTimeMillis();

        try {
            // 第0步：超级管理员判断（优先级最高）
            if (superAdminConfig.getEnabled() && superAdminConfig.isSuperAdmin(userId)) {
                log.info("检测到超级管理员，返回全量权限，用户ID: {}, 权限类型: {}", userId, moduleType);

                UserPermissionsResponseVO superAdminPermissions = superAdminPermissionService.getSuperAdminPermissions(userId, moduleIdentifier);

                long endTime = System.currentTimeMillis();
                log.info("超级管理员权限查询完成，用户ID: {}, 耗时: {}ms", userId, (endTime - startTime));

                return superAdminPermissions;
            }

            // 第1步：查询用户角色
            log.debug("第1步：查询用户角色");
            List<RoleInfoVO> userRoles = userMapper.findUserRolesByUserId(userId);
            if (userRoles.isEmpty()) {
                log.warn("用户ID: {} 没有分配任何角色", userId);
                return createEmptyPermissions(userId);
            }
            log.info("用户拥有角色数量: {}", userRoles.size());

            // 第2步：根据权限类型查询相应权限
            log.debug("第2步：根据权限类型查询相应权限，权限类型: {}", moduleType);

            // 初始化权限列表
            List<UserPermissionsResponseVO.MenuPermissionVO> menuTree = new ArrayList<>();
            List<UserPermissionsResponseVO.ButtonPermissionVO> buttons = new ArrayList<>();
            List<UserPermissionsResponseVO.DataPermissionVO> dataPermissions = new ArrayList<>();

            // 根据moduleType决定查询哪些权限
            if ("menu".equals(moduleType) || "all".equals(moduleType)) {
                // 查询菜单和按钮权限
                List<Map<String, Object>> userMenus = userMapper.findUserMenuPermissions(userId, moduleIdentifier);
                if (!userMenus.isEmpty()) {
                    log.info("用户拥有菜单权限数量: {}", userMenus.size());
                    processMenuPermissions(userMenus, menuTree, buttons);
                } else {
                    log.warn("用户ID: {} 没有任何菜单权限", userId);
                }
            }

            if ("data".equals(moduleType) || "all".equals(moduleType)) {
                // 查询数据权限
                List<Map<String, Object>> userDataPermissions = userMapper.findUserDataPermissions(userId, moduleIdentifier);
                if (!userDataPermissions.isEmpty()) {
                    log.info("用户拥有数据权限数量: {}", userDataPermissions.size());
                    dataPermissions = processDataPermissions(userDataPermissions);
                } else {
                    log.warn("用户ID: {} 没有任何数据权限", userId);
                }
            }

            // 检查是否有任何权限
            if (menuTree.isEmpty() && buttons.isEmpty() && dataPermissions.isEmpty()) {
                log.warn("用户ID: {} 没有任何权限", userId);
                return createEmptyPermissions(userId);
            }

            log.info("菜单权限数量: {}, 按钮权限数量: {}, 数据权限数量: {}", menuTree.size(), buttons.size(), dataPermissions.size());

            // 第3步：构建返回结果
            UserPermissionsResponseVO result = new UserPermissionsResponseVO();
            result.setUserid(userId);

            UserPermissionsResponseVO.PermissionsData permissions = new UserPermissionsResponseVO.PermissionsData();
            permissions.setMenus(menuTree);
            permissions.setButtons(buttons);
            permissions.setDataPermissions(dataPermissions);
            result.setPermissions(permissions);

            long endTime = System.currentTimeMillis();
            log.info("用户权限查询完成，用户ID: {}, 根菜单数量: {}, 按钮数量: {}, 数据权限数量: {}, 耗时: {}ms",
                    userId, menuTree.size(), buttons.size(), dataPermissions.size(), (endTime - startTime));

            return result;

        } catch (Exception e) {
            log.error("查询用户权限失败，用户ID: {}", userId, e);
            throw new RuntimeException("查询用户权限失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理菜单权限数据
     * 分离菜单和按钮权限，并构建菜单树形结构
     *
     * @param userMenus 用户菜单权限原始数据
     * @param menuTree 菜单树形结构（输出参数）
     * @param buttons 按钮权限列表（输出参数）
     */
    private void processMenuPermissions(List<Map<String, Object>> userMenus,
                                       List<UserPermissionsResponseVO.MenuPermissionVO> menuTree,
                                       List<UserPermissionsResponseVO.ButtonPermissionVO> buttons) {
        log.info("=== 开始处理菜单权限数据，原始数据量: {} ===", userMenus.size());

        List<UserPermissionsResponseVO.MenuPermissionVO> menus = new ArrayList<>();

        // 调试：打印所有查询到的菜单
        for (int i = 0; i < userMenus.size(); i++) {
            Map<String, Object> menu = userMenus.get(i);
            log.info("原始菜单数据[{}]: ID={}, Name={}, PreId={}, MenuType={}, RouteAddress={}",
                    i, menu.get("id"), menu.get("name"), menu.get("preId"), menu.get("menuType"), menu.get("routeAddress"));
        }

        // 分离菜单和按钮权限
        for (Map<String, Object> menu : userMenus) {
            Integer menuType = (Integer) menu.get("menuType");
            if (menuType != null && menuType == 3) {
                // 按钮权限（menuType = 3）
                UserPermissionsResponseVO.ButtonPermissionVO button = new UserPermissionsResponseVO.ButtonPermissionVO();
                button.setId(String.valueOf(menu.get("id")));
                button.setName((String) menu.get("name"));
                button.setPermissionIdentifier((String) menu.get("permissionIdentifier"));
                buttons.add(button);
                log.debug("添加按钮权限: {}", button.getName());
            } else {
                // 菜单权限（menuType != 3）
                UserPermissionsResponseVO.MenuPermissionVO menuVO = new UserPermissionsResponseVO.MenuPermissionVO();
                menuVO.setId(String.valueOf(menu.get("id")));
                menuVO.setName((String) menu.get("name"));

                // 正确处理preId，避免null转换成"null"字符串
                Object preIdObj = menu.get("preId");
                menuVO.setPreId(preIdObj != null ? String.valueOf(preIdObj) : "0");

                menuVO.setMenuType(menuType);
                menuVO.setRouteAddress((String) menu.get("routeAddress"));
                menuVO.setComponentPath((String) menu.get("componentPath"));
                menuVO.setPermissionIdentifier((String) menu.get("permissionIdentifier"));
                menuVO.setRouteParam((String) menu.get("routeParam"));
                menuVO.setPreName((String) menu.get("preName"));
                menuVO.setChildren(new ArrayList<>());
                menus.add(menuVO);
                log.debug("添加菜单权限: ID={}, Name={}, PreId={}, MenuType={}",
                        menuVO.getId(), menuVO.getName(), menuVO.getPreId(), menuVO.getMenuType());
            }
        }

        log.info("处理完成 - 菜单数量: {}, 按钮数量: {}", menus.size(), buttons.size());

        // 构建菜单树形结构
        List<UserPermissionsResponseVO.MenuPermissionVO> tree = buildMenuTree(menus);
        menuTree.addAll(tree);

        log.info("=== 菜单权限数据处理完成，最终菜单树根节点数量: {} ===", tree.size());
    }

    /**
     * 创建空权限数据
     *
     * @param userId 用户ID
     * @return 空权限数据
     */
    private UserPermissionsResponseVO createEmptyPermissions(Long userId) {
        UserPermissionsResponseVO result = new UserPermissionsResponseVO();
        result.setUserid(userId);

        UserPermissionsResponseVO.PermissionsData permissions = new UserPermissionsResponseVO.PermissionsData();
        permissions.setMenus(new ArrayList<>());
        permissions.setButtons(new ArrayList<>());
        permissions.setDataPermissions(new ArrayList<>());
        result.setPermissions(permissions);

        return result;
    }

    /**
     * 构建菜单树形结构
     * 在内存中构建父子关系，避免递归数据库查询
     *
     * @param menus 菜单列表
     * @return 菜单树形结构
     */
    private List<UserPermissionsResponseVO.MenuPermissionVO> buildMenuTree(List<UserPermissionsResponseVO.MenuPermissionVO> menus) {
        log.info("=== 开始构建菜单树，总菜单数量: {} ===", menus.size());

        // 构建ID映射和父子关系映射
        Map<String, UserPermissionsResponseVO.MenuPermissionVO> menuMap = new HashMap<>();
        Map<String, List<UserPermissionsResponseVO.MenuPermissionVO>> parentChildMap = new HashMap<>();

        // 第1步：构建映射关系
        for (UserPermissionsResponseVO.MenuPermissionVO menu : menus) {
            log.debug("处理菜单: ID={}, Name={}, PreId={}", menu.getId(), menu.getName(), menu.getPreId());
            menuMap.put(menu.getId(), menu);

            String parentId = menu.getPreId();
            if (parentId == null || "null".equals(parentId)) {
                parentId = "0";
            }
            parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
            log.debug("菜单 {} 归属到父节点 {}", menu.getName(), parentId);
        }

        // 打印父子关系映射
        log.info("=== 父子关系映射统计 ===");
        for (Map.Entry<String, List<UserPermissionsResponseVO.MenuPermissionVO>> entry : parentChildMap.entrySet()) {
            log.info("父节点ID: {}, 子菜单数量: {}, 子菜单: {}",
                    entry.getKey(),
                    entry.getValue().size(),
                    entry.getValue().stream().map(UserPermissionsResponseVO.MenuPermissionVO::getName).collect(java.util.stream.Collectors.joining(", ")));
        }

        // 第2步：找出根节点（preId = 0 或 null）
        List<UserPermissionsResponseVO.MenuPermissionVO> rootMenus = parentChildMap.get("0");
        if (rootMenus == null) {
            rootMenus = new ArrayList<>();
        }
        log.info("根节点菜单数量: {}", rootMenus.size());

        // 第3步：递归构建树形结构
        for (UserPermissionsResponseVO.MenuPermissionVO rootMenu : rootMenus) {
            log.info("开始构建根菜单树: {}", rootMenu.getName());
            buildMenuTreeRecursive(rootMenu, parentChildMap);
        }

        log.info("=== 菜单树构建完成 ===");
        return rootMenus;
    }

    /**
     * 递归构建菜单树形结构
     *
     * @param parentMenu     父菜单
     * @param parentChildMap 父子关系映射
     */
    private void buildMenuTreeRecursive(UserPermissionsResponseVO.MenuPermissionVO parentMenu,
                                        Map<String, List<UserPermissionsResponseVO.MenuPermissionVO>> parentChildMap) {
        log.debug("递归处理菜单: ID={}, Name={}", parentMenu.getId(), parentMenu.getName());

        List<UserPermissionsResponseVO.MenuPermissionVO> children = parentChildMap.get(parentMenu.getId());
        log.debug("查找子菜单: 父菜单ID={}, 找到子菜单数量={}", parentMenu.getId(), children != null ? children.size() : 0);

        if (children != null && !children.isEmpty()) {
            log.info("菜单 {} 设置子菜单，数量: {}, 子菜单列表: {}",
                    parentMenu.getName(),
                    children.size(),
                    children.stream().map(UserPermissionsResponseVO.MenuPermissionVO::getName).collect(java.util.stream.Collectors.joining(", ")));
            parentMenu.setChildren(children);

            // 递归处理子菜单
            for (UserPermissionsResponseVO.MenuPermissionVO child : children) {
                buildMenuTreeRecursive(child, parentChildMap);
            }
        } else {
            log.warn("菜单 {} (ID: {}) 没有找到子菜单", parentMenu.getName(), parentMenu.getId());
            // 确保设置空列表而不是null
            parentMenu.setChildren(new ArrayList<>());
        }
    }

    /**
     * 处理数据权限数据
     * 将原始数据权限数据转换为VO对象，并构建树形结构
     *
     * @param userDataPermissions 用户数据权限原始数据
     * @return 数据权限树形结构列表
     */
    private List<UserPermissionsResponseVO.DataPermissionVO> processDataPermissions(List<Map<String, Object>> userDataPermissions) {
        // 按数据权限分组操作类型
        Map<String, List<Integer>> dataOperationsMap = new HashMap<>();
        Map<String, Map<String, Object>> dataInfoMap = new HashMap<>();

        for (Map<String, Object> permission : userDataPermissions) {
            String dataId = String.valueOf(permission.get("id"));
            Integer operateType = (Integer) permission.get("operateType");

            dataOperationsMap.computeIfAbsent(dataId, k -> new ArrayList<>()).add(operateType);
            dataInfoMap.put(dataId, permission);
        }

        // 构建DataPermissionVO列表
        List<UserPermissionsResponseVO.DataPermissionVO> dataPermissions = new ArrayList<>();
        for (Map.Entry<String, Map<String, Object>> entry : dataInfoMap.entrySet()) {
            Map<String, Object> dataInfo = entry.getValue();
            List<Integer> operations = dataOperationsMap.get(entry.getKey());

            UserPermissionsResponseVO.DataPermissionVO dataPermission = new UserPermissionsResponseVO.DataPermissionVO();
            dataPermission.setId(String.valueOf(dataInfo.get("id")));
            dataPermission.setName((String) dataInfo.get("name"));
            dataPermission.setPreId(String.valueOf(dataInfo.get("preId")));
            dataPermission.setModuleIdentifier((String) dataInfo.get("moduleIdentifier"));
            dataPermission.setDataIdentifier((String) dataInfo.get("dataIdentifier"));
            dataPermission.setOperations(operations);
            dataPermission.setOperationNames(convertOperationNames(operations));
            dataPermission.setChildren(new ArrayList<>());

            dataPermissions.add(dataPermission);
        }

        // 构建树形结构
        return buildDataPermissionTree(dataPermissions);
    }

    /**
     * 转换操作类型为操作名称
     *
     * @param operations 操作类型列表
     * @return 操作名称列表
     */
    private List<String> convertOperationNames(List<Integer> operations) {
        return operations.stream()
                .map(this::getOperateTypeDescription)
                .collect(Collectors.toList());
    }

    /**
     * 获取操作类型描述
     *
     * @param operateType 操作类型
     * @return 操作类型描述
     */
    private String getOperateTypeDescription(Integer operateType) {
        if (operateType == null) {
            return "未知";
        }
        switch (operateType) {
            case 1:
                return "查看";
            case 2:
                return "修改";
            case 3:
                return "下载";
            case 4:
                return "删除";
            default:
                return "未知";
        }
    }

    /**
     * 构建数据权限树形结构
     *
     * @param dataPermissions 数据权限列表
     * @return 数据权限树形结构
     */
    private List<UserPermissionsResponseVO.DataPermissionVO> buildDataPermissionTree(List<UserPermissionsResponseVO.DataPermissionVO> dataPermissions) {
        // 构建ID映射和父子关系映射
        Map<String, UserPermissionsResponseVO.DataPermissionVO> dataMap = new HashMap<>();
        Map<String, List<UserPermissionsResponseVO.DataPermissionVO>> parentChildMap = new HashMap<>();

        // 建立映射关系
        for (UserPermissionsResponseVO.DataPermissionVO data : dataPermissions) {
            dataMap.put(data.getId(), data);
            parentChildMap.computeIfAbsent(data.getPreId(), k -> new ArrayList<>()).add(data);
        }

        // 构建树形结构
        List<UserPermissionsResponseVO.DataPermissionVO> rootDataPermissions = parentChildMap.get("0");
        if (rootDataPermissions != null) {
            for (UserPermissionsResponseVO.DataPermissionVO rootData : rootDataPermissions) {
                buildDataPermissionTreeRecursive(rootData, parentChildMap);
            }
        }

        return rootDataPermissions != null ? rootDataPermissions : new ArrayList<>();
    }

    /**
     * 递归构建数据权限树形结构
     *
     * @param parentData     父数据权限
     * @param parentChildMap 父子关系映射
     */
    private void buildDataPermissionTreeRecursive(UserPermissionsResponseVO.DataPermissionVO parentData,
                                                 Map<String, List<UserPermissionsResponseVO.DataPermissionVO>> parentChildMap) {
        List<UserPermissionsResponseVO.DataPermissionVO> children = parentChildMap.get(parentData.getId());
        if (children != null && !children.isEmpty()) {
            parentData.setChildren(children);
            // 递归处理子数据权限
            for (UserPermissionsResponseVO.DataPermissionVO child : children) {
                buildDataPermissionTreeRecursive(child, parentChildMap);
            }
        }
    }
}