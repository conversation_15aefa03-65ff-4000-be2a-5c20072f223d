package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色详情响应VO类
 * 包含角色基本信息和已关联的权限ID列表
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleDetailResponseVO", description = "角色详情响应")
public class RoleDetailResponseVO {

    @ApiModelProperty(value = "角色基本信息")
    private RoleInfo roleInfo;

    @ApiModelProperty(value = "已关联的菜单权限ID列表", example = "[123, 456, 789]")
    private List<Long> menuPermissions;

    @ApiModelProperty(value = "已关联的数据权限列表，包含权限ID和操作类型")
    private List<DataPermissionItem> dataPermissions;

    /**
     * 数据权限项内部类
     */
    @Data
    @ApiModel(value = "DataPermissionItem", description = "数据权限项")
    public static class DataPermissionItem {

        @ApiModelProperty(value = "数据权限ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long dataId;

        @ApiModelProperty(value = "操作类型列表", example = "[1, 2, 3]",
                         notes = "1-新增，2-修改，3-删除，4-查看")
        private List<Integer> operateTypes;
    }

    /**
     * 角色基本信息内部类
     */
    @Data
    @ApiModel(value = "RoleInfo", description = "角色基本信息")
    public static class RoleInfo {

        @ApiModelProperty(value = "角色ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long roleId;

        @ApiModelProperty(value = "角色名称", example = "系统管理员")
        private String roleName;

        @ApiModelProperty(value = "排序序号", example = "1")
        private Integer orderInfo;

        @ApiModelProperty(value = "是否禁用", example = "false")
        private Boolean isDisable;

        @ApiModelProperty(value = "创建时间", example = "2024-01-01 10:00:00")
        private String createTime;

        @ApiModelProperty(value = "修改时间", example = "2024-01-01 10:00:00")
        private String modifyTime;
    }
}
