-- =====================================================
-- 权限管理系统性能优化索引（兼容低版本PostgreSQL）
-- 解决 /users/getAllUsers 接口性能问题
-- =====================================================

-- 先检查索引是否已存在，避免重复创建
SELECT indexname FROM pg_indexes WHERE tablename = 't_user' AND indexname = 'idx_user_organ_affiliation';
SELECT indexname FROM pg_indexes WHERE tablename = 't_org_structure' AND indexname = 'idx_org_structure_pre_id';

-- =====================================================
-- 1. 最关键的两个索引（必须创建）
-- =====================================================

-- 为用户表的部门归属字段添加索引（最重要）
-- 如果索引已存在会报错，可以忽略
CREATE INDEX idx_user_organ_affiliation ON t_user(organ_affiliation);

-- 为组织架构表的父级ID添加索引（最重要）  
-- 如果索引已存在会报错，可以忽略
CREATE INDEX idx_org_structure_pre_id ON t_org_structure(pre_id);

-- =====================================================
-- 2. 其他重要索引
-- =====================================================

-- 为用户表的删除标记和部门归属添加复合索引
CREATE INDEX idx_user_is_del_organ ON t_user(is_del, organ_affiliation);

-- 为组织架构表的删除标记和父级ID添加复合索引
CREATE INDEX idx_org_structure_is_del_pre ON t_org_structure(is_del, pre_id);

-- 为用户表的用户名添加索引
CREATE INDEX idx_user_name ON t_user(user_name);

-- 为用户表的员工编号添加索引
CREATE INDEX idx_user_employee_code ON t_user(employee_code);

-- 为组织架构表的排序字段添加索引
CREATE INDEX idx_org_structure_order ON t_org_structure(order_info);

-- =====================================================
-- 3. 角色权限相关索引
-- =====================================================

-- 为用户角色关联表添加索引
CREATE INDEX idx_perm_user_role_user_id ON t_perm_user_role(user_id);
CREATE INDEX idx_perm_user_role_role_id ON t_perm_user_role(role_id);

-- 为角色表添加索引
CREATE INDEX idx_role_name ON t_role(role_name);

-- =====================================================
-- 4. 验证索引创建结果
-- =====================================================

-- 查看新创建的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('t_user', 't_org_structure')
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- =====================================================
-- 如果需要删除索引（测试用）
-- =====================================================

/*
-- 删除索引的SQL（如果需要重新创建）
DROP INDEX IF EXISTS idx_user_organ_affiliation;
DROP INDEX IF EXISTS idx_org_structure_pre_id;
DROP INDEX IF EXISTS idx_user_is_del_organ;
DROP INDEX IF EXISTS idx_org_structure_is_del_pre;
DROP INDEX IF EXISTS idx_user_name;
DROP INDEX IF EXISTS idx_user_employee_code;
DROP INDEX IF EXISTS idx_org_structure_order;
DROP INDEX IF EXISTS idx_perm_user_role_user_id;
DROP INDEX IF EXISTS idx_perm_user_role_role_id;
DROP INDEX IF EXISTS idx_role_name;
*/
