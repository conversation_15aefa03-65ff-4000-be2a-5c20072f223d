-- 针对 /users/getUnauthorizedUsers 接口的数据库优化索引
-- 适用于大数据量场景：100个部门 + 4万条员工

-- 1. 组织架构表优化索引
-- 用于 RECURSIVE CTE 查询部门树
CREATE INDEX IF NOT EXISTS idx_org_structure_pre_id_is_del 
ON t_org_structure(pre_id, is_del) 
WHERE is_del = false;

-- 用于根部门查询
CREATE INDEX IF NOT EXISTS idx_org_structure_root_dept 
ON t_org_structure(pre_id, is_del) 
WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false;

-- 2. 用户表优化索引
-- 用于按部门查询用户
CREATE INDEX IF NOT EXISTS idx_user_organ_affiliation_is_del 
ON t_user(organ_affiliation, is_del) 
WHERE is_del = false;

-- 用于用户基本信息查询
CREATE INDEX IF NOT EXISTS idx_user_is_del_is_disable 
ON t_user(is_del, is_disable);

-- 3. 用户角色关联表优化索引
-- 用于检查用户是否已授权某角色（核心查询）
CREATE INDEX IF NOT EXISTS idx_user_role_user_role_is_del 
ON t_perm_user_role(user_id, role_id, is_del);

-- 用于角色相关查询
CREATE INDEX IF NOT EXISTS idx_user_role_role_id_is_del 
ON t_perm_user_role(role_id, is_del) 
WHERE is_del = false;

-- 4. 复合索引优化（针对未授权用户查询）
-- 这个索引专门优化 NOT EXISTS 子查询
CREATE INDEX IF NOT EXISTS idx_user_role_exists_check 
ON t_perm_user_role(user_id, role_id) 
WHERE is_del = false;

-- 5. 统计信息更新（PostgreSQL）
-- 确保查询优化器有最新的统计信息
ANALYZE t_org_structure;
ANALYZE t_user;
ANALYZE t_perm_user_role;

-- 6. 查询性能监控
-- 可以使用以下查询监控性能
/*
-- 查看索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('t_org_structure', 't_user', 't_perm_user_role')
ORDER BY idx_scan DESC;

-- 查看表扫描情况
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch
FROM pg_stat_user_tables 
WHERE tablename IN ('t_org_structure', 't_user', 't_perm_user_role');
*/
