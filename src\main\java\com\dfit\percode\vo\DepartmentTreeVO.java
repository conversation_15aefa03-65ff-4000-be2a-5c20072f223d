package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织架构树形结构VO类
 * 用于前端组织架构选择人员接口
 * 按照前端实际使用的数据格式设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DepartmentTreeVO", description = "组织架构树形结构")
public class DepartmentTreeVO {

    @ApiModelProperty("ID（部门ID或用户ID）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty("组织名称（部门名称）")
    private String organName;

    @ApiModelProperty("用户名称（当节点为用户时使用）")
    private String userName;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用（仅用户节点有效）", example = "false")
    private Boolean isDisable;

    @ApiModelProperty("子节点列表（可能是子部门或用户）")
    private List<DepartmentTreeVO> children;
}
