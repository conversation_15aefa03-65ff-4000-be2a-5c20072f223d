package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.IUserService;
import com.dfit.percode.vo.AddMemberRequestVO;
import com.dfit.percode.vo.DeleteUserRequestVO;
import com.dfit.percode.vo.DepartmentTreeVO;
import com.dfit.percode.vo.MemberInfoVO;
import com.dfit.percode.vo.RoleInfoVO;
import com.dfit.percode.vo.UpdateUserRequestVO;
import com.dfit.percode.vo.UserDetailRequestVO;
import com.dfit.percode.vo.UserDetailResponseVO;
import com.dfit.percode.vo.UserListRequestVO;
import com.dfit.percode.vo.UserListResponseVO;
import com.dfit.percode.vo.UserListByOrgRequestVO;
import com.dfit.percode.vo.UserListByOrgResponseVO;
import com.dfit.percode.vo.RoleUserListRequestVO;
import com.dfit.percode.vo.RoleUserListResponseVO;
import com.dfit.percode.vo.UnauthorizedUsersRequestVO;
import com.dfit.percode.vo.UnauthorizedUsersResponseVO;
import com.dfit.percode.vo.UpdateUserRoleRequestVO;
import com.dfit.percode.vo.DepartmentTreeOnlyVO;
import com.dfit.percode.vo.DepartmentUsersRequestVO;
import com.dfit.percode.vo.DepartmentUsersResponseVO;
import com.dfit.percode.vo.CheckUserDuplicateRequestVO;
import com.dfit.percode.vo.CheckUserDuplicateResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户相关控制器
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@RequestMapping("/users")
@Api(tags = "用户管理接口")
public class UserController {

    @Autowired
    private IUserService userService;

    /**
     * 获取所有用户（组织架构树形结构）
     * 用于前端选择人员功能
     *
     * @return 统一返回格式，包含部门树形结构数据
     */
    @RequestMapping(value = "/getALLUsers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取所有用户", notes = "返回组织架构树形结构，用于前端选择人员")
    public BaseResult getAllUsers() {
        BaseResult baseResult = new BaseResult();

        try {
            // 获取组织架构树形结构数据
            List<DepartmentTreeVO> departmentTree = userService.getAllUsers();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(departmentTree);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取用户数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取所有用户（组织架构树形结构）- 优化版本
     * 使用 RECURSIVE CTE 查询，避免 N+1 查询问题，提升性能
     * 功能与 getALLUsers() 完全相同，仅实现方式不同
     *
     * @return 统一返回格式，包含部门树形结构数据
     */
    @RequestMapping(value = "/getAllUsersOptimized", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取所有用户-优化版", notes = "使用RECURSIVE优化查询，返回组织架构树形结构，性能更佳")
    public BaseResult getAllUsersOptimized() {
        BaseResult baseResult = new BaseResult();

        try {
            // 获取组织架构树形结构数据（优化版本）
            List<DepartmentTreeVO> departmentTree = userService.getAllUsersOptimized();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(departmentTree);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取用户数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 添加成员到权限管理系统
     * 按照前端设计直接接收数组格式
     * 支持新用户创建和现有用户角色分配
     *
     * 使用方式：
     * - 新增用户：MemberInfoVO.userId为null，提供userName、account、password、organAffiliation等信息
     * - 现有用户角色分配：MemberInfoVO.userId不为null，主要用于角色关联
     *
     * @param members 成员列表（直接数组格式）
     * @return 统一返回格式
     */
    @RequestMapping(value = "/addMembers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "添加成员", notes = "批量添加成员到权限管理系统，支持新用户创建和角色分配")
    public BaseResult addMembers(@RequestBody List<MemberInfoVO> members) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层添加成员
            userService.addMembers(members);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("添加成员失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取用户详细信息
     * 用于编辑用户时回显数据
     * 支持单个用户查询和批量用户查询
     *
     * @param request 用户详情请求参数
     * @return 统一返回格式（单个查询返回对象，批量查询返回数组）
     */
    @RequestMapping(value = "/getUserDetail", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取用户详情", notes = "获取用户详细信息，支持单个用户查询和批量用户查询，按传入顺序返回")
    public BaseResult getUserDetail(@RequestBody UserDetailRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 参数验证
            if ((request.getUserId() == null) &&
                (request.getUserIds() == null || request.getUserIds().isEmpty())) {
                baseResult.setCode(400);
                baseResult.setMessage("userId或userIds参数不能为空");
                return baseResult;
            }

            // 调用服务层获取用户详情
            Object result = userService.getUserDetail(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result); // 自动处理单个对象或数组

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取用户详情失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 更新用户信息
     * 包含用户基本信息和角色分配的更新
     *
     * @param request 更新用户信息请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/updateUser", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "更新用户信息", notes = "更新用户基本信息和角色分配")
    public BaseResult updateUser(@RequestBody UpdateUserRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层更新用户信息
            userService.updateUser(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("更新用户信息失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除用户
     * 逻辑删除用户基本信息和所有角色关联
     *
     * @param request 删除用户请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteUser", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除用户", notes = "逻辑删除用户基本信息和所有角色关联")
    public BaseResult deleteUser(@RequestBody DeleteUserRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除用户
            userService.deleteUser(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("删除用户失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 分页查询用户列表
     * 支持多条件搜索和分页
     *
     * @param request 分页查询请求参数
     * @return 统一返回格式，包含分页数据
     */
    @RequestMapping(value = "/getUserList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "分页查询用户列表", notes = "支持多条件搜索和分页，返回用户列表和分页信息")
    public BaseResult getUserList(@RequestBody UserListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层分页查询用户列表
            UserListResponseVO userListResponse = userService.getUserList(request);

            // 按照前端格式设置返回结果：data直接是数组，total在根级别
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(userListResponse.getRecords()); // data直接是用户数组
            baseResult.setTotal(userListResponse.getTotal().intValue()); // total在根级别

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("查询用户列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 清理重复的用户角色关联记录
     * 用于修复数据不一致问题
     *
     * @return 统一返回格式
     */
    @RequestMapping(value = "/cleanDuplicateUserRoles", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "清理重复角色", notes = "清理重复的用户角色关联记录，修复数据不一致问题")
    public BaseResult cleanDuplicateUserRoles() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层清理重复角色
            userService.cleanDuplicateUserRoles();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData("重复角色关联记录清理完成");

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("清理重复角色失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取所有可用的角色选项
     * 用于下拉框选择
     *
     * @return 统一返回格式，包含角色选项列表
     */
    @RequestMapping(value = "/getRoleOptions", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取角色选项", notes = "获取所有可用的角色选项，用于下拉框选择")
    public BaseResult getRoleOptions() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取角色选项
            List<RoleInfoVO> roleOptions = userService.getRoleOptions();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(roleOptions);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取角色选项失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取角色的用户分配列表
     * 用于角色管理中的分配用户功能
     * 支持分页查询和多条件搜索
     *
     * @param request 角色用户列表查询请求参数
     * @return 统一返回格式，包含用户列表和分页信息
     */
    @RequestMapping(value = "/getRoleUserList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取角色用户列表", notes = "获取角色的用户分配列表，支持分页和搜索，返回用户状态使用isDisable字段，包含角色名称")
    public BaseResult getRoleUserList(@RequestBody RoleUserListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取角色用户列表
            RoleUserListResponseVO roleUserListResponse = userService.getRoleUserList(request);

            // 按照前端格式设置返回结果：data直接是数组，total在根级别
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(roleUserListResponse.getRecords()); // data直接是用户数组
            baseResult.setTotal(roleUserListResponse.getTotal().intValue()); // total在根级别

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取角色用户列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 更新用户角色分配状态
     * 用于角色管理中的授权和取消授权操作
     * 修改t_perm_user_role关联表的is_del状态
     *
     * @param request 更新用户角色分配请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/userRole", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "更新用户角色分配", notes = "角色授权和取消授权操作，修改关联表状态")
    public BaseResult updateUserRole(@RequestBody UpdateUserRoleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层更新用户角色分配状态
            userService.updateUserRole(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("更新用户角色分配失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 按部门分页查询用户列表
     * 支持包含子部门查询、多条件搜索和分页
     *
     * @param request 按部门查询用户列表请求参数
     * @return 统一返回格式，包含用户列表和分页信息
     */
    @RequestMapping(value = "/list-by-org", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "按部门查询用户列表", notes = "支持包含子部门查询、多条件搜索和分页")
    public BaseResult getUserListByOrg(@RequestBody UserListByOrgRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层按部门分页查询用户列表
            UserListByOrgResponseVO userListResponse = userService.getUserListByOrg(request);

            // 按照前端格式设置返回结果：data直接是数组，total在根级别
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(userListResponse.getRecords()); // data直接是用户数组
            baseResult.setTotal(userListResponse.getTotal().intValue()); // total在根级别

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("按部门查询用户列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    // ==================== 新增：高性能部门树和用户查询接口 ====================

    /**
     * 获取纯部门树结构（不包含用户信息）
     * 高性能版本：只返回部门层级结构和用户数量统计
     * 用于快速展示部门树，支持按需加载用户
     *
     * @return 统一返回格式，包含部门树结构数据
     */
    @RequestMapping(value = "/departments-tree-only", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取纯部门树结构", notes = "高性能版本，只返回部门层级结构和用户数量统计，用于快速展示部门树")
    public BaseResult getDepartmentTreeOnly() {
        BaseResult baseResult = new BaseResult();

        try {
            // 获取纯部门树结构数据
            List<DepartmentTreeOnlyVO> departmentTree = userService.getDepartmentTreeOnly();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(departmentTree);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取部门树结构失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 根据部门ID获取该部门的用户列表
     * 支持只查询直属用户或包含子部门用户
     * 支持搜索、排序和分页功能
     *
     * @param departmentId 部门ID
     * @param includeChildren 是否包含子部门用户（可选，默认false）
     * @param includeDisabled 是否包含已停用用户（可选，默认false）
     * @param userName 用户名搜索关键词（可选）
     * @param account 账号搜索关键词（可选）
     * @param enablePaging 是否启用分页（可选，默认false）
     * @param currentPage 当前页码（可选，默认1）
     * @param pageSize 每页大小（可选，默认20）
     * @param sortField 排序字段（可选，默认userName）
     * @param sortDirection 排序方向（可选，默认ASC）
     * @return 统一返回格式，包含部门用户列表数据
     */
    @RequestMapping(value = "/by-department/{departmentId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "按部门获取用户列表", notes = "支持只查询直属用户或包含子部门用户，支持搜索、排序和分页功能")
    public BaseResult getUsersByDepartment(
            @PathVariable("departmentId") Long departmentId,
            @RequestParam(value = "includeChildren", required = false, defaultValue = "false") Boolean includeChildren,
            @RequestParam(value = "includeDisabled", required = false, defaultValue = "false") Boolean includeDisabled,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "account", required = false) String account,
            @RequestParam(value = "enablePaging", required = false, defaultValue = "false") Boolean enablePaging,
            @RequestParam(value = "currentPage", required = false, defaultValue = "1") Integer currentPage,
            @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(value = "sortField", required = false, defaultValue = "userName") String sortField,
            @RequestParam(value = "sortDirection", required = false, defaultValue = "ASC") String sortDirection) {

        BaseResult baseResult = new BaseResult();

        try {
            // 构建请求参数
            DepartmentUsersRequestVO request = new DepartmentUsersRequestVO();
            request.setDepartmentId(departmentId);
            request.setIncludeChildren(includeChildren);
            request.setIncludeDisabled(includeDisabled);
            request.setUserName(userName);
            request.setAccount(account);
            request.setEnablePaging(enablePaging);
            request.setCurrentPage(currentPage);
            request.setPageSize(pageSize);
            request.setSortField(sortField);
            request.setSortDirection(sortDirection);

            // 调用服务层获取部门用户列表
            DepartmentUsersResponseVO response = userService.getUsersByDepartment(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(response);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取部门用户列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 根据部门ID获取该部门及子部门的用户列表（POST版本）
     * 支持复杂查询参数，通过请求体传递
     *
     * @param request 部门用户查询请求参数
     * @return 统一返回格式，包含部门用户列表数据
     */
    @RequestMapping(value = "/by-department-advanced", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "按部门获取用户列表-高级版", notes = "支持复杂查询参数，通过请求体传递，包含完整的搜索、排序和分页功能")
    public BaseResult getUsersByDepartmentAdvanced(@RequestBody DepartmentUsersRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取部门用户列表
            DepartmentUsersResponseVO response = userService.getUsersByDepartment(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(response);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取部门用户列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取部门用户树形结构（层级懒加载）
     * 用于角色管理中的用户管理功能
     * 支持按层级懒加载，提升大数据量场景下的性能
     *
     * @param request 部门用户查询请求参数（roleId + parentId）
     * @return 统一返回格式，包含指定层级的部门和用户，含用户状态信息
     */
    @RequestMapping(value = "/getUnauthorizedUsers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取部门用户树形结构", notes = "层级懒加载获取部门和用户，parentId为null时返回顶级部门，否则返回指定部门的直属部门和用户")
    public BaseResult getUnauthorizedUsers(@RequestBody UnauthorizedUsersRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取未授权用户树形结构
            UnauthorizedUsersResponseVO unauthorizedUsersResponse = userService.getUnauthorizedUsers(request);

            // 设置返回结果：data直接是部门树形结构
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(unauthorizedUsersResponse.getDepartmentTree()); // data直接是部门树

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取未授权用户树形结构失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 检查用户重复
     * 实时检测用户名和账号是否重复，支持新增和编辑场景
     * 前端可以在用户输入时调用此接口进行实时验证
     *
     * @param request 检查用户重复请求参数
     * @return 统一返回格式，包含重复检查结果
     */
    @RequestMapping(value = "/checkDuplicate", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "检查用户重复", notes = "实时检测用户名和账号是否重复，支持新增和编辑场景")
    public BaseResult checkUserDuplicate(@RequestBody CheckUserDuplicateRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层检查用户重复
            CheckUserDuplicateResponseVO duplicateResponse = userService.checkUserDuplicate(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(duplicateResponse);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("检查用户重复失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取指定部门下的直属未授权用户（懒加载版本）
     * 用于懒加载场景，按需加载部门的直属未授权用户
     * 前端可以在展开部门时调用此接口获取该部门下的直属未授权用户
     *
     * @param request 未授权用户查询请求参数（只需包含departmentId和roleId）
     * @return 统一返回格式，包含指定部门下的直属未授权用户列表
     */
    @RequestMapping(value = "/getUnauthorizedUsersByDepartment", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取指定部门下的直属未授权用户（懒加载）",
                 notes = "用于懒加载场景，只返回指定部门的直属未授权用户，不包含子部门")
    public BaseResult getUnauthorizedUsersByDepartment(@RequestBody UnauthorizedUsersRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 参数验证
            if (request.getParentId() == null) {
                baseResult.setCode(400);
                baseResult.setMessage("部门ID不能为空");
                baseResult.setData(null);
                return baseResult;
            }

            if (request.getRoleId() == null) {
                baseResult.setCode(400);
                baseResult.setMessage("角色ID不能为空");
                baseResult.setData(null);
                return baseResult;
            }

            // 调用服务层获取指定部门下的未授权用户
            List<DepartmentTreeVO> departmentUsers = userService.getUnauthorizedUsersByDepartmentLazy(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(departmentUsers);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("获取部门未授权用户失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
