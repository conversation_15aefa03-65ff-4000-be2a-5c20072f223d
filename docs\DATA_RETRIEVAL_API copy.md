# 数据获取API文档

## 概述

数据获取API为外部系统提供了从远程MDM系统获取数据的代理服务。该API不涉及本地数据库操作，仅作为数据传递的中间层，将远程系统的XML数据解析为与数据库表结构对应的JSON格式返回给调用方。

## 功能特点

- **代理服务**: 作为远程MDM系统的代理，为外部系统提供数据获取服务
- **数据解析**: 自动解析远程系统返回的XML数据，转换为与数据库表结构对应的JSON格式
- **时间范围查询**: 支持按时间范围获取指定时间段内的数据变更
- **无数据库操作**: 纯数据传递服务，不涉及本地数据库的读写操作
- **统一响应格式**: 提供统一的API响应格式，便于外部系统集成
- **表结构对应**: 返回的数据结构与实际数据库表结构完全对应，便于外部系统直接使用
- **🔄 智能重试机制**: 自动处理网络超时、服务异常等临时性错误，支持可配置的重试策略
- **🔪 自动分片处理**: 大时间范围自动分片处理，避免单次请求数据量过大导致超时
- **⚡ 性能优化**: 智能分片大小选择，分片间隔控制，最大化处理效率
- **🛡️ 错误处理**: 智能识别远程服务错误响应，提供详细的错误信息和处理建议
- **📊 处理统计**: 提供详细的处理统计信息，包括分片数量、重试次数等

## 数据库表结构对应关系

本API返回的数据结构与以下数据库表完全对应：

### 部门相关表
- **department**: 部门主表，存储组织结构中的部门信息
- **department_child**: 部门子表，存储部门在其他系统中的标识信息

### 员工相关表
- **employee**: 员工主表，存储员工基本信息
- **employee_position**: 员工岗位关联表，存储员工的岗位信息
- **employee_title**: 员工职称表，存储员工的职称信息
- **employee_system**: 员工系统标识表，存储员工在其他系统中的标识信息

## API接口

### 1. 获取部门数据

**接口地址**: `GET /api/data/departments`

**功能说明**:
- 从远程MDM系统获取部门数据，包括部门主表和子表信息
- 🔪 **智能分片**: 时间范围超过72小时时自动启用智能分片处理
- 🔄 **重试机制**: 网络超时或服务异常时自动重试（默认3次，间隔5秒）
- ⚡ **性能优化**: 分片间隔1秒，避免对远程服务造成压力

**请求参数**:
- `startDate` (必填): 开始时间，支持多种格式（见下方支持的日期格式）
- `endDate` (必填): 结束时间，支持多种格式（见下方支持的日期格式）

**支持的日期格式**:
- `yyyy-MM-dd HH:mm:ss` (如: 2024-01-01 00:00:00)
- `yyyy/MM/dd HH:mm:ss` (如: 2024/01/01 00:00:00)
- `yyyy-MM-dd` (如: 2024-01-01)
- `yyyy/MM/dd` (如: 2024/01/01)
- `yyyy-MM-ddTHH:mm:ss` (ISO格式)
- `yyyyMMdd` (如: 20240101)
- URL编码格式 (如: 2024-01-01%2000:00:00)

**处理策略**:
- **≤ 72小时**: 单次请求处理
- **> 72小时**: 自动分片处理
  - 1周以内: 按天分片（24小时/片）
  - 1个月以内: 按3天分片（72小时/片）
  - 超过1个月: 按周分片（168小时/片）

**请求示例**:
```http
# 短时间范围（单次处理）
GET /api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00

# 长时间范围（自动分片）
GET /api/data/departments?startDate=2024-01-01&endDate=2024-01-31

# 多种日期格式支持
GET /api/data/departments?startDate=2024/01/01&endDate=2024/01/02
GET /api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-02%2000:00:00
```

**响应格式**:
```json
{
  "success": true,
  "message": "获取部门数据成功",
  "data": {
    "departments": [
      {
        "deptUuid": "*************-4f25-815b-18339c8d76f4",
        "orgCode": "X50070000",
        "orgName": "技术部",
        "parentCode": "X50070000",
        "fullName": "南京钢铁股份有限公司技术部",
        "orgLevel": "3",
        "isHistory": 0,
        "description": "技术研发部门",
        "fax": "025-12345678",
        "webAddress": "http://tech.njsteel.com",
        "orgManager": "张三",
        "postCode": "210000",
        "userPredef13": "判重项目值域",
        "userPredef14": "操作标识",
        "userPredef18": "预留字段",
        "children": [
          {
            "guid": "child-guid-123",
            "deptUuid": "*************-4f25-815b-18339c8d76f4",
            "sourceSystem": "ERP",
            "sourceDataNm": "ERP_DEPT_001",
            "udef1": "自定义字段1",
            "udef2": "自定义字段2",
            "udef3": "自定义字段3",
            "udef4": "自定义字段4",
            "udef5": "自定义字段5",
            "udef6": "自定义字段6"
          }
        ]
      }
    ],
    "totalRecords": 1,
    "mainDepartments": 1,
    "childRecords": 1,
    "processingInfo": {
      "timeRange": "2024-01-01 00:00:00 ~ 2024-01-02 00:00:00",
      "totalHours": 24,
      "slicingEnabled": false,
      "sliceCount": 1,
      "successfulSlices": 1,
      "failedSlices": 0,
      "retryEnabled": true,
      "totalRetries": 0,
      "processingTime": "2.5秒"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

**分片处理响应示例**（长时间范围）:
```json
{
  "success": true,
  "message": "获取部门数据成功（分片处理）",
  "data": {
    "departments": [...],
    "totalRecords": 500,
    "mainDepartments": 500,
    "childRecords": 150,
    "processingInfo": {
      "timeRange": "2024-01-01 00:00:00 ~ 2024-01-31 23:59:59",
      "totalHours": 744,
      "slicingEnabled": true,
      "sliceCount": 31,
      "successfulSlices": 31,
      "failedSlices": 0,
      "retryEnabled": true,
      "totalRetries": 2,
      "processingTime": "45.8秒",
      "sliceStrategy": "按天分片（24小时/片）"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

### 2. 获取员工数据

**接口地址**: `GET /api/data/employees`

**功能说明**:
- 从远程MDM系统获取员工数据，包括员工主表及岗位、职称、系统等子表信息
- 🔪 **智能分片**: 时间范围超过24小时时自动启用分片处理（12小时/片）
- 🔄 **重试机制**: 网络超时或服务异常时自动重试（默认3次，间隔5秒）
- ⚡ **性能优化**: 分片间隔2秒，避免对远程服务造成压力（员工数据量大）

**请求参数**:
- `startDate` (必填): 开始时间，支持多种格式（同部门接口）
- `endDate` (必填): 结束时间，支持多种格式（同部门接口）

**处理策略**:
- **≤ 24小时**: 单次请求处理
- **> 24小时**: 自动分片处理（12小时/片）
- **分片间隔**: 2秒（比部门数据更长，因为员工数据量更大）

**请求示例**:
```http
# 短时间范围（单次处理）
GET /api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00

# 长时间范围（自动分片）
GET /api/data/employees?startDate=2024-01-01&endDate=2024-01-31

# URL编码格式
GET /api/data/employees?startDate=2024-01-01%2000:00:00&endDate=2024-01-02%2000:00:00
```

**响应格式** (对应employee、employee_position、employee_title、employee_system表结构):
```json
{
  "success": true,
  "message": "获取员工数据成功",
  "data": {
    "employees": [
    {
      "mdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
      "mdmHrdwnm": "*************-4f25-815b-18339c8d76f4",
      "employeeCode": "019359",
      "employeeName": "张清辉",
      "gender": "1",
      "mobile": "***********",
      "status": "A",
      "idCard": "110108196609182334",
      "account": "019359",
      "birthDate": "1966-09-18T00:00:00.000+00:00",
      "email": "",
      "orgType": "001",
      "orgLevel1": "01",
      "orgLevel2": "01",
      "orgLevel3": "03",
      "wechat": "",
      "tel": "",
      "note": "",
      "isDisabled": "0",
      "userType": "U",
      "orgCode": "001",
      "idName": "110108196609182334张清辉",
      "createdTime": "2024-01-01T10:00:00.000+00:00",
      "updatedTime": "2024-01-01T10:00:00.000+00:00",
      "positions": [
        {
          "guid": "3b7eaf3d-2d30-4cc3-9da8-12b5488afbc4",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "positionCode": "X50070500",
          "orgCode": "X50070000",
          "departmentCode": "03135",
          "isPrimary": "1",
          "status": "D",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "isActive": "1",
          "positionDetailCode": "019359X5007050051200"
        }
      ],
      "titles": [
        {
          "guid": "5bbf3bc0-01b3-40a6-8e36-627e40f85dae",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "titleCode": "X50070000",
          "titleType": "2",
          "titleLevel": "0",
          "titleName": "研究员级高工",
          "status": "U",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "titleCategory": "O"
        }
      ],
      "systems": [
        {
          "guid": "db14b032-ec3a-4f71-8c23-dae73054904a",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "systemCode": "ERP",
          "systemDataId": "019359",
          "orgCode": "X50070300",
          "departmentCode": "51200",
          "employeeCode": "019359",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "loginAccount": "019359"
        }
      ]
    }
    ],
    "totalRecords": 1,
    "mainEmployees": 1,
    "positionRecords": 1,
    "titleRecords": 1,
    "systemRecords": 1,
    "processingInfo": {
      "timeRange": "2024-01-01 00:00:00 ~ 2024-01-02 00:00:00",
      "totalHours": 24,
      "slicingEnabled": false,
      "sliceCount": 1,
      "successfulSlices": 1,
      "failedSlices": 0,
      "retryEnabled": true,
      "totalRetries": 0,
      "processingTime": "5.2秒"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

**分片处理响应示例**（长时间范围）:
```json
{
  "success": true,
  "message": "获取员工数据成功（分片处理）",
  "data": {
    "employees": [...],
    "totalRecords": 2000,
    "mainEmployees": 2000,
    "positionRecords": 2500,
    "titleRecords": 1800,
    "systemRecords": 3000,
    "processingInfo": {
      "timeRange": "2024-01-01 00:00:00 ~ 2024-01-31 23:59:59",
      "totalHours": 744,
      "slicingEnabled": true,
      "sliceCount": 62,
      "successfulSlices": 62,
      "failedSlices": 0,
      "retryEnabled": true,
      "totalRetries": 5,
      "processingTime": "125.6秒",
      "sliceStrategy": "12小时分片"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

### 3. API测试接口

**接口地址**: `GET /api/data/test`

**请求示例**:
```http
GET /api/data/test
```

**响应格式**:
```json
{
  "success": true,
  "message": "API测试成功",
  "data": "数据获取API正常工作",
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

## 错误处理与重试机制

### 🔄 自动重试机制

系统内置智能重试机制，自动处理以下临时性错误：
- **网络超时**: `timeout`、`超时`
- **HTTP 499错误**: 客户端关闭连接
- **ESB服务异常**: `ESB调用MDM服务异常`
- **连接错误**: `Connection reset`、`Connection refused`
- **服务不可用**: `Service Unavailable`

**重试配置**（可在application.yml中调整）:
```yaml
app:
  retry:
    enabled: true          # 是否启用重试机制
    max-retries: 3         # 最大重试次数
    retry-delay: 5000      # 重试间隔（毫秒）
    exponential-backoff: false  # 是否启用指数退避
```

### 🛡️ 错误响应格式

#### 1. 日期参数错误 (400 Bad Request)
```json
{
  "success": false,
  "message": "日期参数格式错误: 无法解析日期字符串: invalid-date。支持的格式: yyyy-MM-dd HH:mm:ss, yyyy/MM/dd HH:mm:ss, yyyy-MM-dd, ...",
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

#### 2. 远程服务错误 (500 Internal Server Error)
```json
{
  "success": false,
  "message": "远程MDM服务调用失败 - 错误码: ESB-E-000499, 错误信息: ESB调用MDM服务GetDatasFromMDMQuery异常[HTTP_STATUS_CODE:499]，MDM系统内部处理超时",
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

#### 3. 分片处理失败 (500 Internal Server Error)
```json
{
  "success": false,
  "message": "分片处理失败率过高，停止处理",
  "data": {
    "processingInfo": {
      "timeRange": "2024-01-01 00:00:00 ~ 2024-01-31 23:59:59",
      "sliceCount": 31,
      "successfulSlices": 10,
      "failedSlices": 21,
      "failureRate": "67.7%"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

#### 4. 重试耗尽错误 (500 Internal Server Error)
```json
{
  "success": false,
  "message": "操作 获取员工数据 在 3 次重试后仍然失败",
  "data": {
    "retryInfo": {
      "maxRetries": 3,
      "totalRetries": 3,
      "lastError": "远程MDM服务调用失败 - 错误码: ESB-E-000499"
    }
  },
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

### 🔧 常见错误及解决方案

1. **远程服务超时**:
   - **现象**: `HTTP_STATUS_CODE:499`、`MDM系统内部处理超时`
   - **自动处理**: 系统自动重试3次，间隔5秒
   - **建议**: 缩小时间范围，利用分片机制

2. **数据量过大**:
   - **现象**: 长时间无响应或超时
   - **自动处理**: 系统自动启用分片处理
   - **建议**: 合理控制查询时间范围

3. **网络连接问题**:
   - **现象**: `Connection reset`、`Connection refused`
   - **自动处理**: 系统自动重试
   - **建议**: 检查网络连接和防火墙设置

4. **分片处理失败率高**:
   - **现象**: 多个分片连续失败
   - **自动处理**: 失败率超过50%时停止处理
   - **建议**: 检查远程服务状态，稍后重试

## 数据结构说明

### 部门数据结构
- **主表数据**: 对应 `department` 表的所有字段
- **子表数据**: `children` 数组对应 `department_child` 表，存储部门在其他系统中的标识信息

### 员工数据结构
- **主表数据**: 对应 `employee` 表的所有字段
- **岗位数据**: `positions` 数组对应 `employee_position` 表
- **职称数据**: `titles` 数组对应 `employee_title` 表
- **系统标识数据**: `systems` 数组对应 `employee_system` 表

### 字段映射说明
所有返回的字段名称与数据库表字段名称完全一致，外部系统可以直接使用这些数据进行数据库操作，无需额外的字段映射。

## 使用场景

1. **外部系统数据同步**: 其他系统可以调用这些接口获取最新的组织架构和人员信息，直接同步到本地数据库
2. **数据集成**: 作为数据集成平台的数据源接口，提供标准化的数据格式
3. **实时数据查询**: 提供实时的远程数据查询能力，无需维护本地数据副本
4. **系统解耦**: 避免外部系统直接调用远程MDM系统，降低系统间的耦合度
5. **数据一致性**: 确保多个系统使用相同的数据源和数据格式

## 📋 使用建议与注意事项

### ⚡ 性能优化建议

1. **时间范围控制**:
   - **推荐**: 单次查询不超过1个月
   - **原因**: 系统会自动分片，但过长时间范围会增加处理时间
   - **最佳实践**: 按天或按周查询，定期同步

2. **分片处理利用**:
   - **部门数据**: 超过3天自动分片，建议按周查询
   - **员工数据**: 超过1天自动分片，建议按天查询
   - **监控**: 关注响应中的`processingInfo`了解处理详情

3. **重试机制配置**:
   - **默认配置**: 3次重试，5秒间隔，适合大多数场景
   - **高频调用**: 可适当减少重试次数
   - **批量处理**: 可增加重试次数和间隔

### 🔧 集成建议

1. **超时设置**:
   - **短时间范围**: 设置30-60秒超时
   - **长时间范围**: 设置5-10分钟超时（分片处理需要更多时间）
   - **监控**: 根据`processingInfo.processingTime`调整超时设置

2. **错误处理**:
   - **临时性错误**: 系统已自动重试，无需额外处理
   - **持续性错误**: 检查网络连接和远程服务状态
   - **分片失败**: 缩小时间范围重试

3. **数据缓存策略**:
   - **增量同步**: 记录上次同步时间，只获取增量数据
   - **本地缓存**: 缓存稳定的部门数据，减少重复查询
   - **失效策略**: 根据业务需求设置合适的缓存失效时间

### ⚠️ 重要注意事项

1. **日期格式**: 支持多种格式，推荐使用 `yyyy-MM-dd HH:mm:ss`
2. **数据一致性**: API返回的是实时数据，不保证多次调用结果完全一致
3. **并发限制**: 避免同时发起多个大时间范围的请求
4. **监控告警**: 建议监控API响应时间和错误率
5. **版本兼容**: API响应格式可能随版本更新，注意兼容性处理

## ⚙️ 配置参考

### 重试机制配置
```yaml
# application.yml
app:
  retry:
    enabled: true          # 是否启用重试机制
    max-retries: 3         # 最大重试次数
    retry-delay: 5000      # 重试间隔（毫秒）
    exponential-backoff: false  # 是否启用指数退避
    base-delay: 2000       # 指数退避基础延迟（毫秒）
    max-delay: 30000       # 指数退避最大延迟（毫秒）
```

### 分片处理阈值
- **部门数据**: 72小时（3天）
- **员工数据**: 24小时（1天）
- **分片大小**: 智能选择，根据时间范围自动调整

### 日志配置
```yaml
# application.yml
logging:
  level:
    com.nercar.datasynchronization: DEBUG  # 查看详细处理日志
    com.nercar.datasynchronization.utils.RetryUtils: INFO  # 重试日志
    com.nercar.datasynchronization.utils.TimeSliceUtils: INFO  # 分片日志
```

## 📚 Swagger文档

系统启动后，可以通过以下地址访问Swagger API文档：
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API文档**: http://localhost:8080/api-docs
- **健康检查**: http://localhost:8080/api/data/test

## 🔍 监控与调试

### 日志关键字
- `🔪 [分片处理]`: 分片处理相关日志
- `🔄 [重试机制]`: 重试机制相关日志
- `✅ [分片成功]`: 分片处理成功
- `❌ [分片失败]`: 分片处理失败
- `🎯 [分片完成]`: 分片处理完成统计

### 性能指标
- **处理时间**: 查看`processingInfo.processingTime`
- **分片数量**: 查看`processingInfo.sliceCount`
- **重试次数**: 查看`processingInfo.totalRetries`
- **成功率**: 查看`successfulSlices/sliceCount`
