# 移动部门接口测试文档

## 接口信息
- **接口路径**: `POST /t-org-structure/move`
- **接口描述**: 将指定部门移动到新的父部门下，支持排序调整，防止循环引用
- **Content-Type**: `application/json`

## 快速使用指南

### 最常用的请求格式（复制即用）

**1. 移动部门到指定父部门下（最后位置）**
```json
{
    "orgId": "要移动的部门ID",                    // 必填：要移动的部门ID
    "newParentId": "新父部门ID"                  // 新父部门ID，移动到根级别时传null或0
}
```

**2. 移动部门到指定父部门的指定位置**
```json
{
    "orgId": "要移动的部门ID",                    // 必填：要移动的部门ID
    "newParentId": "新父部门ID",                 // 新父部门ID
    "newOrderInfo": 1                           // 可选：在新父部门下的排序位置（1表示第一位）
}
```

**3. 移动部门到根级别**
```json
{
    "orgId": "要移动的部门ID",                    // 必填：要移动的部门ID
    "newParentId": null                         // 移动到根级别
}
```

## 请求参数

### MoveOrgStructureRequestVO
| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| orgId | String | 是 | 要移动的部门ID | "1930806593885179904" |
| newParentId | String | 否 | 新的父部门ID（移动到根级别时传null或0） | "1930806593885179903" |
| newOrderInfo | Integer | 否 | 在新父部门下的排序位置（不传则自动排到最后） | 1 |

## 响应参数

### MoveOrgStructureResponseVO
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| orgId | String | 移动的部门ID | "1930806593885179904" |
| organName | String | 部门名称 | "技术部" |
| oldParentId | String | 原父部门ID | "1930806593885179903" |
| oldParentName | String | 原父部门名称 | "总公司" |
| newParentId | String | 新父部门ID | "1930806593885179905" |
| newParentName | String | 新父部门名称 | "研发中心" |
| newOrderInfo | Integer | 新的排序序号 | 1 |
| success | Boolean | 移动操作是否成功 | true |
| message | String | 操作详细信息 | "部门移动成功" |
| operateTime | String | 操作时间 | "2025-01-20 15:30:45" |

## 测试用例

### 测试用例1：移动部门到新父部门（自动排序）
**请求示例：**
```json
POST /t-org-structure/move
Content-Type: application/json

{
    "orgId": "1930806593885179904",              // 要移动的技术部ID
    "newParentId": "1930806593885179905"         // 移动到研发中心下，自动排到最后
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "oldParentId": "1930806593885179903",
        "oldParentName": "总公司",
        "newParentId": "1930806593885179905",
        "newParentName": "研发中心",
        "newOrderInfo": 3,
        "success": true,
        "message": "部门移动成功",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 测试用例2：移动部门到指定位置
**请求示例：**
```json
POST /t-org-structure/move
Content-Type: application/json

{
    "orgId": "1930806593885179904",              // 要移动的技术部ID
    "newParentId": "1930806593885179905",        // 移动到研发中心下
    "newOrderInfo": 1                           // 排在第一位
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "oldParentId": "1930806593885179903",
        "oldParentName": "总公司",
        "newParentId": "1930806593885179905",
        "newParentName": "研发中心",
        "newOrderInfo": 1,
        "success": true,
        "message": "部门移动成功",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 测试用例3：移动部门到根级别
**请求示例：**
```json
POST /t-org-structure/move
Content-Type: application/json

{
    "orgId": "1930806593885179904",              // 要移动的技术部ID
    "newParentId": null                         // 移动到根级别
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "oldParentId": "1930806593885179903",
        "oldParentName": "总公司",
        "newParentId": null,
        "newParentName": "根级别",
        "newOrderInfo": 2,
        "success": true,
        "message": "部门移动成功",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

## 错误场景测试

### 错误场景1：部门不存在
**请求示例：**
```json
{
    "orgId": "9999999999999999999",              // 不存在的部门ID
    "newParentId": "1930806593885179905"
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "9999999999999999999",
        "success": false,
        "message": "要移动的部门不存在或已被删除",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 错误场景2：目标父部门不存在
**请求示例：**
```json
{
    "orgId": "1930806593885179904",
    "newParentId": "9999999999999999999"         // 不存在的父部门ID
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "success": false,
        "message": "目标父部门不存在或已被删除",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 错误场景3：循环引用（移动到自己）
**请求示例：**
```json
{
    "orgId": "1930806593885179904",
    "newParentId": "1930806593885179904"         // 移动到自己
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "success": false,
        "message": "不能将部门移动到自己或子部门下",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 错误场景4：循环引用（移动到子部门）
**请求示例：**
```json
{
    "orgId": "1930806593885179904",              // 技术部
    "newParentId": "1930806593885179906"         // 技术部的子部门：研发组
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": {
        "orgId": "1930806593885179904",
        "organName": "技术部",
        "success": false,
        "message": "不能将部门移动到自己或子部门下",
        "operateTime": "2025-01-20 15:30:45"
    }
}
```

### 错误场景5：系统异常
**预期响应：**
```json
{
    "code": 500,
    "message": "移动部门失败：具体错误信息",
    "data": null
}
```

## 接口特性

### 功能特性
1. **防循环引用**: 自动检测并阻止移动到自己或子部门下
2. **自动排序**: 不指定位置时自动排到目标父部门的最后
3. **位置插入**: 指定位置时自动调整其他部门的排序
4. **事务安全**: 使用数据库事务确保数据一致性
5. **详细反馈**: 返回移动前后的完整信息

### 技术特性
1. **参数验证**: 完整的输入参数验证
2. **精度保护**: Long类型ID使用字符串序列化
3. **异常处理**: 完善的异常捕获和错误信息返回
4. **日志记录**: 详细的操作日志记录

## 使用场景
1. **组织架构调整**: 部门重组时移动部门位置
2. **层级优化**: 调整部门的层级关系
3. **排序调整**: 调整同级部门的显示顺序
4. **结构重构**: 大规模的组织架构重构

## 注意事项
1. **权限控制**: 确保用户有移动部门的权限
2. **数据备份**: 重要操作前建议备份数据
3. **影响范围**: 移动部门可能影响相关用户的权限
4. **测试验证**: 生产环境使用前充分测试
