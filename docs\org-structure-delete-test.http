### 删除部门接口测试

### 1. 删除叶子部门（无子部门）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1005,
  "cascadeDelete": true
}

### 2. 删除部门但不级联删除子部门（应该失败，如果有子部门）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1002,
  "cascadeDelete": false
}

### 3. 删除部门并级联删除所有子部门
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1002,
  "cascadeDelete": true
}

### 4. 删除根部门（级联删除所有子部门）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1001,
  "cascadeDelete": true
}

### 5. 测试删除不存在的部门（应该失败）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 99999,
  "cascadeDelete": true
}

### 6. 测试删除已删除的部门（应该失败）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1005,
  "cascadeDelete": true
}

### 7. 测试null的部门ID（应该失败）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": null,
  "cascadeDelete": true
}

### 8. 默认级联删除（不指定cascadeDelete参数）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1006
}

### 9. 明确设置不级联删除（如果有子部门应该失败）
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1002,
  "cascadeDelete": false
}

### 10. 测试删除有用户的部门（应该失败）
### 注意：需要先在某个部门下添加用户才能测试此场景
POST http://localhost:8080/t-org-structure/delete
Content-Type: application/json

{
  "id": 1003,
  "cascadeDelete": true
}
