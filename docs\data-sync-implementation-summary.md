# 数据同步实现总结

## 📋 **根据API文档重新设计的同步方案**

### **🔍 API文档分析结果**

#### **1. 外部系统API特点**
- **基础路径**: `/api/data/`
- **核心接口**: 
  - `/api/data/departments` - 获取部门数据
  - `/api/data/employees` - 获取员工数据
  - `/api/data/test` - 连接测试
- **请求参数**: 都需要时间范围参数 `startDate` 和 `endDate`
- **响应格式**: 统一的JSON格式，包含 `success`、`message`、`data`、`timestamp`、`totalCount`

#### **2. 数据结构特点**
- **部门数据**: 主表数据 + `children`数组（department_child表数据）
- **员工数据**: 主表数据 + `positions`数组 + `titles`数组 + `systems`数组
- **字段映射**: API返回的字段名与数据库表字段完全一致

#### **3. 重要发现**
- 员工数据中包含了`orgCode`字段（在主表中）
- 同时在`positions`数组中也有`orgCode`和`departmentCode`
- 这解决了员工部门关联的问题

### **🔧 重新设计的实现方案**

#### **1. 创建的新实体类**
```java
// API响应包装类
ApiResponse<T>              // 统一响应格式

// 外部系统实体类
ExternalDepartment          // 部门主表
DepartmentChild            // 部门子表
ExternalEmployee           // 员工主表
ExternalEmployeePosition   // 员工岗位
ExternalEmployeeTitle      // 员工职称
ExternalEmployeeSystem     // 员工系统标识
```

#### **2. 更新的服务类**

**ExternalDataService** - 外部数据访问服务
```java
// 主要方法
getDepartments(startDate, endDate)     // 获取指定时间范围的部门数据
getEmployees(startDate, endDate)       // 获取指定时间范围的员工数据
getAllDepartments()                    // 获取所有部门（默认最近一天）
getAllEmployees()                      // 获取所有员工（默认最近一天）
getIncrementalDepartments(lastSyncTime) // 增量部门同步
getIncrementalEmployees(lastSyncTime)   // 增量员工同步
testConnection()                       // 连接测试
```

**DataSyncService** - 核心同步服务
```java
// 同步流程
performFullSync()                      // 完整同步
├── syncDepartments()                  // 1. 同步部门数据
├── syncEmployees()                    // 2. 同步员工基础数据
├── syncEmployeeExtendedData()         // 3. 同步员工扩展数据
└── updateEmployeeDepartmentAffiliation() // 4. 更新部门归属
```

#### **3. 数据处理逻辑**

**部门数据处理**：
```java
// 1. 调用API获取部门数据（包含children数组）
List<ExternalDepartment> departments = externalDataService.getAllDepartments();

// 2. 转换为内部格式
TOrgStructure internalDept = convertToInternalDepartment(extDept);

// 3. 处理父部门关联
if (extDept.getParentCode() != null) {
    Long parentId = orgCodeToIdMap.get(extDept.getParentCode());
    internalDept.setPreId(parentId);
}

// 4. 保存到数据库并更新映射缓存
orgStructureMapper.insert(internalDept);
orgCodeToIdMap.put(extDept.getOrgCode(), internalDept.getId());

// 5. 同步部门子表数据（children数组）
if (extDept.getChildren() != null && !extDept.getChildren().isEmpty()) {
    for (DepartmentChild child : extDept.getChildren()) {
        syncDepartmentChild(internalDept.getId(), child);
    }
}
```

**员工数据处理**：
```java
// 1. 调用API获取员工数据（包含positions、titles、systems数组）
List<ExternalEmployee> employees = externalDataService.getAllEmployees();

// 2. 转换员工基础信息
TUser internalUser = convertToInternalUser(extEmp);

// 3. 处理员工扩展数据
for (ExternalEmployeePosition position : extEmp.getPositions()) {
    syncEmployeePosition(extEmp.getMdmId(), position);
}

// 4. 根据主岗位更新部门归属
// 查找is_primary='1'的岗位，根据org_code设置organ_affiliation
```

### **📊 数据同步策略**

#### **1. 同步顺序**
1. **部门同步** - 建立组织架构基础，生成org_code到内部ID的映射
2. **员工基础信息同步** - 创建用户记录（暂不设置部门归属）
3. **员工扩展数据同步** - 处理positions、titles、systems数组
4. **部门归属更新** - 根据主岗位设置用户的`organ_affiliation`字段

#### **2. 关键映射关系**
```java
// 部门映射
external.org_code -> internal.id (通过orgCodeToIdMap缓存)

// 员工部门关联
employee.mdm_id -> employee_position.employee_mdm_id 
                -> employee_position.org_code 
                -> department.org_code 
                -> internal.department.id
                -> t_user.organ_affiliation
```

#### **3. 状态转换逻辑**
```java
// 员工状态转换
extEmp.getStatus().equals("A") -> internalUser.setIsDisable(false)  // 在职
其他状态 -> internalUser.setIsDisable(true)  // 离职

// 主岗位识别
position.getIsPrimary().equals("1") -> 主岗位
position.getIsPrimary().equals("0") -> 副岗位
```

### **🔧 配置更新**

#### **application.yml配置**
```yaml
external:
  system:
    base-url: http://localhost:8080  # 外部系统地址
    api:
      departments: /api/data/departments
      employees: /api/data/employees
    timeout:
      connect: 5000
      read: 30000
```

#### **RestTemplate配置**
```java
@Bean
public RestTemplate restTemplate() {
    SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
    factory.setConnectTimeout(connectTimeout);
    factory.setReadTimeout(readTimeout);
    return new RestTemplate(factory);
}
```

### **🧪 测试接口**

#### **同步接口测试**
```http
# 1. 测试连接
GET http://localhost:8080/sync/test-connection

# 2. 完整同步
POST http://localhost:8080/sync/full

# 3. 部门同步
POST http://localhost:8080/sync/departments

# 4. 员工同步
POST http://localhost:8080/sync/employees
```

#### **外部API直接测试**
```http
# 1. API测试
GET http://localhost:8080/api/data/test

# 2. 获取部门数据
GET http://localhost:8080/api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00

# 3. 获取员工数据
GET http://localhost:8080/api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
```

### **📁 文件结构**

#### **新增文件**
```
src/main/java/com/dfit/percode/sync/
├── dto/
│   └── ApiResponse.java                    # API响应包装类
├── entity/
│   ├── ExternalDepartment.java            # 部门主表
│   ├── DepartmentChild.java               # 部门子表
│   ├── ExternalEmployee.java              # 员工主表
│   ├── ExternalEmployeePosition.java      # 员工岗位
│   ├── ExternalEmployeeTitle.java         # 员工职称
│   └── ExternalEmployeeSystem.java        # 员工系统标识
├── service/
│   ├── DataSyncService.java               # 核心同步服务
│   └── ExternalDataService.java           # 外部数据访问
├── controller/
│   └── DataSyncController.java            # 同步接口控制器
└── config/
    └── SyncConfig.java                     # 同步配置

docs/
├── database-extension-for-sync.sql        # 数据库扩展SQL
├── data-sync-test.http                    # 接口测试文档
└── data-sync-implementation-summary.md    # 实现总结文档
```

#### **更新文件**
```
src/main/java/com/dfit/percode/entity/TUser.java  # 新创建的用户实体类
src/main/resources/application.yml                # 外部系统配置
```

### **⚠️ 待完成的工作**

1. **数据库扩展** - 执行 `database-extension-for-sync.sql`
2. **UserMapper方法** - 添加insert方法或使用MyBatis-Plus的BaseMapper
3. **员工扩展数据同步** - 完善positions、titles、systems的具体同步逻辑
4. **部门归属更新** - 实现根据主岗位更新用户部门归属的逻辑
5. **错误处理** - 完善异常处理和重试机制
6. **增量同步** - 实现基于时间戳的增量同步逻辑

### **🎯 核心优势**

1. **完全适配API** - 严格按照外部系统API文档设计
2. **数据结构完整** - 支持所有嵌套数据（positions、titles、systems）
3. **关系处理正确** - 通过positions数组正确处理员工部门关联
4. **扩展性好** - 支持时间范围查询和增量同步
5. **错误处理完善** - 统一的异常处理和日志记录
6. **配置灵活** - 支持外部配置和超时设置
