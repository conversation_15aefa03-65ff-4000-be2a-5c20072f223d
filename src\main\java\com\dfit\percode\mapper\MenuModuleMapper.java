package com.dfit.percode.mapper;

import com.dfit.percode.vo.MenuDetailResponseVO;
import com.dfit.percode.vo.MenuModuleListResponseVO;
import com.dfit.percode.vo.MenuTreeResponseVO;
import com.dfit.percode.vo.ModuleInfoVO;
import com.dfit.percode.vo.response.RoleUsageVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 菜单模块相关Mapper接口
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface MenuModuleMapper {

    /**
     * 检查模块标识是否已存在
     * 避免重复的模块标识
     *
     * @param moduleIdentifier 模块标识
     * @return 存在的记录数量
     */
    @Select("SELECT COUNT(*) FROM t_menu_module " +
            "WHERE module_identifier = #{moduleIdentifier} AND is_del = false")
    int checkModuleIdentifierExists(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 插入菜单模块记录
     * 使用数据库函数处理时间，避免类型转换问题
     *
     * @param id 模块ID
     * @param moduleName 模块名称
     * @param moduleIdentifier 模块标识
     * @param orderInfo 排序序号
     */
    @Insert("INSERT INTO t_menu_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{moduleName}, #{moduleIdentifier}, #{orderInfo}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertMenuModule(@Param("id") Long id,
                         @Param("moduleName") String moduleName,
                         @Param("moduleIdentifier") String moduleIdentifier,
                         @Param("orderInfo") Integer orderInfo);

    /**
     * 获取菜单模块列表
     * 查询所有未删除的菜单模块，按排序字段排序
     *
     * @return 菜单模块列表
     */
    @Select("SELECT id, module_name as moduleName, module_identifier as moduleIdentifier, order_info as orderInfo " +
            "FROM t_menu_module " +
            "WHERE is_del = false " +
            "ORDER BY order_info ASC")
    List<MenuModuleListResponseVO> getMenuModules();

    /**
     * 查询所有模块信息（用于菜单树构建）
     * 查询所有未删除的菜单模块基本信息，按排序字段排序
     *
     * @return 模块信息列表
     */
    @Select("SELECT module_identifier as moduleIdentifier, module_name as moduleName, order_info as orderInfo " +
            "FROM t_menu_module " +
            "WHERE is_del = false " +
            "ORDER BY order_info ASC")
    List<ModuleInfoVO> findAllModules();



    /**
     * 检查模块是否被菜单权限使用
     * 检查t_menu_permission表中是否有引用该模块标识的记录
     *
     * @param moduleIdentifier 模块标识
     * @return 使用该模块的记录数量
     */
    @Select("SELECT COUNT(*) FROM t_menu_permission " +
            "WHERE module_identifier = #{moduleIdentifier} AND is_del = false")
    int checkModuleUsedByPermissions(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 根据模块标识逻辑删除菜单模块
     * 设置is_del为true，更新修改时间
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_menu_module SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE module_identifier = #{moduleIdentifier}")
    void deleteMenuModuleByIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 检查菜单是否存在
     * 根据菜单ID检查菜单是否存在且未删除
     *
     * @param menuId 菜单ID
     * @return 存在的记录数量
     */
    @Select("SELECT COUNT(*) FROM t_menu_permission " +
            "WHERE id = #{menuId} AND is_del = false")
    int checkMenuExists(@Param("menuId") Long menuId);

    /**
     * 插入菜单记录
     * 新增菜单到t_menu_permission表
     *
     * @param id 菜单ID
     * @param name 菜单名称
     * @param preId 父级菜单ID
     * @param moduleIdentifier 模块标识
     * @param orderInfo 排序序号
     * @param isDisable 是否禁用
     * @param menuType 菜单类型
     * @param routeAddress 路由地址
     * @param componentPath 组件路径
     * @param permissionIdentifier 权限标识
     * @param routeParam 路由参数
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, " +
            "menu_type, route_address, component_path, permission_identifier, route_param, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{name}, #{preId}, #{moduleIdentifier}, #{orderInfo}, #{isDisable}, " +
            "#{menuType}, #{routeAddress}, #{componentPath}, #{permissionIdentifier}, #{routeParam}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertMenu(@Param("id") Long id,
                   @Param("name") String name,
                   @Param("preId") Long preId,
                   @Param("moduleIdentifier") String moduleIdentifier,
                   @Param("orderInfo") Integer orderInfo,
                   @Param("isDisable") Boolean isDisable,
                   @Param("menuType") Integer menuType,
                   @Param("routeAddress") String routeAddress,
                   @Param("componentPath") String componentPath,
                   @Param("permissionIdentifier") String permissionIdentifier,
                   @Param("routeParam") String routeParam);

    /**
     * 根据菜单ID获取菜单详情
     * 查询菜单的详细信息，用于编辑表单回显
     *
     * @param menuId 菜单ID
     * @return 菜单详情数据
     */
    @Select("SELECT " +
            "m.id, m.name, m.pre_id as preId, m.module_identifier as moduleIdentifier, " +
            "m.order_info as orderInfo, " +
            "CASE WHEN m.is_disable = 'true' OR m.is_disable = '1' THEN true ELSE false END as isDisable, " +
            "m.menu_type as menuType, m.route_address as routeAddress, " +
            "m.component_path as componentPath, m.permission_identifier as permissionIdentifier, " +
            "m.route_param as routeParam, " +
            "p.name as preName " +
            "FROM t_menu_permission m " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND p.is_del = false " +
            "WHERE m.id = #{menuId} AND m.is_del = false")
    MenuDetailResponseVO getMenuDetailById(@Param("menuId") Long menuId);

    /**
     * 查询根节点菜单
     * 查询pre_id为0的根节点菜单，支持按模块筛选、菜单名称筛选和禁用状态筛选
     *
     * @param moduleIdentifier 模块标识，为空时查询所有模块
     * @param name 菜单名称，支持模糊查询
     * @param isDisable 是否包含禁用的菜单
     * @return 根节点菜单列表
     */
    @Select("<script>" +
            "SELECT m.id, m.name, m.pre_id as preId, m.module_identifier as moduleIdentifier, " +
            "m.order_info as orderInfo, " +
            "CASE WHEN m.is_disable = 'true' OR m.is_disable = '1' THEN true ELSE false END as isDisable, " +
            "m.menu_type as menuType, m.route_address as routeAddress, " +
            "m.component_path as componentPath, m.permission_identifier as permissionIdentifier, " +
            "m.route_param as routeParam, " +
            "m.create_time as createTime, " +
            "p.name as preName " +
            "FROM t_menu_permission m " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND p.is_del = false " +
            "WHERE m.pre_id = 0 AND m.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND m.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            "AND m.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='isDisable != null and !isDisable'>" +
            "AND (m.is_disable = 'false' OR m.is_disable = '0' OR m.is_disable IS NULL) " +
            "</if>" +
            "ORDER BY m.order_info ASC" +
            "</script>")
    List<MenuTreeResponseVO> getRootMenus(@Param("moduleIdentifier") String moduleIdentifier,
                                         @Param("name") String name,
                                         @Param("isDisable") Boolean isDisable);

    /**
     * 查询子菜单
     * 根据父级菜单ID查询子菜单，支持按模块筛选、菜单名称筛选和禁用状态筛选
     *
     * @param parentId 父级菜单ID
     * @param moduleIdentifier 模块标识，为空时查询所有模块
     * @param name 菜单名称，支持模糊查询
     * @param isDisable 是否包含禁用的菜单
     * @return 子菜单列表
     */
    @Select("<script>" +
            "SELECT m.id, m.name, m.pre_id as preId, m.module_identifier as moduleIdentifier, " +
            "m.order_info as orderInfo, " +
            "CASE WHEN m.is_disable = 'true' OR m.is_disable = '1' THEN true ELSE false END as isDisable, " +
            "m.menu_type as menuType, m.route_address as routeAddress, " +
            "m.component_path as componentPath, m.permission_identifier as permissionIdentifier, " +
            "m.route_param as routeParam, " +
            "m.create_time as createTime, " +
            "p.name as preName " +
            "FROM t_menu_permission m " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND p.is_del = false " +
            "WHERE m.pre_id = #{parentId} AND m.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND m.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            "AND m.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='isDisable != null and !isDisable'>" +
            "AND (m.is_disable = 'false' OR m.is_disable = '0' OR m.is_disable IS NULL) " +
            "</if>" +
            "ORDER BY m.order_info ASC" +
            "</script>")
    List<MenuTreeResponseVO> getChildMenus(@Param("parentId") Long parentId,
                                          @Param("moduleIdentifier") String moduleIdentifier,
                                          @Param("name") String name,
                                          @Param("isDisable") Boolean isDisable);

    /**
     * 修改菜单信息
     * 更新菜单的所有字段信息
     *
     * @param id 菜单ID
     * @param name 菜单名称
     * @param preId 父级菜单ID
     * @param moduleIdentifier 模块标识
     * @param orderInfo 排序序号
     * @param isDisable 是否禁用
     * @param menuType 菜单类型
     * @param routeAddress 路由地址
     * @param componentPath 组件路径
     * @param permissionIdentifier 权限标识
     * @param routeParam 路由参数
     */
    @Update("UPDATE t_menu_permission SET " +
            "name = #{name}, " +
            "pre_id = #{preId}, " +
            "module_identifier = #{moduleIdentifier}, " +
            "order_info = #{orderInfo}, " +
            "is_disable = #{isDisable}, " +
            "menu_type = #{menuType}, " +
            "route_address = #{routeAddress}, " +
            "component_path = #{componentPath}, " +
            "permission_identifier = #{permissionIdentifier}, " +
            "route_param = #{routeParam}, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id} AND is_del = false")
    void updateMenu(@Param("id") Long id,
                   @Param("name") String name,
                   @Param("preId") Long preId,
                   @Param("moduleIdentifier") String moduleIdentifier,
                   @Param("orderInfo") Integer orderInfo,
                   @Param("isDisable") Boolean isDisable,
                   @Param("menuType") Integer menuType,
                   @Param("routeAddress") String routeAddress,
                   @Param("componentPath") String componentPath,
                   @Param("permissionIdentifier") String permissionIdentifier,
                   @Param("routeParam") String routeParam);

    /**
     * 获取指定菜单的所有直接子菜单ID
     * 用于循环引用检查
     *
     * @param parentId 父级菜单ID
     * @return 子菜单ID列表
     */
    @Select("SELECT id FROM t_menu_permission " +
            "WHERE pre_id = #{parentId} AND is_del = false")
    List<Long> getChildMenuIds(@Param("parentId") Long parentId);

    /**
     * 检查菜单权限标识符是否已存在
     * 用于权限标识符重复检测
     *
     * @param permissionIdentifier 权限标识符
     * @param excludeId 排除的菜单ID（编辑时使用）
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_menu_permission " +
            "WHERE permission_identifier = #{permissionIdentifier} " +
            "AND is_del = false " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    int checkMenuPermissionIdentifierExists(@Param("permissionIdentifier") String permissionIdentifier,
                                          @Param("excludeId") Long excludeId);

    /**
     * 检查数据权限标识符是否已存在
     * 用于权限标识符重复检测
     *
     * @param dataIdentifier 数据权限标识符
     * @param excludeId 排除的数据权限ID（编辑时使用）
     * @return 存在的数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_data_permission " +
            "WHERE data_identifier = #{dataIdentifier} " +
            "AND is_del = false " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "</script>")
    int checkDataPermissionIdentifierExists(@Param("dataIdentifier") String dataIdentifier,
                                          @Param("excludeId") Long excludeId);

    /**
     * 检查菜单是否被角色权限使用
     * 检查t_roles_menu_permission表中是否有引用该菜单的记录
     * 注意：menu_id字段是integer类型，但为防止精度丢失，使用字符串比较
     *
     * @param menuId 菜单ID
     * @return 使用该菜单的记录数量
     */
    @Select("SELECT COUNT(*) FROM t_roles_menu_permission " +
            "WHERE menu_id::text = #{menuId}::text AND is_del = false")
    int checkMenuUsedByRoles(@Param("menuId") Long menuId);

    /**
     * 根据菜单ID逻辑删除菜单
     * 设置is_del为true，更新修改时间
     *
     * @param menuId 菜单ID
     */
    @Update("UPDATE t_menu_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{menuId}")
    void deleteMenuById(@Param("menuId") Long menuId);

    /**
     * 批量查询所有符合条件的菜单数据
     * 用于性能优化，一次性查询所有菜单数据，避免递归查询导致的N+1问题
     * 支持模块筛选、名称筛选、禁用状态筛选，查询条件与原getRootMenus和getChildMenus方法保持一致
     *
     * @param moduleIdentifier 模块标识，为空时查询所有模块
     * @param name 菜单名称，支持模糊查询
     * @param isDisable 是否包含禁用的菜单
     * @return 所有符合条件的菜单列表
     */
    @Select("<script>" +
            "SELECT m.id, m.name, m.pre_id as preId, m.module_identifier as moduleIdentifier, " +
            "m.order_info as orderInfo, " +
            "CASE WHEN m.is_disable = 'true' OR m.is_disable = '1' THEN true ELSE false END as isDisable, " +
            "m.menu_type as menuType, m.route_address as routeAddress, " +
            "m.component_path as componentPath, m.permission_identifier as permissionIdentifier, " +
            "m.route_param as routeParam, " +
            "m.create_time as createTime, " +
            "p.name as preName " +
            "FROM t_menu_permission m " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND p.is_del = false " +
            "WHERE m.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND m.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            "AND m.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='isDisable != null and !isDisable'>" +
            "AND (m.is_disable = 'false' OR m.is_disable = '0' OR m.is_disable IS NULL) " +
            "</if>" +
            "ORDER BY m.order_info ASC, m.id ASC" +
            "</script>")
    List<MenuTreeResponseVO> getAllMenusForTree(@Param("moduleIdentifier") String moduleIdentifier,
                                               @Param("name") String name,
                                               @Param("isDisable") Boolean isDisable);

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 查询使用指定菜单的角色列表
     * 用于V2版本删除功能中显示使用情况
     *
     * @param menuId 菜单ID
     * @return 角色列表
     */
    @Select("SELECT DISTINCT r.id, r.role_name " +
            "FROM t_role r " +
            "JOIN t_roles_menu_permission rmp ON r.id = rmp.role_id " +
            "WHERE rmp.menu_id = #{menuId} " +
            "AND r.is_del = false " +
            "AND rmp.is_del = false")
    List<RoleUsageVO> getRolesByMenuId(@Param("menuId") Long menuId);

    /**
     * 查询使用指定模块下菜单的角色列表
     * 用于V2版本删除功能中显示模块使用情况
     *
     * @param moduleIdentifier 模块标识
     * @return 角色列表
     */
    @Select("SELECT DISTINCT r.id, r.role_name " +
            "FROM t_role r " +
            "JOIN t_roles_menu_permission rmp ON r.id = rmp.role_id " +
            "JOIN t_menu_permission mp ON rmp.menu_id = mp.id " +
            "WHERE mp.module_identifier = #{moduleIdentifier} " +
            "AND r.is_del = false " +
            "AND rmp.is_del = false " +
            "AND mp.is_del = false")
    List<RoleUsageVO> getRolesByModuleIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 根据模块标识获取模块名称
     * 用于V2版本删除功能中显示模块信息
     *
     * @param moduleIdentifier 模块标识
     * @return 模块名称
     */
    @Select("SELECT module_name FROM t_menu_module " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false")
    String getModuleNameByIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 根据菜单ID获取菜单名称
     * 用于V2版本删除功能中显示菜单信息
     *
     * @param menuId 菜单ID
     * @return 菜单名称
     */
    @Select("SELECT name FROM t_menu_permission " +
            "WHERE id = #{menuId} " +
            "AND is_del = false")
    String getMenuNameById(@Param("menuId") Long menuId);

    /**
     * 级联删除菜单的角色权限关联
     * 用于V2版本强制删除功能
     *
     * @param menuId 菜单ID
     */
    @Update("UPDATE t_roles_menu_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE menu_id = #{menuId}")
    void deleteMenuRolePermissions(@Param("menuId") Long menuId);

    /**
     * 级联删除模块下所有菜单的角色权限关联
     * 用于V2版本强制删除功能
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_roles_menu_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE menu_id IN (" +
            "  SELECT id FROM t_menu_permission " +
            "  WHERE module_identifier = #{moduleIdentifier} AND is_del = false" +
            ")")
    void deleteModuleRolePermissions(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 级联删除模块下所有菜单
     * 用于V2版本强制删除功能
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_menu_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE module_identifier = #{moduleIdentifier}")
    void deleteMenusByModuleIdentifier(@Param("moduleIdentifier") String moduleIdentifier);
}
