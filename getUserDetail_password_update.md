# getUserDetail 接口密码字段添加任务

## 任务概述
为 `/users/getUserDetail` 接口添加密码字段返回，满足管理员查看和编辑用户密码的需求。

## 已完成步骤

### ✅ 步骤 1: 修改 UserMapper.findUserDetailById 方法
- 文件: `src/main/java/com/dfit/percode/mapper/UserMapper.java`
- 在 SELECT 查询中添加 `u.password AS password` 字段
- 更新方法注释，说明包含密码（管理员权限）

### ✅ 步骤 2: 修改 UserDetailResponseVO 类
- 文件: `src/main/java/com/dfit/percode/vo/UserDetailResponseVO.java`
- 添加 `password` 字段定义
- 添加相应的 API 文档注释

## 修改详情

### 修改前的查询SQL
```sql
SELECT 
    u.id AS userId, 
    u.user_name AS userName, 
    u.account AS account, 
    u.organ_affiliation AS organAffiliation, 
    o.organ_name AS organName, 
    u.is_disable AS isDisable 
FROM t_user u 
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id 
WHERE u.id = #{userId} AND u.is_del = false
```

### 修改后的查询SQL
```sql
SELECT 
    u.id AS userId, 
    u.user_name AS userName, 
    u.account AS account, 
    u.password AS password,           -- ✅ 新增
    u.organ_affiliation AS organAffiliation, 
    o.organ_name AS organName, 
    u.is_disable AS isDisable 
FROM t_user u 
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id 
WHERE u.id = #{userId} AND u.is_del = false
```

### UserDetailResponseVO 新增字段
```java
@ApiModelProperty(value = "登录密码", example = "123456")
private String password;
```

## 接口字段完整性验证

现在三个相关接口的字段完全一致：

| 字段 | addMembers | updateUser | getUserDetail |
|------|------------|------------|---------------|
| userId | ✅ | ✅ | ✅ |
| userName | ✅ | ✅ | ✅ |
| account | ✅ | ✅ | ✅ |
| password | ✅ | ✅ | ✅ |
| organAffiliation | ✅ | ✅ | ✅ |
| organName | ❌ | ❌ | ✅ (额外提供) |
| roles | ✅ | ✅ | ✅ |
| isDisable | ✅ | ✅ | ✅ |

## 功能验证

### 测试接口
```http
POST /users/getUserDetail
Content-Type: application/json

{
  "userId": 1
}
```

### 预期响应
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "userId": "1",
    "userName": "张三",
    "account": "zhang001",
    "password": "123456",
    "organAffiliation": "1001",
    "organName": "技术部",
    "isDisable": false,
    "roles": [
      {
        "roleId": "1",
        "roleName": "管理员"
      }
    ]
  }
}
```

## 安全说明

- 该接口返回密码是基于管理员权限的需求
- 适用于权限管理系统的管理员用户
- 前端应确保只有具备相应权限的用户才能访问此接口

## 状态
🎯 **任务完成** - getUserDetail 接口现在完整支持密码字段返回