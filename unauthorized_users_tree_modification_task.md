# 未授权用户接口树形结构修改任务

## 任务概述
根据前端需求，将 `getUnauthorizedUsers` 接口从分页列表格式修改为树形结构格式，按部门层级展示未授权用户，便于用户选择。

## 已完成步骤

### ✅ 步骤 1: 简化 UnauthorizedUsersRequestVO
- 文件: `src/main/java/com/dfit/percode/vo/UnauthorizedUsersRequestVO.java`
- 移除分页相关字段：currentPage、pageSize
- 移除搜索相关字段：userName、userState、startTime、endTime
- 只保留核心字段：roleId

### ✅ 步骤 2: 修改 UnauthorizedUsersResponseVO
- 文件: `src/main/java/com/dfit/percode/vo/UnauthorizedUsersResponseVO.java`
- 移除分页相关字段：records、total、pageNum、pageSize、totalPages、hasPrevious、hasNext
- 改为返回树形结构：departmentTree (List<DepartmentTreeVO>)

### ✅ 步骤 3: 在 UserMapper 中添加树形查询方法
- 文件: `src/main/java/com/dfit/percode/mapper/UserMapper.java`
- 添加 `findUnauthorizedUsersTreeOptimized` 方法声明
- 文件: `src/main/resources/mapper/UserMapper.xml`
- 实现 RECURSIVE CTE 查询，一次性获取部门树和未授权用户

### ✅ 步骤 4: 修改 UserServiceImpl 业务逻辑
- 文件: `src/main/java/com/dfit/percode/service/impl/UserServiceImpl.java`
- 修改 `getUnauthorizedUsers` 方法，改为构建树形结构
- 添加 `buildUnauthorizedUsersTreeFromRecursive` 方法
- 添加 `hasUnauthorizedUsers` 方法过滤空部门
- 添加 `convertToBoolean` 类型转换方法

### ✅ 步骤 5: 修改 UserController 接口
- 文件: `src/main/java/com/dfit/percode/controller/UserController.java`
- 修改接口注释和返回格式
- 返回部门树形结构而非分页数据

## 核心 SQL 实现

### RECURSIVE CTE 查询
```sql
WITH RECURSIVE dept_tree AS (
    -- 递归起点：查询所有根部门
    SELECT 
        id AS deptId,
        organ_name AS deptName,
        pre_id AS parentId,
        1 AS level
    FROM t_org_structure 
    WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false
    
    UNION ALL
    
    -- 递归部分：查询子部门
    SELECT 
        os.id AS deptId,
        os.organ_name AS deptName,
        os.pre_id AS parentId,
        dt.level + 1 AS level
    FROM t_org_structure os
    INNER JOIN dept_tree dt ON os.pre_id = dt.deptId
    WHERE os.is_del = false
)
SELECT 
    dt.deptId,
    dt.deptName,
    dt.parentId,
    dt.level,
    u.id AS userId,
    u.user_name AS userName,
    u.account AS account,
    u.is_disable AS userState,
    u.create_time AS createTime
FROM dept_tree dt
LEFT JOIN t_user u ON u.organ_affiliation = dt.deptId 
    AND u.is_del = false
    AND (
        -- 未授权用户：从未分配该角色或已取消授权
        NOT EXISTS (
            SELECT 1 FROM t_perm_user_role pur 
            WHERE pur.user_id = u.id 
            AND pur.role_id = #{roleId} 
            AND pur.is_del = false
        )
    )
ORDER BY dt.level, dt.deptId, u.user_name
```

## 接口变化对比

### 修改前（分页列表格式）

#### 请求参数
```json
{
  "roleId": *********,
  "currentPage": 1,
  "pageSize": 10,
  "userName": "张三",
  "userState": false
}
```

#### 响应格式
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "userId": "1",
      "userName": "张三",
      "account": "zhang003",
      "department": "技术部",
      "userState": false,
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "total": 15
}
```

### 修改后（树形结构格式）

#### 请求参数
```json
{
  "roleId": *********
}
```

#### 响应格式
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "1001",
      "organName": "技术部",
      "children": [
        {
          "id": "1",
          "userName": "张三"
        },
        {
          "id": "2",
          "userName": "王五"
        },
        {
          "id": "1003",
          "organName": "开发组",
          "children": [
            {
              "id": "3",
              "userName": "李四"
            }
          ]
        }
      ]
    },
    {
      "id": "1002",
      "organName": "市场部",
      "children": [
        {
          "id": "4",
          "userName": "赵六"
        }
      ]
    }
  ]
}
```

## 树形结构构建逻辑

### 1. 数据处理流程
1. **RECURSIVE 查询**：一次性获取所有部门和未授权用户数据
2. **部门映射**：构建部门ID到部门对象的映射
3. **用户节点**：为每个未授权用户创建用户节点
4. **树形构建**：根据父子关系构建完整的树形结构
5. **空部门过滤**：移除没有未授权用户的部门

### 2. 节点类型识别
- **部门节点**：有 `organName` 字段，包含 `children` 数组
- **用户节点**：有 `userName` 字段，表示可选择的用户

### 3. 性能优化
- 使用 RECURSIVE CTE 避免 N+1 查询问题
- 一次查询获取所有数据，减少数据库交互
- 内存中构建树形结构，高效处理

## 前端使用方式

### 1. 调用接口
```javascript
// 获取未授权用户树形结构
fetch('/users/getUnauthorizedUsers', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    roleId: currentRoleId
  })
})
.then(response => response.json())
.then(data => {
  if (data.code === 200) {
    // data.data 是部门树形结构
    renderUserTree(data.data);
  }
});
```

### 2. 树形展示
```javascript
function renderUserTree(departments) {
  departments.forEach(dept => {
    // 渲染部门节点
    if (dept.organName) {
      renderDepartment(dept);
    }
    
    // 渲染子节点（部门或用户）
    if (dept.children) {
      dept.children.forEach(child => {
        if (child.userName) {
          // 用户节点，可选择
          renderSelectableUser(child);
        } else if (child.organName) {
          // 子部门，递归渲染
          renderUserTree([child]);
        }
      });
    }
  });
}
```

### 3. 用户选择
```javascript
function selectUser(userId) {
  // 调用授权接口
  fetch('/users/userRole', {
    method: 'POST',
    body: JSON.stringify({
      userId: userId,
      roleId: currentRoleId,
      isAssigned: true
    })
  });
}
```

## 功能特性

### 1. 简化的接口参数
- 只需要角色ID，无需分页参数
- 减少前端调用复杂度

### 2. 直观的树形展示
- 按部门层级组织用户
- 符合用户的认知习惯
- 便于快速定位和选择

### 3. 高性能查询
- RECURSIVE CTE 一次性获取所有数据
- 避免多次数据库查询
- 内存中高效构建树形结构

### 4. 智能过滤
- 只显示有未授权用户的部门
- 避免空部门干扰用户选择

## 状态
🎯 **任务完成** - 未授权用户接口已成功修改为树形结构格式，支持按部门层级展示未授权用户，提供更直观的用户选择界面