package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询数据权限列表请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataPermissionListRequestVO", description = "查询数据权限列表请求参数")
public class DataPermissionListRequestVO {
    
    @ApiModelProperty(value = "页码", required = true, example = "1")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    private Integer pageSize;
    
    @ApiModelProperty(value = "模块标识", required = false, example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "数据名称", required = false, example = "用户数据")
    private String name;
    
    @ApiModelProperty(value = "是否禁用", required = false, example = "true")
    private Boolean isDisable;
}
