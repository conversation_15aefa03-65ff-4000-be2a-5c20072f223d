<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dfit.orgsync.mapper.OrganizationSyncMapper">
    
    <!-- 结果映射 -->
    <resultMap id="OrganizationTreeNodeMap" type="com.dfit.orgsync.dto.OrganizationTreeNode">
        <id property="id" column="id"/>
        <result property="organName" column="organ_name"/>
        <result property="fullName" column="full_name"/>
        <result property="preId" column="pre_id"/>
        <result property="level" column="level"/>
        <result property="orderInfo" column="order_info"/>
        <result property="isDel" column="is_del"/>
        <result property="createTime" column="create_time"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="dataSource" column="data_source"/>
    </resultMap>
    
    <!-- 批量插入组织架构数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_org_structure 
        (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.organName}, #{item.preId}, #{item.orderInfo}, 
             #{item.isDel}, #{item.createTime}, #{item.modifyTime}, #{item.dataSource})
        </foreach>
    </insert>
    
    <!-- 根据数据来源删除记录 -->
    <delete id="deleteByDataSource">
        DELETE FROM t_org_structure WHERE data_source = #{dataSource}
    </delete>
    
    <!-- 根据数据来源统计记录数 -->
    <select id="countByDataSource" resultType="int">
        SELECT COUNT(*) FROM t_org_structure WHERE data_source = #{dataSource}
    </select>
    
    <!-- 获取当前最大ID -->
    <select id="getMaxId" resultType="java.lang.Long">
        SELECT COALESCE(MAX(id), 2000000000) FROM t_org_structure
    </select>
    
    <!-- 验证层级结构，使用递归CTE查询 -->
    <select id="validateHierarchy" resultType="java.util.Map">
        WITH RECURSIVE dept_tree AS (
            -- 查找根节点（一级部门）
            SELECT id, organ_name, pre_id, 1 as level
            FROM t_org_structure 
            WHERE pre_id = 0 AND data_source = #{dataSource}
            
            UNION ALL
            
            -- 递归查找子节点
            SELECT o.id, o.organ_name, o.pre_id, dt.level + 1
            FROM t_org_structure o
            JOIN dept_tree dt ON o.pre_id = dt.id
            WHERE o.data_source = #{dataSource}
        )
        SELECT level, COUNT(*) as count
        FROM dept_tree
        GROUP BY level
        ORDER BY level
    </select>
    
    <!-- 查找孤立节点 -->
    <select id="findOrphanNodes" resultMap="OrganizationTreeNodeMap">
        SELECT id, organ_name, pre_id
        FROM t_org_structure o1
        WHERE o1.data_source = #{dataSource} 
        AND o1.pre_id != 0 
        AND NOT EXISTS (
            SELECT 1 FROM t_org_structure o2 
            WHERE o2.id = o1.pre_id AND o2.data_source = #{dataSource}
        )
    </select>
    
    <!-- 查找重复名称 -->
    <select id="findDuplicateNames" resultType="java.util.Map">
        SELECT organ_name, COUNT(*) as count
        FROM t_org_structure 
        WHERE data_source = #{dataSource}
        GROUP BY organ_name 
        HAVING COUNT(*) > 1
        ORDER BY count DESC
    </select>
    
    <!-- 查询一级部门列表 -->
    <select id="findLevel1Departments" resultMap="OrganizationTreeNodeMap">
        SELECT id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source
        FROM t_org_structure 
        WHERE pre_id = 0 AND data_source = #{dataSource}
        ORDER BY organ_name
    </select>
    
    <!-- 根据ID查询组织节点 -->
    <select id="findById" resultMap="OrganizationTreeNodeMap">
        SELECT id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source
        FROM t_org_structure 
        WHERE id = #{id}
    </select>
    
    <!-- 根据名称查询组织节点 -->
    <select id="findByName" resultMap="OrganizationTreeNodeMap">
        SELECT id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source
        FROM t_org_structure 
        WHERE organ_name = #{organName} AND data_source = #{dataSource}
        ORDER BY id
    </select>
    
    <!-- 查询指定节点的所有子节点 -->
    <select id="findChildren" resultMap="OrganizationTreeNodeMap">
        SELECT id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source
        FROM t_org_structure 
        WHERE pre_id = #{preId} AND data_source = #{dataSource}
        ORDER BY order_info, organ_name
    </select>
    
    <!-- 获取树的最大深度 -->
    <select id="getMaxDepth" resultType="java.lang.Integer">
        WITH RECURSIVE dept_tree AS (
            SELECT id, organ_name, pre_id, 1 as level
            FROM t_org_structure 
            WHERE pre_id = 0 AND data_source = #{dataSource}
            
            UNION ALL
            
            SELECT o.id, o.organ_name, o.pre_id, dt.level + 1
            FROM t_org_structure o
            JOIN dept_tree dt ON o.pre_id = dt.id
            WHERE o.data_source = #{dataSource}
        )
        SELECT COALESCE(MAX(level), 0) FROM dept_tree
    </select>
    
    <!-- 检查数据完整性 -->
    <select id="checkDataIntegrity" resultType="java.util.Map">
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT id) as unique_ids,
            COUNT(DISTINCT organ_name) as unique_names,
            MIN(id) as min_id,
            MAX(id) as max_id,
            SUM(CASE WHEN pre_id = 0 THEN 1 ELSE 0 END) as root_count,
            SUM(CASE WHEN is_del = true THEN 1 ELSE 0 END) as deleted_count
        FROM t_org_structure 
        WHERE data_source = #{dataSource}
    </select>
    
</mapper>
