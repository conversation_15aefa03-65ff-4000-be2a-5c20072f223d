package com.dfit.percode.sync.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部API统一响应格式
 * 对应外部系统API的响应结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ApiResponse<T> {
    
    /**
     * 请求是否成功
     */
    private Boolean success;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     * 可以是单个对象、列表或字符串
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private String timestamp;
    
    /**
     * 数据总数
     */
    private Integer totalCount;
}
