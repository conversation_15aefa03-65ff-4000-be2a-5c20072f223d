package com.dfit.percode.sync.entity;

import lombok.Data;

/**
 * 部门子表实体类
 * 对应外部系统的department_child表结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class DepartmentChild {
    
    /**
     * 全局唯一标识符
     */
    private String guid;
    
    /**
     * 关联的部门UUID
     */
    private String deptUuid;
    
    /**
     * 来源系统
     */
    private String sourceSystem;
    
    /**
     * 来源系统数据标识
     */
    private String sourceDataNm;
    
    /**
     * 自定义字段1
     */
    private String udef1;
    
    /**
     * 自定义字段2
     */
    private String udef2;
    
    /**
     * 自定义字段3
     */
    private String udef3;
    
    /**
     * 自定义字段4
     */
    private String udef4;
    
    /**
     * 自定义字段5
     */
    private String udef5;
    
    /**
     * 自定义字段6
     */
    private String udef6;
}
