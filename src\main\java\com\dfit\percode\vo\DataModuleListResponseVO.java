package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据模块列表响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataModuleListResponseVO", description = "数据模块列表响应数据")
public class DataModuleListResponseVO {
    
    @ApiModelProperty(value = "模块ID", example = "1001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "模块名称", example = "用户数据模块")
    private String moduleName;
    
    @ApiModelProperty(value = "模块标识", example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "创建时间", example = "2023-05-15 14:30:00")
    private String createTime;
    
    @ApiModelProperty(value = "修改时间", example = "2023-05-15 14:30:00")
    private String modifyTime;
}
