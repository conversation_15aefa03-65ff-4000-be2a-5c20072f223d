package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增角色响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 返回新创建角色的基本信息
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddRoleResponseVO", description = "新增角色响应数据")
public class AddRoleResponseVO {
    
    @ApiModelProperty(value = "角色ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "角色名称")
    private String roleName;
    
    @ApiModelProperty(value = "排序序号")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "是否停用：false-正常，true-停用")
    private Boolean isDisable;
    
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    
    @ApiModelProperty(value = "菜单权限数量")
    private Integer menuPermissionCount = 0;
    
    @ApiModelProperty(value = "数据权限数量")
    private Integer dataPermissionCount = 0;
}
