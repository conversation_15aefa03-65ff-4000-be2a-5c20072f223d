package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.config.SuperAdminConfig;
import com.dfit.percode.service.SuperAdminPermissionService;
import com.dfit.percode.util.SuperAdminUtil;
import com.dfit.percode.vo.UserPermissionsResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 超级管理员测试控制器
 * 提供超级管理员功能的测试接口
 * 注意：这些接口仅用于开发和测试阶段
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/super-admin")
@Api(tags = "超级管理员测试接口")
public class SuperAdminController {

    @Autowired
    private SuperAdminConfig superAdminConfig;

    @Autowired
    private SuperAdminPermissionService superAdminPermissionService;

    @Autowired
    private SuperAdminUtil superAdminUtil;

    /**
     * 检查当前用户是否为超级管理员
     * 返回用户身份信息和权限状态
     */
    @GetMapping("/check")
    @ApiOperation(value = "检查超级管理员身份", notes = "检查当前用户是否为超级管理员，返回身份信息")
    public BaseResult<Map<String, Object>> checkSuperAdmin(HttpServletRequest request) {
        log.info("检查超级管理员身份请求");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取用户ID
            Long userId = (Long) request.getAttribute("userId");
            String authMethod = (String) request.getAttribute("authMethod");
            Boolean isSuperAdminFromRequest = (Boolean) request.getAttribute("isSuperAdmin");
            
            result.put("userId", userId);
            result.put("authMethod", authMethod);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            if (userId != null) {
                // 检查超级管理员身份
                boolean isSuperAdmin = superAdminUtil.isSuperAdmin(userId);
                result.put("isSuperAdmin", isSuperAdmin);
                result.put("isSuperAdminFromRequest", isSuperAdminFromRequest);
                
                // 获取用户信息
                Map<String, Object> userInfo = superAdminUtil.getUserInfoFromDatabase(userId);
                result.put("userInfo", userInfo);
                
                // 获取配置摘要
                result.put("configSummary", superAdminUtil.getConfigSummary());
                
                if (isSuperAdmin) {
                    log.info("🔥 超级管理员身份确认，用户ID: {}", userId);
                    result.put("message", "当前用户是超级管理员");
                } else {
                    log.info("普通用户身份确认，用户ID: {}", userId);
                    result.put("message", "当前用户不是超级管理员");
                }
            } else {
                result.put("isSuperAdmin", false);
                result.put("message", "未能获取用户ID，请检查认证状态");
            }
            
            return new BaseResult<>(200, "检查完成", result);
            
        } catch (Exception e) {
            log.error("检查超级管理员身份失败", e);
            return new BaseResult<>(500, "检查失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取超级管理员配置信息（脱敏处理）
     * 仅超级管理员可访问
     */
    @GetMapping("/config")
    @ApiOperation(value = "获取超级管理员配置", notes = "获取当前超级管理员配置信息（脱敏处理），仅超级管理员可访问")
    public BaseResult<Map<String, Object>> getSuperAdminConfig(HttpServletRequest request) {
        log.info("获取超级管理员配置请求");
        
        try {
            Long userId = (Long) request.getAttribute("userId");
            
            // 检查是否为超级管理员
            if (userId == null || !superAdminUtil.isSuperAdmin(userId)) {
                log.warn("非超级管理员尝试访问配置信息，用户ID: {}", userId);
                return new BaseResult<>(403, "仅超级管理员可访问此接口", null);
            }
            
            Map<String, Object> result = new HashMap<>();
            
            // 基本配置信息（脱敏处理）
            result.put("enabled", superAdminConfig.getEnabled());
            result.put("userIdsCount", superAdminConfig.getUserIds().size());
            result.put("loginAccountsCount", superAdminConfig.getLoginAccounts().size());
            result.put("usernamesCount", superAdminConfig.getUsernames().size());
            result.put("configValid", superAdminConfig.isConfigValid());
            result.put("configSummary", superAdminConfig.getConfigSummary());
            
            // 当前用户信息
            Map<String, Object> currentUserInfo = superAdminUtil.getUserInfoFromDatabase(userId);
            result.put("currentUser", currentUserInfo);
            
            // 系统信息
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            result.put("serverInfo", "权限管理系统 - 超级管理员模式");
            
            log.info("🔥 超级管理员配置信息查询成功，用户ID: {}", userId);
            return new BaseResult<>(200, "配置信息获取成功", result);
            
        } catch (Exception e) {
            log.error("获取超级管理员配置失败", e);
            return new BaseResult<>(500, "获取配置失败: " + e.getMessage(), null);
        }
    }

    /**
     * 测试超级管理员权限绕过功能
     * 模拟需要特殊权限的操作
     */
    @PostMapping("/test-access")
    @ApiOperation(value = "测试权限绕过", notes = "测试超级管理员权限绕过功能，模拟需要特殊权限的操作")
    public BaseResult<Map<String, Object>> testSuperAdminAccess(
            @ApiParam(value = "测试操作类型", example = "SYSTEM_CONFIG") @RequestParam(defaultValue = "SYSTEM_CONFIG") String operation,
            HttpServletRequest request) {
        
        log.info("超级管理员权限测试请求，操作类型: {}", operation);
        
        try {
            Long userId = (Long) request.getAttribute("userId");
            String authMethod = (String) request.getAttribute("authMethod");
            
            Map<String, Object> result = new HashMap<>();
            result.put("operation", operation);
            result.put("userId", userId);
            result.put("authMethod", authMethod);
            result.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            if (userId != null && superAdminUtil.isSuperAdmin(userId)) {
                // 超级管理员权限测试
                result.put("accessGranted", true);
                result.put("accessLevel", "SUPER_ADMIN");
                result.put("message", "超级管理员权限验证通过，可执行所有操作");
                
                // 模拟不同类型的操作
                switch (operation.toUpperCase()) {
                    case "SYSTEM_CONFIG":
                        result.put("operationResult", "系统配置访问成功");
                        result.put("details", "可以修改系统核心配置");
                        break;
                    case "USER_MANAGEMENT":
                        result.put("operationResult", "用户管理访问成功");
                        result.put("details", "可以管理所有用户账号");
                        break;
                    case "PERMISSION_MANAGEMENT":
                        result.put("operationResult", "权限管理访问成功");
                        result.put("details", "可以管理所有角色和权限");
                        break;
                    case "DATA_ACCESS":
                        result.put("operationResult", "数据访问成功");
                        result.put("details", "可以访问所有敏感数据");
                        break;
                    default:
                        result.put("operationResult", "通用操作访问成功");
                        result.put("details", "超级管理员拥有所有权限");
                        break;
                }
                
                // 记录超级管理员操作
                superAdminUtil.logSuperAdminAccess(userId, request, "权限测试-" + operation);
                
                log.info("🔥 超级管理员权限测试成功，用户ID: {}, 操作: {}", userId, operation);
                
            } else {
                // 非超级管理员
                result.put("accessGranted", false);
                result.put("accessLevel", "NORMAL_USER");
                result.put("message", "权限不足，需要超级管理员权限");
                result.put("operationResult", "访问被拒绝");
                
                log.warn("非超级管理员尝试权限测试，用户ID: {}, 操作: {}", userId, operation);
            }
            
            return new BaseResult<>(200, "权限测试完成", result);
            
        } catch (Exception e) {
            log.error("超级管理员权限测试失败", e);
            return new BaseResult<>(500, "权限测试失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取超级管理员权限数据
     * 直接调用权限查询服务进行测试
     */
    @GetMapping("/permissions")
    @ApiOperation(value = "获取超级管理员权限", notes = "直接调用权限查询服务，测试超级管理员权限获取")
    public BaseResult<UserPermissionsResponseVO> getSuperAdminPermissions(
            @ApiParam(value = "模块标识符", example = "system") @RequestParam(required = false) String moduleIdentifier,
            HttpServletRequest request) {
        
        log.info("超级管理员权限获取测试，模块标识: {}", moduleIdentifier);
        
        try {
            Long userId = (Long) request.getAttribute("userId");
            
            if (userId == null) {
                return new BaseResult<>(401, "未能获取用户ID，请检查认证状态", null);
            }
            
            if (!superAdminUtil.isSuperAdmin(userId)) {
                return new BaseResult<>(403, "仅超级管理员可访问此接口", null);
            }
            
            // 直接调用超级管理员权限查询服务
            UserPermissionsResponseVO permissions = superAdminPermissionService.getSuperAdminPermissions(userId, moduleIdentifier);
            
            log.info("🔥 超级管理员权限获取成功，用户ID: {}, 菜单数量: {}, 按钮数量: {}", 
                    userId, 
                    permissions.getPermissions().getMenus().size(),
                    permissions.getPermissions().getButtons().size());
            
            return new BaseResult<>(200, "超级管理员权限获取成功", permissions);
            
        } catch (Exception e) {
            log.error("超级管理员权限获取失败", e);
            return new BaseResult<>(500, "权限获取失败: " + e.getMessage(), null);
        }
    }
}
