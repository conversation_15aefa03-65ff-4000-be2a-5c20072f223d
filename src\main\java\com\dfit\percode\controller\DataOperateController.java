package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITDataOperateService;
import com.dfit.percode.vo.ConfigureDataOperateRequestVO;
import com.dfit.percode.vo.ConfigureDataOperateListRequestVO;
import com.dfit.percode.vo.DataOperateConfigResponseVO;

import java.util.List;
import java.util.Map;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据操作权限管理控制器
 * 提供数据操作权限的配置和查询接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@Api(tags = "数据操作权限管理接口")
@RequestMapping("/data-operates")
public class DataOperateController {

    @Autowired
    private ITDataOperateService dataOperateService;

    /**
     * 配置模块操作权限
     * 为指定的模块配置可执行的操作类型（新增、修改、删除等）
     * 该模块下所有数据都继承这些操作权限
     *
     * @param request 配置模块操作权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/configure", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "配置模块操作权限", notes = "为指定的模块配置可执行的操作类型，该模块下所有数据都继承这些权限")
    public BaseResult configureDataOperate(@RequestBody ConfigureDataOperateRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始配置模块操作权限");
            log.info("模块标识: {}, 操作类型: {}",
                    request.getModuleIdentifier(), request.getOperateTypes());

            // 调用服务层配置模块操作权限
            dataOperateService.configureDataOperate(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData("模块操作权限配置成功，已执行级联删除检查");

            log.info("模块操作权限配置成功");

        } catch (Exception e) {
            log.error("配置数据操作权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("配置数据操作权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 配置数据权限操作权限（数据级别）
     * 为多个数据权限分别配置可执行的操作类型
     *
     * @param request 配置数据权限操作权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/configure-list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "配置数据权限操作权限", notes = "为多个数据权限分别配置可执行的操作类型，支持细粒度权限控制")
    public BaseResult configureDataOperateList(@RequestBody ConfigureDataOperateListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始配置数据级别操作权限");
            log.info("模块标识: {}, 数据权限数量: {}",
                    request.getModuleIdentifier(),
                    request.getDataOperateConfigs() != null ? request.getDataOperateConfigs().size() : 0);

            // 调用服务层配置数据级别操作权限
            dataOperateService.configureDataOperateList(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData("数据级别操作权限配置成功，已执行级联删除检查");

            log.info("数据级别操作权限配置成功");

        } catch (Exception e) {
            log.error("配置数据级别操作权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("配置数据级别操作权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取数据操作权限配置
     * 查询指定数据权限的操作类型配置
     *
     * @param dataIdentifier 数据标识
     * @param moduleIdentifier 模块标识
     * @return 统一返回格式，data为数据操作权限配置信息
     */
    @RequestMapping(value = "/config", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取数据操作权限配置", notes = "查询指定数据权限的操作类型配置")
    public BaseResult getDataOperateConfig(
            @RequestParam(value = "dataIdentifier", required = true)
            @ApiParam(value = "数据标识", required = true, example = "user_basic_data")
            String dataIdentifier,
            @RequestParam(value = "moduleIdentifier", required = true)
            @ApiParam(value = "模块标识", required = true, example = "user_data_module")
            String moduleIdentifier) {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始获取数据操作权限配置");
            log.info("数据标识: {}, 模块标识: {}", dataIdentifier, moduleIdentifier);

            // 调用服务层获取数据操作权限配置
            DataOperateConfigResponseVO config = dataOperateService.getDataOperateConfig(dataIdentifier, moduleIdentifier);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(config);

            log.info("数据操作权限配置获取成功");

        } catch (Exception e) {
            log.error("获取数据操作权限配置失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("获取数据操作权限配置失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 调试接口：查看角色数据权限详情
     * 用于调试级联删除功能
     */
    @RequestMapping(value = "/debug/role-data-permissions", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "调试：查看角色数据权限详情", notes = "用于调试级联删除功能")
    public BaseResult debugRoleDataPermissions(@RequestBody Map<String, Object> request) {
        BaseResult baseResult = new BaseResult();

        try {
            Long roleId = Long.valueOf(request.get("roleId").toString());
            String moduleIdentifier = (String) request.get("moduleIdentifier");

            log.info("调试查询角色数据权限，角色ID: {}, 模块: {}", roleId, moduleIdentifier);

            // 查询角色的所有数据权限记录
            List<Map<String, Object>> permissions = dataOperateService.debugRoleDataPermissions(roleId, moduleIdentifier);

            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(permissions);

        } catch (Exception e) {
            log.error("调试查询角色数据权限失败: {}", e.getMessage(), e);
            baseResult.setCode(500);
            baseResult.setMessage("调试查询失败: " + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
