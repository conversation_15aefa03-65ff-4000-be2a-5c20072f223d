package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单树形结构响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持递归的树形结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MenuTreeResponseVO", description = "菜单树形结构响应数据")
public class MenuTreeResponseVO {

    @ApiModelProperty(value = "菜单ID", example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "菜单名称", example = "用户管理")
    private String name;

    @ApiModelProperty(value = "父级菜单ID，根节点为0", example = "0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;

    @ApiModelProperty(value = "模块标识", example = "permission_management")
    private String moduleIdentifier;

    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;

    @ApiModelProperty(value = "是否禁用：false-启用，true-禁用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "菜单类型：1-目录，2-菜单，3-按钮", example = "2")
    private Integer menuType;

    @ApiModelProperty(value = "路由地址", example = "/permission/user")
    private String routeAddress;

    @ApiModelProperty(value = "组件路径", example = "/views/permission/user")
    private String componentPath;

    @ApiModelProperty(value = "权限标识", example = "permission:user:view")
    private String permissionIdentifier;

    @ApiModelProperty(value = "路由参数", example = "id=123&type=edit")
    private String routeParam;

    @ApiModelProperty(value = "父级菜单名称", example = "系统管理")
    private String preName;

    @ApiModelProperty(value = "创建时间", example = "2023-05-15 10:45:20")
    private String createTime;

    @ApiModelProperty(value = "子菜单列表")
    private List<MenuTreeResponseVO> children;
}
