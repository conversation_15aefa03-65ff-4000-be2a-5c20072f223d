-- 数据权限测试数据插入脚本
-- 为t_data_permission表插入测试数据

-- 清理可能存在的测试数据
DELETE FROM t_data_permission WHERE data_identifier IN (
    'user_basic_data', 'user_profile_data', 'user_auth_data',
    'order_basic_data', 'order_detail_data', 'order_payment_data',
    'product_basic_data', 'product_inventory_data'
);

-- 插入数据权限测试数据
INSERT INTO t_data_permission (id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time) VALUES
-- 用户数据模块权限
(2001, '用户基础数据', 0, 'user_data_module', 1, 1, false, false, 'user_basic_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2002, '用户档案数据', 0, 'user_data_module', 1, 2, false, false, 'user_profile_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2003, '用户认证数据', 0, 'user_data_module', 1, 3, true, false, 'user_auth_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 订单数据模块权限
(2004, '订单基础数据', 0, 'order_data_module', 1, 1, false, false, 'order_basic_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2005, '订单详情数据', 0, 'order_data_module', 1, 2, false, false, 'order_detail_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2006, '订单支付数据', 0, 'order_data_module', 1, 3, true, false, 'order_payment_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- 商品数据模块权限
(2007, '商品基础数据', 0, 'product_data_module', 1, 1, false, false, 'product_basic_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2008, '商品库存数据', 0, 'product_data_module', 1, 2, false, false, 'product_inventory_data', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 验证插入结果
SELECT 
    dp.id,
    dp.name as "数据权限名称",
    dp.module_identifier as "模块标识",
    dm.module_name as "模块名称",
    dp.data_identifier as "数据标识",
    dp.is_disable as "是否禁用",
    dp.create_time as "创建时间"
FROM t_data_permission dp
LEFT JOIN t_data_module dm ON dp.module_identifier = dm.module_identifier AND dm.is_del = false
WHERE dp.is_del = false 
ORDER BY dp.module_identifier, dp.order_info;
