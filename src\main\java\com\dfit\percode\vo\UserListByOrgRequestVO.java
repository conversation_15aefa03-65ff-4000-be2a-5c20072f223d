package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 按部门查询用户列表请求VO类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListByOrgRequestVO", description = "按部门查询用户列表请求参数")
public class UserListByOrgRequestVO {
    
    @ApiModelProperty(value = "页码", example = "1", required = true)
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小", example = "10", required = true)
    private Integer pageSize;
    
    @ApiModelProperty(value = "部门ID", example = "1002", required = true)
    @JsonDeserialize(using = LongDeserializer.class)
    private Long orgId;
    
    @ApiModelProperty(value = "是否包含子部门用户", example = "true")
    private Boolean includeSubOrgs;
    
    @ApiModelProperty(value = "用户名（模糊搜索）", example = "张三")
    private String userName;
    
    @ApiModelProperty(value = "用户状态（null-全部，false-正常，true-禁用）", example = "false")
    private Boolean status;
}
