package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员角色对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_perm_user_role")
@ApiModel(value = "TPermUserRole对象", description = "人员角色对应表")
public class TPermUserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("人员id，对应用户管理表人员id")
    private Long userId;

    @ApiModelProperty("角色id")
    private Long roleId;

    private Integer orderInfo;

    private Integer isDel;

    private String createTime;

    private String modifyTime;
}
