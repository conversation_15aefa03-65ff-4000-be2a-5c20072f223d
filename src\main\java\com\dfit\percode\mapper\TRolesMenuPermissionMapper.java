package com.dfit.percode.mapper;

import com.dfit.percode.entity.TRolesMenuPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 角色菜单权限关联表 Mapper接口
 * 用于管理角色与菜单权限的关联关系
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface TRolesMenuPermissionMapper extends BaseMapper<TRolesMenuPermission> {

    /**
     * 插入角色菜单权限关联
     * 使用数据库函数处理时间字段，避免类型转换问题
     *
     * @param id 关联ID
     * @param roleId 角色ID
     * @param moduleIdentifier 模块标识
     * @param menuId 菜单ID
     */
    @Insert("INSERT INTO t_roles_menu_permission " +
            "(id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{roleId}, #{moduleIdentifier}, #{menuId}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertRoleMenuPermission(@Param("id") Long id,
                                  @Param("roleId") Long roleId,
                                  @Param("moduleIdentifier") String moduleIdentifier,
                                  @Param("menuId") Long menuId);

    /**
     * 删除角色的所有菜单权限
     * 逻辑删除，设置is_del=true
     *
     * @param roleId 角色ID
     */
    @Delete("UPDATE t_roles_menu_permission SET is_del = true, modify_time = CURRENT_TIMESTAMP " +
            "WHERE role_id = #{roleId}")
    void deleteRoleMenuPermissions(@Param("roleId") Long roleId);

    /**
     * 物理删除角色的所有菜单权限
     * 用于彻底清理数据
     *
     * @param roleId 角色ID
     */
    @Delete("DELETE FROM t_roles_menu_permission WHERE role_id = #{roleId}")
    void physicalDeleteRoleMenuPermissions(@Param("roleId") Long roleId);
}
