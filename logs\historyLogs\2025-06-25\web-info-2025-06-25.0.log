2025-06-25 12:01:02.449 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 37668 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 12:01:02.456 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:01:02.458 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 12:01:06.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:01:06.858 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:01:06.907 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 12:01:06.913 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:01:06.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:01:06.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 12:01:06.946 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:01:06.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 12:01:06.986 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 JPA repository interfaces.
2025-06-25 12:01:07.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:01:07.030 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:01:07.082 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-06-25 12:01:09.109 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 12:01:09.137 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 12:01:09.138 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:01:09.139 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:01:09.506 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:01:09.506 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6586 ms
2025-06-25 12:01:09.668 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:01:09.956 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:01:10.954 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 12:01:11.102 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 12:01:11.555 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 12:01:11.879 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 12:01:12.441 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 12:01:12.494 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:01:17.016 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 12:01:21.820 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 12:01:21.859 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 12:01:23.465 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.591 seconds (JVM running for 28.296)
2025-06-25 12:03:36.142 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:03:36.163 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:04:31.971 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 3012 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 12:04:31.973 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 12:04:31.988 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:04:34.788 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:04:34.792 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:04:34.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 12:04:34.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:04:34.890 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:04:34.911 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 12:04:34.954 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:04:34.955 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 12:04:35.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 JPA repository interfaces.
2025-06-25 12:04:35.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:04:35.104 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:04:35.190 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 63 ms. Found 0 Redis repository interfaces.
2025-06-25 12:04:37.031 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 12:04:37.049 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 12:04:37.050 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:04:37.050 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:04:37.456 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:04:37.457 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5395 ms
2025-06-25 12:04:37.641 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:04:37.986 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:04:38.976 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 12:04:39.253 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 12:04:39.577 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 12:04:39.896 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 12:04:40.452 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 12:04:40.475 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:04:45.635 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 12:04:49.846 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 12:04:49.869 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 12:04:51.073 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.469 seconds (JVM running for 27.952)
2025-06-25 12:04:58.924 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:04:58.928 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:04:58.940 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-25 12:06:17.911 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:17:31.049 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:17:31.054 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:17:45.261 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:17:45.296 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 19256 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 12:17:45.298 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 12:17:47.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:17:47.522 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:17:47.564 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 12:17:47.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:17:47.571 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:17:47.589 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 12:17:47.601 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:17:47.602 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 12:17:47.674 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 60 ms. Found 0 JPA repository interfaces.
2025-06-25 12:17:47.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:17:47.706 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:17:47.738 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-25 12:17:49.039 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 12:17:49.053 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 12:17:49.054 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:17:49.054 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:17:49.346 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:17:49.346 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3900 ms
2025-06-25 12:17:49.482 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:17:49.711 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:17:50.588 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 12:17:50.678 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 12:17:50.872 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 12:17:51.097 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 12:17:51.573 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 12:17:51.601 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:17:55.569 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 12:17:59.445 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 12:17:59.475 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 12:18:01.232 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.055 seconds (JVM running for 20.367)
2025-06-25 12:18:51.926 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:18:51.927 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:18:51.930 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-25 12:18:52.388 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:18:56.711 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 4319ms
2025-06-25 12:19:01.216 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:19:01.292 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 74ms
2025-06-25 12:19:15.526 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:19:15.593 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 67ms
2025-06-25 12:21:31.363 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:21:31.367 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:21:31.600 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:21:31.703 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 5
2025-06-25 12:21:31.703 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 5, 按钮权限数量: 0
2025-06-25 12:21:31.704 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 5, 按钮数量: 0, 耗时: 337ms
2025-06-25 12:21:31.705 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 5, 按钮数量: 0, 耗时: 342ms
2025-06-25 12:22:37.338 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:22:37.338 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:22:37.460 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:22:37.523 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:22:37.524 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:22:37.524 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 186ms
2025-06-25 12:22:37.524 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 186ms
2025-06-25 12:22:54.180 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:22:54.180 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:22:54.245 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:22:54.316 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:22:54.316 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:22:54.316 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 135ms
2025-06-25 12:22:54.316 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 136ms
2025-06-25 12:25:07.522 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: system_management
2025-06-25 12:25:07.523 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: system_management
2025-06-25 12:25:07.693 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:25:07.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 2
2025-06-25 12:25:07.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 2, 按钮权限数量: 0
2025-06-25 12:25:07.756 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 2, 按钮数量: 0, 耗时: 233ms
2025-06-25 12:25:07.756 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 2, 按钮数量: 0, 耗时: 233ms
2025-06-25 12:28:27.197 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:28:27.197 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:28:27.369 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:28:27.434 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:28:27.435 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:28:27.436 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 239ms
2025-06-25 12:28:27.436 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 239ms
2025-06-25 12:30:48.649 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:30:48.650 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:30:48.762 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:30:48.830 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:30:48.830 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:30:48.831 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 181ms
2025-06-25 12:30:48.831 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 181ms
2025-06-25 12:31:01.640 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:31:01.640 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:31:01.703 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:31:01.766 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:31:01.767 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:31:01.767 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 127ms
2025-06-25 12:31:01.767 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 127ms
2025-06-25 12:31:10.748 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:31:10.748 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:31:10.807 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:31:10.865 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:31:10.865 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:31:10.865 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 117ms
2025-06-25 12:31:10.865 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 117ms
2025-06-25 12:31:11.800 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:31:11.800 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:31:11.858 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:31:11.914 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:31:11.914 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:31:11.915 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 115ms
2025-06-25 12:31:11.915 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 115ms
2025-06-25 12:31:24.059 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:31:24.059 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:31:24.117 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:31:24.174 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:31:24.174 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:31:24.174 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 115ms
2025-06-25 12:31:24.175 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 116ms
2025-06-25 12:31:30.900 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.AuthController - 用户登出，用户ID: 1936640367617249280
2025-06-25 12:31:41.759 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:31:41.760 [http-nio-8285-exec-3] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：用户未登录或Token已过期
2025-06-25 12:31:53.575 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:31:53.683 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 107ms
2025-06-25 12:32:00.470 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:32:00.471 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:32:00.581 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:32:00.646 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:32:00.647 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:32:00.647 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 176ms
2025-06-25 12:32:00.647 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 176ms
2025-06-25 12:47:06.537 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:47:06.544 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:47:21.727 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 35556 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 12:47:21.729 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 12:47:21.747 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:47:24.961 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:47:24.964 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:47:25.011 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 12:47:25.017 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:47:25.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:47:25.038 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 12:47:25.051 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:47:25.053 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 12:47:25.079 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-25 12:47:25.112 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:47:25.115 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:47:25.151 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-25 12:47:26.424 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 12:47:26.446 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 12:47:26.447 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:47:26.448 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:47:27.042 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:47:27.043 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5160 ms
2025-06-25 12:47:27.237 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:47:27.650 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:47:28.787 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 12:47:28.907 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 12:47:29.131 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 12:47:29.348 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 12:47:29.741 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 12:47:29.764 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:47:33.291 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 12:47:36.572 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 12:47:36.598 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 12:47:37.861 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.34 seconds (JVM running for 20.515)
2025-06-25 12:48:14.044 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:48:14.044 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:48:14.047 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 12:48:14.481 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:48:14.491 [http-nio-8285-exec-3] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：用户未登录或Token已过期
2025-06-25 12:48:19.977 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:48:22.948 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2970ms
2025-06-25 12:48:30.841 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:48:30.843 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:48:30.919 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:52:07.400 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:52:07.411 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 12:52:20.878 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 12:52:20.888 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 36988 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 12:52:20.893 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 12:52:23.564 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:52:23.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:52:23.640 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 12:52:23.656 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:52:23.657 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 12:52:23.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 12:52:23.729 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:52:23.730 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 12:52:23.764 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-25 12:52:23.815 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 12:52:23.820 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 12:52:23.977 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 0 Redis repository interfaces.
2025-06-25 12:52:25.563 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 12:52:25.581 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 12:52:25.582 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 12:52:25.582 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 12:52:25.966 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 12:52:25.967 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4949 ms
2025-06-25 12:52:26.102 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 12:52:26.330 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 12:52:27.097 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 12:52:27.172 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 12:52:27.412 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 12:52:27.587 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 12:52:27.973 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 12:52:27.988 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 12:52:31.419 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 12:52:34.424 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 12:52:34.452 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 12:52:35.894 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.302 seconds (JVM running for 19.333)
2025-06-25 12:53:30.159 [http-nio-8285-exec-7] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 12:53:30.160 [http-nio-8285-exec-7] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 12:53:30.165 [http-nio-8285-exec-7] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-25 12:53:30.660 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 12:53:33.230 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2567ms
2025-06-25 12:53:36.064 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 12:53:36.067 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 12:53:36.143 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 12:53:36.235 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 12:53:36.236 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 12:53:36.237 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 169ms
2025-06-25 12:53:36.237 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 173ms
2025-06-25 13:05:40.872 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 13:05:40.996 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 123ms
2025-06-25 13:05:44.476 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:05:44.481 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:05:44.578 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:05:44.649 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 13:05:44.650 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 13:05:44.651 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 165ms
2025-06-25 13:05:44.651 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 170ms
2025-06-25 13:15:35.850 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:15:36.201 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 13:15:59.977 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 37404 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 13:15:59.979 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 13:16:00.005 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 13:16:02.540 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:16:02.543 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:16:02.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 13:16:02.588 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:16:02.589 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:16:02.604 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 13:16:02.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:16:02.616 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 13:16:02.639 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-25 13:16:02.664 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:16:02.666 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 13:16:02.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-25 13:16:04.397 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 13:16:04.413 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 13:16:04.413 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 13:16:04.413 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 13:16:04.950 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 13:16:04.950 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4811 ms
2025-06-25 13:16:05.095 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 13:16:05.393 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 13:16:06.750 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 13:16:07.076 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 13:16:07.561 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 13:16:08.062 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 13:16:08.728 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 13:16:08.757 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:16:12.396 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 13:16:16.409 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 13:16:16.426 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 13:16:17.671 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.214 seconds (JVM running for 23.793)
2025-06-25 13:16:23.169 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 13:16:23.173 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 13:16:23.186 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-06-25 13:16:23.718 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 13:16:26.853 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 3134ms
2025-06-25 13:16:31.554 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:16:31.558 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:16:31.650 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:16:31.787 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 13:16:31.788 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 13:16:31.788 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 13:16:31.788 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 13:16:31.788 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 13:16:31.789 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 13:16:31.789 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 13:16:31.790 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 13:16:31.790 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 13:16:31.790 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 13:16:31.791 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 13:16:31.792 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 233ms
2025-06-25 13:16:31.792 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 237ms
2025-06-25 13:16:39.518 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:16:39.519 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:16:39.584 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:16:39.647 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 13:16:39.647 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 13:16:39.647 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 13:16:39.648 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 13:16:39.649 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 13:16:39.649 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 13:16:39.649 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 130ms
2025-06-25 13:16:39.649 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 130ms
2025-06-25 13:20:17.125 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:20:17.126 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:20:17.278 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:20:17.345 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 9
2025-06-25 13:20:17.347 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 13:20:17.347 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 13:20:17.347 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 13:20:17.348 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 13:20:17.348 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 13:20:17.348 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 13:20:17.348 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 13:20:17.349 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 13:20:17.349 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 13:20:17.349 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 9, 按钮权限数量: 0
2025-06-25 13:20:17.349 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 9, 按钮数量: 0, 耗时: 223ms
2025-06-25 13:20:17.349 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 9, 按钮数量: 0, 耗时: 223ms
2025-06-25 13:44:52.043 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:44:52.055 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 13:45:13.840 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 13:45:13.861 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 29232 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 13:45:13.883 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 13:45:16.608 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:45:16.612 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:45:16.652 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 13:45:16.659 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:45:16.660 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:45:16.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 13:45:16.689 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:45:16.689 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 13:45:16.713 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-06-25 13:45:16.737 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:45:16.739 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 13:45:16.769 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-25 13:45:17.973 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 13:45:17.983 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 13:45:17.984 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 13:45:17.985 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 13:45:18.410 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 13:45:18.410 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4382 ms
2025-06-25 13:45:18.580 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 13:45:19.011 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 13:45:19.859 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 13:45:19.946 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 13:45:20.148 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 13:45:20.354 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 13:45:20.757 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 13:45:20.780 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:45:24.383 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 13:45:27.792 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 13:45:27.807 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 13:45:29.040 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.582 seconds (JVM running for 20.81)
2025-06-25 13:47:33.465 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 13:47:33.466 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 13:47:33.468 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-25 13:47:34.374 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-25 13:47:34.375 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1936703912811827200, 角色名称: 管理员, 是否停用: false
2025-06-25 13:47:34.780 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功，角色ID: 1936703912811827200
2025-06-25 13:47:34.831 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 10
2025-06-25 13:47:34.880 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378357923840, 结果: 1
2025-06-25 13:47:34.931 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378471170048, 结果: 1
2025-06-25 13:47:34.980 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378584416256, 结果: 1
2025-06-25 13:47:35.030 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378697662464, 结果: 1
2025-06-25 13:47:35.090 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378810908672, 结果: 1
2025-06-25 13:47:35.140 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749378924154880, 结果: 1
2025-06-25 13:47:35.190 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749379037401088, 结果: 1
2025-06-25 13:47:35.246 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749379150647296, 结果: 1
2025-06-25 13:47:35.305 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749379263893504, 结果: 1
2025-06-25 13:47:35.356 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749379372945408, 结果: 1
2025-06-25 13:47:35.408 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 6
2025-06-25 13:47:35.465 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749379490385920, 结果: 1
2025-06-25 13:47:35.515 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749379603632128, 结果: 1
2025-06-25 13:47:35.565 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749379716878336, 结果: 1
2025-06-25 13:47:35.616 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749379830124544, 结果: 1
2025-06-25 13:47:35.665 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749379943370752, 结果: 1
2025-06-25 13:47:35.722 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749380056616960, 结果: 1
2025-06-25 13:47:35.722 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1936703912811827200
2025-06-25 13:47:36.276 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限重新保存完成，数量: 10
2025-06-25 13:47:36.647 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限重新保存完成，数量: 6
2025-06-25 13:47:36.648 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色修改完成，角色ID: 1936703912811827200, 菜单权限数: 10, 数据权限数: 6
2025-06-25 13:47:50.363 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:47:50.369 [http-nio-8285-exec-3] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：用户未登录或Token已过期
2025-06-25 13:47:55.775 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 13:47:58.159 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2384ms
2025-06-25 13:48:00.525 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:48:00.527 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:48:00.597 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:51:21.717 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:51:21.725 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 13:51:32.025 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 26200 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 13:51:32.028 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 13:51:32.046 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 13:51:34.818 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:51:34.821 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:51:34.855 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 13:51:34.863 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:51:34.865 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 13:51:34.885 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 13:51:34.904 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:51:34.905 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 13:51:34.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-25 13:51:34.974 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 13:51:34.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 13:51:35.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-25 13:51:36.124 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 13:51:36.138 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 13:51:36.140 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 13:51:36.141 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 13:51:36.532 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 13:51:36.532 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4385 ms
2025-06-25 13:51:36.779 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 13:51:37.190 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 13:51:37.973 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 13:51:38.052 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 13:51:38.237 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 13:51:38.481 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 13:51:39.011 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 13:51:39.035 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:51:43.706 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 13:51:46.227 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 13:51:46.245 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 13:51:47.383 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.588 seconds (JVM running for 19.528)
2025-06-25 13:52:01.020 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 13:52:01.020 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 13:52:01.026 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-25 13:52:01.256 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:52:01.272 [http-nio-8285-exec-1] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：用户未登录或Token已过期
2025-06-25 13:52:06.743 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 13:52:08.962 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2219ms
2025-06-25 13:52:10.942 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 13:52:10.944 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 13:52:11.012 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 13:52:11.098 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 13:52:11.098 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null
2025-06-25 13:52:11.099 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 13:52:11.099 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 13:52:11.099 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 13:52:11.099 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 13:52:11.100 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 13:52:11.100 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 13:52:11.100 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 13:52:11.101 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 13:52:11.102 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 13:52:11.103 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 13:52:11.105 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 160ms
2025-06-25 13:52:11.105 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 163ms
2025-06-25 13:59:50.264 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 13:59:50.272 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:00:04.365 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 34908 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:00:04.369 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:00:04.408 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:00:07.448 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:00:07.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:00:07.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:00:07.510 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:00:07.511 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:00:07.536 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:00:07.554 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:00:07.554 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:00:07.587 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
2025-06-25 14:00:07.621 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:00:07.623 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:00:07.667 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-25 14:00:09.041 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:00:09.060 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:00:09.061 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:00:09.062 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:00:09.413 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:00:09.414 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4961 ms
2025-06-25 14:00:09.562 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:00:09.779 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:00:10.589 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:00:10.668 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:00:10.875 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:00:11.061 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:00:11.528 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:00:11.552 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:00:15.943 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:00:18.691 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:00:18.715 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:00:20.123 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.032 seconds (JVM running for 20.148)
2025-06-25 14:00:31.105 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:00:31.106 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:00:31.111 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-25 14:00:31.614 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:00:34.558 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2942ms
2025-06-25 14:00:37.746 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:00:37.749 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:00:37.818 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:00:37.928 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:00:37.930 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null
2025-06-25 14:00:37.931 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 14:00:37.931 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 14:00:37.932 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 14:00:37.932 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 14:00:37.932 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 14:00:37.932 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 14:00:37.933 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 14:00:37.933 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 14:00:37.933 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 14:00:37.934 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:00:37.935 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 185ms
2025-06-25 14:00:37.935 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 189ms
2025-06-25 14:00:44.417 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:00:44.420 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:00:44.483 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:00:44.546 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:00:44.546 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null
2025-06-25 14:00:44.546 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 14:00:44.546 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 14:00:44.547 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 14:00:44.547 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 14:00:44.547 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 14:00:44.547 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 14:00:44.547 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 14:00:44.548 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 14:00:44.548 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 14:00:44.548 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:00:44.548 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 128ms
2025-06-25 14:00:44.548 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 129ms
2025-06-25 14:00:46.836 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:00:46.837 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:00:46.903 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:00:46.965 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:00:46.966 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null
2025-06-25 14:00:46.966 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 14:00:46.966 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 14:00:46.966 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 14:00:46.967 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 14:00:46.970 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 14:00:46.970 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 14:00:46.970 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 14:00:46.971 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 14:00:46.971 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 14:00:46.971 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:00:46.971 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 134ms
2025-06-25 14:00:46.972 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 134ms
2025-06-25 14:08:32.512 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:08:32.518 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:08:44.162 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 35648 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:08:44.164 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:08:44.179 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:08:46.661 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:08:46.664 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:08:46.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:08:46.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:08:46.703 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:08:46.725 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:08:46.734 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:08:46.734 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:08:46.760 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-25 14:08:46.781 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:08:46.783 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:08:46.810 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-25 14:08:47.911 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:08:47.928 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:08:47.929 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:08:47.929 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:08:48.323 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:08:48.324 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4046 ms
2025-06-25 14:08:48.547 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:08:49.006 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:08:49.874 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:08:49.942 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:08:50.114 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:08:50.351 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:08:50.745 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:08:50.767 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:08:54.746 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:08:57.724 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:08:57.742 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:08:59.286 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.179 seconds (JVM running for 19.442)
2025-06-25 14:09:08.382 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:09:08.382 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:09:08.386 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-25 14:09:09.261 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:09:11.942 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2668ms
2025-06-25 14:09:19.151 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:09:19.155 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:09:19.229 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:09:19.337 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:09:19.338 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null
2025-06-25 14:09:19.338 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null
2025-06-25 14:09:19.338 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null
2025-06-25 14:09:19.338 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null
2025-06-25 14:09:19.339 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null
2025-06-25 14:09:19.340 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:09:19.341 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 185ms
2025-06-25 14:09:19.341 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 189ms
2025-06-25 14:12:57.763 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:12:57.768 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:13:09.748 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 36680 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:13:09.750 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:13:09.762 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:13:11.567 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:13:11.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:13:11.618 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:13:11.624 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:13:11.625 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:13:11.648 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:13:11.670 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:13:11.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:13:11.706 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 JPA repository interfaces.
2025-06-25 14:13:11.746 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:13:11.750 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:13:11.789 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-25 14:13:12.807 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:13:12.823 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:13:12.824 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:13:12.824 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:13:13.092 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:13:13.092 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3245 ms
2025-06-25 14:13:13.186 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:13:13.475 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:13:14.381 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:13:14.567 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:13:14.823 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:13:15.041 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:13:15.437 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:13:15.454 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:13:18.437 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:13:20.847 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:13:20.867 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:13:22.318 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.742 seconds (JVM running for 16.741)
2025-06-25 14:13:33.277 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:13:33.279 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:13:33.294 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-06-25 14:13:33.929 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:13:36.139 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2207ms
2025-06-25 14:13:39.414 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:13:39.416 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:13:39.478 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:13:39.543 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:13:39.614 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.614 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.671 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.672 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.735 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.736 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.798 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.798 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.864 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.864 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.922 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.922 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:39.982 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:39.983 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:40.038 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:40.038 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:40.093 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:40.094 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:40.151 [http-nio-8285-exec-1] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 查询父菜单名称失败，parentId: null
2025-06-25 14:13:40.151 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null, routeAddress=null
2025-06-25 14:13:40.152 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:13:40.152 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 735ms
2025-06-25 14:13:40.153 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 739ms
2025-06-25 14:17:55.852 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:17:55.861 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:18:10.436 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 988 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:18:10.439 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:18:10.472 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:18:13.200 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:18:13.204 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:18:13.248 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:18:13.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:18:13.256 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:18:13.282 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:18:13.299 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:18:13.299 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:18:13.329 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-25 14:18:13.369 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:18:13.372 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:18:13.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-25 14:18:14.730 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:18:14.748 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:18:14.749 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:18:14.749 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:18:15.150 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:18:15.150 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4617 ms
2025-06-25 14:18:15.307 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:18:15.616 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:18:16.427 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:18:16.516 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:18:16.845 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:18:17.169 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:18:17.655 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:18:17.710 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:18:21.813 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:18:25.654 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:18:25.682 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:18:26.726 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.444 seconds (JVM running for 21.092)
2025-06-25 14:19:00.939 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:19:00.941 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:19:00.949 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-25 14:19:01.593 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:19:03.629 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2032ms
2025-06-25 14:19:06.653 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:19:06.655 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:19:06.724 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:19:06.790 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:19:06.851 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=system_config, pre_id=9001, menu_type=2, name=系统配置, id=9002, component_path=system/config/index, route_address=/system/config, order_info=1}
2025-06-25 14:19:06.914 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=system/config/index, permissionidentifier=system_config, preid=9001, name=系统配置, menutype=2, id=9002, routeaddress=/system/config, orderinfo=1}
2025-06-25 14:19:06.915 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:06.980 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=system_config_test, pre_id=9002, menu_type=2, name=测试, id=1937730143049093120, component_path=system/config/index, route_address=/system/config/test, order_info=1}
2025-06-25 14:19:07.050 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=system/config/index, permissionidentifier=system_config_test, preid=9002, name=测试, menutype=2, id=1937730143049093120, routeaddress=/system/config/test, orderinfo=1}
2025-06-25 14:19:07.051 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.114 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=system_log_view, pre_id=9001, menu_type=2, name=系统日志, id=9003, component_path=system/log/index, route_address=/system/log, order_info=2, route_param=oo}
2025-06-25 14:19:07.174 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=system/log/index, permissionidentifier=system_log_view, preid=9001, name=系统日志, menutype=2, routeparam=oo, id=9003, routeaddress=/system/log, orderinfo=2}
2025-06-25 14:19:07.175 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.238 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=user_list_view, pre_id=9006, menu_type=2, name=用户列表, id=9007, component_path=user/list/index, route_address=/user/list, order_info=1}
2025-06-25 14:19:07.294 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=user/list/index, permissionidentifier=user_list_view, preid=9006, name=用户列表, menutype=2, id=9007, routeaddress=/user/list, orderinfo=1}
2025-06-25 14:19:07.294 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.354 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=user:dept, pre_id=9006, menu_type=2, name=部门管理, id=9008, component_path=user/dept/index, route_address=/user/dept, order_info=2}
2025-06-25 14:19:07.415 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=user/dept/index, permissionidentifier=user:dept, preid=9006, name=部门管理, menutype=2, id=9008, routeaddress=/user/dept, orderinfo=2}
2025-06-25 14:19:07.415 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.469 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=permission_role_view, pre_id=9012, menu_type=2, name=角色管理, id=9013, component_path=permission/role/index, route_address=/permission/role, order_info=1}
2025-06-25 14:19:07.523 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=permission/role/index, permissionidentifier=permission_role_view, preid=9012, name=角色管理, menutype=2, id=9013, routeaddress=/permission/role, orderinfo=1}
2025-06-25 14:19:07.523 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.574 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=permission_menu_view, pre_id=9012, menu_type=2, name=菜单管理, id=9014, component_path=permission/menu/index, route_address=/permission/menu, order_info=2}
2025-06-25 14:19:07.634 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=permission/menu/index, permissionidentifier=permission_menu_view, preid=9012, name=菜单管理, menutype=2, id=9014, routeaddress=/permission/menu, orderinfo=2}
2025-06-25 14:19:07.635 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.694 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=permission:data, pre_id=9012, menu_type=2, name=数据权限, id=9015, component_path=permission/data/index, route_address=/permission/data, order_info=3}
2025-06-25 14:19:07.754 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=permission/data/index, permissionidentifier=permission:data, preid=9012, name=数据权限, menutype=2, id=9015, routeaddress=/permission/data, orderinfo=3}
2025-06-25 14:19:07.754 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.808 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=dev:generator, pre_id=9016, menu_type=2, name=代码生成, id=9017, component_path=dev/generator/index, route_address=/dev/generator, order_info=1}
2025-06-25 14:19:07.864 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=dev/generator/index, permissionidentifier=dev:generator, preid=9016, name=代码生成, menutype=2, id=9017, routeaddress=/dev/generator, orderinfo=1}
2025-06-25 14:19:07.864 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.919 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 原始查询结果: {permission_identifier=dev:swagger, pre_id=9016, menu_type=2, name=API文档, id=9018, component_path=dev/swagger/index, route_address=/dev/swagger, order_info=2}
2025-06-25 14:19:07.978 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 格式化查询结果: {componentpath=dev/swagger/index, permissionidentifier=dev:swagger, preid=9016, name=API文档, menutype=2, id=9018, routeaddress=/dev/swagger, orderinfo=2}
2025-06-25 14:19:07.978 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=null, menuType=null, routeAddress=null
2025-06-25 14:19:07.978 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:19:07.979 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 10, 按钮数量: 0, 耗时: 1323ms
2025-06-25 14:19:07.980 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 10, 按钮数量: 0, 耗时: 1327ms
2025-06-25 14:24:47.948 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:24:47.956 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:25:00.100 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 19536 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:25:00.102 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:25:00.134 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:25:02.187 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:25:02.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:25:02.222 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:25:02.229 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:25:02.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:25:02.244 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:25:02.254 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:25:02.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:25:02.279 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-25 14:25:02.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:25:02.307 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:25:02.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-25 14:25:03.412 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:25:03.423 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:25:03.424 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:25:03.424 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:25:03.714 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:25:03.714 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3514 ms
2025-06-25 14:25:03.841 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:25:04.065 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:25:04.818 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:25:04.885 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:25:05.090 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:25:05.280 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:25:05.667 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:25:05.685 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:25:09.434 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:25:12.378 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:25:12.406 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:25:14.211 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.257 seconds (JVM running for 18.379)
2025-06-25 14:25:20.613 [http-nio-8285-exec-4] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:25:20.614 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:25:20.626 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2025-06-25 14:25:21.494 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:25:23.882 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2386ms
2025-06-25 14:25:26.949 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:25:26.952 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:25:27.018 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:25:27.117 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:25:27.117 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 14:25:27.118 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 14:25:27.118 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 14:25:27.118 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 14:25:27.118 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 14:25:27.119 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 14:25:27.119 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 14:25:27.119 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 14:25:27.120 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 14:25:27.120 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 14:25:27.120 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:25:27.121 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 0, 按钮数量: 0, 耗时: 168ms
2025-06-25 14:25:27.121 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 0, 按钮数量: 0, 耗时: 171ms
2025-06-25 14:26:08.517 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:26:08.518 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:26:08.597 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 10
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 14:26:08.660 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 14:26:08.661 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 14:26:08.661 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 14:26:08.661 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 14:26:08.661 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 14:26:08.661 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 14:26:08.662 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 10, 按钮权限数量: 0
2025-06-25 14:26:08.662 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 0, 按钮数量: 0, 耗时: 144ms
2025-06-25 14:26:08.662 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 0, 按钮数量: 0, 耗时: 144ms
2025-06-25 14:48:55.593 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:48:55.621 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 14:49:17.718 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 17384 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 14:49:17.719 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 14:49:17.747 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 14:49:21.053 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:49:21.083 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:49:21.239 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 120 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 14:49:21.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:49:21.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 14:49:21.295 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 14:49:21.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:49:21.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 14:49:21.359 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 JPA repository interfaces.
2025-06-25 14:49:21.401 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 14:49:21.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 14:49:21.487 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Redis repository interfaces.
2025-06-25 14:49:23.316 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 14:49:23.349 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 14:49:23.350 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 14:49:23.350 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 14:49:23.799 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 14:49:23.799 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5949 ms
2025-06-25 14:49:23.987 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 14:49:24.416 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 14:49:25.767 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 14:49:26.047 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 14:49:26.910 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 14:49:27.353 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 14:49:28.012 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 14:49:28.082 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 14:49:35.467 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 14:49:38.525 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 14:49:38.548 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 14:49:40.136 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 23.812 seconds (JVM running for 27.931)
2025-06-25 14:49:53.643 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-25 14:49:53.644 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-25 14:49:53.645 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-25 14:49:54.011 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-25 14:49:56.598 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 2584ms
2025-06-25 14:49:58.675 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:49:58.679 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:49:58.789 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:49:58.883 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 14
2025-06-25 14:49:58.884 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 14:49:58.884 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 14:49:58.885 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 14:49:58.886 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 14:49:58.886 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 14:49:58.886 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 14:49:58.886 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 14:49:58.886 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 14:49:58.887 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 14:49:58.887 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 14:49:58.887 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 14:49:58.887 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 14:49:58.887 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 14:49:58.888 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 14:49:58.889 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 0
2025-06-25 14:49:58.889 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 0, 耗时: 209ms
2025-06-25 14:49:58.889 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 0, 耗时: 213ms
2025-06-25 14:55:22.811 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-25 14:55:22.812 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1936703912811827200, 角色名称: 管理员, 是否停用: false
2025-06-25 14:55:23.168 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功，角色ID: 1936703912811827200
2025-06-25 14:55:23.223 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 10
2025-06-25 14:55:23.277 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749494217183232, 结果: 1
2025-06-25 14:55:23.330 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749494477230080, 结果: 1
2025-06-25 14:55:23.380 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749494707916800, 结果: 1
2025-06-25 14:55:23.430 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749494942797824, 结果: 1
2025-06-25 14:55:23.480 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749495173484544, 结果: 1
2025-06-25 14:55:23.530 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749495399976960, 结果: 1
2025-06-25 14:55:23.586 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749495630663680, 结果: 1
2025-06-25 14:55:23.635 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749495844573184, 结果: 1
2025-06-25 14:55:23.685 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749496071065600, 结果: 1
2025-06-25 14:55:23.734 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1937749496301752320, 结果: 1
2025-06-25 14:55:23.788 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 6
2025-06-25 14:55:23.841 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749496536633344, 结果: 1
2025-06-25 14:55:23.890 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749496826040320, 结果: 1
2025-06-25 14:55:23.941 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749497102864384, 结果: 1
2025-06-25 14:55:23.990 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749497354522624, 结果: 1
2025-06-25 14:55:24.046 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749497606180864, 结果: 1
2025-06-25 14:55:24.101 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1937749497832673280, 结果: 1
2025-06-25 14:55:24.101 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1936703912811827200
2025-06-25 14:55:24.650 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限重新保存完成，数量: 10
2025-06-25 14:55:24.991 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限重新保存完成，数量: 6
2025-06-25 14:55:24.992 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色修改完成，角色ID: 1936703912811827200, 菜单权限数: 10, 数据权限数: 6
2025-06-25 14:55:33.755 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 14:55:33.755 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 14:55:33.818 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 14:55:33.882 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 14
2025-06-25 14:55:33.882 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 14:55:33.882 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 14:55:33.882 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 14:55:33.883 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 14:55:33.884 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 14:55:33.884 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 14:55:33.884 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 14:55:33.884 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 14:55:33.923 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 14:55:33.924 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 0
2025-06-25 14:55:33.924 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 0, 耗时: 168ms
2025-06-25 14:55:33.924 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 0, 耗时: 169ms
2025-06-25 15:09:36.319 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 15:09:36.320 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 15:09:36.433 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 15:09:36.493 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 14
2025-06-25 15:09:36.493 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 15:09:36.493 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 15:09:36.493 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 15:09:36.493 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 15:09:36.494 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 0
2025-06-25 15:09:36.495 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 0, 耗时: 175ms
2025-06-25 15:09:36.496 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 0, 耗时: 176ms
2025-06-25 15:09:40.566 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 15:09:40.567 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 15:09:40.697 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 15:09:40.754 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 14
2025-06-25 15:09:40.754 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 15:09:40.754 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 15:09:40.754 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 15:09:40.755 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 15:09:40.756 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 15:09:40.756 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 0
2025-06-25 15:09:40.756 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 0, 耗时: 189ms
2025-06-25 15:09:40.756 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 0, 耗时: 189ms
2025-06-25 15:11:21.851 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 15:11:21.851 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 15:11:21.962 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 15:11:22.027 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 14
2025-06-25 15:11:22.027 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 15:11:22.027 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 15:11:22.028 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 15:11:22.029 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 0
2025-06-25 15:11:22.029 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 0, 耗时: 178ms
2025-06-25 15:11:22.029 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 0, 耗时: 178ms
2025-06-25 15:30:06.395 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 15:30:06.396 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 15:30:06.538 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 15:30:06.608 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 19
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9004, name=配置查看, preId=9002, menuType=3, routeAddress=
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9009, name=用户新增, preId=9007, menuType=3, routeAddress=
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9005, name=配置编辑, preId=9002, menuType=3, routeAddress=
2025-06-25 15:30:06.609 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9010, name=用户编辑, preId=9007, menuType=3, routeAddress=
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9011, name=用户删除, preId=9007, menuType=3, routeAddress=
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 15:30:06.610 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 5
2025-06-25 15:30:06.611 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 5, 耗时: 215ms
2025-06-25 15:30:06.611 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 5, 耗时: 215ms
2025-06-25 15:33:01.323 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: null
2025-06-25 15:33:01.324 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: null
2025-06-25 15:33:01.430 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 19
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937730143049093120, name=测试, preId=9002, menuType=2, routeAddress=/system/config/test
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9001, name=系统管理, preId=0, menuType=1, routeAddress=/system
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9002, name=系统配置, preId=9001, menuType=2, routeAddress=/system/config
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9004, name=配置查看, preId=9002, menuType=3, routeAddress=
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9006, name=用户管理, preId=0, menuType=1, routeAddress=/user
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9007, name=用户列表, preId=9006, menuType=2, routeAddress=/user/list
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9009, name=用户新增, preId=9007, menuType=3, routeAddress=
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9012, name=权限管理, preId=0, menuType=1, routeAddress=/permission
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9013, name=角色管理, preId=9012, menuType=2, routeAddress=/permission/role
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9016, name=开发工具, preId=0, menuType=1, routeAddress=/dev
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9017, name=代码生成, preId=9016, menuType=2, routeAddress=/dev/generator
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9003, name=系统日志, preId=9001, menuType=2, routeAddress=/system/log
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9005, name=配置编辑, preId=9002, menuType=3, routeAddress=
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9008, name=部门管理, preId=9006, menuType=2, routeAddress=/user/dept
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9010, name=用户编辑, preId=9007, menuType=3, routeAddress=
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9014, name=菜单管理, preId=9012, menuType=2, routeAddress=/permission/menu
2025-06-25 15:33:01.489 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9018, name=API文档, preId=9016, menuType=2, routeAddress=/dev/swagger
2025-06-25 15:33:01.490 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9011, name=用户删除, preId=9007, menuType=3, routeAddress=
2025-06-25 15:33:01.490 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=9015, name=数据权限, preId=9012, menuType=2, routeAddress=/permission/data
2025-06-25 15:33:01.490 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 14, 按钮权限数量: 5
2025-06-25 15:33:01.490 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 4, 按钮数量: 5, 耗时: 166ms
2025-06-25 15:33:01.490 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 4, 按钮数量: 5, 耗时: 166ms
2025-06-25 17:02:53.379 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 17:02:53.726 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-25 17:03:55.699 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 22476 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-25 17:03:55.701 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-25 17:03:55.752 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-25 17:03:58.804 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 17:03:58.807 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-25 17:03:58.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Elasticsearch repository interfaces.
2025-06-25 17:03:58.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 17:03:58.861 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-25 17:03:58.883 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-25 17:03:58.905 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 17:03:58.910 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-25 17:03:58.944 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-25 17:03:58.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-25 17:03:58.990 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-25 17:03:59.043 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-25 17:04:00.848 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-25 17:04:00.865 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-25 17:04:00.866 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-25 17:04:00.866 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-25 17:04:01.357 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-25 17:04:01.358 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5485 ms
2025-06-25 17:04:01.514 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-25 17:04:01.873 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-25 17:04:02.873 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-25 17:04:03.048 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-25 17:04:03.572 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-25 17:04:04.086 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-25 17:04:04.605 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-25 17:04:04.628 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-25 17:04:08.943 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-25 17:04:13.822 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-25 17:04:13.840 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-25 17:04:15.171 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 21.617 seconds (JVM running for 28.064)
