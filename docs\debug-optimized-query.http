### 调试优化查询结果
### 查看优化查询返回的数据量和内容

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试优化查询（查看日志中的数据条数）
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 2. 如果数据条数为0，直接测试数据库查询
### 请在数据库中执行以下SQL：

/*
-- 测试优化查询的SQL
SELECT 
    o.id AS orgId, 
    o.organ_name AS orgName, 
    o.pre_id AS parentId, 
    o.order_info AS orgOrder, 
    u.id AS userId, 
    u.user_name AS userName 
FROM t_org_structure o 
LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false 
WHERE o.is_del = false 
ORDER BY o.order_info, u.user_name;

-- 预期结果：应该返回部门和用户的关联数据
-- 每个部门可能有多行（如果有多个用户）
-- 没有用户的部门也会有一行（userId和userName为NULL）

-- 如果上面查询返回空，分别测试：

-- 1. 测试部门表
SELECT id, organ_name, pre_id, is_del FROM t_org_structure WHERE is_del = false;

-- 2. 测试用户表  
SELECT id, user_name, organ_affiliation, is_del FROM t_user WHERE is_del = false;

-- 3. 测试关联关系
SELECT 
    u.id as user_id,
    u.user_name,
    u.organ_affiliation,
    o.id as org_id,
    o.organ_name
FROM t_user u
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
WHERE u.is_del = false AND o.is_del = false;
*/

### 3. 根据日志结果分析问题：

### 情况1：如果日志显示"优化查询返回数据条数: 0"
### 说明SQL查询本身有问题，需要检查：
### - 表名是否正确
### - 字段名是否正确  
### - 数据是否存在
### - 连接条件是否正确

### 情况2：如果日志显示有数据条数，但最终返回空
### 说明数据处理逻辑有问题，需要检查：
### - 数据类型转换
### - 空值处理
### - 树形结构构建逻辑

### 情况3：如果有警告"发现部门ID为空的记录"
### 说明查询结果中有空数据，已经通过orgId != null修复

### 预期的日志输出：
### "优化查询返回数据条数: XX"（应该大于0）
### "获取所有用户完成，耗时: XXXms"
### 不应该有"发现部门ID为空的记录"警告

### 如果还是返回空数据，可能需要：
### 1. 检查数据库连接
### 2. 检查表结构是否匹配
### 3. 检查数据是否正确插入
### 4. 回退到原始查询方法进行对比
