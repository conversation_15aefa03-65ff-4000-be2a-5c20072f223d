package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 按部门查询用户列表响应VO类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListByOrgResponseVO", description = "按部门查询用户列表响应")
public class UserListByOrgResponseVO {
    
    @ApiModelProperty(value = "用户列表")
    private List<UserListByOrgItemVO> records;
    
    @ApiModelProperty(value = "总记录数")
    private Long total;
    
    @ApiModelProperty(value = "当前页码")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
    
    @ApiModelProperty(value = "总页数")
    private Integer totalPages;
    
    @ApiModelProperty(value = "是否有上一页")
    private Boolean hasPrevious;
    
    @ApiModelProperty(value = "是否有下一页")
    private Boolean hasNext;
}
