package com.dfit.orgsync.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 层级解析工具
 * 用于解析组织架构的层级结构
 */
@Component
@Slf4j
public class HierarchyParser {
    
    /**
     * 一级部门列表（根据用户提供的准确列表）
     */
    private static final Set<String> LEVEL_1_DEPARTMENTS = Set.of(
        "新材料科研究院（合署）", "公司办公室", "人力资源部", "企业文化部", "财务部",
        "党委办公室", "组织部", "党委工作部", "审计部", "集团领导", "风险合规部",
        "安全环保部", "纪委功公室（党风廉政办公室）", "集团战略发展部", 
        "江苏金珂水务有限公司", "工会", "印尼焦化项目部", "特钢事业部",
        "数字应用研究院（人工智能研完院）", "南京三金房地产开发有限公司",
        "南钢退休职工服务中心", "集国资产处置办公室", "江苏金贸钢宝电子商务有限公司2",
        "团委", "公司领导", "离京鑫智链科技信息有限公司2", "科技质量部",
        "数字应用研究院", "蔚蓝高科技集团", "战略运营部（产业发展研究院）",
        "物流中心", "能源动力事业部", "炼铁事业部", "保卫部", "新产业投资集团",
        "采购中心", "制造部", "板材事业部", "市场部", "江苏金凯节能环保投资控股有限公司",
        "印尼钢铁项目指挥部", "江苏南钢鑫洋供应链有限公司", "集团宿迁金鑫公司",
        "南京金智工程技术有限公司", "集团综合资产部", "证券部", "香港金腾公司",
        "南京钢铁集团国际经济贸易有限公司", "集团工会", "集团财务审计部",
        "集团宿迁金鑫靖江项目指挥部", "江苏金恒信息科技股份有限公司"
    );
    
    /**
     * 层级关键词（按优先级排序）
     * 用于识别组织层级的分界点
     */
    private static final List<String> HIERARCHY_KEYWORDS = Arrays.asList(
        "事业部", "集团", "公司", "中心", "厂", "车间", "部", "科", "室", "院", "会", "委", "班", "组", "队", "站"
    );
    
    /**
     * 解析组织架构层级
     * 
     * @param fullName 完整组织名称
     * @return 层级列表，从一级到最后一级
     */
    public List<String> parseHierarchy(String fullName) {
        if (fullName == null || fullName.trim().isEmpty()) {
            log.warn("fullName为空，无法解析层级");
            return Collections.emptyList();
        }
        
        log.debug("解析层级结构: {}", fullName);
        
        // 检查是否为一级部门
        if (LEVEL_1_DEPARTMENTS.contains(fullName)) {
            log.debug("识别为一级部门: {}", fullName);
            return Collections.singletonList(fullName);
        }
        
        // 多级部门解析
        List<String> levels = new ArrayList<>();
        String remaining = fullName;
        
        // 从左到右逐级解析
        for (String keyword : HIERARCHY_KEYWORDS) {
            int pos = remaining.indexOf(keyword);
            if (pos > 0) { // 关键词不能在开头
                String levelName = remaining.substring(0, pos + keyword.length());
                levels.add(levelName);
                remaining = remaining.substring(pos + keyword.length());
                
                log.debug("找到层级: {} -> 剩余: {}", levelName, remaining);
                
                // 如果剩余部分为空，跳出循环
                if (remaining.isEmpty()) {
                    break;
                }
            }
        }
        
        // 如果还有剩余部分，或者没有解析出任何层级，最后一级使用完整名称
        if (!remaining.isEmpty() || levels.isEmpty()) {
            levels.add(fullName);
            log.debug("添加最后一级（完整名称）: {}", fullName);
        }
        
        log.debug("解析结果: {} -> {}", fullName, levels);
        return levels;
    }
    
    /**
     * 检查是否为一级部门
     * 
     * @param fullName 完整组织名称
     * @return true表示是一级部门
     */
    public boolean isLevel1Department(String fullName) {
        return LEVEL_1_DEPARTMENTS.contains(fullName);
    }
    
    /**
     * 获取一级部门列表
     * 
     * @return 一级部门集合
     */
    public Set<String> getLevel1Departments() {
        return new HashSet<>(LEVEL_1_DEPARTMENTS);
    }
    
    /**
     * 统计层级信息
     * 
     * @param fullNames 完整名称列表
     * @return 层级统计信息
     */
    public Map<Integer, Integer> getHierarchyStats(List<String> fullNames) {
        Map<Integer, Integer> stats = new HashMap<>();
        
        for (String fullName : fullNames) {
            List<String> hierarchy = parseHierarchy(fullName);
            int level = hierarchy.size();
            stats.put(level, stats.getOrDefault(level, 0) + 1);
        }
        
        return stats;
    }
}
