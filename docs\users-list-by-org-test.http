### 按部门查询用户列表接口测试

### 1. 查询指定部门的用户（不包含子部门）
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "orgId": 1002,
  "includeSubOrgs": false
}

### 2. 查询指定部门的用户（包含子部门）
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "orgId": 1002,
  "includeSubOrgs": true
}

### 3. 按部门查询用户并按用户名筛选
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "orgId": 1002,
  "includeSubOrgs": true,
  "userName": "张"
}

### 4. 按部门查询用户并按状态筛选（正常用户）
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "orgId": 1002,
  "includeSubOrgs": true,
  "status": false
}

### 5. 按部门查询用户并按状态筛选（禁用用户）
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "orgId": 1002,
  "includeSubOrgs": true,
  "status": true
}

### 6. 组合条件查询
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 5,
  "orgId": 1001,
  "includeSubOrgs": true,
  "userName": "李",
  "status": false
}

### 7. 分页测试（第2页）
POST http://localhost:8080/users/list-by-org
Content-Type: application/json

{
  "pageNum": 2,
  "pageSize": 5,
  "orgId": 1001,
  "includeSubOrgs": true
}
