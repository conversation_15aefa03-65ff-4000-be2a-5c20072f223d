### 🧪 接口测试用例（基于实际测试数据）
### 每个模块提供一个代表性的测试用例

### 变量定义
@baseUrl = http://localhost:8080

### =====================================================
### 👥 用户管理模块测试用例
### =====================================================

### 1. 获取所有用户（组织架构树）
POST {{baseUrl}}/users/getALLUsers
Content-Type: application/json

### 1.1 获取所有用户（组织架构树）- 优化版本
### 🚀 使用 RECURSIVE CTE 查询，性能更佳，功能完全相同
POST {{baseUrl}}/users/getAllUsersOptimized
Content-Type: application/json

### 2. 分页查询用户列表
POST {{baseUrl}}/users/getUserList
Content-Type: application/json

{
  "currentPage": 1,
  "pageSize": 10,
  "userName": "张",
  "userState": false,
  "startTime": "",
  "endTime": ""
}

### 3. 获取用户详情
POST {{baseUrl}}/users/getUserDetail
Content-Type: application/json

{
  "id": "6001"
}

### 4. 新增用户
POST {{baseUrl}}/users/addMembers
Content-Type: application/json

[
  {
    "userId": 9001,
    "userName": "测试用户001",
    "isDisable": false,
    "roles": [
      {
        "roleId": 7001,
        "roleName": "系统管理员"
      }
    ]
  }
]
[
  {
    "userId": 5,
    "userName": "王小明",
    "isDisable": false,
    "roles": [
      {
        "roleId": 2,
        "roleName": "普通用户"
      }
    ]
  }
]

### 5. 更新用户信息
POST {{baseUrl}}/users/updateUser
Content-Type: application/json

{
  "account": "zhang001",
  "isDisable": false,
  "organAffiliation": 5001,
  "roles": [
    {
      "roleId": 7001,
      "roleName": "系统管理员"
    }
  ],
  "userId": 6001,
  "userName": "张总经理"
}
{
  "account": "zhang001",
  "isDisable": false,
  "organAffiliation": 1,
  "roles": [
    {
      "roleId": 0,
      "roleName": "string"
    }
  ],
  "userId": 1,
  "userName": "张三"
}

### 6. 用户角色授权
POST {{baseUrl}}/users/userRole
Content-Type: application/json

{
  "userId": "6001",
  "roleId": "7001",
  "isDel": false
}

### 7. 获取角色用户列表
POST {{baseUrl}}/users/getRoleUserList
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "roleId": 7001,
  "userName": ""
}

### =====================================================
### 🏢 组织架构模块测试用例
### =====================================================

### 8. 获取组织架构树
POST {{baseUrl}}/org-structure/tree
Content-Type: application/json

{
  "excludeOrgId": null,
  "includeDeleted": false,
  "maxLevel": 0
}

### 9. 搜索部门
POST {{baseUrl}}/org-structure/search
Content-Type: application/json

{
  "organName": "技术",
  "preId": null,
  "includeDeleted": false
}

### 10. 新增部门
POST {{baseUrl}}/org-structure/add
Content-Type: application/json

{
  "organName": "测试部门001",
  "preId": 5002,
  "orderInfo": 99,
  "description": "测试用部门"
}

### =====================================================
### 👤 角色管理模块测试用例
### =====================================================

### 11. 获取角色列表
POST {{baseUrl}}/roles/getRoleList
Content-Type: application/json

{
  "currentPage": 1,
  "pageSize": 10,
  "roleName": "管理"
}

### 12. 新增角色
POST {{baseUrl}}/roles/addRole
Content-Type: application/json

{
  "roleName": "测试角色001",
  "isDisable": false,
  "description": "测试用角色"
}

### =====================================================
### 📋 菜单管理模块测试用例
### =====================================================

### 13. 获取模块列表
GET {{baseUrl}}/menus/getModules
Content-Type: application/json

### 14. 查询菜单
POST {{baseUrl}}/menus/getMenus
Content-Type: application/json

{
  "moduleIdentifier": "user_management",
  "name": "",
  "isDisable": false
}

### 15. 新增菜单
POST {{baseUrl}}/menus/addMenu
Content-Type: application/json

{
  "moduleIdentifier": "user_management",
  "name": "测试菜单001",
  "permissionIdentifier": "test_menu_001",
  "componentPath": "/test/menu001",
  "isDisable": false,
  "orderInfo": 99,
  "description": "测试用菜单"
}

### =====================================================
### 🔐 数据权限模块测试用例
### =====================================================

### 16. 查询数据模块列表
POST {{baseUrl}}/data-modules/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "moduleIdentifier": "",
  "name": ""
}

### 17. 查询数据权限列表
POST {{baseUrl}}/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "moduleIdentifier": "user_data_module",
  "name": "",
  "isDisable": false
}

### 18. 新增数据权限
POST {{baseUrl}}/data-permissions/add
Content-Type: application/json

{
  "moduleIdentifier": "user_data_module",
  "name": "测试数据权限001",
  "permissionIdentifier": "test_data_001",
  "isDisable": false,
  "description": "测试用数据权限",
  "operateList": [
    {
      "operateType": "READ",
      "isEnabled": true
    },
    {
      "operateType": "WRITE", 
      "isEnabled": false
    }
  ]
}

### =====================================================
### 📊 预期测试结果
### =====================================================

### ✅ 成功的响应格式：
/*
{
  "code": 200,
  "message": "SUCCESS",
  "data": [...],
  "total": 10  // 分页接口才有此字段
}
*/

### ❌ 如果出现问题：
/*
{
  "code": 500,
  "message": "错误信息",
  "data": null
}
*/

### =====================================================
### 🔍 测试重点关注
### =====================================================

### 1. 响应状态码
### - 应该返回 200 OK
### - 不应该有 404 或 500 错误

### 2. 响应时间
### - /users/getALLUsers: < 5秒（原版本）
### - /users/getAllUsersOptimized: < 2秒（RECURSIVE优化版本）⚡
### - /org-structure/tree: < 3秒
### - 其他接口: < 2秒

### 3. 数据格式
### - code: 200 表示成功
### - message: "SUCCESS"
### - data: 包含实际数据
### - total: 分页接口包含总数

### 4. 业务逻辑
### - 树形结构数据正确嵌套
### - 分页数据正确返回
### - 新增/更新操作成功执行
### - 搜索功能正常工作

### =====================================================
### 🚨 常见问题排查
### =====================================================

### 问题1：404 Not Found
### 原因：接口路径错误
### 解决：检查接口路径是否正确

### 问题2：500 Internal Server Error  
### 原因：业务逻辑异常或数据问题
### 解决：查看后端错误日志

### 问题3：响应时间过长
### 原因：数据库查询性能问题
### 解决：检查是否创建了数据库索引

### 问题4：返回空数据
### 原因：数据库中没有对应数据
### 解决：检查测试数据是否正确插入
