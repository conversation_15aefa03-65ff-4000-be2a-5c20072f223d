package com.dfit.percode.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.exception.NotLoginException;
import com.dfit.percode.common.BaseResult;
import com.dfit.percode.config.SuperAdminConfig;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.service.IUserPermissionService;
import com.dfit.percode.util.CustomJwtUtil;
import com.dfit.percode.util.PasswordUtil;
import com.dfit.percode.vo.LoginRequestVO;
import com.dfit.percode.vo.LoginResponseVO;
import com.dfit.percode.vo.UserDetailResponseVO;
import com.dfit.percode.vo.UserPermissionsResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * 认证控制器
 * 提供用户登录和权限查询功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Api(tags = "认证管理接口")
public class AuthController {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private IUserPermissionService userPermissionService;

    @Autowired
    private CustomJwtUtil customJwtUtil;

    @Autowired
    private SuperAdminConfig superAdminConfig;

    /**
     * 用户登录接口
     * 验证用户名密码，生成JWT Token
     *
     * @param request 登录请求参数
     * @return 登录响应数据，包含Token和用户信息
     */
    @PostMapping("/login")
    @ApiOperation(value = "用户登录", notes = "验证用户名密码，返回JWT Token")
    public BaseResult<LoginResponseVO> login(@Valid @RequestBody LoginRequestVO request) {
        log.info("用户登录请求，账号: {}", request.getUsername());
        long startTime = System.currentTimeMillis();

        try {
            // 第1步：验证用户名和密码（支持MD5加密）
            log.debug("第1步：验证用户名和密码");

            // 1.1：根据账号查询用户信息
            UserDetailResponseVO user = userMapper.findUserByAccount(request.getUsername());
            if (user == null) {
                log.warn("登录失败：用户不存在，账号: {}", request.getUsername());
                return new BaseResult<>(401, "用户名或密码错误", null);
            }

            // 1.2：验证密码（MD5加密比较）
            boolean passwordValid = PasswordUtil.verifyPassword(request.getPassword(), user.getPassword());
            if (!passwordValid) {
                log.warn("登录失败：密码错误，账号: {}", request.getUsername());
                return new BaseResult<>(401, "用户名或密码错误", null);
            }

            log.debug("用户名和密码验证成功，账号: {}", request.getUsername());

            // 第2步：检查用户状态
            log.debug("第2步：检查用户状态");
            if (user.getIsDisable() != null && user.getIsDisable()) {
                log.warn("登录失败：用户已被禁用，账号: {}", request.getUsername());
                return new BaseResult<>(403, "用户已被禁用，请联系管理员", null);
            }

            // 第3步：混合JWT Token生成（Sa-Token会话 + 自定义JWT）
            log.debug("第3步：混合JWT Token生成");

            // 3.1：建立Sa-Token会话（保持Sa-Token功能完整性）
            StpUtil.login(user.getUserId());
            log.debug("Sa-Token会话建立成功，用户ID: {}", user.getUserId());

            // 3.2：生成自定义格式JWT Token（符合领导要求的格式）
            String customToken = customJwtUtil.generateCustomToken(user.getUserId());
            log.debug("自定义JWT Token生成成功，格式: {userid, iat, exp}");

            // 第4步：计算Token过期时间
            long expireTimeMillis = System.currentTimeMillis() + (28800 * 1000L); // 8小时后过期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String expireTime = sdf.format(new Date(expireTimeMillis));

            // 第5步：构建响应数据
            LoginResponseVO response = new LoginResponseVO();
            response.setToken(customToken);  // 返回自定义JWT Token
            response.setUserid(user.getUserId());
            response.setUsername(user.getUserName());
            response.setExpireTime(expireTime);

            long endTime = System.currentTimeMillis();
            log.info("用户登录成功（混合模式），账号: {}, 用户ID: {}, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: {}ms",
                    request.getUsername(), user.getUserId(), (endTime - startTime));

            // 检查是否为超级管理员并记录特殊日志
            if (superAdminConfig.getEnabled() && superAdminConfig.isSuperAdmin(user.getUserId())) {
                log.warn(" [安全审计] 超级管理员登录 - 账号: {}, 用户ID: {}, 时间: {}, 具有系统最高权限",
                        request.getUsername(), user.getUserId(),
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date()));
            }

            return new BaseResult<>(200, "登录成功", response);

        } catch (Exception e) {
            log.error("用户登录失败，账号: {}", request.getUsername(), e);
            return new BaseResult<>(500, "登录失败：" + e.getMessage(), null);
        }
    }

    /**
     * 查询用户权限接口
     * 根据JWT Token获取用户权限信息
     *
     * @param moduleIdentifier 模块标识符，可选，用于筛选特定模块的权限
     * @param moduleType 权限类型，可选值：menu（菜单）、data（数据）、all（全部），默认menu
     * @return 用户权限数据，包含菜单、按钮和数据权限
     */
    @GetMapping("/permissions")
    @ApiOperation(value = "查询用户权限", notes = "根据Token获取用户权限（支持权限类型筛选和混合认证）")
    public BaseResult<UserPermissionsResponseVO> getPermissions(
            @ApiParam(value = "模块标识符", example = "rb.kb")
            @RequestParam(required = false) String moduleIdentifier,
            @ApiParam(value = "权限类型", example = "menu", allowableValues = "menu,data,all")
            @RequestParam(required = false, defaultValue = "menu") String moduleType,
            HttpServletRequest request) {

        log.info("查询用户权限请求，模块标识: {}, 权限类型: {}", moduleIdentifier, moduleType);
        long startTime = System.currentTimeMillis();

        try {
            // 第0步：参数验证
            if (moduleType != null && !Arrays.asList("menu", "data", "all").contains(moduleType)) {
                log.warn("权限查询失败：moduleType参数无效，值: {}", moduleType);
                return new BaseResult<>(400, "moduleType参数无效，支持值：menu, data, all", null);
            }

            // 第1步：混合认证获取用户ID
            log.debug("第1步：混合认证获取用户ID");

            Long userId = getHybridAuthUserId(request);
            if (userId == null) {
                log.warn("权限查询失败：混合认证未能获取用户ID");
                return new BaseResult<>(401, "认证失败，请重新登录", null);
            }

            log.debug("混合认证成功，用户ID: {}", userId);

            // 第2步：查询用户权限
            log.debug("第2步：查询用户权限");
            UserPermissionsResponseVO permissions = userPermissionService.getUserPermissions(userId, moduleIdentifier, moduleType);

            long endTime = System.currentTimeMillis();
            String authMethod = (String) request.getAttribute("authMethod");

            // 安全获取权限数量，避免空指针异常
            int menuCount = permissions.getPermissions().getMenus() != null ? permissions.getPermissions().getMenus().size() : 0;
            int buttonCount = permissions.getPermissions().getButtons() != null ? permissions.getPermissions().getButtons().size() : 0;
            int dataCount = permissions.getPermissions().getDataPermissions() != null ? permissions.getPermissions().getDataPermissions().size() : 0;

            log.info("用户权限查询成功（混合认证），用户ID: {}, 认证方式: {}, 权限类型: {}, 菜单数量: {}, 按钮数量: {}, 数据权限数量: {}, 耗时: {}ms",
                    userId,
                    authMethod != null ? authMethod : "Sa-Token-Fallback",
                    moduleType,
                    menuCount,
                    buttonCount,
                    dataCount,
                    (endTime - startTime));

            return new BaseResult<>(200, "查询成功", permissions);

        } catch (NotLoginException e) {
            log.warn("权限查询失败：用户未登录或Token已过期，异常类型: {}, 异常消息: {}", e.getClass().getSimpleName(), e.getMessage());
            return new BaseResult<>(401, "用户未登录或Token已过期，请重新登录", null);
        } catch (Exception e) {
            log.error("查询用户权限失败，异常类型: {}, 异常消息: {}", e.getClass().getSimpleName(), e.getMessage(), e);
            return new BaseResult<>(500, "查询权限失败：" + e.getMessage(), null);
        }
    }

    /**
     * 用户登出接口
     * 注销当前用户的登录状态
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @ApiOperation(value = "用户登出", notes = "注销当前用户的登录状态")
    public BaseResult<String> logout() {
        try {
            Object loginIdObj = StpUtil.getLoginId();
            if (loginIdObj != null) {
                Long userId = Long.valueOf(loginIdObj.toString());
                log.info("用户登出，用户ID: {}", userId);
                StpUtil.logout();
                return new BaseResult<>(200, "登出成功", null);
            } else {
                return new BaseResult<>(200, "用户未登录", null);
            }
        } catch (NotLoginException e) {
            return new BaseResult<>(200, "用户未登录", null);
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return new BaseResult<>(500, "登出失败：" + e.getMessage(), null);
        }
    }

    /**
     * 混合认证获取用户ID
     * 优先从混合认证拦截器获取用户ID，fallback到Sa-Token验证
     *
     * @param request HTTP请求对象
     * @return 用户ID，获取失败返回null
     */
    private Long getHybridAuthUserId(HttpServletRequest request) {
        // 策略1：优先从混合认证拦截器的request attribute中获取用户ID
        Long userId = (Long) request.getAttribute("userId");
        String authMethod = (String) request.getAttribute("authMethod");

        if (userId != null) {
            log.debug("从混合认证拦截器获取用户ID成功，用户ID: {}, 认证方式: {}", userId, authMethod);
            return userId;
        }

        // 策略2：fallback到Sa-Token验证
        try {
            Object loginIdObj = StpUtil.getLoginId();
            if (loginIdObj != null) {
                userId = Long.valueOf(loginIdObj.toString());
                log.debug("从Sa-Token获取用户ID成功，用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("Sa-Token获取用户ID失败: {}", e.getMessage());
        }

        log.warn("混合认证失败：所有认证方式都无法获取用户ID");
        return null;
    }
}