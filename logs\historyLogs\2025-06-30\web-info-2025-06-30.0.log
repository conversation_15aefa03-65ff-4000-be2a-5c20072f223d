2025-06-30 08:57:54.810 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-06-30 08:57:56.652 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
2025-06-30 08:57:57.437 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行每日增量数据同步任务 ===
2025-06-30 08:57:57.437 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - 同步时间范围: 2025-06-29 00:00:00 - 2025-06-29 23:59:59
2025-06-30 08:57:57.438 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始执行增量数据同步，时间范围: 2025-06-29 00:00:00 - 2025-06-29 23:59:59
2025-06-30 08:57:57.442 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 采用增量同步策略，只处理时间范围内的变更数据
2025-06-30 08:57:57.443 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2025-06-29T00:00 - 2025-06-29T23:59:59
2025-06-30 08:57:57.444 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 同步策略: 建议策略：单次同步（23小时 ≤ 72小时阈值）
2025-06-30 08:57:57.444 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2025-06-29T00:00 - 2025-06-29T23:59:59
2025-06-30 08:57:57.460 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2025-06-29%2000:00:00&endDate=2025-06-29%2023:59:59
2025-06-30 08:57:57.460 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部系统获取部门数据（外部系统已内置重试和分片机制）
2025-06-30 08:58:00.700 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行员工部门归属更新任务 ===
2025-06-30 08:58:00.701 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始更新员工部门归属
2025-06-30 16:23:25.484 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 34912 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-30 16:23:25.486 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-30 16:23:25.505 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-30 16:23:28.725 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-30 16:23:28.730 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-30 16:23:28.793 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51 ms. Found 0 Elasticsearch repository interfaces.
2025-06-30 16:23:28.799 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-30 16:23:28.800 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-30 16:23:28.823 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-30 16:23:28.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-30 16:23:28.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-30 16:23:28.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-30 16:23:29.014 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-30 16:23:29.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-30 16:23:29.048 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-30 16:23:31.096 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-30 16:23:31.135 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-30 16:23:31.135 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-30 16:23:31.136 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-30 16:23:31.657 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-30 16:23:31.657 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6066 ms
2025-06-30 16:23:31.858 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-30 16:23:32.125 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-30 16:23:35.001 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-30 16:23:35.490 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-30 16:23:36.071 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-30 16:23:36.416 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-30 16:23:36.941 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-30 16:23:36.954 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-30 16:23:40.001 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-30 16:23:44.130 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-30 16:23:44.149 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-30 16:23:45.162 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.891 seconds (JVM running for 28.836)
2025-06-30 16:24:09.248 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-30 16:24:09.248 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-30 16:24:09.250 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-30 16:26:36.423 [http-nio-8285-exec-7] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:26:36.425 [http-nio-8285-exec-7] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:01.829 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-30 16:27:04.738 [http-nio-8285-exec-8] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: 123, 用户ID: 1936640367617249280, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 2908ms
2025-06-30 16:27:16.949 [http-nio-8285-exec-9] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:16.950 [http-nio-8285-exec-9] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:19.764 [http-nio-8285-exec-10] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:19.764 [http-nio-8285-exec-10] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:20.200 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:20.200 [http-nio-8285-exec-1] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:20.454 [http-nio-8285-exec-4] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:20.454 [http-nio-8285-exec-4] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:20.648 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:20.648 [http-nio-8285-exec-3] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:20.815 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:20.815 [http-nio-8285-exec-2] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:20.966 [http-nio-8285-exec-5] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:20.967 [http-nio-8285-exec-5] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:27:22.966 [http-nio-8285-exec-6] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:27:22.966 [http-nio-8285-exec-6] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：缺少Authorization头, URI: /menus/deleteMenuV2
2025-06-30 16:29:16.273 [http-nio-8285-exec-9] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:29:16.441 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 16:29:16.441 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 1936603100576092160, 强制删除: false
2025-06-30 16:29:24.694 [http-nio-8285-exec-10] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:29:24.700 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 16:29:24.701 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 1936603100576092160, 强制删除: false
2025-06-30 16:29:42.349 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:29:42.352 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 16:29:42.352 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 9016, 强制删除: false
2025-06-30 16:30:31.433 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: admin
2025-06-30 16:30:31.516 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: admin, 用户ID: 1938155631131365376, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 83ms
2025-06-30 16:30:44.697 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:30:44.705 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 16:30:44.705 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 9016, 强制删除: false
2025-06-30 16:31:20.390 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 16:31:20.399 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 16:31:20.399 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 9007, 强制删除: false
2025-06-30 17:58:52.917 [http-nio-8285-exec-7] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /menus/deleteMenuV2, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-06-30 17:58:54.559 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单V2
2025-06-30 17:58:54.561 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 1938159115759128576, 强制删除: false
2025-06-30 17:58:55.677 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单正在被角色使用，返回使用情况，菜单ID: 1938159115759128576
2025-06-30 17:58:56.161 [http-nio-8285-exec-7] WARN  com.dfit.percode.controller.MenuModuleController - 菜单删除检测到使用情况: 检测到使用情况，请确认是否强制删除
2025-06-30 17:58:56.247 [http-nio-8285-exec-7] INFO  com.dfit.percode.listener.UserActionInterceptor - 请求处理完成 - URI: /menus/deleteMenuV2, 处理时间: 3067ms
2025-06-30 21:59:02.723 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-06-30 21:59:05.848 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
