package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 检查用户重复请求VO
 * 用于实时检测用户名和账号是否重复
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "CheckUserDuplicateRequestVO", description = "检查用户重复请求参数")
public class CheckUserDuplicateRequestVO {

    @ApiModelProperty(value = "用户名", example = "张三")
    private String userName;

    @ApiModelProperty(value = "账号", example = "zhangsan")
    private String account;

    @ApiModelProperty(value = "用户ID", notes = "编辑时传入，用于排除自己")
    private Long userId;

    @ApiModelProperty(value = "检查类型", required = false, example = "userName",
                     notes = "可选参数。userName-检查用户名，account-检查账号，both-同时检查。" +
                            "如果不传此参数，会根据传入的userName和account自动判断检查类型")
    private String checkType;
}
