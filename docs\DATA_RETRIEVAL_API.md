# 数据获取API文档

## 概述

数据获取API为外部系统提供了从远程MDM系统获取数据的代理服务。该API不涉及本地数据库操作，仅作为数据传递的中间层，将远程系统的XML数据解析为与数据库表结构对应的JSON格式返回给调用方。

## 功能特点

- **代理服务**: 作为远程MDM系统的代理，为外部系统提供数据获取服务
- **数据解析**: 自动解析远程系统返回的XML数据，转换为与数据库表结构对应的JSON格式
- **时间范围查询**: 支持按时间范围获取指定时间段内的数据变更
- **无数据库操作**: 纯数据传递服务，不涉及本地数据库的读写操作
- **统一响应格式**: 提供统一的API响应格式，便于外部系统集成
- **表结构对应**: 返回的数据结构与实际数据库表结构完全对应，便于外部系统直接使用

## 数据库表结构对应关系

本API返回的数据结构与以下数据库表完全对应：

### 部门相关表
- **department**: 部门主表，存储组织结构中的部门信息
- **department_child**: 部门子表，存储部门在其他系统中的标识信息

### 员工相关表
- **employee**: 员工主表，存储员工基本信息
- **employee_position**: 员工岗位关联表，存储员工的岗位信息
- **employee_title**: 员工职称表，存储员工的职称信息
- **employee_system**: 员工系统标识表，存储员工在其他系统中的标识信息

## API接口

### 1. 获取部门数据

**接口地址**: `GET /api/data/departments`

**请求参数**:
- `startDate` (必填): 开始时间，格式: yyyy-MM-dd HH:mm:ss
- `endDate` (必填): 结束时间，格式: yyyy-MM-dd HH:mm:ss

**请求示例**:
```http
GET /api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
```

**响应格式**:
```json
{
  "success": true,
  "message": "成功获取部门数据",
  "data": [
    {
      "deptUuid": "********-6571-4f25-815b-18339c8d76f4",
      "orgCode": "X50070000",
      "orgName": "技术部",
      "parentId": "parent-dept-id",
      "parentCode": "X50070000",
      "fullName": "南京钢铁股份有限公司技术部",
      "isHistory": 0,
      "description": "技术研发部门",
      "fax": "025-12345678",
      "webAddress": "http://tech.njsteel.com",
      "orgManager": "张三",
      "postCode": "210000",
      "userPredef13": "判重项目值域",
      "userPredef14": "操作标识",
      "userPredef18": "预留字段",
      "updateTime": "2024-01-01T10:00:00.000+00:00",
      "orgType": "部门",
      "orgLevel": 3,
      "orgPath": "/集团/公司/技术部",
      "fullOrgCode": "001.002.X50070000",
      "children": [
        {
          "guid": "child-guid-123",
          "deptUuid": "********-6571-4f25-815b-18339c8d76f4",
          "sourceSystem": "ERP",
          "sourceDataNm": "ERP_DEPT_001",
          "udef1": "自定义字段1",
          "udef2": "自定义字段2",
          "udef3": "自定义字段3",
          "udef4": "自定义字段4",
          "udef5": "自定义字段5",
          "udef6": "自定义字段6"
        }
      ]
    }
  ],
  "timestamp": "2024-01-01T10:00:00.000+00:00",
  "totalCount": 1
}
```

### 2. 获取员工数据

**接口地址**: `GET /api/data/employees`

**请求参数**:
- `startDate` (必填): 开始时间，格式: yyyy-MM-dd HH:mm:ss
- `endDate` (必填): 结束时间，格式: yyyy-MM-dd HH:mm:ss

**请求示例**:
```http
GET /api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
```

**响应格式** (对应employee、employee_position、employee_title、employee_system表结构):
```json
{
  "success": true,
  "message": "成功获取员工数据",
  "data": [
    {
      "mdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
      "mdmHrdwnm": "********-6571-4f25-815b-18339c8d76f4",
      "employeeCode": "019359",
      "employeeName": "张清辉",
      "gender": "1",
      "mobile": "***********",
      "status": "A",
      "idCard": "110108196609182334",
      "account": "019359",
      "birthDate": "1966-09-18T00:00:00.000+00:00",
      "email": "",
      "orgType": "001",
      "orgLevel1": "01",
      "orgLevel2": "01",
      "orgLevel3": "03",
      "wechat": "",
      "tel": "",
      "note": "",
      "isDisabled": "0",
      "userType": "U",
      "orgCode": "001",
      "idName": "110108196609182334张清辉",
      "createdTime": "2024-01-01T10:00:00.000+00:00",
      "updatedTime": "2024-01-01T10:00:00.000+00:00",
      "positions": [
        {
          "guid": "3b7eaf3d-2d30-4cc3-9da8-12b5488afbc4",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "positionCode": "X50070500",
          "orgCode": "X50070000",
          "departmentCode": "03135",
          "isPrimary": "1",
          "status": "D",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "isActive": "1",
          "positionDetailCode": "019359X5007050051200"
        }
      ],
      "titles": [
        {
          "guid": "5bbf3bc0-01b3-40a6-8e36-627e40f85dae",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "titleCode": "X50070000",
          "titleType": "2",
          "titleLevel": "0",
          "titleName": "研究员级高工",
          "status": "U",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "titleCategory": "O"
        }
      ],
      "systems": [
        {
          "guid": "db14b032-ec3a-4f71-8c23-dae73054904a",
          "employeeMdmId": "81ee8ed6-edab-4a1d-832b-812dcaf55aee",
          "systemCode": "ERP",
          "systemDataId": "019359",
          "orgCode": "X50070300",
          "departmentCode": "51200",
          "employeeCode": "019359",
          "createdTime": "2024-01-01T10:00:00.000+00:00",
          "updatedTime": "2024-01-01T10:00:00.000+00:00",
          "loginAccount": "019359"
        }
      ]
    }
  ],
  "timestamp": "2024-01-01T10:00:00.000+00:00",
  "totalCount": 1
}
```

### 3. API测试接口

**接口地址**: `GET /api/data/test`

**请求示例**:
```http
GET /api/data/test
```

**响应格式**:
```json
{
  "success": true,
  "message": "API测试成功",
  "data": "数据获取API正常工作",
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

## 错误处理

当API调用失败时，会返回错误响应：

```json
{
  "success": false,
  "message": "获取数据失败: 具体错误信息",
  "timestamp": "2024-01-01T10:00:00.000+00:00"
}
```

## 数据结构说明

### 部门数据结构
- **主表数据**: 对应 `department` 表的所有字段
- **子表数据**: `children` 数组对应 `department_child` 表，存储部门在其他系统中的标识信息

### 员工数据结构
- **主表数据**: 对应 `employee` 表的所有字段
- **岗位数据**: `positions` 数组对应 `employee_position` 表
- **职称数据**: `titles` 数组对应 `employee_title` 表
- **系统标识数据**: `systems` 数组对应 `employee_system` 表

### 字段映射说明
所有返回的字段名称与数据库表字段名称完全一致，外部系统可以直接使用这些数据进行数据库操作，无需额外的字段映射。

## 使用场景

1. **外部系统数据同步**: 其他系统可以调用这些接口获取最新的组织架构和人员信息，直接同步到本地数据库
2. **数据集成**: 作为数据集成平台的数据源接口，提供标准化的数据格式
3. **实时数据查询**: 提供实时的远程数据查询能力，无需维护本地数据副本
4. **系统解耦**: 避免外部系统直接调用远程MDM系统，降低系统间的耦合度
5. **数据一致性**: 确保多个系统使用相同的数据源和数据格式

## 注意事项

1. **时间格式**: 所有时间参数必须使用 `yyyy-MM-dd HH:mm:ss` 格式
2. **数据量**: 建议合理控制查询时间范围，避免一次性获取过多数据
3. **网络超时**: 由于需要调用远程系统，请设置合适的网络超时时间
4. **错误重试**: 建议在调用失败时实现重试机制
5. **数据缓存**: 外部系统可以根据需要实现数据缓存机制

## Swagger文档

系统启动后，可以通过以下地址访问Swagger API文档：
- Swagger UI: http://localhost:8080/swagger-ui.html
- API文档: http://localhost:8080/api-docs
