package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 部门用户列表响应 VO
 * 用于返回部门用户查询结果
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@ApiModel(value = "DepartmentUsersResponseVO", description = "部门用户列表响应数据")
public class DepartmentUsersResponseVO {
    
    @ApiModelProperty(value = "部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long departmentId;
    
    @ApiModelProperty(value = "部门名称")
    private String departmentName;
    
    @ApiModelProperty(value = "部门完整路径")
    private String departmentPath;
    
    @ApiModelProperty(value = "是否包含了子部门用户")
    private Boolean includeChildren;
    
    @ApiModelProperty(value = "用户列表")
    private List<DepartmentUserItemVO> users;
    
    @ApiModelProperty(value = "用户总数")
    private Integer totalUsers;
    
    @ApiModelProperty(value = "直属用户数（不包含子部门）")
    private Integer directUsers;
    
    @ApiModelProperty(value = "子部门用户数")
    private Integer childrenUsers;
    
    @ApiModelProperty(value = "分页信息（如果启用分页）")
    private PaginationInfo pagination;
    
    /**
     * 部门用户项 VO
     */
    @Data
    @ApiModel(value = "DepartmentUserItemVO", description = "部门用户项数据")
    public static class DepartmentUserItemVO {
        
        @ApiModelProperty(value = "用户ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long userId;
        
        @ApiModelProperty(value = "用户名")
        private String userName;
        
        @ApiModelProperty(value = "账号")
        private String account;
        
        @ApiModelProperty(value = "是否停用")
        private Boolean isDisabled;
        
        @ApiModelProperty(value = "所属部门ID")
        @JsonSerialize(using = ToStringSerializer.class)
        private Long departmentId;
        
        @ApiModelProperty(value = "所属部门名称")
        private String departmentName;
        
        @ApiModelProperty(value = "是否为直属用户（true=直属，false=子部门用户）")
        private Boolean isDirect;
        
        @ApiModelProperty(value = "用户类型")
        private String userType;
        
        @ApiModelProperty(value = "员工编号")
        private String employeeCode;
        
        @ApiModelProperty(value = "手机号")
        private String mobile;
        
        @ApiModelProperty(value = "邮箱")
        private String email;
    }
    
    /**
     * 分页信息 VO
     */
    @Data
    @ApiModel(value = "PaginationInfo", description = "分页信息")
    public static class PaginationInfo {
        
        @ApiModelProperty(value = "当前页码")
        private Integer currentPage;
        
        @ApiModelProperty(value = "每页大小")
        private Integer pageSize;
        
        @ApiModelProperty(value = "总记录数")
        private Long totalRecords;
        
        @ApiModelProperty(value = "总页数")
        private Integer totalPages;
        
        @ApiModelProperty(value = "是否有上一页")
        private Boolean hasPrevious;
        
        @ApiModelProperty(value = "是否有下一页")
        private Boolean hasNext;
    }
}
