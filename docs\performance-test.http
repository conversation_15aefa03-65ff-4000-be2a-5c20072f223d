### 权限管理系统性能测试
### 测试 /users/getAllUsers 接口优化效果

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试获取所有用户接口（优化前后对比）
### 注意：这个接口可能会很慢，请耐心等待
GET {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 2. 测试用户列表查询（分页）
POST {{baseUrl}}/users/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20
}

### 3. 测试按部门查询用户
POST {{baseUrl}}/users/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20,
  "organAffiliation": 5007
}

### 4. 测试用户搜索
POST {{baseUrl}}/users/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20,
  "userName": "张"
}

### 5. 测试获取用户详情
POST {{baseUrl}}/users/detail
Content-Type: application/json

{
  "id": "6001"
}

### 6. 测试获取用户角色信息
POST {{baseUrl}}/users/roles
Content-Type: application/json

{
  "userId": 6001
}

### 7. 测试部门树查询
GET {{baseUrl}}/departments/tree
Content-Type: application/json

### 8. 测试角色列表查询
POST {{baseUrl}}/roles/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20
}

### 9. 测试菜单树查询
POST {{baseUrl}}/menus/tree
Content-Type: application/json

{
  "moduleIdentifier": "user_management"
}

### 10. 测试数据权限列表查询
POST {{baseUrl}}/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 20
}

### =====================================================
### 性能测试说明
### =====================================================

### 测试步骤：
### 1. 执行索引创建SQL（docs/performance-optimization-indexes.sql）
### 2. 重启应用
### 3. 执行上述测试用例，记录响应时间
### 4. 对比优化前后的性能差异

### 预期结果：
### - /users/getAllUsers 接口：从50+秒降低到5秒以内
### - 用户列表查询：从几秒降低到毫秒级
### - 部门相关查询：显著提升
### - 角色权限查询：显著提升

### 性能监控指标：
### 1. 响应时间（Response Time）
### 2. 数据库查询次数
### 3. 内存使用情况
### 4. CPU使用率

### 如果性能仍然不理想，可以考虑：
### 1. 增加更多索引
### 2. 使用缓存（Redis）
### 3. 分页查询替代全量查询
### 4. 异步加载部分数据

### 数据库性能分析SQL：
### 
### -- 查看慢查询
### SELECT query, mean_time, calls, total_time
### FROM pg_stat_statements 
### WHERE query LIKE '%t_user%' OR query LIKE '%t_org_structure%'
### ORDER BY mean_time DESC;
### 
### -- 查看表统计信息
### SELECT 
###     schemaname,
###     tablename,
###     n_tup_ins as inserts,
###     n_tup_upd as updates,
###     n_tup_del as deletes,
###     n_live_tup as live_tuples,
###     n_dead_tup as dead_tuples,
###     last_vacuum,
###     last_autovacuum,
###     last_analyze,
###     last_autoanalyze
### FROM pg_stat_user_tables 
### WHERE tablename IN ('t_user', 't_org_structure')
### ORDER BY n_live_tup DESC;
### 
### -- 查看索引使用情况
### SELECT 
###     schemaname,
###     tablename,
###     indexname,
###     idx_scan as index_scans,
###     idx_tup_read as tuples_read,
###     idx_tup_fetch as tuples_fetched
### FROM pg_stat_user_indexes 
### WHERE tablename IN ('t_user', 't_org_structure')
### ORDER BY idx_scan DESC;
