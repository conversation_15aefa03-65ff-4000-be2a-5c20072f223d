package com.dfit.percode.service;

import com.dfit.percode.entity.SuperAdminAuditLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 超级管理员审计日志服务
 * 统一管理超级管理员的审计日志记录
 * 确保所有超级管理员操作都有完整的审计记录
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class SuperAdminAuditService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 记录超级管理员登录审计日志
     *
     * @param userId 用户ID
     * @param loginAccount 登录账号
     * @param clientIp 客户端IP
     * @param userAgent User-Agent信息
     */
    public void logSuperAdminLogin(Long userId, String loginAccount, String clientIp, String userAgent) {
        try {
            SuperAdminAuditLog auditLog = SuperAdminAuditLog.createAuditLog(
                    userId, SuperAdminAuditLog.OperationType.LOGIN, "/auth/login", clientIp);
            
            auditLog.setLoginAccount(loginAccount);
            auditLog.setHttpMethod("POST");
            auditLog.setOperationDescription("超级管理员登录系统");
            auditLog.setUserAgent(userAgent);
            auditLog.setResponseStatus(200);
            auditLog.setOperationResult("登录成功");
            auditLog.setAuthMethod("混合认证");
            auditLog.setRiskLevel(SuperAdminAuditLog.RiskLevel.HIGH);
            auditLog.setRemarks("超级管理员具有系统最高权限");

            // 记录到日志文件（生产环境可考虑写入数据库）
            logAuditRecord(auditLog, "超级管理员登录");

        } catch (Exception e) {
            log.error("记录超级管理员登录审计日志失败", e);
        }
    }

    /**
     * 记录超级管理员权限查询审计日志
     *
     * @param userId 用户ID
     * @param moduleIdentifier 模块标识符
     * @param menuCount 菜单数量
     * @param buttonCount 按钮数量
     * @param clientIp 客户端IP
     */
    public void logSuperAdminPermissionQuery(Long userId, String moduleIdentifier, int menuCount, int buttonCount, String clientIp) {
        try {
            SuperAdminAuditLog auditLog = SuperAdminAuditLog.createAuditLog(
                    userId, SuperAdminAuditLog.OperationType.PERMISSION_QUERY, "/auth/permissions", clientIp);
            
            auditLog.setHttpMethod("GET");
            auditLog.setOperationDescription("超级管理员查询权限信息");
            auditLog.setRequestParams("moduleIdentifier=" + (moduleIdentifier != null ? moduleIdentifier : "ALL"));
            auditLog.setResponseStatus(200);
            auditLog.setOperationResult(String.format("返回全量权限：菜单%d个，按钮%d个", menuCount, buttonCount));
            auditLog.setAuthMethod("SuperAdmin");
            auditLog.setRiskLevel(SuperAdminAuditLog.RiskLevel.MEDIUM);
            auditLog.setRemarks("超级管理员获取系统全量权限");

            // 记录到日志文件
            logAuditRecord(auditLog, "超级管理员权限查询");

        } catch (Exception e) {
            log.error("记录超级管理员权限查询审计日志失败", e);
        }
    }

    /**
     * 记录超级管理员系统访问审计日志
     *
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @param operationType 操作类型
     * @param operationDescription 操作描述
     */
    public void logSuperAdminAccess(Long userId, HttpServletRequest request, String operationType, String operationDescription) {
        try {
            String clientIp = getClientIpAddress(request);
            
            SuperAdminAuditLog auditLog = SuperAdminAuditLog.createAuditLog(
                    userId, operationType, request.getRequestURI(), clientIp);
            
            auditLog.setHttpMethod(request.getMethod());
            auditLog.setOperationDescription(operationDescription);
            auditLog.setUserAgent(request.getHeader("User-Agent"));
            auditLog.setRequestParams(getRequestParams(request));
            auditLog.setResponseStatus(200);
            auditLog.setOperationResult("访问成功");
            auditLog.setAuthMethod("SuperAdmin");
            auditLog.setRiskLevel(SuperAdminAuditLog.RiskLevel.HIGH);
            auditLog.setRemarks("超级管理员系统访问");

            // 记录到日志文件
            logAuditRecord(auditLog, "超级管理员系统访问");

        } catch (Exception e) {
            log.error("记录超级管理员系统访问审计日志失败", e);
        }
    }

    /**
     * 记录超级管理员配置访问审计日志
     *
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @param configType 配置类型
     */
    public void logSuperAdminConfigAccess(Long userId, HttpServletRequest request, String configType) {
        try {
            String clientIp = getClientIpAddress(request);
            
            SuperAdminAuditLog auditLog = SuperAdminAuditLog.createAuditLog(
                    userId, SuperAdminAuditLog.OperationType.CONFIG_ACCESS, request.getRequestURI(), clientIp);
            
            auditLog.setHttpMethod(request.getMethod());
            auditLog.setOperationDescription("超级管理员访问配置信息：" + configType);
            auditLog.setUserAgent(request.getHeader("User-Agent"));
            auditLog.setResponseStatus(200);
            auditLog.setOperationResult("配置访问成功");
            auditLog.setAuthMethod("SuperAdmin");
            auditLog.setRiskLevel(SuperAdminAuditLog.RiskLevel.CRITICAL);
            auditLog.setRemarks("超级管理员访问敏感配置信息");

            // 记录到日志文件
            logAuditRecord(auditLog, "超级管理员配置访问");

        } catch (Exception e) {
            log.error("记录超级管理员配置访问审计日志失败", e);
        }
    }

    /**
     * 统一的审计日志记录方法
     *
     * @param auditLog 审计日志对象
     * @param logType 日志类型
     */
    private void logAuditRecord(SuperAdminAuditLog auditLog, String logType) {
        // 使用WARN级别确保审计日志被记录
        log.warn("🔒 [{}] 用户ID: {}, 账号: {}, 操作: {}, 路径: {}, 方法: {}, IP: {}, 时间: {}, 风险: {}, 结果: {}",
                logType,
                auditLog.getUserId(),
                auditLog.getLoginAccount(),
                auditLog.getOperationType(),
                auditLog.getAccessPath(),
                auditLog.getHttpMethod(),
                auditLog.getClientIp(),
                auditLog.getOperationTime().format(FORMATTER),
                auditLog.getRiskLevel(),
                auditLog.getOperationResult());

        // 详细审计信息（DEBUG级别）
        log.debug("🔒 [{}详细] 描述: {}, 参数: {}, UserAgent: {}, 备注: {}",
                logType,
                auditLog.getOperationDescription(),
                auditLog.getRequestParams(),
                auditLog.getUserAgent(),
                auditLog.getRemarks());
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 获取请求参数字符串
     *
     * @param request HTTP请求对象
     * @return 请求参数字符串
     */
    private String getRequestParams(HttpServletRequest request) {
        StringBuilder params = new StringBuilder();
        
        request.getParameterMap().forEach((key, values) -> {
            if (params.length() > 0) {
                params.append("&");
            }
            params.append(key).append("=");
            if (values.length > 0) {
                params.append(values[0]);
            }
        });
        
        return params.toString();
    }

    /**
     * 记录超级管理员操作统计信息
     */
    public void logSuperAdminStatistics() {
        try {
            String timestamp = LocalDateTime.now().format(FORMATTER);
            log.info("🔒 [超级管理员统计] 时间: {}, 说明: 超级管理员审计日志服务正常运行", timestamp);
        } catch (Exception e) {
            log.error("记录超级管理员统计信息失败", e);
        }
    }
}
