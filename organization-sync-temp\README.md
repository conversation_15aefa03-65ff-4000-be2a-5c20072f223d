# 组织架构数据转换临时工具

这是一个临时的组织架构数据转换工具，用于将 `department_sync_test.sql` 中的数据转换并导入到 `t_org_structure` 表中。

## 目录结构

```
organization-sync-temp/
├── README.md                           # 说明文档
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/dfit/orgsync/
│   │   │       ├── dto/
│   │   │       │   ├── DepartmentSyncDto.java
│   │   │       │   └── OrganizationTreeNode.java
│   │   │       ├── mapper/
│   │   │       │   └── OrganizationSyncMapper.java
│   │   │       ├── service/
│   │   │       │   └── OrganizationSyncService.java
│   │   │       ├── controller/
│   │   │       │   └── OrganizationSyncController.java
│   │   │       └── util/
│   │   │           ├── SqlFileParser.java
│   │   │           └── HierarchyParser.java
│   │   └── resources/
│   │       └── mapper/
│   │           └── OrganizationSyncMapper.xml
│   └── test/
│       └── java/
│           └── com/dfit/orgsync/
│               └── OrganizationSyncServiceTest.java
└── department_sync_test.sql            # 源数据文件
```

## 使用说明

1. 将 `department_sync_test.sql` 文件放在此目录下
2. 运行 `OrganizationSyncService.syncOrganizationData()` 方法
3. 数据转换完成后，可以删除整个 `organization-sync-temp` 目录

## 使用方法

### 方法1：通过REST API调用

1. 启动Spring Boot应用
2. 调用同步接口：

```bash
# 使用默认路径同步
curl -X POST http://localhost:8080/api/organization-sync/sync-default

# 或指定文件路径
curl -X POST "http://localhost:8080/api/organization-sync/sync?filePath=organization-sync-temp/department_sync_test.sql"

# 查看同步状态
curl -X GET http://localhost:8080/api/organization-sync/status

# 获取帮助信息
curl -X GET http://localhost:8080/api/organization-sync/help
```

### 方法2：直接调用服务

```java
@Autowired
private OrganizationSyncService organizationSyncService;

public void performSync() {
    String filePath = "organization-sync-temp/department_sync_test.sql";
    SyncResult result = organizationSyncService.syncOrganizationData(filePath);

    if (result.isSuccess()) {
        log.info("同步成功: {}", result);
        System.out.println(result.generateDetailedReport());
    } else {
        log.error("同步失败: {}", result.getErrorMessage());
    }
}
```

### 方法3：运行测试

```bash
# 运行所有测试
mvn test -Dtest=OrganizationSyncServiceTest

# 运行特定测试
mvn test -Dtest=OrganizationSyncServiceTest#testSyncOrganizationData
```

## 功能特性

- **智能层级解析**：基于关键词自动识别组织层级结构
- **数据去重**：按fullName去重，保留最新记录
- **批量处理**：分批插入数据，避免内存溢出
- **事务控制**：使用Spring事务，确保数据一致性
- **完整验证**：多维度验证数据质量
- **详细日志**：完整的处理过程日志记录
- **性能监控**：执行时间和处理速度统计

## 数据转换规则

1. **一级部门识别**：根据预定义的56个一级部门列表进行精确匹配
2. **层级拆解**：使用关键词（事业部、厂、车间、班等）智能拆解
3. **ID分配**：从2000000000开始递增分配唯一ID
4. **父子关系**：自动建立正确的pre_id关系
5. **数据来源**：所有记录的data_source设置为2（数据同步）

## 注意事项

- 这是临时转换工具，转换完成后建议删除
- 所有生成的数据 `data_source` 字段值为 2
- ID分配从 2000000000 开始
- 执行前会清空现有的同步数据（data_source=2）
- 建议在测试环境先验证结果
