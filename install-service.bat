@echo off
chcp 65001 >nul

REM 将应用安装为Windows服务
REM 需要先下载 nssm.exe (Non-Sucking Service Manager)
REM 下载地址: https://nssm.cc/download

echo ==========================================
echo 安装权限管理系统为Windows服务
echo ==========================================

set SERVICE_NAME=PermissionSystem
set JAR_NAME=percode-0.0.1-SNAPSHOT.jar
set JAVA_HOME=%JAVA_HOME%
set CURRENT_DIR=%~dp0

REM 检查nssm.exe是否存在
if not exist "nssm.exe" (
    echo [ERROR] nssm.exe 不存在
    echo [INFO] 请从 https://nssm.cc/download 下载 nssm.exe 并放在当前目录
    pause
    exit /b 1
)

REM 检查Java环境
if "%JAVA_HOME%"=="" (
    echo [ERROR] JAVA_HOME 环境变量未设置
    pause
    exit /b 1
)

REM 检查JAR文件
if not exist "%JAR_NAME%" (
    echo [ERROR] JAR文件不存在: %JAR_NAME%
    pause
    exit /b 1
)

echo [INFO] 安装服务: %SERVICE_NAME%

REM 安装服务
nssm install %SERVICE_NAME% "%JAVA_HOME%\bin\java.exe"

REM 设置服务参数
nssm set %SERVICE_NAME% Arguments "-Xms512m -Xmx1024m -Dspring.profiles.active=test -Dfile.encoding=UTF-8 -Duser.timezone=Asia/Shanghai -jar \"%CURRENT_DIR%%JAR_NAME%\""
nssm set %SERVICE_NAME% AppDirectory "%CURRENT_DIR%"
nssm set %SERVICE_NAME% DisplayName "权限管理系统"
nssm set %SERVICE_NAME% Description "权限管理系统服务"
nssm set %SERVICE_NAME% Start SERVICE_AUTO_START

REM 设置日志
nssm set %SERVICE_NAME% AppStdout "%CURRENT_DIR%logs\service.log"
nssm set %SERVICE_NAME% AppStderr "%CURRENT_DIR%logs\service-error.log"

REM 创建日志目录
if not exist "logs" (
    mkdir logs
)

echo [INFO] 服务安装完成
echo [INFO] 服务名称: %SERVICE_NAME%
echo [INFO] 启动服务: net start %SERVICE_NAME%
echo [INFO] 停止服务: net stop %SERVICE_NAME%
echo [INFO] 卸载服务: nssm remove %SERVICE_NAME% confirm

pause
