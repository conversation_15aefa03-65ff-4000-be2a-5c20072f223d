package com.dfit.percode.service;

import com.dfit.percode.mapper.MenuModuleMapper;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.vo.MenuTreeResponseVO;
import com.dfit.percode.vo.ModuleInfoVO;
import com.dfit.percode.vo.ModuleMenuTreeResponseVO;
import com.dfit.percode.vo.UserPermissionsResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 超级管理员权限查询服务
 * 专门为超级管理员提供全量权限查询服务
 * 直接查询数据库中所有可用的菜单和数据权限，不依赖角色权限关联
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class SuperAdminPermissionService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private MenuModuleMapper menuModuleMapper;

    /**
     * 获取超级管理员的完整权限数据
     * 返回系统中所有可用的菜单和数据权限
     *
     * @param userId 超级管理员用户ID
     * @param moduleIdentifier 模块标识符，可选
     * @return 完整的权限数据
     */
    public UserPermissionsResponseVO getSuperAdminPermissions(Long userId, String moduleIdentifier) {
        log.info("开始查询超级管理员权限，用户ID: {}, 模块标识: {}", userId, moduleIdentifier);
        long startTime = System.currentTimeMillis();

        try {
            // 使用新的模块级方法获取权限，然后转换为原有格式
            List<ModuleMenuTreeResponseVO> moduleMenus = getSuperAdminMenusWithModules(moduleIdentifier);

            // 转换为原有的UserPermissionsResponseVO格式
            UserPermissionsResponseVO result = convertModuleMenusToUserPermissions(userId, moduleMenus);

            // 查询按钮权限（单独处理）
            List<Map<String, Object>> allMenus = getAllMenuPermissions(moduleIdentifier);
            List<UserPermissionsResponseVO.ButtonPermissionVO> buttons = new ArrayList<>();

            for (Map<String, Object> menu : allMenus) {
                Integer menuType = (Integer) menu.get("menuType");
                if (menuType != null && menuType == 3) {
                    // 按钮权限 (menuType = 3)
                    UserPermissionsResponseVO.ButtonPermissionVO button = new UserPermissionsResponseVO.ButtonPermissionVO();
                    button.setId((String) menu.get("id"));
                    button.setName((String) menu.get("name"));
                    button.setPermissionIdentifier((String) menu.get("permissionIdentifier"));
                    buttons.add(button);
                }
            }

            result.getPermissions().setButtons(buttons);

            long endTime = System.currentTimeMillis();
            log.info("超级管理员权限查询完成，用户ID: {}, 根菜单数量: {}, 按钮数量: {}, 耗时: {}ms",
                    userId, result.getPermissions().getMenus().size(), buttons.size(), (endTime - startTime));

            return result;

        } catch (Exception e) {
            log.error("查询超级管理员权限失败，用户ID: {}", userId, e);
            throw new RuntimeException("查询超级管理员权限失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将模块级菜单结构转换为UserPermissionsResponseVO格式
     * 用于保持向后兼容性
     *
     * @param userId 用户ID
     * @param moduleMenus 模块级菜单列表
     * @return UserPermissionsResponseVO格式的权限数据
     */
    private UserPermissionsResponseVO convertModuleMenusToUserPermissions(Long userId, List<ModuleMenuTreeResponseVO> moduleMenus) {
        List<UserPermissionsResponseVO.MenuPermissionVO> allMenus = new ArrayList<>();

        // 遍历所有模块，提取菜单
        for (ModuleMenuTreeResponseVO module : moduleMenus) {
            if (module.getMenus() != null) {
                for (MenuTreeResponseVO menu : module.getMenus()) {
                    convertMenuTreeToUserPermissionMenu(menu, allMenus);
                }
            }
        }

        // 构建返回结果
        UserPermissionsResponseVO result = new UserPermissionsResponseVO();
        result.setUserid(userId);

        UserPermissionsResponseVO.PermissionsData permissions = new UserPermissionsResponseVO.PermissionsData();
        permissions.setMenus(allMenus);
        result.setPermissions(permissions);

        return result;
    }

    /**
     * 递归转换MenuTreeResponseVO为UserPermissionsResponseVO.MenuPermissionVO
     * 保持树形结构，正确设置children字段
     *
     * @param menuTree 菜单树节点
     * @param result 结果列表
     */
    private void convertMenuTreeToUserPermissionMenu(MenuTreeResponseVO menuTree, List<UserPermissionsResponseVO.MenuPermissionVO> result) {
        // 转换当前节点
        UserPermissionsResponseVO.MenuPermissionVO menuVO = new UserPermissionsResponseVO.MenuPermissionVO();
        menuVO.setId(menuTree.getId().toString());
        menuVO.setName(menuTree.getName());
        menuVO.setPreId(menuTree.getPreId().toString());
        menuVO.setMenuType(menuTree.getMenuType());
        menuVO.setRouteAddress(menuTree.getRouteAddress());
        menuVO.setComponentPath(menuTree.getComponentPath());
        menuVO.setPermissionIdentifier(menuTree.getPermissionIdentifier());

        log.debug("转换菜单节点: ID={}, Name={}, 子菜单数量={}",
                menuTree.getId(), menuTree.getName(),
                menuTree.getChildren() != null ? menuTree.getChildren().size() : 0);

        // 处理子菜单 - 保持树形结构
        if (menuTree.getChildren() != null && !menuTree.getChildren().isEmpty()) {
            List<UserPermissionsResponseVO.MenuPermissionVO> children = new ArrayList<>();
            for (MenuTreeResponseVO child : menuTree.getChildren()) {
                convertMenuTreeToUserPermissionMenu(child, children);  // 递归到children列表
            }
            menuVO.setChildren(children);
            log.debug("菜单 {} 设置了 {} 个子菜单", menuTree.getName(), children.size());
        } else {
            // 确保children字段不为null
            menuVO.setChildren(new ArrayList<>());
            log.debug("菜单 {} 没有子菜单，设置空列表", menuTree.getName());
        }

        result.add(menuVO);
    }

    /**
     * 查询所有可用的菜单权限
     * 直接查询数据库，不依赖角色权限关联
     *
     * @param moduleIdentifier 模块标识符，可选
     * @return 所有可用的菜单列表
     */
    private List<Map<String, Object>> getAllMenuPermissions(String moduleIdentifier) {
        // 使用自定义SQL查询所有未删除且未禁用的菜单
        return userMapper.findAllMenuPermissions(moduleIdentifier);
    }

    /**
     * 构建菜单树形结构
     * 在内存中构建父子关系，避免递归数据库查询
     *
     * @param menus 菜单列表
     * @return 菜单树形结构
     */
    private List<UserPermissionsResponseVO.MenuPermissionVO> buildMenuTree(List<UserPermissionsResponseVO.MenuPermissionVO> menus) {
        // 构建ID映射和父子关系映射
        Map<String, UserPermissionsResponseVO.MenuPermissionVO> menuMap = new HashMap<>();
        Map<String, List<UserPermissionsResponseVO.MenuPermissionVO>> parentChildMap = new HashMap<>();

        // 第1步：构建映射关系
        for (UserPermissionsResponseVO.MenuPermissionVO menu : menus) {
            menuMap.put(menu.getId(), menu);

            String parentId = menu.getPreId();
            if (parentId == null || "null".equals(parentId)) {
                parentId = "0";
            }
            parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
        }

        // 第2步：找出根节点（preId = 0 或 null）
        List<UserPermissionsResponseVO.MenuPermissionVO> rootMenus = parentChildMap.get("0");
        if (rootMenus == null) {
            rootMenus = new ArrayList<>();
        }

        // 第3步：递归构建树形结构
        for (UserPermissionsResponseVO.MenuPermissionVO rootMenu : rootMenus) {
            buildMenuTreeRecursive(rootMenu, parentChildMap);
        }

        return rootMenus;
    }

    /**
     * 递归构建菜单树形结构
     *
     * @param parentMenu     父菜单
     * @param parentChildMap 父子关系映射
     */
    private void buildMenuTreeRecursive(UserPermissionsResponseVO.MenuPermissionVO parentMenu,
                                        Map<String, List<UserPermissionsResponseVO.MenuPermissionVO>> parentChildMap) {
        List<UserPermissionsResponseVO.MenuPermissionVO> children = parentChildMap.get(parentMenu.getId());
        if (children != null && !children.isEmpty()) {
            parentMenu.setChildren(children);
            // 递归处理子菜单
            for (UserPermissionsResponseVO.MenuPermissionVO child : children) {
                buildMenuTreeRecursive(child, parentChildMap);
            }
        }
    }

    /**
     * 获取超级管理员的模块级菜单权限
     * 返回按模块分组的菜单树结构，与MenuModuleController的getMenus接口格式一致
     *
     * @param moduleIdentifier 模块标识符，可选
     * @return 模块级菜单树结构数据
     */
    public List<ModuleMenuTreeResponseVO> getSuperAdminMenusWithModules(String moduleIdentifier) {
        log.info("开始查询超级管理员模块级菜单权限，模块标识: {}", moduleIdentifier);
        long startTime = System.currentTimeMillis();

        try {
            List<ModuleMenuTreeResponseVO> result = new ArrayList<>();

            // 第1步：查询所有模块
            log.debug("第1步：查询所有模块");
            List<ModuleInfoVO> allModules = menuModuleMapper.findAllModules();
            log.info("查询到模块总数: {}", allModules.size());

            // 第2步：为每个模块构建菜单树
            for (ModuleInfoVO module : allModules) {
                // 如果指定了模块标识，只处理该模块
                if (moduleIdentifier != null && !moduleIdentifier.isEmpty()
                    && !module.getModuleIdentifier().equals(moduleIdentifier)) {
                    continue;
                }

                log.debug("处理模块: {} - {}", module.getModuleIdentifier(), module.getModuleName());

                // 第3步：查询该模块下的所有菜单（超级管理员看到所有菜单）
                List<Map<String, Object>> allMenus = userMapper.findAllMenuPermissions(module.getModuleIdentifier());

                if (!allMenus.isEmpty()) {
                    // 第4步：转换为MenuTreeResponseVO格式
                    List<MenuTreeResponseVO> menuVOs = new ArrayList<>();
                    for (Map<String, Object> menu : allMenus) {
                        // 只处理菜单类型（menuType = 1 或 2），排除按钮（menuType = 3）
                        Integer menuType = (Integer) menu.get("menuType");
                        if (menuType != null && menuType != 3) {
                            MenuTreeResponseVO menuVO = new MenuTreeResponseVO();
                            menuVO.setId(Long.valueOf(menu.get("id").toString()));
                            menuVO.setName((String) menu.get("name"));
                            menuVO.setPreId(Long.valueOf(menu.get("preId").toString()));
                            menuVO.setModuleIdentifier((String) menu.get("moduleIdentifier"));
                            menuVO.setOrderInfo((Integer) menu.get("orderInfo"));
                            menuVO.setIsDisable((Boolean) menu.get("isDisable"));
                            menuVO.setMenuType(menuType);
                            menuVO.setRouteAddress((String) menu.get("routeAddress"));
                            menuVO.setComponentPath((String) menu.get("componentPath"));
                            menuVO.setPermissionIdentifier((String) menu.get("permissionIdentifier"));
                            menuVO.setRouteParam((String) menu.get("routeParam"));
                            menuVO.setPreName((String) menu.get("preName"));
                            menuVO.setCreateTime((String) menu.get("createTime"));
                            menuVOs.add(menuVO);
                        }
                    }

                    // 第5步：在内存中构建父子关系映射
                    Map<Long, List<MenuTreeResponseVO>> parentChildMap = new HashMap<>();
                    for (MenuTreeResponseVO menu : menuVOs) {
                        Long parentId = menu.getPreId();
                        parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
                    }

                    // 第6步：找出根节点（pre_id = 0）
                    List<MenuTreeResponseVO> rootMenus = parentChildMap.get(0L);
                    if (rootMenus == null) {
                        rootMenus = new ArrayList<>();
                    }

                    // 第7步：构建树形结构
                    for (MenuTreeResponseVO rootMenu : rootMenus) {
                        buildOptimizedMenuTree(rootMenu, parentChildMap);
                    }

                    // 第8步：创建模块包装器
                    ModuleMenuTreeResponseVO moduleWrapper = ModuleMenuTreeResponseVO.create(
                        module.getModuleIdentifier(),
                        module.getModuleName(),
                        module.getOrderInfo(),
                        rootMenus
                    );

                    result.add(moduleWrapper);
                    log.debug("模块 {} 处理完成，菜单数量: {}", module.getModuleName(), rootMenus.size());
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("超级管理员模块级菜单权限查询完成，模块数量: {}，耗时: {}ms",
                    result.size(), (endTime - startTime));

            return result;

        } catch (Exception e) {
            log.error("查询超级管理员模块级菜单权限失败", e);
            throw new RuntimeException("查询超级管理员模块级菜单权限失败: " + e.getMessage(), e);
        }
    }

    /**
     * 优化的递归构建菜单树形结构（内存操作，无数据库查询）
     * 复用MenuModuleServiceImpl的实现逻辑
     *
     * @param parentMenu 父菜单节点
     * @param parentChildMap 父子关系映射
     */
    private void buildOptimizedMenuTree(MenuTreeResponseVO parentMenu,
                                       Map<Long, List<MenuTreeResponseVO>> parentChildMap) {
        // 获取子菜单列表
        List<MenuTreeResponseVO> children = parentChildMap.get(parentMenu.getId());

        if (children != null && !children.isEmpty()) {
            // 递归构建子菜单树
            for (MenuTreeResponseVO child : children) {
                buildOptimizedMenuTree(child, parentChildMap);
            }
            parentMenu.setChildren(children);
        }
    }

    /**
     * 获取所有可用的数据权限
     * 为将来扩展数据权限功能预留
     *
     * @param moduleIdentifier 模块标识符，可选
     * @return 所有可用的数据权限列表
     */
    public List<Map<String, Object>> getAllDataPermissions(String moduleIdentifier) {
        log.info("查询所有数据权限，模块标识: {}", moduleIdentifier);

        try {
            // TODO: 实现数据权限查询逻辑
            // 这里可以添加数据权限的查询逻辑
            List<Map<String, Object>> dataPermissions = new ArrayList<>();

            log.info("查询到数据权限数量: {}", dataPermissions.size());
            return dataPermissions;

        } catch (Exception e) {
            log.error("查询数据权限失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 创建空权限数据
     * 用于异常情况下的默认返回
     *
     * @param userId 用户ID
     * @return 空权限数据
     */
    private UserPermissionsResponseVO createEmptyPermissions(Long userId) {
        UserPermissionsResponseVO result = new UserPermissionsResponseVO();
        result.setUserid(userId);

        UserPermissionsResponseVO.PermissionsData permissions = new UserPermissionsResponseVO.PermissionsData();
        permissions.setMenus(new ArrayList<>());
        permissions.setButtons(new ArrayList<>());
        result.setPermissions(permissions);

        return result;
    }
}
