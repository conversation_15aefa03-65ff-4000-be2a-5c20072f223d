-- <PERSON><PERSON> Script to Repair Organization Structure

-- Step 1: Clean up existing synchronized data
-- Mark old synchronized records for deletion
UPDATE "public"."t_org_structure" SET "is_del" = true WHERE "data_source" = 2;
-- Delete the marked records
DELETE FROM "public"."t_org_structure" WHERE "data_source" = 2 AND "is_del" = true;

-- Step 2: Insert the corrected organization structure
-- This part will be dynamically generated by the script based on department_sync_test table.
-- Below is a sample of what the generated INSERT statements will look like:

-- INSERT INTO "public"."t_org_structure" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) 
-- VALUES ('X47000000', '炼铁事业部', '0', 'f', 2, NOW(), NOW());

-- INSERT INTO "public"."t_org_structure" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) 
-- VALUES ('X62000000', '第二炼铁厂', 'X47000000', 'f', 2, NOW(), NOW());

-- INSERT INTO "public"."t_org_structure" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) 
-- VALUES ('X62070000', '原料车间', 'X62000000', 'f', 2, NOW(), NOW());

-- ... and so on for all other departments.