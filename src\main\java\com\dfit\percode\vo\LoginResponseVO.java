package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录响应VO类
 * 用于用户登录接口的响应数据
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "LoginResponseVO", description = "用户登录响应数据")
public class LoginResponseVO {

    @ApiModelProperty(value = "JWT Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    @ApiModelProperty(value = "用户ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userid;

    @ApiModelProperty(value = "用户姓名", example = "管理员")
    private String username;

    @ApiModelProperty(value = "Token过期时间", example = "2024-12-31 23:59:59")
    private String expireTime;
}