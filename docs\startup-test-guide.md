# 应用启动和数据同步测试指南

## 📋 **启动前准备**

### **1. 数据库扩展**
确保已执行数据库扩展SQL：
```sql
-- 使用旧版PostgreSQL兼容版本（推荐）
\i docs/database-extension-postgresql-old.sql
```

### **2. 检查配置**
确认`application.yml`中的外部系统配置：
```yaml
external:
  system:
    base-url: http://localhost:8080  # 外部系统地址
    api:
      departments: /api/data/departments
      employees: /api/data/employees
    timeout:
      connect: 5000
      read: 30000
```

## 🚀 **启动应用**

### **启动命令**
```bash
# 使用开发环境配置启动
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### **预期结果**
应用应该能够正常启动，不再出现`SnowflakeIdGenerator` Bean找不到的错误。

## 🧪 **数据同步功能测试**

### **1. 测试外部系统连接**
```http
GET http://localhost:8080/sync/test-connection
Content-Type: application/json
```

**预期响应**：
```json
{
  "success": true,
  "message": "外部系统连接测试成功"
}
```

### **2. 测试完整数据同步**
```http
POST http://localhost:8080/sync/full
Content-Type: application/json

{}
```

**预期响应**：
```json
{
  "success": true,
  "message": "完整数据同步执行成功",
  "data": null
}
```

### **3. 测试部门同步**
```http
POST http://localhost:8080/sync/departments
Content-Type: application/json

{}
```

### **4. 测试员工同步**
```http
POST http://localhost:8080/sync/employees
Content-Type: application/json

{}
```

### **5. 获取同步状态**
```http
GET http://localhost:8080/sync/status
Content-Type: application/json
```

## 🔍 **验证同步结果**

### **1. 检查部门数据**
```sql
-- 查看同步的部门数据
SELECT id, organ_name, org_code, sync_status, last_sync_time 
FROM t_org_structure 
WHERE sync_status = 'SYNCED' 
ORDER BY last_sync_time DESC;

-- 查看部门子表数据
SELECT id, dept_id, guid, source_system, sync_status 
FROM t_department_child 
WHERE sync_status = 'SYNCED';
```

### **2. 检查员工数据**
```sql
-- 查看同步的员工数据
SELECT id, user_name, employee_code, mdm_id, sync_status, last_sync_time 
FROM t_user 
WHERE sync_status = 'SYNCED' 
ORDER BY last_sync_time DESC;

-- 查看员工岗位数据
SELECT id, user_id, employee_mdm_id, position_code, org_code, is_primary 
FROM t_employee_position 
WHERE sync_status = 'SYNCED';

-- 查看员工职称数据
SELECT id, user_id, employee_mdm_id, title_name, title_type 
FROM t_employee_title 
WHERE sync_status = 'SYNCED';

-- 查看员工系统标识数据
SELECT id, user_id, employee_mdm_id, system_code, login_account 
FROM t_employee_system 
WHERE sync_status = 'SYNCED';
```

### **3. 检查同步日志**
```sql
-- 查看同步日志
SELECT id, sync_type, sync_action, sync_status, error_message, sync_time 
FROM t_sync_log 
ORDER BY sync_time DESC 
LIMIT 20;
```

### **4. 验证部门归属更新**
```sql
-- 检查用户的部门归属是否正确设置
SELECT u.id, u.user_name, u.organ_affiliation, o.organ_name, u.sync_status
FROM t_user u
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
WHERE u.sync_status = 'SYNCED' AND u.organ_affiliation IS NOT NULL;
```

## ⚠️ **常见问题排查**

### **1. 外部系统连接失败**
**错误**：连接超时或404错误
**解决**：
- 检查外部系统是否启动
- 确认`base-url`配置是否正确
- 检查网络连接

### **2. 数据同步失败**
**错误**：同步过程中出现异常
**解决**：
- 查看应用日志：`logs/web_error.log`
- 检查数据库连接
- 验证表结构是否正确创建

### **3. 部门归属未更新**
**问题**：员工的`organ_affiliation`字段为空
**解决**：
- 检查员工是否有主岗位（`is_primary='1'`）
- 验证岗位的`org_code`是否能找到对应部门
- 手动执行部门归属更新

### **4. ID生成错误**
**错误**：SnowflakeIdGenerator相关错误
**解决**：
- 确认`SyncConfig`中的Bean配置正确
- 检查是否有ID冲突

## 📊 **性能监控**

### **同步性能指标**
- 部门同步速度：预期每秒处理100+部门
- 员工同步速度：预期每秒处理50+员工
- 内存使用：监控同步过程中的内存占用

### **监控SQL**
```sql
-- 统计同步数据量
SELECT 
    sync_type,
    sync_status,
    COUNT(*) as count,
    MAX(sync_time) as last_sync
FROM t_sync_log 
GROUP BY sync_type, sync_status;
```

## ✅ **测试完成检查清单**

- [ ] 应用正常启动，无Bean找不到错误
- [ ] 外部系统连接测试成功
- [ ] 部门数据同步成功
- [ ] 员工数据同步成功
- [ ] 员工扩展数据（岗位、职称、系统标识）同步成功
- [ ] 员工部门归属正确更新
- [ ] 同步日志记录完整
- [ ] 数据库表结构正确
- [ ] 索引创建成功
- [ ] 无数据丢失或重复

完成以上检查后，数据同步功能就可以正常使用了！
