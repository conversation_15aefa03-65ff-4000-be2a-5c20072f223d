package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 模块菜单树响应VO
 * 用于返回按模块分组的菜单树结构
 * 外层为模块信息，内层保持现有的菜单树结构不变
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "ModuleMenuTreeResponseVO对象", description = "模块菜单树响应")
public class ModuleMenuTreeResponseVO {

    @ApiModelProperty("模块标识符")
    private String moduleIdentifier;

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("模块排序序号")
    private Integer orderInfo;

    @ApiModelProperty("模块下的菜单列表")
    private List<MenuTreeResponseVO> menus;

    /**
     * 创建模块菜单树对象
     *
     * @param moduleIdentifier 模块标识符
     * @param moduleName 模块名称
     * @param orderInfo 排序序号
     * @param menus 菜单列表
     * @return 模块菜单树对象
     */
    public static ModuleMenuTreeResponseVO create(String moduleIdentifier, String moduleName, Integer orderInfo, List<MenuTreeResponseVO> menus) {
        ModuleMenuTreeResponseVO moduleTree = new ModuleMenuTreeResponseVO();
        moduleTree.setModuleIdentifier(moduleIdentifier);
        moduleTree.setModuleName(moduleName);
        moduleTree.setOrderInfo(orderInfo);
        moduleTree.setMenus(menus);
        return moduleTree;
    }

    /**
     * 获取模块下菜单数量
     *
     * @return 菜单数量
     */
    public int getMenuCount() {
        return menus != null ? menus.size() : 0;
    }

    /**
     * 判断模块是否有菜单
     *
     * @return true表示有菜单，false表示无菜单
     */
    public boolean hasMenus() {
        return menus != null && !menus.isEmpty();
    }
}
