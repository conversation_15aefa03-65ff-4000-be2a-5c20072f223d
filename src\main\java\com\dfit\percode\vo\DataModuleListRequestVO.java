package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询数据模块列表请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataModuleListRequestVO", description = "查询数据模块列表请求参数")
public class DataModuleListRequestVO {
    
    @ApiModelProperty(value = "页码", required = false, example = "1")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小", required = false, example = "10")
    private Integer pageSize;
}
