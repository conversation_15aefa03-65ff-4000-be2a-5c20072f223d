package com.dfit.percode.service;

import com.dfit.percode.entity.TDataModule;
import com.dfit.percode.vo.AddDataModuleRequestVO;
import com.dfit.percode.vo.DataModuleListRequestVO;
import com.dfit.percode.vo.DataModuleListResponseVO;
import com.dfit.percode.vo.DeleteDataModuleRequestVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 数据权限模块信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface ITDataModuleService extends IService<TDataModule> {

    /**
     * 获取数据模块列表（原有方法）
     * @return 数据模块列表
     */
    List<TDataModule> dataModule();

    /**
     * 分页查询数据模块列表
     * @param request 查询请求参数
     * @return 数据模块列表
     */
    List<DataModuleListResponseVO> getDataModuleList(DataModuleListRequestVO request);

    /**
     * 获取数据模块总数
     * @param request 查询请求参数
     * @return 总数
     */
    Integer getDataModuleTotal(DataModuleListRequestVO request);

    /**
     * 新增数据模块
     * @param request 新增请求参数
     */
    void addDataModule(AddDataModuleRequestVO request);

    /**
     * 删除数据模块
     * @param request 删除请求参数
     */
    void deleteDataModule(DeleteDataModuleRequestVO request);

}
