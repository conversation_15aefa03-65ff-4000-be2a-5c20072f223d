/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 18/06/2025 16:42:42
*/


-- ----------------------------
-- Sequence structure for t_data_operate_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."t_data_operate_id_seq";
CREATE SEQUENCE "public"."t_data_operate_id_seq"
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Table structure for t_data_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_module";
CREATE TABLE "public"."t_data_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_data_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_data_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_data_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_data_module"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_data_module" IS '数据权限模块信息表';

-- ----------------------------
-- Records of t_data_module
-- ----------------------------
INSERT INTO "public"."t_data_module" VALUES (10001, '员工数据模块', 'employee_data_module', 1, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10002, '部门数据模块', 'department_data_module', 2, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10003, '项目数据模块', 'project_data_module', 3, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10004, '客户数据模块', 'customer_data_module', 4, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10005, '合同数据模块', 'contract_data_module', 5, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10006, '报表数据模块', 'report_data_module', 6, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (1932720361229651968, 'test', 'aaa', 11, 'f', '2025-06-11 16:43:36.965141', '2025-06-11 16:43:36.965141');
INSERT INTO "public"."t_data_module" VALUES (1935155973312352256, 'sdf', 'sdf', 1, 'f', '2025-06-18 10:01:52.140474', '2025-06-18 10:01:52.140474');

-- ----------------------------
-- Table structure for t_data_operate
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_operate";
CREATE TABLE "public"."t_data_operate" (
  "id" int8 NOT NULL DEFAULT nextval('t_data_operate_id_seq'::regclass),
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int2,
  "operate_type" int8,
  "is_del" bool DEFAULT false,
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_operate"."module_identifier" IS '模块标识符，与系统模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_operate"."data_type" IS '数据类型，1为条';
COMMENT ON COLUMN "public"."t_data_operate"."operate_type" IS '操作类型，1为新增2为修改3为删除';
COMMENT ON COLUMN "public"."t_data_operate"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_operate"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_data_operate"."modify_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_data_operate"."data_identifier" IS '数据标识,与数据权限管理列表中data_identifier一致';
COMMENT ON TABLE "public"."t_data_operate" IS '数据操作表';

-- ----------------------------
-- Records of t_data_operate
-- ----------------------------
INSERT INTO "public"."t_data_operate" VALUES (1934509720429666305, 'department_data_module', 1, 1, 'f', '2025-06-16 15:13:53.434492', '2025-06-16 15:13:53.434492', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509720840708097, 'department_data_module', 1, 2, 'f', '2025-06-16 15:13:53.571312', '2025-06-16 15:13:53.571312', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509721230778370, 'department_data_module', 1, 3, 'f', '2025-06-16 15:13:53.661475', '2025-06-16 15:13:53.661475', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509721620848641, 'department_data_module', 1, 4, 'f', '2025-06-16 15:13:53.752497', '2025-06-16 15:13:53.752497', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934519126735257602, 'employee_data_module', 1, 3, 'f', '2025-06-16 15:51:16.097529', '2025-06-16 15:51:16.097536', 'employee_basic_info');
INSERT INTO "public"."t_data_operate" VALUES (1934608862434934786, 'project_data_module', 1, 4, 'f', '2025-06-16 21:47:50.753765', '2025-06-16 21:47:50.753787', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935156147619151874, 'employee_data_module', 1, 2, 'f', '2025-06-18 10:02:33.710514', '2025-06-18 10:02:33.710524', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935156147757563906, 'employee_data_module', 1, 3, 'f', '2025-06-18 10:02:33.746209', '2025-06-18 10:02:33.746215', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935156147887587330, 'employee_data_module', 1, 4, 'f', '2025-06-18 10:02:33.776999', '2025-06-18 10:02:33.777006', NULL);

-- ----------------------------
-- Table structure for t_data_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_permission";
CREATE TABLE "public"."t_data_permission" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int4,
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_permission"."name" IS '名称';
COMMENT ON COLUMN "public"."t_data_permission"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_data_permission"."module_identifier" IS '模块标识 与菜单模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_permission"."data_type" IS '数据类型，1检签';
COMMENT ON COLUMN "public"."t_data_permission"."order_info" IS '排序序号，从1开始';
COMMENT ON COLUMN "public"."t_data_permission"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_data_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_permission"."data_identifier" IS '数据标识页面输入或表同步';
COMMENT ON TABLE "public"."t_data_permission" IS '数据权限信息列表';

-- ----------------------------
-- Records of t_data_permission
-- ----------------------------
INSERT INTO "public"."t_data_permission" VALUES (11001, '员工基本信息', 0, 'employee_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11002, '员工薪资信息', 0, 'employee_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_salary_info');
INSERT INTO "public"."t_data_permission" VALUES (11003, '员工绩效数据', 0, 'employee_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_performance_data');
INSERT INTO "public"."t_data_permission" VALUES (11004, '员工考勤记录', 0, 'employee_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_attendance_record');
INSERT INTO "public"."t_data_permission" VALUES (11005, '部门基本信息', 0, 'department_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11006, '部门预算数据', 0, 'department_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_budget_data');
INSERT INTO "public"."t_data_permission" VALUES (11007, '部门人员统计', 0, 'department_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_staff_statistics');
INSERT INTO "public"."t_data_permission" VALUES (11008, '部门绩效指标', 0, 'department_data_module', 1, 4, 't', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_performance_kpi');
INSERT INTO "public"."t_data_permission" VALUES (11009, '项目基本信息', 0, 'project_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11010, '项目进度数据', 0, 'project_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_progress_data');
INSERT INTO "public"."t_data_permission" VALUES (11011, '项目成本数据', 0, 'project_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_cost_data');
INSERT INTO "public"."t_data_permission" VALUES (11012, '项目质量报告', 0, 'project_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_quality_report');
INSERT INTO "public"."t_data_permission" VALUES (11013, '客户基本资料', 0, 'customer_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_basic_profile');
INSERT INTO "public"."t_data_permission" VALUES (11014, '客户联系记录', 0, 'customer_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_contact_record');
INSERT INTO "public"."t_data_permission" VALUES (11015, '客户交易历史', 0, 'customer_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_transaction_history');
INSERT INTO "public"."t_data_permission" VALUES (11016, '客户信用评级', 0, 'customer_data_module', 1, 4, 't', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_credit_rating');
INSERT INTO "public"."t_data_permission" VALUES (11017, '合同基本信息', 0, 'contract_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11018, '合同条款内容', 0, 'contract_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_terms_content');
INSERT INTO "public"."t_data_permission" VALUES (11019, '合同执行状态', 0, 'contract_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_execution_status');
INSERT INTO "public"."t_data_permission" VALUES (11020, '合同财务数据', 0, 'contract_data_module', 1, 4, 't', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_financial_data');
INSERT INTO "public"."t_data_permission" VALUES (11021, '业务运营报表', 0, 'report_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'business_operation_report');
INSERT INTO "public"."t_data_permission" VALUES (11022, '财务分析报表', 0, 'report_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'financial_analysis_report');
INSERT INTO "public"."t_data_permission" VALUES (11023, '人力资源报表', 0, 'report_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'hr_statistics_report');
INSERT INTO "public"."t_data_permission" VALUES (11024, '管理决策报表', 0, 'report_data_module', 1, 4, 't', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'management_decision_report');
INSERT INTO "public"."t_data_permission" VALUES (1932722647272132608, 'vv', NULL, 'employee_data_module', NULL, 1, 't', 't', '2025-06-11 16:52:41.967741', '2025-06-11 16:55:10.592304', 'aaa');

-- ----------------------------
-- Table structure for t_employee_position
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_position";
CREATE TABLE "public"."t_employee_position" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "position_code" varchar(255) COLLATE "pg_catalog"."default",
  "org_code" varchar(255) COLLATE "pg_catalog"."default",
  "department_code" varchar(255) COLLATE "pg_catalog"."default",
  "is_primary" varchar(255) COLLATE "pg_catalog"."default",
  "status" varchar(255) COLLATE "pg_catalog"."default",
  "is_active" varchar(255) COLLATE "pg_catalog"."default",
  "position_detail_code" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_position"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Records of t_employee_position
-- ----------------------------

-- ----------------------------
-- Table structure for t_employee_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_system";
CREATE TABLE "public"."t_employee_system" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "system_code" varchar(255) COLLATE "pg_catalog"."default",
  "system_data_id" varchar(255) COLLATE "pg_catalog"."default",
  "org_code" varchar(255) COLLATE "pg_catalog"."default",
  "department_code" varchar(255) COLLATE "pg_catalog"."default",
  "employee_code" varchar(255) COLLATE "pg_catalog"."default",
  "login_account" varchar(50) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_system"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Records of t_employee_system
-- ----------------------------

-- ----------------------------
-- Table structure for t_employee_title
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_title";
CREATE TABLE "public"."t_employee_title" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "title_code" varchar(255) COLLATE "pg_catalog"."default",
  "title_type" varchar(255) COLLATE "pg_catalog"."default",
  "title_level" varchar(255) COLLATE "pg_catalog"."default",
  "title_name" varchar(255) COLLATE "pg_catalog"."default",
  "status" varchar(255) COLLATE "pg_catalog"."default",
  "title_category" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_title"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Records of t_employee_title
-- ----------------------------

-- ----------------------------
-- Table structure for t_menu_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_menu_module";
CREATE TABLE "public"."t_menu_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_menu_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_menu_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_menu_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_menu_module"."is_del" IS '是否删除';
COMMENT ON TABLE "public"."t_menu_module" IS '菜单模块信息表，用于标识菜单管理列表中新建模块存储信息和标识';

-- ----------------------------
-- Records of t_menu_module
-- ----------------------------
INSERT INTO "public"."t_menu_module" VALUES (8001, '系统管理', 'system_management', 1, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8002, '用户管理', 'user_management', 2, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8003, '权限管理', 'permission_management', 3, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8004, '开发工具', 'development_tools', 4, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8005, '运维监控', 'operations_monitoring', 5, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8006, '数据分析', 'data_analytics', 6, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8007, '财务管理', 'finance_management', 7, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8008, '项目管理', 'project_management', 8, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (1932638701989531648, 'test', 'testzz', NULL, 't', '2025-06-11 11:19:07.887076', '2025-06-11 11:19:47.294778');
INSERT INTO "public"."t_menu_module" VALUES (1935155422495379456, 'sdf', 'asdf', NULL, 'f', '2025-06-18 09:59:40.814591', '2025-06-18 09:59:40.814591');

-- ----------------------------
-- Table structure for t_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_menu_permission";
CREATE TABLE "public"."t_menu_permission" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_disable" bool,
  "menu_type" int4,
  "route_address" varchar(255) COLLATE "pg_catalog"."default",
  "component_path" varchar(255) COLLATE "pg_catalog"."default",
  "permission_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "route_param" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_menu_permission"."name" IS '名称';
COMMENT ON COLUMN "public"."t_menu_permission"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_menu_permission"."module_identifier" IS '模块标识 与菜单模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_menu_permission"."is_disable" IS '是否停用';
COMMENT ON COLUMN "public"."t_menu_permission"."menu_type" IS '菜单类型，1目录 2菜单 3按钮';
COMMENT ON COLUMN "public"."t_menu_permission"."route_address" IS '路由地址';
COMMENT ON COLUMN "public"."t_menu_permission"."component_path" IS '组件路径';
COMMENT ON COLUMN "public"."t_menu_permission"."permission_identifier" IS '权限标识';
COMMENT ON COLUMN "public"."t_menu_permission"."route_param" IS '路由参数，用于动态路由传参';
COMMENT ON COLUMN "public"."t_menu_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_menu_permission" IS '菜单权限管理列表设定';

-- ----------------------------
-- Records of t_menu_permission
-- ----------------------------
INSERT INTO "public"."t_menu_permission" VALUES (9001, '系统管理', 0, 'system_management', 1, 'f', 1, '/system', 'Layout', 'system', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9002, '系统配置', 9001, 'system_management', 1, 'f', 2, '/system/config', 'system/config/index', 'system:config', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9003, '系统日志', 9001, 'system_management', 2, 'f', 2, '/system/log', 'system/log/index', 'system:log', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9004, '配置查看', 9002, 'system_management', 1, 'f', 3, '', '', 'system:config:view', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9005, '配置编辑', 9002, 'system_management', 2, 'f', 3, '', '', 'system:config:edit', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9006, '用户管理', 0, 'user_management', 1, 'f', 1, '/user', 'Layout', 'user', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9007, '用户列表', 9006, 'user_management', 1, 'f', 2, '/user/list', 'user/list/index', 'user:list', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9008, '部门管理', 9006, 'user_management', 2, 'f', 2, '/user/dept', 'user/dept/index', 'user:dept', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9009, '用户新增', 9007, 'user_management', 1, 'f', 3, '', '', 'user:add', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9010, '用户编辑', 9007, 'user_management', 2, 'f', 3, '', '', 'user:edit', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9011, '用户删除', 9007, 'user_management', 3, 'f', 3, '', '', 'user:delete', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9012, '权限管理', 0, 'permission_management', 1, 'f', 1, '/permission', 'Layout', 'permission', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9013, '角色管理', 9012, 'permission_management', 1, 'f', 2, '/permission/role', 'permission/role/index', 'permission:role', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9014, '菜单管理', 9012, 'permission_management', 2, 'f', 2, '/permission/menu', 'permission/menu/index', 'permission:menu', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9015, '数据权限', 9012, 'permission_management', 3, 'f', 2, '/permission/data', 'permission/data/index', 'permission:data', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9016, '开发工具', 0, 'development_tools', 1, 'f', 1, '/dev', 'Layout', 'dev', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9017, '代码生成', 9016, 'development_tools', 1, 'f', 2, '/dev/generator', 'dev/generator/index', 'dev:generator', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9018, 'API文档', 9016, 'development_tools', 2, 'f', 2, '/dev/swagger', 'dev/swagger/index', 'dev:swagger', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9019, '数据分析', 0, 'data_analytics', 1, 'f', 1, '/analytics', 'Layout', 'analytics', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9020, '用户统计', 9019, 'data_analytics', 1, 'f', 2, '/analytics/user', 'analytics/user/index', 'analytics:user', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (9021, '业务报表', 9019, 'data_analytics', 2, 'f', 2, '/analytics/business', 'analytics/business/index', 'analytics:business', 'f', '2025-06-11 01:58:48.75855', '2025-06-11 01:58:48.75855');
INSERT INTO "public"."t_menu_permission" VALUES (1932688441452138496, 'test', 9001, 'system_management', NULL, 't', 1, 'rout', 'comp', 'ad', 't', '2025-06-11 14:36:46.668887', '2025-06-11 14:38:12.349544');
INSERT INTO "public"."t_menu_permission" VALUES (1932688837960667136, 'test2', 9001, 'system_management', NULL, 't', 2, 'rout2', 'comp2', 'ad2', 't', '2025-06-11 14:38:21.204337', '2025-06-11 16:35:47.657063');
INSERT INTO "public"."t_menu_permission" VALUES (1933467732280479744, 'test', 9001, 'system_management', NULL, 't', 1, 'rout', 'comp', 'system', 'f', '2025-06-13 18:13:24.07617', '2025-06-13 18:13:24.07617');
INSERT INTO "public"."t_menu_permission" VALUES (1933470095095500800, 'test', 9001, 'system_management', NULL, 't', 1, 'rout', 'comp', 'system', 'f', '2025-06-13 18:22:47.416313', '2025-06-13 18:22:47.416313');
INSERT INTO "public"."t_menu_permission" VALUES (1933470184102825984, 'test', 9001, 'system_management', NULL, 't', 1, 'rout', 'comp', 'system', 'f', '2025-06-13 18:23:08.640731', '2025-06-13 18:23:08.640731');

-- ----------------------------
-- Table structure for t_org_structure
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_org_structure";
CREATE TABLE "public"."t_org_structure" (
  "id" int8 NOT NULL,
  "organ_name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_org_structure"."organ_name" IS '组织名称';
COMMENT ON COLUMN "public"."t_org_structure"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_org_structure"."order_info" IS '顺序，从1开始';
COMMENT ON COLUMN "public"."t_org_structure"."is_del" IS '是否删除';
COMMENT ON TABLE "public"."t_org_structure" IS '组织架构表';

-- ----------------------------
-- Records of t_org_structure
-- ----------------------------
INSERT INTO "public"."t_org_structure" VALUES (1932979029460258816, 'test', NULL, 1, 'f', '2025-06-12 09:51:28.269639', '2025-06-12 09:51:28.269639');
INSERT INTO "public"."t_org_structure" VALUES (1932981618243407872, 'a', NULL, 1, 'f', '2025-06-12 10:01:45.484875', '2025-06-12 10:01:45.484875');
INSERT INTO "public"."t_org_structure" VALUES (1932981702259511296, 'aa', NULL, 1, 'f', '2025-06-12 10:02:05.522763', '2025-06-12 10:02:05.522763');
INSERT INTO "public"."t_org_structure" VALUES (1932982339693056000, 'avav', NULL, 1, 'f', '2025-06-12 10:04:37.49165', '2025-06-12 10:04:37.49165');
INSERT INTO "public"."t_org_structure" VALUES (5001, '科技集团总公司', 0, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5002, '技术研发中心', 5001, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5003, '产品运营中心', 5001, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5004, '市场营销中心', 5001, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5005, '人力资源中心', 5001, 4, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5006, '财务管理中心', 5001, 5, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5007, '前端开发部', 5002, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5008, '后端开发部', 5002, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5009, '测试质量部', 5002, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5010, '运维安全部', 5002, 4, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5011, '产品设计部', 5003, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5012, '用户运营部', 5003, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');
INSERT INTO "public"."t_org_structure" VALUES (5013, '数据分析部', 5003, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-11 01:58:47.668562');

-- ----------------------------
-- Table structure for t_perm_user_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_perm_user_role";
CREATE TABLE "public"."t_perm_user_role" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "role_id" int8,
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_perm_user_role"."user_id" IS '人员id，对应用户管理表人员id';
COMMENT ON COLUMN "public"."t_perm_user_role"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_perm_user_role"."order_info" IS '序号，一个角色对多个人员，人员所在的序号';
COMMENT ON COLUMN "public"."t_perm_user_role"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_perm_user_role" IS '人员角色对应表';

-- ----------------------------
-- Records of t_perm_user_role
-- ----------------------------
INSERT INTO "public"."t_perm_user_role" VALUES (1934508304562786304, 6003, 7001, 1, 't', '2025-06-16 15:08:15.874843', '2025-06-16 15:08:38.144868');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508322636042240, 6003, 7001, 1, 't', '2025-06-16 15:08:20.183787', '2025-06-16 15:08:38.144868');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508370971201536, 6003, 7001, 1, 't', '2025-06-16 15:08:31.707694', '2025-06-16 15:08:38.144868');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508411450429440, 6004, 7001, 1, 't', '2025-06-16 15:08:41.358862', '2025-06-16 15:09:00.980015');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508447143956480, 6004, 7001, 1, 't', '2025-06-16 15:08:49.868645', '2025-06-16 15:09:00.980015');
INSERT INTO "public"."t_perm_user_role" VALUES (1934773648699297792, 1934773648393113600, 7004, 1, 't', '2025-06-17 08:42:38.774829', '2025-06-17 08:43:01.147842');
INSERT INTO "public"."t_perm_user_role" VALUES (1933415880650264576, 6012, 7001, 1, 'f', '2025-06-13 14:47:21.659123', '2025-06-17 08:50:23.495781');
INSERT INTO "public"."t_perm_user_role" VALUES (1933415974887886848, 6012, 7001, 1, 'f', '2025-06-13 14:47:44.128464', '2025-06-17 08:50:23.495781');
INSERT INTO "public"."t_perm_user_role" VALUES (1933416471740944384, 6012, 7001, 1, 'f', '2025-06-13 14:49:42.59163', '2025-06-17 08:50:23.495781');
INSERT INTO "public"."t_perm_user_role" VALUES (1934773943655337984, 6012, 7001, 1, 'f', '2025-06-17 08:43:49.170388', '2025-06-17 08:50:23.495781');
INSERT INTO "public"."t_perm_user_role" VALUES (12005, 6005, 7003, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-11 01:58:51.469438');
INSERT INTO "public"."t_perm_user_role" VALUES (12007, 6007, 7005, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-11 01:58:51.469438');
INSERT INTO "public"."t_perm_user_role" VALUES (12008, 6008, 7004, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-11 01:58:51.469438');
INSERT INTO "public"."t_perm_user_role" VALUES (12010, 6010, 7007, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-11 01:58:51.469438');
INSERT INTO "public"."t_perm_user_role" VALUES (1932639748946530304, 1932639748158001152, 7001, 1, 't', '2025-06-11 11:23:16.875571', '2025-06-11 18:11:21.214319');
INSERT INTO "public"."t_perm_user_role" VALUES (12002, 6002, 7002, 1, 't', '2025-06-11 01:58:51.469438', '2025-06-11 18:12:50.842345');
INSERT INTO "public"."t_perm_user_role" VALUES (12011, 6002, 7004, 2, 't', '2025-06-11 01:58:51.469438', '2025-06-11 18:12:50.842345');
INSERT INTO "public"."t_perm_user_role" VALUES (1932742817147392000, 6002, 7006, 1, 't', '2025-06-11 18:12:50.842345', '2025-06-11 18:23:35.418619');
INSERT INTO "public"."t_perm_user_role" VALUES (1932746056165101568, 6008, 7002, 1, 'f', '2025-06-11 18:25:43.08961', '2025-06-11 18:25:43.08961');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148279058796544, 1935148278794555392, 7009, 1, 't', '2025-06-18 09:31:17.625514', '2025-06-18 09:33:51.853054');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148279323037696, 1935148278794555392, 7006, 2, 't', '2025-06-18 09:31:17.625514', '2025-06-18 09:33:51.853054');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148925925330944, 1935148278794555392, 7006, 1, 'f', '2025-06-18 09:33:51.853054', '2025-06-18 09:33:51.853054');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148926038577152, 1935148278794555392, 7009, 2, 'f', '2025-06-18 09:33:51.853054', '2025-06-18 09:33:51.853054');
INSERT INTO "public"."t_perm_user_role" VALUES (12004, 6004, 7003, 1, 't', '2025-06-11 01:58:51.469438', '2025-06-18 09:34:08.999715');
INSERT INTO "public"."t_perm_user_role" VALUES (1932742209195610112, 6004, 7007, 1, 't', '2025-06-11 18:10:25.900364', '2025-06-18 09:34:08.999715');
INSERT INTO "public"."t_perm_user_role" VALUES (12012, 6012, 7009, 1, 't', '2025-06-11 01:58:51.469438', '2025-06-13 14:47:44.128464');
INSERT INTO "public"."t_perm_user_role" VALUES (1933415975085019136, 6012, 7009, 2, 't', '2025-06-13 14:47:44.128464', '2025-06-13 14:49:42.59163');
INSERT INTO "public"."t_perm_user_role" VALUES (1933416471938076672, 6012, 7009, 2, 'f', '2025-06-13 14:49:42.59163', '2025-06-13 14:49:42.59163');
INSERT INTO "public"."t_perm_user_role" VALUES (1934421435560890368, 6012, 7006, 1, 'f', '2025-06-16 09:23:04.646907', '2025-06-16 09:23:04.646907');
INSERT INTO "public"."t_perm_user_role" VALUES (12003, 6003, 7003, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-16 11:08:18.267787');
INSERT INTO "public"."t_perm_user_role" VALUES (1932745554866081792, 6003, 7003, 1, 'f', '2025-06-11 18:23:43.569919', '2025-06-16 11:08:18.267787');
INSERT INTO "public"."t_perm_user_role" VALUES (1934460205442142208, 1934460205056266240, 7008, 1, 't', '2025-06-16 11:57:08.052351', '2025-06-16 11:58:08.137016');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148997819895808, 6004, 7003, 1, 'f', '2025-06-18 09:34:08.999715', '2025-06-18 09:34:08.999715');
INSERT INTO "public"."t_perm_user_role" VALUES (1935148997933142016, 6004, 7007, 2, 'f', '2025-06-18 09:34:08.999715', '2025-06-18 09:34:08.999715');
INSERT INTO "public"."t_perm_user_role" VALUES (1934460457230405632, 1934460205056266240, 7008, 1, 't', '2025-06-16 11:58:08.137016', '2025-06-18 09:34:28.578154');
INSERT INTO "public"."t_perm_user_role" VALUES (1934507995954286592, 1934460205056266240, 7001, 1, 't', '2025-06-16 15:07:02.29497', '2025-06-16 15:07:56.094518');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508021627621376, 1934460205056266240, 7001, 1, 't', '2025-06-16 15:07:08.415918', '2025-06-16 15:07:56.094518');
INSERT INTO "public"."t_perm_user_role" VALUES (1934508125545697280, 1934460205056266240, 7001, 1, 't', '2025-06-16 15:07:33.193005', '2025-06-16 15:07:56.094518');
INSERT INTO "public"."t_perm_user_role" VALUES (12001, 6001, 7001, 1, 'f', '2025-06-11 01:58:51.469438', '2025-06-16 15:08:02.014031');
INSERT INTO "public"."t_perm_user_role" VALUES (1932640919031517184, 6001, 7001, 1, 'f', '2025-06-11 11:27:55.855035', '2025-06-16 15:08:02.014031');
INSERT INTO "public"."t_perm_user_role" VALUES (1932750437040132096, 6001, 7001, 1, 'f', '2025-06-11 18:43:07.600179', '2025-06-16 15:08:02.014031');
INSERT INTO "public"."t_perm_user_role" VALUES (12006, 6006, 7003, 1, 't', '2025-06-11 01:58:51.469438', '2025-06-18 09:34:51.252518');
INSERT INTO "public"."t_perm_user_role" VALUES (1935149175041822720, 6006, 7003, 1, 'f', '2025-06-18 09:34:51.252518', '2025-06-18 09:34:51.252518');
INSERT INTO "public"."t_perm_user_role" VALUES (12009, 6009, 7006, 1, 't', '2025-06-11 01:58:51.469438', '2025-06-18 09:35:48.559354');
INSERT INTO "public"."t_perm_user_role" VALUES (1935149415404802048, 6009, 7006, 1, 'f', '2025-06-18 09:35:48.559354', '2025-06-18 09:35:48.559354');
INSERT INTO "public"."t_perm_user_role" VALUES (1935155281633873920, 1935155281398992896, 7005, 1, 'f', '2025-06-18 09:59:07.177399', '2025-06-18 09:59:07.177399');
INSERT INTO "public"."t_perm_user_role" VALUES (1935155281864560640, 1935155281398992896, 7002, 2, 'f', '2025-06-18 09:59:07.177399', '2025-06-18 09:59:07.177399');
INSERT INTO "public"."t_perm_user_role" VALUES (1934845562604097536, 1934845562293719040, 7008, 1, 't', '2025-06-17 13:28:24.422316', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1934845562876727296, 1934845562293719040, 7009, 2, 't', '2025-06-17 13:28:24.422316', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1934845563132579840, 1934845562293719040, 7005, 3, 't', '2025-06-17 13:28:24.422316', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1935252518636490752, 1934845562293719040, 7005, 1, 'f', '2025-06-18 16:25:30.250985', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1935252518808457216, 1934845562293719040, 7008, 2, 'f', '2025-06-18 16:25:30.250985', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1935252518972035072, 1934845562293719040, 7009, 3, 'f', '2025-06-18 16:25:30.250985', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_perm_user_role" VALUES (1935149079935979520, 1934460205056266240, 7008, 1, 't', '2025-06-18 09:34:28.578154', '2025-06-18 16:32:37.943775');
INSERT INTO "public"."t_perm_user_role" VALUES (1935254312406093824, 1934460205056266240, 7008, 1, 'f', '2025-06-18 16:32:37.943775', '2025-06-18 16:32:37.943775');

-- ----------------------------
-- Table structure for t_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_role";
CREATE TABLE "public"."t_role" (
  "id" int8 NOT NULL,
  "role_name" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_role"."id" IS '角色id';
COMMENT ON COLUMN "public"."t_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."t_role"."order_info" IS '顺序';
COMMENT ON COLUMN "public"."t_role"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_role"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_role" IS '角色信息表';

-- ----------------------------
-- Records of t_role
-- ----------------------------
INSERT INTO "public"."t_role" VALUES (7001, '超级管理员', 1, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7002, '系统管理员', 2, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7003, '部门经理', 3, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7004, '高级开发工程师', 4, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7005, '普通开发工程师', 5, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7006, '测试工程师', 6, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7007, '运维工程师', 7, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7008, '产品经理', 8, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7009, '数据分析师', 9, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (7010, '财务专员', 10, 't', 'f', '2025-06-11 01:58:48.189059', '2025-06-11 01:58:48.189059');
INSERT INTO "public"."t_role" VALUES (1932739129100079104, 'test', 999, 'f', 't', '2025-06-11 17:58:11.591544', '2025-06-11 17:58:36.352424');
INSERT INTO "public"."t_role" VALUES (1934611306124546048, 'test', 999, 't', 'f', '2025-06-16 21:57:33.377784', '2025-06-16 21:57:33.377784');

-- ----------------------------
-- Table structure for t_roles_data_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_roles_data_permission";
CREATE TABLE "public"."t_roles_data_permission" (
  "id" int8 NOT NULL,
  "role_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int4,
  "data_id" int8,
  "operate_type" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_roles_data_permission"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_roles_data_permission"."module_identifier" IS '模块标识符';
COMMENT ON COLUMN "public"."t_roles_data_permission"."data_type" IS '数据类型，1检签';
COMMENT ON COLUMN "public"."t_roles_data_permission"."data_id" IS '数据id，对应数据权限管理列表中的id';
COMMENT ON COLUMN "public"."t_roles_data_permission"."operate_type" IS '操作类型1与数据权限操作类型中operate_type一致';
COMMENT ON COLUMN "public"."t_roles_data_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_roles_data_permission" IS '角色数据权限对应表';

-- ----------------------------
-- Records of t_roles_data_permission
-- ----------------------------
INSERT INTO "public"."t_roles_data_permission" VALUES (1932739129385291776, 1932739129100079104, 'department_data_module', 1, 11008, 4, 't', '2025-06-11 17:58:11.659462', '2025-06-11 17:58:25.346522');
INSERT INTO "public"."t_roles_data_permission" VALUES (1932739129527898112, 1932739129100079104, 'contract_data_module', 1, 11020, 4, 't', '2025-06-11 17:58:11.659462', '2025-06-11 17:58:25.346522');
INSERT INTO "public"."t_roles_data_permission" VALUES (1932739188109742080, 1932739129100079104, 'department_data_module', 1, 11008, 4, 't', '2025-06-11 17:58:25.660558', '2025-06-11 17:58:36.352424');
INSERT INTO "public"."t_roles_data_permission" VALUES (1932739188243959808, 1932739129100079104, 'contract_data_module', 1, 11020, 4, 't', '2025-06-11 17:58:25.660558', '2025-06-11 17:58:36.352424');
INSERT INTO "public"."t_roles_data_permission" VALUES (1934611306472673280, 1934611306124546048, NULL, 1, NULL, 2, 'f', '2025-06-16 21:57:33.460901', '2025-06-16 21:57:33.460901');
INSERT INTO "public"."t_roles_data_permission" VALUES (1934611306653028352, 1934611306124546048, NULL, 1, NULL, 4, 'f', '2025-06-16 21:57:33.460901', '2025-06-16 21:57:33.460901');
INSERT INTO "public"."t_roles_data_permission" VALUES (1934611306820800512, 1934611306124546048, NULL, 1, NULL, 2, 'f', '2025-06-16 21:57:33.460901', '2025-06-16 21:57:33.460901');
INSERT INTO "public"."t_roles_data_permission" VALUES (14001, 7001, 'employee_data_module', 1, 11001, 1, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14002, 7001, 'employee_data_module', 1, 11001, 2, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14003, 7001, 'employee_data_module', 1, 11001, 3, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14004, 7001, 'employee_data_module', 1, 11001, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14005, 7001, 'employee_data_module', 1, 11002, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14007, 7003, 'department_data_module', 1, 11007, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14008, 7003, 'employee_data_module', 1, 11001, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14009, 7003, 'employee_data_module', 1, 11003, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14010, 7004, 'project_data_module', 1, 11009, 1, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14011, 7004, 'project_data_module', 1, 11009, 2, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14012, 7004, 'project_data_module', 1, 11009, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14013, 7004, 'project_data_module', 1, 11010, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14014, 7009, 'report_data_module', 1, 11021, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14015, 7009, 'report_data_module', 1, 11023, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14016, 7009, 'employee_data_module', 1, 11001, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');
INSERT INTO "public"."t_roles_data_permission" VALUES (14017, 7009, 'department_data_module', 1, 11007, 4, 'f', '2025-06-11 01:58:53.193466', '2025-06-11 01:58:53.193466');

-- ----------------------------
-- Table structure for t_roles_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_roles_menu_permission";
CREATE TABLE "public"."t_roles_menu_permission" (
  "id" int8 NOT NULL,
  "role_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "menu_id" int8,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_roles_menu_permission"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."module_identifier" IS '模块标识符';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."menu_id" IS '菜单id，对应菜单管理列表表中的id';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_roles_menu_permission" IS '角色菜单权限对应表';

-- ----------------------------
-- Records of t_roles_menu_permission
-- ----------------------------
INSERT INTO "public"."t_roles_menu_permission" VALUES (1932739129242685440, 1932739129100079104, '1', 9015, 't', '2025-06-11 17:58:11.625129', '2025-06-11 17:58:25.346522');
INSERT INTO "public"."t_roles_menu_permission" VALUES (1932739187975524352, 1932739129100079104, '1', 9015, 't', '2025-06-11 17:58:25.628837', '2025-06-11 17:58:36.352424');
INSERT INTO "public"."t_roles_menu_permission" VALUES (1934611306296512512, 1934611306124546048, '1', 9018, 'f', '2025-06-16 21:57:33.41809', '2025-06-16 21:57:33.41809');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13001, 7001, 'system_management', 9001, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13002, 7001, 'system_management', 9002, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13003, 7001, 'system_management', 9003, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13004, 7001, 'user_management', 9006, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13005, 7001, 'user_management', 9007, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13006, 7001, 'user_management', 9008, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13007, 7001, 'permission_management', 9012, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13008, 7001, 'permission_management', 9013, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13009, 7001, 'permission_management', 9014, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13010, 7001, 'permission_management', 9015, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13011, 7002, 'user_management', 9006, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13012, 7002, 'user_management', 9007, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13013, 7002, 'user_management', 9008, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13014, 7002, 'permission_management', 9012, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13015, 7002, 'permission_management', 9013, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13016, 7003, 'user_management', 9006, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13017, 7003, 'user_management', 9007, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13018, 7003, 'data_analytics', 9019, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13019, 7003, 'data_analytics', 9020, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13020, 7004, 'development_tools', 9016, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13021, 7004, 'development_tools', 9017, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13022, 7004, 'development_tools', 9018, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13023, 7009, 'data_analytics', 9019, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13024, 7009, 'data_analytics', 9020, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');
INSERT INTO "public"."t_roles_menu_permission" VALUES (13025, 7009, 'data_analytics', 9021, 'f', '2025-06-11 01:58:52.023613', '2025-06-11 01:58:52.023613');

-- ----------------------------
-- Table structure for t_sync_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_sync_log";
CREATE TABLE "public"."t_sync_log" (
  "id" int8 NOT NULL,
  "sync_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "sync_action" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "external_id" varchar(255) COLLATE "pg_catalog"."default",
  "internal_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "error_message" text COLLATE "pg_catalog"."default",
  "sync_time" timestamp(6) DEFAULT now(),
  "external_data" text COLLATE "pg_catalog"."default",
  "processed_data" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of t_sync_log
-- ----------------------------

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_user";
CREATE TABLE "public"."t_user" (
  "id" int8 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "origin_id" varchar(255) COLLATE "pg_catalog"."default",
  "organ_affiliation" int8,
  "account" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "is_disable" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_user"."id" IS '人员信息id';
COMMENT ON COLUMN "public"."t_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."t_user"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."t_user"."origin_id" IS '原始关联id，用于外部系统对接';
COMMENT ON COLUMN "public"."t_user"."organ_affiliation" IS '组织归属，对应组织架构表中的id';
COMMENT ON COLUMN "public"."t_user"."account" IS '账户';
COMMENT ON COLUMN "public"."t_user"."password" IS '密码';
COMMENT ON COLUMN "public"."t_user"."is_disable" IS '是否停用';
COMMENT ON TABLE "public"."t_user" IS '人员信息表，与组织架构绑定';

-- ----------------------------
-- Records of t_user
-- ----------------------------
INSERT INTO "public"."t_user" VALUES (6001, '张总经理', 'f', 'zhang_ceo', 5001, 'zhang001', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 11:27:55.855035');
INSERT INTO "public"."t_user" VALUES (1932639748158001152, '测试用户001', 't', NULL, NULL, '测试用户001', '123456', 'f', '2025-06-11 11:23:17.337668', '2025-06-11 18:11:21.214319');
INSERT INTO "public"."t_user" VALUES (6002, '李技术总监', 't', 'li_cto', NULL, NULL, 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 18:23:35.418619');
INSERT INTO "public"."t_user" VALUES (6003, '王产品总监', 'f', 'wang_cpo', 5003, 'wang.cpo', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-11 18:23:43.569919');
INSERT INTO "public"."t_user" VALUES (6012, '陈实习生', 'f', 'chen_intern', 5013, '123', '123', 't', '2025-06-11 01:58:47.958483', '2025-06-13 14:49:42.59163');
INSERT INTO "public"."t_user" VALUES (1934773648393113600, '测试人员', 't', NULL, 5008, 'admintest', '123456', 'f', '2025-06-17 08:42:38.791092', '2025-06-17 08:43:01.147842');
INSERT INTO "public"."t_user" VALUES (1935148278794555392, 'test', 'f', NULL, 5007, 'test12345', '123456', 't', '2025-06-18 09:31:17.638376', '2025-06-18 09:33:51.853054');
INSERT INTO "public"."t_user" VALUES (6004, '赵前端经理', 'f', 'zhao_fe_mgr', 5007, 'zhao.fe', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-18 09:34:08.999715');
INSERT INTO "public"."t_user" VALUES (6005, '钱后端经理', 'f', 'qian_be_mgr', 5008, 'qian.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 01:58:47.958483');
INSERT INTO "public"."t_user" VALUES (6007, '周前端开发', 'f', 'zhou_fe_dev', 5007, 'zhou.fe', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 01:58:47.958483');
INSERT INTO "public"."t_user" VALUES (6008, '吴后端开发', 'f', 'wu_be_dev', 5008, 'wu.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 01:58:47.958483');
INSERT INTO "public"."t_user" VALUES (6010, '王运维工程师', 'f', 'wang_ops', 5010, 'wang.ops', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 01:58:47.958483');
INSERT INTO "public"."t_user" VALUES (6011, '冯离职员工', 'f', 'feng_former', 5007, 'feng.former', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-11 01:58:47.958483');
INSERT INTO "public"."t_user" VALUES (6006, '孙测试经理', 'f', 'sun_qa_mgr', 5009, 'sun.qa', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-18 09:34:51.252518');
INSERT INTO "public"."t_user" VALUES (6009, '郑测试工程师', 'f', 'zheng_qa', 5009, 'zheng.qa', 'password123', 't', '2025-06-11 01:58:47.958483', '2025-06-18 09:35:48.559354');
INSERT INTO "public"."t_user" VALUES (1935155281398992896, 'sdf', 'f', NULL, NULL, 'asdf', 'asdf', 'f', '2025-06-18 09:59:07.189611', '2025-06-18 09:59:07.189618');
INSERT INTO "public"."t_user" VALUES (1934845562293719040, '徐小青', 'f', NULL, 5011, '徐小青', '123456', 'f', '2025-06-17 13:28:24.400219', '2025-06-18 16:25:30.250985');
INSERT INTO "public"."t_user" VALUES (1934460205056266240, 'testa', 'f', NULL, 5008, 'z', '1', 'f', '2025-06-16 11:57:08.075555', '2025-06-18 16:32:37.943775');

-- ----------------------------
-- Function structure for link_orphan_employee_records
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."link_orphan_employee_records"();
CREATE OR REPLACE FUNCTION "public"."link_orphan_employee_records"()
  RETURNS "pg_catalog"."int4" AS $BODY$
DECLARE
    linked_count INTEGER := 0;
    position_count INTEGER := 0;
    title_count INTEGER := 0;
    system_count INTEGER := 0;
BEGIN
    -- 关联员工岗位表的孤儿记录
    UPDATE t_employee_position
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_position.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_position.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS position_count = ROW_COUNT;

    -- 关联员工职称表的孤儿记录
    UPDATE t_employee_title
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_title.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_title.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS title_count = ROW_COUNT;

    -- 关联员工系统标识表的孤儿记录
    UPDATE t_employee_system
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_system.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_system.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS system_count = ROW_COUNT;

    linked_count := position_count + title_count + system_count;

    -- 记录关联结果
    RAISE NOTICE '孤儿记录关联完成: 岗位表=% 条, 职称表=% 条, 系统表=% 条, 总计=% 条',
        position_count, title_count, system_count, linked_count;

    RETURN linked_count;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- View structure for v_orphan_employee_records
-- ----------------------------
DROP VIEW IF EXISTS "public"."v_orphan_employee_records";
CREATE VIEW "public"."v_orphan_employee_records" AS         (         SELECT 'position'::text AS record_type,
                    t_employee_position.id, t_employee_position.employee_mdm_id,
                    NULL::character varying AS title_name,
                    NULL::character varying AS system_code,
                    t_employee_position.create_time
                   FROM t_employee_position
                  WHERE t_employee_position.user_id IS NULL AND t_employee_position.employee_mdm_id IS NOT NULL
        UNION ALL
                 SELECT 'title'::text AS record_type, t_employee_title.id,
                    t_employee_title.employee_mdm_id,
                    t_employee_title.title_name,
                    NULL::character varying AS system_code,
                    t_employee_title.create_time
                   FROM t_employee_title
                  WHERE t_employee_title.user_id IS NULL AND t_employee_title.employee_mdm_id IS NOT NULL)
UNION ALL
         SELECT 'system'::text AS record_type, t_employee_system.id,
            t_employee_system.employee_mdm_id,
            NULL::character varying AS title_name,
            t_employee_system.system_code, t_employee_system.create_time
           FROM t_employee_system
          WHERE t_employee_system.user_id IS NULL AND t_employee_system.employee_mdm_id IS NOT NULL
  ORDER BY 6 DESC;
COMMENT ON VIEW "public"."v_orphan_employee_records" IS '孤儿记录查询视图 - 显示所有未关联到用户的员工扩展数据';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."t_data_operate_id_seq"
OWNED BY "public"."t_data_operate"."id";
SELECT setval('"public"."t_data_operate_id_seq"', 8, true);

-- ----------------------------
-- Primary Key structure for table t_data_module
-- ----------------------------
ALTER TABLE "public"."t_data_module" ADD CONSTRAINT "t_data_module_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_data_operate
-- ----------------------------
ALTER TABLE "public"."t_data_operate" ADD CONSTRAINT "t_data_operate_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_data_permission
-- ----------------------------
ALTER TABLE "public"."t_data_permission" ADD CONSTRAINT "t_data_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_position
-- ----------------------------
CREATE INDEX "idx_employee_position_mdm_id" ON "public"."t_employee_position" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_position_org_code" ON "public"."t_employee_position" USING btree (
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_position_user_id" ON "public"."t_employee_position" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_position
-- ----------------------------
ALTER TABLE "public"."t_employee_position" ADD CONSTRAINT "t_employee_position_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_system
-- ----------------------------
CREATE INDEX "idx_employee_system_login_account" ON "public"."t_employee_system" USING btree (
  "login_account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_system_mdm_id" ON "public"."t_employee_system" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_system_user_id" ON "public"."t_employee_system" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_system
-- ----------------------------
ALTER TABLE "public"."t_employee_system" ADD CONSTRAINT "t_employee_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_title
-- ----------------------------
CREATE INDEX "idx_employee_title_mdm_id" ON "public"."t_employee_title" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_title_user_id" ON "public"."t_employee_title" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_title
-- ----------------------------
ALTER TABLE "public"."t_employee_title" ADD CONSTRAINT "t_employee_title_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_menu_module
-- ----------------------------
ALTER TABLE "public"."t_menu_module" ADD CONSTRAINT "t_menu_module_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_menu_permission
-- ----------------------------
ALTER TABLE "public"."t_menu_permission" ADD CONSTRAINT "t_menu_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_org_structure
-- ----------------------------
CREATE INDEX "idx_org_structure_pre_id" ON "public"."t_org_structure" USING btree (
  "pre_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_org_structure
-- ----------------------------
ALTER TABLE "public"."t_org_structure" ADD CONSTRAINT "t_org_structure_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_perm_user_role
-- ----------------------------
ALTER TABLE "public"."t_perm_user_role" ADD CONSTRAINT "t_perm_user_role_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_role
-- ----------------------------
ALTER TABLE "public"."t_role" ADD CONSTRAINT "t_role_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_roles_data_permission
-- ----------------------------
ALTER TABLE "public"."t_roles_data_permission" ADD CONSTRAINT "t_roles_data_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_roles_menu_permission
-- ----------------------------
ALTER TABLE "public"."t_roles_menu_permission" ADD CONSTRAINT "t_roles_menu_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_sync_log
-- ----------------------------
CREATE INDEX "idx_sync_log_status" ON "public"."t_sync_log" USING btree (
  "sync_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_sync_log_time" ON "public"."t_sync_log" USING btree (
  "sync_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_sync_log_type" ON "public"."t_sync_log" USING btree (
  "sync_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_sync_log
-- ----------------------------
ALTER TABLE "public"."t_sync_log" ADD CONSTRAINT "t_sync_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_user
-- ----------------------------
CREATE INDEX "idx_user_organ_affiliation" ON "public"."t_user" USING btree (
  "organ_affiliation" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_user
-- ----------------------------
ALTER TABLE "public"."t_user" ADD CONSTRAINT "t_user_pk" PRIMARY KEY ("id");
