const { Client } = require('pg');

const client = new Client({
  connectionString: '***********************************************/rbac'
});

async function testConnection() {
  try {
    console.log('正在连接数据库...');
    await client.connect();
    console.log('✅ 数据库连接成功！');
    
    // 测试查询
    const result = await client.query('SELECT version()');
    console.log('📊 数据库版本:', result.rows[0].version);
    
    // 查看表列表
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `);
    console.log('📋 数据库中的表:');
    tables.rows.forEach(row => {
      console.log('  -', row.table_name);
    });
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await client.end();
  }
}

testConnection(); 