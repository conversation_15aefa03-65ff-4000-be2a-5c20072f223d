package com.dfit.percode.sync.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 外部系统员工职称实体类
 * 对应外部系统的employee_title表结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExternalEmployeeTitle {
    
    /**
     * 全局唯一标识符
     */
    private String guid;
    
    /**
     * 关联的员工MDM ID
     */
    private String employeeMdmId;
    
    /**
     * 职称代码
     */
    private String titleCode;
    
    /**
     * 职称类型
     */
    private String titleType;
    
    /**
     * 职称级别
     */
    private String titleLevel;
    
    /**
     * 职称名称
     */
    private String titleName;
    
    /**
     * 状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 职称类别
     */
    private String titleCategory;
}
