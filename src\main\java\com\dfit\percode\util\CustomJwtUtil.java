package com.dfit.percode.util;

import com.dfit.percode.config.JwtConfig;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 自定义JWT工具类
 * 专门用于生成符合{userid, iat, exp}格式的JWT token
 * 与Sa-Token并存使用，避免功能冲突
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Slf4j
@Component
public class CustomJwtUtil {

    @Autowired
    private JwtConfig jwtConfig;

    /**
     * 生成自定义格式的JWT token
     * JWT payload格式严格按照：{"userid": Long, "iat": Long, "exp": Long}
     *
     * @param userId 用户ID
     * @return 生成的JWT token字符串
     */
    public String generateCustomToken(Long userId) {
        if (userId == null) {
            log.error("生成JWT token失败：用户ID不能为空");
            throw new IllegalArgumentException("用户ID不能为空");
        }

        try {
            // 当前时间（秒）
            long currentTimeSeconds = System.currentTimeMillis() / 1000;
            // 过期时间（秒）
            long expirationTimeSeconds = currentTimeSeconds + jwtConfig.getExpiration();

            // 构建JWT payload，严格按照要求的格式
            Map<String, Object> claims = new HashMap<>();
            claims.put("userid", userId);
            claims.put("iat", currentTimeSeconds);
            claims.put("exp", expirationTimeSeconds);

            // 生成密钥
            SecretKey key = Keys.hmacShaKeyFor(jwtConfig.getSecretKey().getBytes(StandardCharsets.UTF_8));

            // 构建JWT token，严格按照{userid, iat, exp}格式，不添加额外字段
            String token = Jwts.builder()
                    .setClaims(claims)
                    .signWith(key, SignatureAlgorithm.HS256)
                    .compact();

            log.debug("成功生成自定义JWT token，用户ID: {}, 过期时间: {}", userId, new Date(expirationTimeSeconds * 1000));
            return token;

        } catch (Exception e) {
            log.error("生成自定义JWT token失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            throw new RuntimeException("生成JWT token失败", e);
        }
    }

    /**
     * 验证自定义JWT token的有效性
     *
     * @param token JWT token字符串
     * @return 是否有效
     */
    public boolean validateCustomToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("验证JWT token失败：token为空");
            return false;
        }

        try {
            // 移除Bearer前缀（如果存在）
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 生成密钥
            SecretKey key = Keys.hmacShaKeyFor(jwtConfig.getSecretKey().getBytes(StandardCharsets.UTF_8));

            // 解析并验证token
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 验证必要字段是否存在
            if (!claims.containsKey("userid") || !claims.containsKey("iat") || !claims.containsKey("exp")) {
                log.warn("JWT token格式不正确：缺少必要字段");
                return false;
            }

            log.debug("JWT token验证成功，用户ID: {}", claims.get("userid"));
            return true;

        } catch (ExpiredJwtException e) {
            log.warn("JWT token已过期: {}", e.getMessage());
            return false;
        } catch (UnsupportedJwtException e) {
            log.warn("不支持的JWT token: {}", e.getMessage());
            return false;
        } catch (MalformedJwtException e) {
            log.warn("JWT token格式错误: {}", e.getMessage());
            return false;
        } catch (SecurityException e) {
            log.warn("JWT token签名验证失败: {}", e.getMessage());
            return false;
        } catch (IllegalArgumentException e) {
            log.warn("JWT token参数错误: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("验证JWT token时发生未知错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从自定义JWT token中提取用户ID
     *
     * @param token JWT token字符串
     * @return 用户ID，如果提取失败返回null
     */
    public Long getUserIdFromCustomToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("提取用户ID失败：token为空");
            return null;
        }

        try {
            // 移除Bearer前缀（如果存在）
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 生成密钥
            SecretKey key = Keys.hmacShaKeyFor(jwtConfig.getSecretKey().getBytes(StandardCharsets.UTF_8));

            // 解析token
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 提取用户ID
            Object useridObj = claims.get("userid");
            if (useridObj == null) {
                log.warn("JWT token中不包含userid字段");
                return null;
            }

            Long userId = Long.valueOf(useridObj.toString());
            log.debug("成功从JWT token中提取用户ID: {}", userId);
            return userId;

        } catch (Exception e) {
            log.error("从JWT token中提取用户ID失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查自定义JWT token是否已过期
     *
     * @param token JWT token字符串
     * @return 是否已过期
     */
    public boolean isCustomTokenExpired(String token) {
        if (token == null || token.trim().isEmpty()) {
            log.warn("检查token过期状态失败：token为空");
            return true;
        }

        try {
            // 移除Bearer前缀（如果存在）
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 生成密钥
            SecretKey key = Keys.hmacShaKeyFor(jwtConfig.getSecretKey().getBytes(StandardCharsets.UTF_8));

            // 解析token
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 检查过期时间
            Date expiration = claims.getExpiration();
            boolean isExpired = expiration.before(new Date());

            log.debug("JWT token过期检查，过期时间: {}, 是否过期: {}", expiration, isExpired);
            return isExpired;

        } catch (ExpiredJwtException e) {
            log.debug("JWT token已过期");
            return true;
        } catch (Exception e) {
            log.error("检查JWT token过期状态时发生错误: {}", e.getMessage(), e);
            return true;
        }
    }

    /**
     * 获取JWT token的详细信息（用于调试）
     *
     * @param token JWT token字符串
     * @return token详细信息的Map
     */
    public Map<String, Object> getTokenDetails(String token) {
        Map<String, Object> details = new HashMap<>();

        if (token == null || token.trim().isEmpty()) {
            details.put("error", "token为空");
            return details;
        }

        try {
            // 移除Bearer前缀（如果存在）
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }

            // 生成密钥
            SecretKey key = Keys.hmacShaKeyFor(jwtConfig.getSecretKey().getBytes(StandardCharsets.UTF_8));

            // 解析token
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(key)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            // 提取所有信息
            details.put("userid", claims.get("userid"));
            details.put("iat", claims.get("iat"));
            details.put("exp", claims.get("exp"));
            details.put("issuer", claims.getIssuer());
            details.put("issuedAt", claims.getIssuedAt());
            details.put("expiration", claims.getExpiration());
            details.put("isExpired", claims.getExpiration().before(new Date()));

        } catch (Exception e) {
            details.put("error", e.getMessage());
        }

        return details;
    }
}
