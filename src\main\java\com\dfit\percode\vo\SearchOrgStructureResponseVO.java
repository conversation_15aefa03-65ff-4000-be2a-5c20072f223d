package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 搜索部门响应VO类
 * 按照数据库字段设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "SearchOrgStructureResponseVO", description = "搜索部门响应数据")
public class SearchOrgStructureResponseVO {

    @ApiModelProperty(value = "部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value = "组织名称")
    private String organName;

    @ApiModelProperty(value = "父部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;

    @ApiModelProperty(value = "父部门名称")
    private String parentName;

    @ApiModelProperty(value = "排序序号")
    private Integer orderInfo;

    @ApiModelProperty(value = "是否删除")
    private Boolean isDel;

    @ApiModelProperty(value = "完整路径")
    private String fullPath;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "数据来源，1页面输入，2数据同步")
    private Integer dataSource;
}
