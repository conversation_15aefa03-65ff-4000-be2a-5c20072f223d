package com.dfit.percode.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

@Configuration
public class JacksonConfig {
    @Bean
    @Primary
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder builder) {
        /**
         * 这个builder.createXmlMapper(false).build();是什么意思？
         *1、builder.build很好理解，就是创建一个ObjectMapper的对象。
         *2、createXmlMapper(false)这个的意思就是表示不使用XML映射。也就是，
         * 创建的ObjectMapper指挥处理Json格式的数据，而不是XML格式的数据。
         *
         *3、这个默认创建的时候是会创建一个可以处理JSON和XML的ObjectMapper的对象的。
         * 大多数情况下我们只需要使用json数据，不需要xml。
         * 这个操作也是为了提升性能，因为这样就可以减少了不必要的配置，提供性能。
         */
        ObjectMapper objectMapper = builder.createXmlMapper(false).build();
        //创建一个自定义模块
        SimpleModule simpleModule = new SimpleModule();
        //添加一个自定义序列化器，将Long类型序列化为字符串。
        simpleModule.addSerializer(Long.class, ToStringSerializer.instance);
        //将自定义的模块注册到ObjectMapper中。
        objectMapper.registerModule(simpleModule);
        return objectMapper;
    }
}
