package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITDataModuleService;
import com.dfit.percode.service.impl.TDataModuleServiceImpl;
import com.dfit.percode.exception.UsageConflictException;
import com.dfit.percode.vo.AddDataModuleRequestVO;
import com.dfit.percode.vo.DataModuleListRequestVO;
import com.dfit.percode.vo.DataModuleListResponseVO;
import com.dfit.percode.vo.DeleteDataModuleRequestVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 数据模块管理控制器
 * 提供数据模块的CRUD操作接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@Api(tags = "数据管理模块接口")
@RequestMapping("/data-modules")
public class DataModuleController {

    @Autowired
    private ITDataModuleService dataModuleService;

    /**
     * 查询数据模块列表
     * 支持分页查询，返回数据模块列表和总数
     *
     * @param request 查询数据模块列表请求参数
     * @return 统一返回格式，data为数据模块数组，total为总数
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "查询数据模块列表", notes = "支持分页查询数据模块列表")
    public BaseResult getDataModuleList(@RequestBody DataModuleListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 异步获取总数
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                return dataModuleService.getDataModuleTotal(request);
            });

            // 查询数据模块列表
            List<DataModuleListResponseVO> moduleList = dataModuleService.getDataModuleList(request);

            // 获取总数
            Integer total = future.join();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(moduleList);
            baseResult.setTotal(total);

        } catch (Exception e) {
            log.error("查询数据模块列表失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("查询数据模块列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 新增数据模块
     * 创建新的数据模块，包含模块名称、标识和排序序号
     *
     * @param request 新增数据模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增数据模块", notes = "创建新的数据模块")
    public BaseResult addDataModule(@RequestBody AddDataModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增数据模块
            dataModuleService.addDataModule(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("新增数据模块失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("新增数据模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除数据模块
     * 逻辑删除数据模块，设置is_del为true
     *
     * @param request 删除数据模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除数据模块", notes = "逻辑删除数据模块")
    public BaseResult deleteDataModule(@RequestBody DeleteDataModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除数据模块
            dataModuleService.deleteDataModule(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("删除数据模块失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除数据模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 插入数据模块测试数据
     * 用于快速创建测试数据，方便接口测试
     *
     * @return 统一返回格式
     */
    @RequestMapping(value = "/insertTestData", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "插入数据模块测试数据", notes = "快速创建测试数据")
    public BaseResult insertTestData() {
        BaseResult baseResult = new BaseResult();

        try {
            // 创建测试数据
            String[][] testData = {
                {"用户数据模块", "user_data_module", "1"},
                {"订单数据模块", "order_data_module", "2"},
                {"商品数据模块", "product_data_module", "3"},
                {"财务数据模块", "finance_data_module", "4"},
                {"报表数据模块", "report_data_module", "5"}
            };

            int successCount = 0;
            for (String[] data : testData) {
                try {
                    AddDataModuleRequestVO request = new AddDataModuleRequestVO();
                    request.setModuleName(data[0]);
                    request.setModuleIdentifier(data[1]);
                    request.setOrderInfo(Integer.parseInt(data[2]));

                    dataModuleService.addDataModule(request);
                    successCount++;
                } catch (Exception e) {
                    // 如果模块已存在，跳过
                    log.warn("模块已存在，跳过: {}", data[1]);
                }
            }

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData("成功插入 " + successCount + " 条测试数据");

        } catch (Exception e) {
            log.error("插入测试数据失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("插入测试数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    // ==================== V2版本删除功能相关接口 ====================

    /**
     * 删除数据模块 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除数据模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteV2", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除数据模块V2", notes = "支持两阶段删除，检查使用情况后可强制删除")
    public BaseResult deleteDataModuleV2(@RequestBody DeleteDataModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除数据模块V2
            TDataModuleServiceImpl serviceImpl = (TDataModuleServiceImpl) dataModuleService;
            Object result = serviceImpl.deleteDataModuleV2(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result);

        } catch (UsageConflictException e) {
            // 检测到使用情况，返回409状态码
            log.warn("数据模块删除检测到使用情况: {}", e.getMessage());
            baseResult.setCode(409);
            baseResult.setMessage(e.getMessage());
            baseResult.setData(e.getUsageInfo());

        } catch (Exception e) {
            log.error("删除数据模块失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除数据模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
