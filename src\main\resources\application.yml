spring:
  profiles:
    #    开发环境
    active: dev
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
# 自定义的Swagger配置
sys:
  swagger:
    enable-swgger: true

# 外部系统配置
external:
  system:
    # 外部系统基础URL
    base-url: http://localhost:8080
    # API接口路径
    api:
      departments: /api/data/departments
      employees: /api/data/employees
    # 连接超时配置（单位：毫秒）
    # 根据外部系统分片处理能力调整超时时间
    timeout:
      connect: 60000    # 连接超时：1分钟
      read: 7200000     # 读取超时：2小时，适应一年数据量的大规模同步
    # 同步策略配置
    sync:
      # 推荐的时间范围阈值（根据外部系统分片策略）
      department-threshold-hours: 72    # 部门数据：72小时内单次处理
      employee-threshold-hours: 24      # 员工数据：24小时内单次处理
      # 批量同步建议
      batch-size-days: 7               # 建议按周同步（7天）
