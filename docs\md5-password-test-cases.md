# MD5密码加密功能测试用例

## 测试概述
验证MD5密码加密功能的完整性，包括新增用户、密码验证、登录功能等。

## 测试环境
- 数据库：使用public702-md5.sql（包含MD5加密密码的版本）
- 后端：已集成PasswordUtil工具类和相关业务逻辑

## 测试用例

### 1. 密码工具类测试

#### 1.1 MD5加密功能测试
```java
// 测试常见密码的MD5加密
String password1 = "123456";
String encrypted1 = PasswordUtil.encryptPassword(password1);
// 预期结果：e10adc3949ba59abbe56e057f20f883e

String password2 = "password123";
String encrypted2 = PasswordUtil.encryptPassword(password2);
// 预期结果：482c811da5d5b4bc6d497ffa98491e38
```

#### 1.2 密码验证功能测试
```java
// 测试密码验证
boolean result1 = PasswordUtil.verifyPassword("123456", "e10adc3949ba59abbe56e057f20f883e");
// 预期结果：true

boolean result2 = PasswordUtil.verifyPassword("123456", "wrong_hash");
// 预期结果：false
```

#### 1.3 默认密码生成测试
```java
// 测试默认密码生成
String defaultPassword = PasswordUtil.generateDefaultPassword();
// 预期结果：12位复杂密码，包含大小写字母、数字、特殊字符

boolean isComplex = PasswordUtil.isComplexPassword(defaultPassword);
// 预期结果：true
```

### 2. 用户新增功能测试

#### 2.1 传密码的新增用户测试
```http
POST /users/addMembers
Content-Type: application/json

[
  {
    "userId": null,
    "userName": "测试用户MD5",
    "account": "testmd5",
    "password": "mypassword123",
    "organAffiliation": 5008,
    "roles": [
      {
        "roleId": 1939247925561528320,
        "roleName": "测试角色"
      }
    ]
  }
]
```
**预期结果**：
- 用户创建成功
- 数据库中密码字段存储为MD5加密值
- 可以使用原始密码"mypassword123"登录

#### 2.2 不传密码的新增用户测试
```http
POST /users/addMembers
Content-Type: application/json

[
  {
    "userId": null,
    "userName": "测试用户默认密码",
    "account": "testdefault",
    "organAffiliation": 5008,
    "roles": [
      {
        "roleId": 1939247925561528320,
        "roleName": "测试角色"
      }
    ]
  }
]
```
**预期结果**：
- 用户创建成功
- 系统自动生成12位复杂默认密码
- 密码经MD5加密后存储
- 日志中显示生成的默认密码

### 3. 用户更新功能测试

#### 3.1 更新密码测试
```http
POST /users/updateUser
Content-Type: application/json

{
  "userId": 1938155631131365376,
  "userName": "管理员",
  "account": "admin",
  "password": "newpassword123",
  "organAffiliation": 5008,
  "isDisable": false,
  "roles": [
    {
      "roleId": 1939247925561528320,
      "roleName": "测试角色"
    }
  ]
}
```
**预期结果**：
- 用户信息更新成功
- 密码经MD5加密后存储
- 可以使用新密码"newpassword123"登录

#### 3.2 不更新密码测试
```http
POST /users/updateUser
Content-Type: application/json

{
  "userId": 1938155631131365376,
  "userName": "管理员",
  "account": "admin",
  "organAffiliation": 5008,
  "isDisable": false,
  "roles": [
    {
      "roleId": 1939247925561528320,
      "roleName": "测试角色"
    }
  ]
}
```
**预期结果**：
- 用户信息更新成功
- 密码保持不变
- 仍可使用原密码登录

### 4. 登录功能测试

#### 4.1 MD5密码登录测试
```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "123456"
}
```
**预期结果**：
- 登录成功
- 返回JWT Token
- 用户信息正确

#### 4.2 错误密码登录测试
```http
POST /auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}
```
**预期结果**：
- 登录失败
- 返回401错误
- 提示"用户名或密码错误"

### 5. 数据库验证测试

#### 5.1 密码格式验证
```sql
-- 检查所有用户密码是否为MD5格式
SELECT 
    user_name,
    account,
    password,
    LENGTH(password) as password_length,
    CASE 
        WHEN LENGTH(password) = 32 AND password ~ '^[a-f0-9]+$' THEN 'MD5格式'
        ELSE '其他格式'
    END as password_format
FROM t_user 
WHERE is_del = false 
ORDER BY user_name;
```
**预期结果**：所有密码都是32位小写十六进制字符

#### 5.2 特定用户密码验证
```sql
-- 验证admin用户的密码是否为123456的MD5值
SELECT 
    user_name,
    account,
    password,
    CASE 
        WHEN password = 'e10adc3949ba59abbe56e057f20f883e' THEN '密码是123456'
        ELSE '密码不是123456'
    END as password_check
FROM t_user 
WHERE account = 'admin' AND is_del = false;
```

## 测试检查清单

### 功能测试
- [ ] 新增用户（传密码）- 密码MD5加密存储
- [ ] 新增用户（不传密码）- 自动生成复杂默认密码
- [ ] 更新用户（传密码）- 密码MD5加密存储
- [ ] 更新用户（不传密码）- 密码保持不变
- [ ] 登录验证（正确密码）- 登录成功
- [ ] 登录验证（错误密码）- 登录失败

### 安全测试
- [ ] 数据库中无明文密码
- [ ] 所有密码都是32位MD5格式
- [ ] 默认密码符合复杂度要求
- [ ] 密码验证逻辑正确

### 兼容性测试
- [ ] 现有用户可正常登录
- [ ] 新增用户功能正常
- [ ] 密码修改功能正常
- [ ] 系统其他功能不受影响

## 常见问题排查

### 1. 登录失败
- 检查数据库中密码是否为MD5格式
- 检查PasswordUtil.verifyPassword方法是否正常工作
- 检查AuthController中的登录逻辑

### 2. 新增用户密码问题
- 检查createNewUser方法中的密码处理逻辑
- 检查PasswordUtil.generateDefaultPassword方法
- 检查数据库插入的密码值

### 3. 密码更新问题
- 检查updateUser方法中的密码加密逻辑
- 检查UserMapper.updateUserInfo的动态SQL
- 检查密码为空时的处理逻辑
