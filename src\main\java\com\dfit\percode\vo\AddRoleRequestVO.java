package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新增角色请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持角色基本信息、菜单权限、数据权限的设置
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddRoleRequestVO", description = "新增角色请求参数")
public class AddRoleRequestVO {

    @ApiModelProperty(value = "角色名称", required = true, example = "新角色")
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 50, message = "角色名称长度不能超过50个字符")
    private String roleName;

    @ApiModelProperty(value = "排序序号", example = "10")
    private Integer orderInfo;

    @ApiModelProperty(value = "是否停用：false-正常，true-停用", example = "false")
    private Boolean isDisable = false;

    @ApiModelProperty(value = "菜单权限列表")
    private List<MenuPermissionItem> menuName;

    @ApiModelProperty(value = "数据权限列表")
    private List<DataPermissionItem> dataPermission;

    /**
     * 菜单权限项
     */
    @Data
    @ApiModel(value = "MenuPermissionItem", description = "菜单权限项")
    public static class MenuPermissionItem {

        @ApiModelProperty(value = "菜单ID", required = true)
        private Long id;

        @ApiModelProperty(value = "菜单名称")
        private String name;

        @ApiModelProperty(value = "模块标识符")
        private String moduleIdentifier;

        @ApiModelProperty(value = "子菜单列表（支持嵌套结构）")
        private List<MenuPermissionItem> children;
    }

    /**
     * 数据权限项
     */
    @Data
    @ApiModel(value = "DataPermissionItem", description = "数据权限项")
    public static class DataPermissionItem {

        @ApiModelProperty(value = "数据权限ID", required = true)
        private String dataId;

        @ApiModelProperty(value = "数据权限名称")
        private String name;

        @ApiModelProperty(value = "操作类型列表")
        private List<Integer> operateTypes;

        @ApiModelProperty(value = "模块标识符")
        private String moduleIdentifier;

        @ApiModelProperty(value = "数据类型")
        private Integer dataType;
    }
}
