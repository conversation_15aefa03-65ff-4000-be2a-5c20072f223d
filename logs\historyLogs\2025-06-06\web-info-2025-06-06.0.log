2025-06-06 01:09:42.623 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 24260 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:09:42.635 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:09:46.875 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:09:46.887 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:09:46.939 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:09:46.946 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:09:46.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:09:46.970 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:09:46.986 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:09:46.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:09:47.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-06 01:09:47.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:09:47.065 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:09:47.114 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-06 01:09:48.993 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:09:49.023 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:09:49.024 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:09:49.026 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:09:49.487 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:09:49.488 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6606 ms
2025-06-06 01:09:49.577 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:09:49.652 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:09:49.949 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:09:51.155 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:09:51.400 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:09:52.199 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:09:52.638 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:09:54.120 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:09:54.136 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:09:55.861 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:10:00.481 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:10:00.542 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:10:02.582 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.169 seconds (JVM running for 25.532)
2025-06-06 01:10:15.162 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:10:15.162 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:10:15.165 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 01:10:15.527 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始删除数据模块
2025-06-06 01:10:15.528 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块ID: 1930548900859613184
2025-06-06 01:10:29.542 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始删除数据模块
2025-06-06 01:10:29.543 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块ID: 1930559428256468992
2025-06-06 01:14:07.003 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:14:07.034 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:14:29.201 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 38244 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:14:29.209 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:14:33.755 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:14:33.774 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:14:33.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:14:33.862 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:14:33.864 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:14:33.895 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:14:33.916 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:14:33.918 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:14:33.961 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 JPA repository interfaces.
2025-06-06 01:14:34.008 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:14:34.014 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:14:34.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-06 01:14:38.410 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:14:38.482 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:14:38.483 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:14:38.484 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:14:39.171 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:14:39.171 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9766 ms
2025-06-06 01:14:39.319 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:14:39.478 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:14:40.180 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:14:41.944 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:14:42.564 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:14:43.359 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:14:44.104 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:14:45.192 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:14:45.207 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:14:46.658 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:14:55.339 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:14:56.010 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 28.156 seconds (JVM running for 30.674)
2025-06-06 01:14:56.089 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:14:56.093 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:15:14.550 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 34552 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:15:14.554 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:15:17.550 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:15:17.564 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:15:17.658 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:15:17.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:15:17.672 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:15:17.720 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:15:17.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:15:17.762 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:15:17.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48 ms. Found 0 JPA repository interfaces.
2025-06-06 01:15:17.855 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:15:17.857 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:15:17.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-06 01:15:20.213 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:15:20.226 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:15:20.227 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:15:20.227 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:15:20.577 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:15:20.578 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5867 ms
2025-06-06 01:15:20.732 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:15:20.880 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:15:21.315 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:15:22.387 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:15:22.523 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:15:23.194 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:15:23.939 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:15:25.576 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:15:25.597 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:15:28.137 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:15:33.318 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:15:33.346 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:15:34.639 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 21.828 seconds (JVM running for 25.626)
2025-06-06 01:16:01.875 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:16:01.875 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:16:01.885 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2025-06-06 01:17:03.518 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 用户基础数据
2025-06-06 01:17:03.719 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 用户档案数据
2025-06-06 01:17:03.870 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 用户认证数据
2025-06-06 01:17:04.002 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 订单基础数据
2025-06-06 01:17:04.143 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 订单详情数据
2025-06-06 01:17:04.282 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 订单支付数据
2025-06-06 01:17:04.422 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 商品基础数据
2025-06-06 01:17:04.563 [http-nio-8285-exec-8] WARN  c.dfit.percode.controller.DataPermissionController - 数据权限已存在，跳过: 商品库存数据
2025-06-06 01:21:15.422 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:21:15.436 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:21:33.674 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 18744 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:21:33.677 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:21:36.403 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:21:36.406 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:21:36.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:21:36.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:21:36.455 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:21:36.477 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:21:36.490 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:21:36.491 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:21:36.525 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-06 01:21:36.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:21:36.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:21:36.593 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-06 01:21:38.299 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:21:38.319 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:21:38.320 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:21:38.320 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:21:38.607 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:21:38.608 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4828 ms
2025-06-06 01:21:38.678 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:21:38.737 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:21:38.933 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:21:39.941 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:21:40.034 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:21:40.337 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:21:40.612 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:21:41.606 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:21:41.621 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:21:43.709 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:21:46.785 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:21:46.800 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:21:47.465 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.02 seconds (JVM running for 17.431)
2025-06-06 01:27:21.954 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:27:21.954 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:27:21.965 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-06-06 01:28:52.160 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:28:52.166 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:29:04.469 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 11320 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:29:04.471 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:29:07.409 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:29:07.413 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:29:07.448 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:29:07.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:29:07.455 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:29:07.475 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:29:07.486 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:29:07.487 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:29:07.514 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-06 01:29:07.532 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:29:07.533 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:29:07.559 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-06 01:29:08.760 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:29:08.769 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:29:08.770 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:29:08.771 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:29:09.010 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:29:09.011 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4440 ms
2025-06-06 01:29:09.077 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:29:09.127 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:29:09.310 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:29:10.266 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:29:10.333 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:29:10.569 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:29:10.890 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:29:12.106 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:29:12.132 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:29:13.829 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:29:17.076 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:29:17.098 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:29:17.838 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.649 seconds (JVM running for 16.944)
2025-06-06 01:29:34.374 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:29:34.375 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:29:34.376 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 01:29:41.585 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户基础数据, ID: 2001
2025-06-06 01:29:41.863 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 用户基础数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:41.863 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户档案数据, ID: 2002
2025-06-06 01:29:41.938 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 用户档案数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:41.938 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户认证数据, ID: 2003
2025-06-06 01:29:42.018 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 用户认证数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:42.018 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单基础数据, ID: 2004
2025-06-06 01:29:42.088 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 订单基础数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:42.089 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单详情数据, ID: 2005
2025-06-06 01:29:42.161 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 订单详情数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:42.162 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单支付数据, ID: 2006
2025-06-06 01:29:42.235 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 订单支付数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:42.236 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 商品基础数据, ID: 2007
2025-06-06 01:29:42.303 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 商品基础数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:29:42.303 [http-nio-8285-exec-6] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 商品库存数据, ID: 2008
2025-06-06 01:29:42.385 [http-nio-8285-exec-6] WARN  c.dfit.percode.controller.DataPermissionController - 插入数据权限失败: 商品库存数据, 错误: 
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
### The error may exist in com/dfit/percode/mapper/TDataPermissionMapper.java (best guess)
### The error may involve com.dfit.percode.mapper.TDataPermissionMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO t_data_permission  ( id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "create_time" is of type timestamp without time zone but expression is of type character varying
  建议：You will need to rewrite or cast the expression.
  位置：205
2025-06-06 01:32:23.283 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:32:23.289 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:32:35.775 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 17584 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:32:35.782 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:32:37.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:32:37.823 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:32:37.850 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:32:37.856 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:32:37.856 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:32:37.874 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:32:37.888 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:32:37.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:32:37.921 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-06 01:32:37.942 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:32:37.944 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:32:37.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 01:32:39.617 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:32:39.630 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:32:39.630 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:32:39.631 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:32:39.847 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:32:39.847 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3943 ms
2025-06-06 01:32:39.931 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:32:39.991 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:32:40.212 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:32:41.237 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:32:41.362 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:32:41.585 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:32:41.815 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:32:42.818 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:32:42.832 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:32:44.537 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:32:47.650 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:32:47.681 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:32:48.399 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.885 seconds (JVM running for 16.31)
2025-06-06 01:33:11.741 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:33:11.741 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:33:11.742 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 01:33:11.971 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户基础数据, ID: 2001
2025-06-06 01:33:12.141 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 用户基础数据
2025-06-06 01:33:12.141 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户档案数据, ID: 2002
2025-06-06 01:33:12.199 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 用户档案数据
2025-06-06 01:33:12.200 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 用户认证数据, ID: 2003
2025-06-06 01:33:12.264 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 用户认证数据
2025-06-06 01:33:12.264 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单基础数据, ID: 2004
2025-06-06 01:33:12.332 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 订单基础数据
2025-06-06 01:33:12.332 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单详情数据, ID: 2005
2025-06-06 01:33:12.395 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 订单详情数据
2025-06-06 01:33:12.395 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 订单支付数据, ID: 2006
2025-06-06 01:33:12.456 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 订单支付数据
2025-06-06 01:33:12.458 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 商品基础数据, ID: 2007
2025-06-06 01:33:12.514 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 商品基础数据
2025-06-06 01:33:12.514 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 尝试插入数据权限: 商品库存数据, ID: 2008
2025-06-06 01:33:12.570 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 成功插入数据权限: 商品库存数据
2025-06-06 01:33:44.568 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:33:44.568 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 01:35:54.791 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:35:54.824 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:36:06.378 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 33484 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:36:06.380 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:36:08.906 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:36:08.908 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:36:08.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:36:08.945 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:36:08.946 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:36:08.954 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:36:08.972 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:36:08.974 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:36:08.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-06 01:36:09.010 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:36:09.013 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:36:09.030 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-06 01:36:10.253 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:36:10.264 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:36:10.265 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:36:10.265 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:36:10.599 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:36:10.599 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4094 ms
2025-06-06 01:36:10.673 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:36:10.756 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:36:10.985 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:36:12.027 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:36:12.102 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:36:12.375 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:36:12.625 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:36:13.986 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:36:14.000 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:36:15.425 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:36:18.388 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:36:18.405 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:36:19.515 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.451 seconds (JVM running for 16.744)
2025-06-06 01:37:02.900 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:37:02.900 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:37:02.903 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 01:37:03.227 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:37:03.227 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 01:37:03.399 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共8条记录
2025-06-06 01:37:27.086 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:37:27.086 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=user_data_module, name=null, isDisable=null
2025-06-06 01:37:27.160 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共3条记录
2025-06-06 01:37:56.211 [http-nio-8285-exec-3] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:37:56.212 [http-nio-8285-exec-3] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=用户, isDisable=null
2025-06-06 01:37:56.539 [http-nio-8285-exec-3] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共3条记录
2025-06-06 01:38:13.501 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:38:13.503 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=false
2025-06-06 01:38:13.582 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共6条记录
2025-06-06 01:38:28.701 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 01:38:28.701 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=5, moduleIdentifier=order_data_module, name=订单, isDisable=false
2025-06-06 01:38:28.774 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共2条记录
2025-06-06 01:48:01.206 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:48:01.212 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 01:52:10.058 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 33668 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 01:52:10.060 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 01:52:11.492 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:52:11.495 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:52:11.514 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 01:52:11.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:52:11.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 01:52:11.528 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 01:52:11.537 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:52:11.538 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 01:52:11.551 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-06 01:52:11.568 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 01:52:11.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 01:52:11.586 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-06 01:52:12.543 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 01:52:12.552 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 01:52:12.553 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 01:52:12.554 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 01:52:12.739 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 01:52:12.739 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2611 ms
2025-06-06 01:52:12.788 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 01:52:12.833 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 01:52:13.008 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 01:52:13.878 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 01:52:13.941 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 01:52:14.128 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 01:52:14.280 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 01:52:15.085 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 01:52:15.096 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:52:16.177 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 01:52:18.301 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 01:52:18.331 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 01:52:18.911 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.53 seconds (JVM running for 10.901)
2025-06-06 01:52:33.233 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 01:52:33.233 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 01:52:33.235 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 01:52:42.809 [http-nio-8285-exec-7] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始新增数据权限
2025-06-06 01:52:42.809 [http-nio-8285-exec-7] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 权限名称: 用户敏感数据, 模块标识: user_data_module, 数据标识: user_sensitive_data
2025-06-06 01:52:43.044 [http-nio-8285-exec-7] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限新增成功，ID: 1930684219567247360
2025-06-06 01:53:06.194 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始新增数据权限
2025-06-06 01:53:06.194 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 权限名称: , 模块标识: user_data_module, 数据标识: test_empty_name
2025-06-06 01:53:19.110 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始新增数据权限
2025-06-06 01:53:19.111 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 权限名称: 重复数据标识测试, 模块标识: user_data_module, 数据标识: user_basic_data
2025-06-06 01:59:54.051 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 01:59:54.059 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:00:02.896 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 29608 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:00:02.900 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:00:05.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:00:05.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:00:05.153 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:00:05.158 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:00:05.160 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:00:05.171 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:00:05.185 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:00:05.186 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:00:05.203 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-06 02:00:05.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:00:05.229 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:00:05.246 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-06 02:00:06.113 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:00:06.123 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:00:06.123 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:00:06.124 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:00:06.301 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:00:06.301 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3152 ms
2025-06-06 02:00:06.349 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:00:06.407 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:00:06.572 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:00:07.690 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:00:07.770 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:00:08.046 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:00:08.653 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:00:09.536 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:00:09.551 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:00:10.763 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:00:12.875 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:00:12.896 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:00:13.631 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.475 seconds (JVM running for 12.78)
2025-06-06 02:00:24.290 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:00:24.290 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:00:24.291 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 02:00:37.558 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限详情
2025-06-06 02:00:37.559 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限ID: 2001
2025-06-06 02:00:37.740 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限详情获取成功，ID: 2001, 名称: 用户基础数据
2025-06-06 02:08:39.955 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:08:39.960 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:08:47.377 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 16972 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:08:47.378 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:08:48.531 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:08:48.534 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:08:48.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:08:48.563 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:08:48.564 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:08:48.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:08:48.582 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:08:48.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:08:48.598 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-06 02:08:48.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:08:48.616 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:08:48.639 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-06 02:08:49.797 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:08:49.807 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:08:49.807 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:08:49.808 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:08:50.014 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:08:50.015 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2585 ms
2025-06-06 02:08:50.064 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:08:50.108 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:08:50.255 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:08:51.396 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:08:51.447 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:08:51.619 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:08:51.771 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:08:52.481 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:08:52.491 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:08:53.704 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:08:55.695 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:08:55.707 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:08:56.138 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.441 seconds (JVM running for 10.848)
2025-06-06 02:09:48.148 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:09:48.148 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:09:48.149 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 02:10:14.154 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始修改数据权限
2025-06-06 02:10:14.155 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 权限ID: 2001, 权限名称: 用户基础数据（已修改）, 模块标识: user_data_module, 数据标识: user_basic_data_updated
2025-06-06 02:10:14.493 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限修改成功，ID: 2001
2025-06-06 02:18:20.828 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:18:20.831 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:18:28.515 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12624 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:18:28.517 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:18:29.676 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:18:29.679 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:18:29.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:18:29.706 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:18:29.707 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:18:29.714 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:18:29.723 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:18:29.724 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:18:29.742 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 02:18:29.760 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:18:29.762 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:18:29.785 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-06 02:18:30.766 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:18:30.785 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:18:30.787 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:18:30.787 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:18:31.045 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:18:31.045 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2447 ms
2025-06-06 02:18:31.109 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:18:31.218 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:18:31.432 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:18:32.290 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:18:32.354 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:18:32.523 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:18:32.663 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:18:33.340 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:18:33.354 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:18:34.336 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:18:36.484 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:18:36.500 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:18:36.957 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.161 seconds (JVM running for 10.711)
2025-06-06 02:18:52.548 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:18:52.548 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:18:52.549 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 02:19:03.716 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始删除数据权限
2025-06-06 02:19:03.716 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限ID: 2008
2025-06-06 02:22:12.653 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:22:12.658 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:22:19.442 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 40752 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:22:19.444 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:22:20.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:22:20.900 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:22:20.920 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:22:20.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:22:20.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:22:20.941 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:22:20.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:22:20.952 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:22:20.970 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-06 02:22:20.991 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:22:20.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:22:21.014 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-06 02:22:21.858 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:22:21.868 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:22:21.870 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:22:21.870 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:22:22.099 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:22:22.100 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2589 ms
2025-06-06 02:22:22.157 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:22:22.213 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:22:22.427 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:22:23.351 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:22:23.415 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:22:23.598 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:22:23.744 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:22:24.391 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:22:24.404 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:22:25.450 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:22:27.549 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:22:27.564 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:22:28.290 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.621 seconds (JVM running for 11.261)
2025-06-06 02:23:00.166 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:23:00.166 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:23:00.169 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 02:23:17.823 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始删除数据权限
2025-06-06 02:23:17.824 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限ID: 2008
2025-06-06 02:23:18.085 [http-nio-8285-exec-6] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限删除成功，ID: 2008
2025-06-06 02:29:25.410 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:29:25.415 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:29:34.127 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 39748 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:29:34.129 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:29:36.085 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:29:36.088 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:29:36.111 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:29:36.115 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:29:36.115 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:29:36.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:29:36.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:29:36.139 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:29:36.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 02:29:36.175 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:29:36.176 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:29:36.195 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 02:29:37.155 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:29:37.169 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:29:37.170 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:29:37.171 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:29:37.368 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:29:37.368 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3140 ms
2025-06-06 02:29:37.425 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:29:37.470 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:29:37.659 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:29:38.551 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:29:38.616 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:29:38.833 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:29:39.044 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:29:39.979 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:29:39.995 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:29:41.256 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:29:43.394 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:29:43.410 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:29:44.018 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.76 seconds (JVM running for 12.308)
2025-06-06 02:34:19.873 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:34:19.874 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:34:19.878 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-06 02:41:08.806 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:41:08.810 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:41:16.690 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 10844 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:41:16.691 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:41:17.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:41:17.978 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:41:18.003 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:41:18.006 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:41:18.007 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:41:18.015 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:41:18.026 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:41:18.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:41:18.041 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-06 02:41:18.054 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:41:18.055 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:41:18.079 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 02:41:19.035 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:41:19.044 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:41:19.045 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:41:19.045 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:41:19.237 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:41:19.237 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2463 ms
2025-06-06 02:41:19.298 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:41:19.354 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:41:19.517 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:41:20.472 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:41:20.555 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:41:20.788 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:41:20.979 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:41:21.871 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:41:21.883 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:41:22.966 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:41:24.979 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:41:24.999 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:41:25.552 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.806 seconds (JVM running for 11.096)
2025-06-06 02:41:41.467 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:41:41.468 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:41:41.469 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 02:41:56.439 [http-nio-8285-exec-7] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:41:56.440 [http-nio-8285-exec-7] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:42:18.986 [http-nio-8285-exec-9] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:42:18.987 [http-nio-8285-exec-9] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=null, pageSize=null, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:42:24.276 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:42:24.277 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:44:10.107 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:44:10.108 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:45:40.855 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:45:40.859 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 02:45:44.860 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 18736 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 02:45:44.862 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 02:45:46.093 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:45:46.098 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:45:46.117 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 02:45:46.125 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:45:46.127 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 02:45:46.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 02:45:46.149 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:45:46.150 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 02:45:46.170 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-06 02:45:46.192 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 02:45:46.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 02:45:46.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-06 02:45:47.034 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 02:45:47.045 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 02:45:47.046 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 02:45:47.047 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 02:45:47.243 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 02:45:47.243 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2317 ms
2025-06-06 02:45:47.300 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 02:45:47.350 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 02:45:47.479 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 02:45:48.377 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 02:45:48.442 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 02:45:48.617 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 02:45:48.755 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 02:45:49.425 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 02:45:49.444 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 02:45:50.831 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 02:45:52.663 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 02:45:52.679 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 02:45:53.135 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 8.894 seconds (JVM running for 10.263)
2025-06-06 02:46:11.618 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 02:46:11.619 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 02:46:11.620 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 02:46:11.846 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:46:11.847 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:46:12.048 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共8条记录
2025-06-06 02:47:03.056 [http-nio-8285-exec-2] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('/' (code 47)): maybe a (non-standard) comment? (not recognized as one since Feature 'ALLOW_COMMENTS' not enabled for parser); nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('/' (code 47)): maybe a (non-standard) comment? (not recognized as one since Feature 'ALLOW_COMMENTS' not enabled for parser)<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 5, column: 31]]
2025-06-06 02:50:04.855 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置数据操作权限
2025-06-06 02:50:04.856 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 数据标识: user_basic_data, 模块标识: user_data_module, 操作类型: [1, 2, 3]
2025-06-06 02:50:31.709 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置数据操作权限
2025-06-06 02:50:31.710 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 数据标识: user_profile_data, 模块标识: user_data_module, 操作类型: [1, 2]
2025-06-06 02:50:31.948 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 数据操作权限配置成功，数据标识: user_profile_data, 配置的操作类型数量: 2
2025-06-06 02:50:54.736 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-06 02:50:54.737 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询参数: pageNum=1, pageSize=10, moduleIdentifier=null, name=null, isDisable=null
2025-06-06 02:50:54.961 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共8条记录
2025-06-06 03:17:17.747 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 03:17:17.753 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:01:50.453 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 30808 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:01:50.461 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:01:54.839 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:01:54.844 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:01:54.876 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:01:54.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:01:54.883 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:01:54.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:01:54.910 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:01:54.911 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:01:54.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-06 09:01:54.948 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:01:54.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:01:54.984 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-06 09:01:56.385 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:01:56.401 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:01:56.402 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:01:56.402 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:01:57.781 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:01:57.782 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7051 ms
2025-06-06 09:01:58.091 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:01:58.261 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:01:58.868 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:02:00.347 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:02:00.789 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:02:01.346 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:02:01.688 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:02:04.380 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:02:04.427 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:02:06.098 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:02:13.514 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:02:13.529 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:02:14.338 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 26.282 seconds (JVM running for 32.562)
2025-06-06 09:02:25.101 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:02:25.102 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:02:25.108 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 09:03:39.500 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:03:39.506 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:03:56.803 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 26264 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:03:56.809 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:04:01.475 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:04:01.483 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:04:01.528 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:04:01.537 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:04:01.538 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:04:01.555 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:04:01.578 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:04:01.581 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:04:01.616 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-06 09:04:01.645 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:04:01.649 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:04:01.680 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-06 09:04:03.551 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:04:03.567 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:04:03.569 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:04:03.569 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:04:03.871 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:04:03.872 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6890 ms
2025-06-06 09:04:03.938 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:04:04.035 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:04:04.233 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:04:05.432 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:04:05.629 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:04:06.118 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:04:06.536 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:04:07.584 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:04:07.626 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:04:09.323 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:04:14.945 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:04:15.013 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:04:16.767 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 21.583 seconds (JVM running for 25.58)
2025-06-06 09:04:39.199 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:04:39.200 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:04:39.207 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 09:05:29.216 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:05:29.216 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=研发, preId=null, includeDeleted=null
2025-06-06 09:05:29.714 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门搜索完成，共找到0条记录
2025-06-06 09:05:50.650 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:05:50.651 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=总, preId=null, includeDeleted=null
2025-06-06 09:09:43.877 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:09:43.887 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:09:54.091 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 19612 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:09:54.093 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:09:55.381 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:09:55.384 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:09:55.413 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:09:55.418 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:09:55.419 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:09:55.430 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:09:55.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:09:55.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:09:55.459 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-06 09:09:55.482 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:09:55.483 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:09:55.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 09:09:56.947 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:09:56.973 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:09:56.975 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:09:56.976 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:09:57.811 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:09:57.817 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3633 ms
2025-06-06 09:09:57.984 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:09:58.109 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:09:58.472 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:09:59.729 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:09:59.832 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:10:00.137 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:10:00.353 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:10:01.404 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:10:01.427 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:10:03.167 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:10:06.355 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:10:06.381 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:10:07.191 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.969 seconds (JVM running for 16.612)
2025-06-06 09:10:14.481 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:10:14.482 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:10:14.487 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 09:10:15.160 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:10:15.160 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=总, preId=null, includeDeleted=null
2025-06-06 09:10:32.792 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:10:32.793 [http-nio-8285-exec-8] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=公司, preId=null, includeDeleted=null
2025-06-06 09:13:10.329 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:13:10.334 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:13:24.808 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12312 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:13:24.812 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:13:27.321 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:13:27.327 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:13:27.363 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:13:27.370 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:13:27.371 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:13:27.385 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:13:27.402 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:13:27.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:13:27.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-06 09:13:27.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:13:27.457 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:13:27.478 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 09:13:29.379 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:13:29.411 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:13:29.413 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:13:29.414 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:13:30.132 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:13:30.133 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5166 ms
2025-06-06 09:13:30.244 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:13:30.332 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:13:30.558 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:13:31.691 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:13:31.794 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:13:32.123 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:13:32.496 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:13:34.065 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:13:34.101 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:13:36.360 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:13:39.159 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:13:39.191 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:13:40.191 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.996 seconds (JVM running for 20.56)
2025-06-06 09:13:45.197 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:13:45.198 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:13:45.203 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 09:13:45.739 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:13:45.739 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=公司, preId=null, includeDeleted=null
2025-06-06 09:13:53.033 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:13:53.034 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=公司, preId=null, includeDeleted=null
2025-06-06 09:20:17.697 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:20:17.705 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:20:32.050 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 39784 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:20:32.053 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:20:34.548 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:20:34.552 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:20:34.592 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:20:34.600 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:20:34.602 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:20:34.620 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:20:34.643 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:20:34.646 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:20:34.693 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-06 09:20:34.736 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:20:34.740 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:20:34.834 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
2025-06-06 09:20:36.377 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:20:36.395 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:20:36.396 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:20:36.396 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:20:36.658 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:20:36.658 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4450 ms
2025-06-06 09:20:36.723 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:20:36.783 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:20:37.019 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:20:38.613 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:20:38.796 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:20:39.099 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:20:39.329 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:20:40.627 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:20:40.642 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:20:42.496 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:20:46.373 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:20:46.393 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:20:47.091 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.19 seconds (JVM running for 18.181)
2025-06-06 09:20:54.088 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:20:54.090 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:20:54.097 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 09:20:54.858 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始搜索部门
2025-06-06 09:20:54.862 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 搜索参数: organName=公司, preId=null, includeDeleted=null
2025-06-06 09:20:55.382 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门搜索完成，共找到1条记录
2025-06-06 09:30:00.331 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:30:00.340 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:30:18.006 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 30832 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:30:18.009 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:30:19.754 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:30:19.762 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:30:19.800 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:30:19.809 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:30:19.813 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:30:19.832 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:30:19.869 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:30:19.872 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:30:19.907 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-06 09:30:19.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:30:19.945 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:30:20.009 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-06 09:30:21.412 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:30:21.427 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:30:21.428 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:30:21.429 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:30:21.694 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:30:21.695 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3605 ms
2025-06-06 09:30:21.783 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:30:21.996 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:30:22.656 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:30:23.814 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:30:23.899 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:30:24.184 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:30:24.662 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:30:25.714 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:30:25.729 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:30:27.915 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:30:31.258 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:30:31.276 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:30:32.128 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.421 seconds (JVM running for 19.259)
2025-06-06 09:30:39.303 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:30:39.304 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:30:39.311 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 09:31:00.378 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始按部门分页查询用户列表
2025-06-06 09:31:00.379 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门ID: 1002, 包含子部门: true, 页码: 1, 页大小: 10
2025-06-06 09:33:32.042 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:33:32.066 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:33:39.858 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 9624 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:33:39.863 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:33:41.960 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:33:41.964 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:33:41.999 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:33:42.009 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:33:42.012 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:33:42.026 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:33:42.038 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:33:42.040 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:33:42.070 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-06-06 09:33:42.092 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:33:42.094 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:33:42.123 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-06 09:33:43.676 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:33:43.694 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:33:43.695 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:33:43.696 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:33:44.162 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:33:44.163 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4149 ms
2025-06-06 09:33:44.279 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:33:44.378 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:33:44.653 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:33:45.659 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:33:45.744 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:33:46.015 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:33:46.313 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:33:47.417 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:33:47.445 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:33:49.453 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:33:53.970 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:33:54.014 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:33:54.822 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.21 seconds (JVM running for 19.681)
2025-06-06 09:33:59.678 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:33:59.679 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:33:59.688 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 09:34:00.507 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始按部门分页查询用户列表
2025-06-06 09:34:00.508 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门ID: 1002, 包含子部门: true, 页码: 1, 页大小: 10
2025-06-06 09:34:00.863 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 4
2025-06-06 09:34:01.224 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 4
2025-06-06 09:34:01.225 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 按部门查询用户列表完成
2025-06-06 09:34:49.805 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始按部门分页查询用户列表
2025-06-06 09:34:49.805 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门ID: 1002, 包含子部门: true, 页码: 1, 页大小: 10
2025-06-06 09:34:49.879 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 4
2025-06-06 09:34:50.236 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 4
2025-06-06 09:34:50.237 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 按部门查询用户列表完成
2025-06-06 09:45:51.801 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:45:51.809 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:52:52.689 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 09:52:52.705 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 34732 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:52:52.722 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:52:56.053 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:52:56.056 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:52:56.090 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:52:56.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:52:56.096 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:52:56.107 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:52:56.118 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:52:56.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:52:56.148 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-06 09:52:56.171 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:52:56.173 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:52:56.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 09:52:58.435 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:52:58.457 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:52:58.458 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:52:58.458 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:52:58.761 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:52:58.762 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4865 ms
2025-06-06 09:52:58.852 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:52:58.994 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:52:59.294 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:53:00.599 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:53:00.690 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:53:01.135 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:53:01.641 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:53:03.161 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:53:03.174 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:53:05.452 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:53:08.459 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:53:08.482 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:53:09.521 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.445 seconds (JVM running for 22.458)
2025-06-06 09:53:56.933 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:53:56.933 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:53:56.941 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-06-06 09:54:54.540 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始新增部门
2025-06-06 09:54:54.541 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门名称: 测试部, 父部门ID: 10086, 排序序号: 1
2025-06-06 09:55:27.568 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始新增部门
2025-06-06 09:55:27.568 [http-nio-8285-exec-9] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门名称: 测试部, 父部门ID: null, 排序序号: 1
2025-06-06 09:55:37.194 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始新增部门
2025-06-06 09:55:37.194 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门名称: 测试部, 父部门ID: null, 排序序号: 1
2025-06-06 09:58:17.019 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:58:17.028 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 09:58:31.720 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 09:58:31.724 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 26520 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 09:58:31.726 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 09:58:34.036 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:58:34.049 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:58:34.113 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 09:58:34.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:58:34.121 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 09:58:34.142 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 09:58:34.169 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:58:34.171 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 09:58:34.210 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 JPA repository interfaces.
2025-06-06 09:58:34.270 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 09:58:34.276 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 09:58:34.342 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Redis repository interfaces.
2025-06-06 09:58:36.059 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 09:58:36.071 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 09:58:36.072 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 09:58:36.072 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 09:58:36.319 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 09:58:36.320 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4498 ms
2025-06-06 09:58:36.367 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 09:58:36.421 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 09:58:36.670 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 09:58:37.869 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 09:58:37.957 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 09:58:38.198 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 09:58:38.396 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 09:58:39.618 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 09:58:39.637 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 09:58:41.530 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 09:58:45.788 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 09:58:45.810 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 09:58:46.601 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.871 seconds (JVM running for 18.414)
2025-06-06 09:58:57.175 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 09:58:57.176 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 09:58:57.178 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 09:58:57.655 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始新增部门
2025-06-06 09:58:57.655 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门名称: 测试部, 父部门ID: null, 排序序号: 1
2025-06-06 09:58:59.829 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门新增成功，ID: 1930806593885179904, 完整路径: 测试部
2025-06-06 10:17:19.356 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:17:19.363 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:17:37.270 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 20196 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:17:37.273 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:17:37.327 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:17:39.812 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:17:39.815 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:17:39.853 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:17:39.858 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:17:39.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:17:39.874 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:17:39.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:17:39.884 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:17:39.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-06 10:17:39.936 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:17:39.938 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:17:39.965 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 10:17:41.112 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:17:41.129 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:17:41.130 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:17:41.130 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:17:41.455 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:17:41.456 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3908 ms
2025-06-06 10:17:41.528 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:17:41.609 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:17:42.050 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:17:42.974 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:17:43.071 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:17:43.351 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:17:43.559 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:17:44.773 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:17:44.795 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:17:46.664 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:17:50.229 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:17:50.253 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:17:50.935 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.919 seconds (JVM running for 19.15)
2025-06-06 10:18:04.562 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 10:18:04.563 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 10:18:04.571 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 10:23:25.374 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:23:25.383 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:23:38.603 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 24320 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:23:38.606 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:23:38.608 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:23:40.327 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:23:40.332 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:23:40.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:23:40.362 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:23:40.364 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:23:40.377 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:23:40.385 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:23:40.387 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:23:40.403 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 10:23:40.423 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:23:40.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:23:40.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 10:23:41.891 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:23:41.909 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:23:41.910 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:23:41.912 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:23:42.146 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:23:42.147 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3452 ms
2025-06-06 10:23:42.211 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:23:42.273 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:23:42.539 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:23:43.540 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:23:43.603 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:23:43.822 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:23:44.056 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:23:45.554 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:23:45.579 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:23:46.959 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:23:49.785 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:23:49.801 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:23:50.571 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.276 seconds (JVM running for 15.461)
2025-06-06 10:24:16.337 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 10:24:16.337 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 10:24:16.343 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 10:24:17.012 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始修改部门
2025-06-06 10:24:17.013 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1002, 新名称: 技术研发部, 排序序号: 1
2025-06-06 10:24:17.540 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门修改成功，ID: 1002, 新名称: 技术研发部, 完整路径: 总公司/技术研发部
2025-06-06 10:31:01.930 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:31:01.941 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:31:15.534 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 32608 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:31:15.536 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:31:15.536 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:31:17.609 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:31:17.613 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:31:17.675 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 43 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:31:17.685 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:31:17.686 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:31:17.704 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:31:17.727 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:31:17.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:31:17.773 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-06 10:31:17.809 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:31:17.812 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:31:18.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
2025-06-06 10:31:19.344 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:31:19.357 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:31:19.359 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:31:19.359 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:31:19.634 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:31:19.634 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3982 ms
2025-06-06 10:31:19.699 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:31:19.757 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:31:19.942 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:31:20.855 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:31:20.983 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:31:21.376 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:31:21.638 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:31:22.572 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:31:22.586 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:31:24.561 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:31:27.441 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:31:27.466 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:31:28.518 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.898 seconds (JVM running for 16.63)
2025-06-06 10:31:56.513 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 10:31:56.513 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 10:31:56.516 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 10:32:20.073 [http-nio-8285-exec-6] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 10:32:20.074 [http-nio-8285-exec-6] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1005, 级联删除: true
2025-06-06 10:37:06.416 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:37:06.430 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:37:21.011 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:37:21.110 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27008 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:37:21.117 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:37:25.773 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:37:25.778 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:37:25.841 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:37:25.848 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:37:25.849 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:37:25.868 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:37:25.890 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:37:25.893 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:37:25.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-06 10:37:25.961 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:37:25.964 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:37:26.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Redis repository interfaces.
2025-06-06 10:37:28.250 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:37:28.285 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:37:28.286 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:37:28.286 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:37:28.903 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:37:28.904 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7652 ms
2025-06-06 10:37:29.021 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:37:29.239 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:37:29.609 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:37:31.140 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:37:31.286 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:37:31.779 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:37:32.210 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:37:33.389 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:37:33.407 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:37:36.141 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:37:40.073 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:37:40.101 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:37:42.315 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.405 seconds (JVM running for 25.244)
2025-06-06 10:40:26.728 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:40:26.735 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:40:38.988 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:40:38.990 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 2492 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:40:38.992 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:40:40.795 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:40:40.799 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:40:40.826 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:40:40.829 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:40:40.830 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:40:40.841 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:40:40.853 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:40:40.855 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:40:40.870 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 10:40:40.898 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:40:40.900 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:40:40.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 10:40:42.500 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:40:42.521 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:40:42.521 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:40:42.521 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:40:42.746 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:40:42.746 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3662 ms
2025-06-06 10:40:42.799 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:40:42.854 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:40:43.094 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:40:44.055 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:40:44.124 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:40:44.520 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:40:44.787 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:40:46.251 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:40:46.275 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:40:48.232 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:40:50.938 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:40:50.957 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:40:51.780 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.973 seconds (JVM running for 16.089)
2025-06-06 10:40:58.265 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 10:40:58.266 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 10:40:58.275 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 10:40:59.107 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 10:40:59.108 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1005, 级联删除: true
2025-06-06 10:40:59.520 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 找到0个子部门需要删除
2025-06-06 10:41:36.010 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 10:41:36.011 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1930806593885180000, 级联删除: true
2025-06-06 10:55:06.278 [SpringApplicationShutdownHook] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:55:06.280 [SpringApplicationShutdownHook] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-06 10:55:06.287 [SpringApplicationShutdownHook] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-06 10:55:06.677 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:55:06.711 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 10:56:00.157 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 10:56:00.174 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 2772 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 10:56:00.176 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 10:56:07.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:56:07.053 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:56:07.144 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 71 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 10:56:07.160 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:56:07.162 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 10:56:07.204 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 10:56:07.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:56:07.258 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 10:56:07.335 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 JPA repository interfaces.
2025-06-06 10:56:07.403 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 10:56:07.409 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 10:56:07.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 94 ms. Found 0 Redis repository interfaces.
2025-06-06 10:56:12.080 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 10:56:12.171 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 10:56:12.176 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 10:56:12.180 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 10:56:12.876 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 10:56:12.878 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 12473 ms
2025-06-06 10:56:13.008 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 10:56:13.192 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 10:56:13.653 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 10:56:15.535 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 10:56:15.815 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 10:56:17.021 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 10:56:18.191 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 10:56:21.800 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 10:56:21.855 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 10:56:27.520 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 10:56:35.979 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 10:56:36.080 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 10:56:40.828 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 45.813 seconds (JVM running for 53.297)
2025-06-06 10:57:03.108 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 10:57:03.110 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 10:57:03.121 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-06-06 10:57:04.174 [http-nio-8285-exec-2] WARN  o.s.h.c.json.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson deserialization for type [[simple type, class com.dfit.percode.vo.DeleteOrgStructureRequestVO]]: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.fasterxml.jackson.databind.deser.std.NumberDeserializers$LongDeserializer': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.lang.Class<java.lang.Long>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-06 10:57:04.300 [http-nio-8285-exec-2] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content type 'application/json;charset=UTF-8' not supported]
2025-06-06 11:05:23.545 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:05:23.558 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 11:05:44.755 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 26372 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 11:05:44.763 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 11:05:44.765 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 11:05:49.397 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:05:49.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:05:49.458 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 11:05:49.465 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:05:49.466 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:05:49.494 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 11:05:49.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:05:49.517 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 11:05:49.541 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-06 11:05:49.576 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:05:49.579 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 11:05:49.624 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Redis repository interfaces.
2025-06-06 11:05:52.496 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 11:05:52.544 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 11:05:52.549 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 11:05:52.549 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 11:05:53.393 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 11:05:53.395 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8473 ms
2025-06-06 11:05:53.587 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 11:05:53.754 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 11:05:54.207 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 11:05:55.528 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 11:05:55.720 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 11:05:56.268 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 11:05:57.965 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 11:05:59.984 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 11:06:00.014 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:06:03.246 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 11:06:09.927 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 11:06:09.968 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 11:06:12.609 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 30.613 seconds (JVM running for 34.632)
2025-06-06 11:07:03.262 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 11:07:03.263 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 11:07:03.281 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 11:07:03.922 [http-nio-8285-exec-1] WARN  o.s.h.c.json.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson deserialization for type [[simple type, class com.dfit.percode.vo.DeleteOrgStructureRequestVO]]: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.fasterxml.jackson.databind.deser.std.NumberDeserializers$LongDeserializer': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.lang.Class<java.lang.Long>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-06 11:07:03.929 [http-nio-8285-exec-1] WARN  o.s.h.c.json.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson deserialization for type [[simple type, class com.dfit.percode.vo.DeleteOrgStructureRequestVO]]: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Class com.fasterxml.jackson.databind.deser.std.NumberDeserializers$LongDeserializer has no default (no arg) constructor
2025-06-06 11:07:03.936 [http-nio-8285-exec-1] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content type 'application/json;charset=UTF-8' not supported]
2025-06-06 11:07:40.552 [http-nio-8285-exec-7] WARN  o.s.h.c.json.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson deserialization for type [[simple type, class com.dfit.percode.vo.DeleteOrgStructureRequestVO]]: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'com.fasterxml.jackson.databind.deser.std.NumberDeserializers$LongDeserializer': Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'java.lang.Class<java.lang.Long>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-06-06 11:07:40.556 [http-nio-8285-exec-7] WARN  o.s.h.c.json.MappingJackson2HttpMessageConverter - Failed to evaluate Jackson deserialization for type [[simple type, class com.dfit.percode.vo.DeleteOrgStructureRequestVO]]: com.fasterxml.jackson.databind.exc.InvalidDefinitionException: Class com.fasterxml.jackson.databind.deser.std.NumberDeserializers$LongDeserializer has no default (no arg) constructor
2025-06-06 11:07:40.557 [http-nio-8285-exec-7] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.HttpMediaTypeNotSupportedException: Content type 'application/json;charset=UTF-8' not supported]
2025-06-06 11:12:27.526 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:12:27.535 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 11:13:00.804 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 31000 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 11:13:00.810 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 11:13:01.443 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 11:13:07.858 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:13:07.872 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:13:08.109 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 134 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 11:13:08.121 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:13:08.124 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:13:08.200 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 11:13:08.285 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:13:08.291 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 11:13:08.356 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 JPA repository interfaces.
2025-06-06 11:13:08.485 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:13:08.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 11:13:08.609 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-06 11:13:13.381 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 11:13:13.418 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 11:13:13.421 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 11:13:13.422 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 11:13:14.364 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 11:13:14.368 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 13229 ms
2025-06-06 11:13:14.620 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 11:13:14.789 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 11:13:15.358 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 11:13:16.773 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 11:13:17.016 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 11:13:17.773 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 11:13:18.437 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 11:13:21.025 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 11:13:21.069 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:13:24.196 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 11:13:30.142 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 11:13:30.203 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 11:13:33.000 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 36.008 seconds (JVM running for 40.441)
2025-06-06 11:13:39.259 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 11:13:39.261 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 11:13:39.280 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 15 ms
2025-06-06 11:13:40.253 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 11:13:40.255 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1930806593885180000, 级联删除: true
2025-06-06 11:21:50.200 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:21:50.250 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 11:22:15.103 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 11:22:15.120 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 33364 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 11:22:15.123 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 11:22:19.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:22:19.455 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:22:19.600 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 116 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 11:22:19.611 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:22:19.613 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:22:19.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 61 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 11:22:19.810 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:22:19.815 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 11:22:19.876 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 JPA repository interfaces.
2025-06-06 11:22:19.956 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:22:19.965 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 11:22:20.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-06 11:22:22.444 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 11:22:22.500 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 11:22:22.501 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 11:22:22.502 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 11:22:23.674 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 11:22:23.677 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8376 ms
2025-06-06 11:22:24.295 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 11:22:24.658 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 11:22:25.501 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 11:22:26.730 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 11:22:27.027 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 11:22:27.720 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 11:22:28.432 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 11:22:32.152 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 11:22:32.201 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:22:37.064 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 11:22:45.331 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 11:22:45.376 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 11:22:47.740 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 35.563 seconds (JVM running for 40.57)
2025-06-06 11:22:57.326 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 11:22:57.327 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 11:22:57.340 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2025-06-06 11:22:58.686 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 11:22:58.688 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1930806593885180000, 级联删除: true
2025-06-06 11:23:19.186 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始删除部门
2025-06-06 11:23:19.186 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门ID: 1930806593885179904, 级联删除: true
2025-06-06 11:23:19.421 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 找到0个子部门需要删除
2025-06-06 11:23:19.887 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门删除成功，主部门ID: 1930806593885179904, 删除子部门数量: 0, 总删除数量: 1
2025-06-06 11:42:04.950 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:42:04.963 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 11:42:40.615 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 25220 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 11:42:40.631 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 11:42:40.637 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 11:42:46.287 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:42:46.311 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:42:46.538 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 174 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 11:42:46.552 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:42:46.554 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 11:42:46.685 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 11:42:46.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:42:46.734 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 11:42:46.879 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 JPA repository interfaces.
2025-06-06 11:42:46.999 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 11:42:47.004 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 11:42:47.090 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-06-06 11:42:50.972 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 11:42:51.010 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 11:42:51.013 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 11:42:51.014 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 11:42:51.755 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 11:42:51.757 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 10672 ms
2025-06-06 11:42:51.926 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 11:42:52.185 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 11:42:52.783 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 11:42:54.292 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 11:42:54.439 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 11:42:54.856 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 11:42:55.446 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 11:42:58.113 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 11:42:58.152 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 11:43:02.211 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 11:43:07.842 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 11:43:07.892 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 11:43:09.621 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 31.577 seconds (JVM running for 38.194)
2025-06-06 11:43:15.286 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 11:43:15.287 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 11:43:15.293 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 11:43:53.837 [http-nio-8285-exec-6] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-06 11:43:53.839 [http-nio-8285-exec-6] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: null, 包含已删除: false, 最大层级: 0
2025-06-06 11:43:56.497 [http-nio-8285-exec-6] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功，根部门数量: 2
2025-06-06 12:08:47.736 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表
2025-06-06 12:08:47.737 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: permission_management, 菜单名称: 用户, 包含禁用菜单: true
2025-06-06 12:08:48.005 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功，根节点数量: 0
2025-06-06 12:09:10.628 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表
2025-06-06 12:09:10.629 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: permission_management, 菜单名称: null, 包含禁用菜单: true
2025-06-06 12:09:11.758 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功，根节点数量: 2
2025-06-06 12:32:59.907 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 12:32:59.956 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 12:35:28.946 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 32880 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 12:35:28.948 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 12:35:28.949 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 12:35:32.438 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:35:32.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 12:35:32.485 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 12:35:32.491 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:35:32.492 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 12:35:32.514 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 12:35:32.523 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:35:32.524 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 12:35:32.548 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-06-06 12:35:32.565 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:35:32.568 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 12:35:32.593 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 12:35:34.934 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 12:35:34.953 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 12:35:34.954 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 12:35:34.955 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 12:35:35.259 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 12:35:35.260 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5686 ms
2025-06-06 12:35:35.339 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 12:35:35.403 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 12:35:35.633 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 12:35:36.521 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 12:35:36.586 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 12:35:36.980 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 12:35:37.348 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 12:35:39.147 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 12:35:39.175 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 12:35:41.100 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 12:35:46.093 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 12:35:46.118 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 12:35:46.981 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.163 seconds (JVM running for 22.506)
2025-06-06 12:46:24.041 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 12:46:24.049 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 12:46:36.805 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 12:46:36.807 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 4328 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 12:46:36.809 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 12:46:38.958 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:46:38.963 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 12:46:39.005 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 12:46:39.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:46:39.017 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 12:46:39.032 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 12:46:39.041 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:46:39.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 12:46:39.066 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 12:46:39.084 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 12:46:39.085 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 12:46:39.111 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-06 12:46:40.179 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 12:46:40.192 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 12:46:40.193 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 12:46:40.193 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 12:46:40.405 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 12:46:40.405 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3516 ms
2025-06-06 12:46:40.496 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 12:46:40.596 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 12:46:40.917 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 12:46:41.813 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 12:46:41.895 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 12:46:42.131 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 12:46:42.370 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 12:46:43.321 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 12:46:43.332 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 12:46:44.740 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 12:46:47.520 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 12:46:47.533 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 12:46:48.143 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.47 seconds (JVM running for 15.354)
2025-06-06 12:47:12.068 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 12:47:12.069 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 12:47:12.077 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-06-06 12:47:22.783 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始分页查询角色列表
2025-06-06 12:47:22.785 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 页码: 1, 页大小: 10, 角色名称: null, 状态: null
2025-06-06 12:47:22.981 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到角色数量: 6
2025-06-06 12:47:23.048 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色总数: 6
2025-06-06 12:47:23.049 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色列表查询完成，当前页: 1/1, 总记录数: 6
2025-06-06 12:48:34.054 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始分页查询角色列表
2025-06-06 12:48:34.055 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 页码: 1, 页大小: 10, 角色名称: 管理, 状态: null
2025-06-06 12:48:34.196 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到角色数量: 3
2025-06-06 12:48:34.264 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色总数: 3
2025-06-06 12:48:34.265 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色列表查询完成，当前页: 1/1, 总记录数: 3
2025-06-06 13:03:14.771 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:03:14.877 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:07:38.631 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:07:38.858 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 35868 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:07:38.862 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:07:45.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:07:45.171 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:07:45.237 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:07:45.244 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:07:45.245 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:07:45.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:07:45.288 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:07:45.291 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:07:45.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 JPA repository interfaces.
2025-06-06 13:07:45.397 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:07:45.401 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:07:45.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-06 13:07:47.528 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:07:47.545 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:07:47.546 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:07:47.546 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:07:47.884 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:07:47.884 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8235 ms
2025-06-06 13:07:47.996 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:07:48.070 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:07:48.257 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:07:49.154 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:07:49.243 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:07:49.910 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:07:50.615 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:07:51.665 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:07:51.680 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:07:53.319 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:08:01.065 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:08:01.189 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:08:04.755 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 30.929 seconds (JVM running for 34.077)
2025-06-06 13:08:29.141 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:08:29.141 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:08:29.150 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 13:09:18.384 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 13:09:18.385 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 完整权限角色, 是否停用: false
2025-06-06 13:09:18.697 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 生成角色ID: 1930854490345443328
2025-06-06 13:12:12.379 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:12:12.386 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:12:23.969 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:12:23.970 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 3340 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:12:23.971 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:12:25.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:12:25.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:12:25.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:12:25.458 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:12:25.459 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:12:25.470 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:12:25.478 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:12:25.480 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:12:25.497 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-06 13:12:25.517 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:12:25.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:12:25.537 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 13:12:26.998 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:12:27.014 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:12:27.014 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:12:27.014 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:12:27.230 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:12:27.230 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3174 ms
2025-06-06 13:12:27.295 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:12:27.338 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:12:27.503 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:12:31.141 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:12:31.202 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:12:31.388 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:12:31.541 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:12:33.231 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:12:33.247 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:12:34.852 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:12:37.522 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:12:37.539 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:12:38.200 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.347 seconds (JVM running for 17.199)
2025-06-06 13:12:43.182 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:12:43.183 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:12:43.188 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 13:12:43.665 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 13:12:43.666 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 完整权限角色, 是否停用: false
2025-06-06 13:12:44.312 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 生成角色ID: 1930855352757260288
2025-06-06 13:17:18.002 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:17:18.007 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:17:29.880 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 37336 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:17:29.881 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:17:29.882 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:17:31.316 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:17:31.321 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:17:31.360 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:17:31.364 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:17:31.364 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:17:31.376 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:17:31.391 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:17:31.392 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:17:31.413 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-06 13:17:31.446 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:17:31.448 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:17:31.478 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-06 13:17:32.939 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:17:32.951 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:17:32.952 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:17:32.952 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:17:33.159 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:17:33.160 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3202 ms
2025-06-06 13:17:33.221 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:17:33.278 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:17:33.547 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:17:34.648 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:17:34.725 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:17:34.948 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:17:35.185 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:17:36.472 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:17:36.495 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:17:37.907 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:17:40.416 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:17:40.432 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:17:41.040 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.116 seconds (JVM running for 14.064)
2025-06-06 13:18:07.141 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:18:07.142 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:18:07.146 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 13:18:07.550 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 13:18:07.551 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 完整权限角色, 是否停用: false
2025-06-06 13:18:07.832 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 生成角色ID: 1930856709698490368
2025-06-06 13:18:07.906 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息插入成功
2025-06-06 13:18:08.041 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限保存完成，数量: 2
2025-06-06 13:18:08.299 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限保存完成，数量: 3
2025-06-06 13:18:08.300 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色新增完成，角色ID: 1930856709698490368, 菜单权限数: 2, 数据权限数: 3
2025-06-06 13:29:43.655 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:29:43.659 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:30:00.770 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:30:00.789 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 14328 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:30:00.790 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:30:03.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:30:03.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:30:03.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 63 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:30:03.127 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:30:03.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:30:03.185 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 56 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:30:03.201 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:30:03.204 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:30:03.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-06 13:30:03.379 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:30:03.382 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:30:03.584 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 92 ms. Found 0 Redis repository interfaces.
2025-06-06 13:30:05.416 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:30:05.437 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:30:05.437 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:30:05.438 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:30:05.813 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:30:05.814 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4883 ms
2025-06-06 13:30:05.947 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:30:06.016 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:30:06.215 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:30:07.157 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:30:07.257 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:30:07.645 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:30:07.961 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:30:11.133 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:30:11.160 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:30:13.661 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:30:16.851 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:30:16.869 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:30:18.157 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.823 seconds (JVM running for 20.948)
2025-06-06 13:30:22.382 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:30:22.382 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:30:22.385 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 13:30:33.347 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:30:33.348 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930806593885180000, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:35:27.103 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:35:27.111 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:35:40.443 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 7772 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:35:40.444 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:35:40.445 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:35:42.153 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:35:42.157 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:35:42.206 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 39 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:35:42.212 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:35:42.213 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:35:42.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:35:42.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:35:42.280 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:35:42.300 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-06 13:35:42.320 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:35:42.322 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:35:42.353 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-06 13:35:43.493 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:35:43.512 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:35:43.512 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:35:43.513 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:35:43.742 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:35:43.743 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3220 ms
2025-06-06 13:35:43.793 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:35:43.838 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:35:43.996 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:35:45.217 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:35:45.370 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:35:45.723 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:35:45.886 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:35:46.829 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:35:46.839 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:35:48.231 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:35:50.612 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:35:50.631 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:35:51.362 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.724 seconds (JVM running for 14.032)
2025-06-06 13:36:04.964 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:36:04.965 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:36:04.967 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 13:36:05.361 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:36:05.363 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930806593885180000, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:36:20.603 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:36:20.604 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930806593885179907, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:36:23.449 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:36:23.449 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930806593885179907, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:36:54.969 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:36:54.970 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:43:18.288 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:43:18.295 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:43:27.747 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 14940 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:43:27.749 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:43:27.750 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:43:29.301 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:43:29.307 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:43:29.342 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:43:29.348 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:43:29.349 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:43:29.363 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:43:29.372 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:43:29.373 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:43:29.396 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-06 13:43:29.416 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:43:29.418 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:43:29.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 13:43:30.896 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:43:30.912 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:43:30.913 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:43:30.913 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:43:31.182 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:43:31.182 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3339 ms
2025-06-06 13:43:31.236 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:43:31.296 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:43:31.451 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:43:32.330 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:43:32.390 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:43:32.577 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:43:32.753 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:43:33.662 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:43:33.676 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:43:35.378 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:43:37.638 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:43:37.658 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:43:38.594 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.05 seconds (JVM running for 13.876)
2025-06-06 13:44:02.713 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:44:02.713 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:44:02.717 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 13:44:03.140 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:44:03.142 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:49:18.875 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:49:18.880 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:49:29.924 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:49:29.929 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 2696 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:49:29.931 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:49:31.749 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:49:31.752 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:49:31.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:49:31.781 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:49:31.782 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:49:31.794 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:49:31.801 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:49:31.804 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:49:31.821 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 13:49:31.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:49:31.838 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:49:31.857 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-06 13:49:32.891 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:49:32.903 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:49:32.903 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:49:32.904 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:49:33.120 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:49:33.120 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3103 ms
2025-06-06 13:49:33.212 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:49:33.286 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:49:33.493 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:49:34.406 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:49:34.471 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:49:34.653 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:49:34.827 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:49:35.877 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:49:35.888 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:49:36.155 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataModuleController': Unsatisfied dependency expressed through field 'dataModuleService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'TDataModuleServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'TDataModuleMapper' defined in file [G:\fushun\permissionCode\target\classes\com\dfit\percode\mapper\TDataModuleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mybatisPlusInterceptor' defined in class path resource [com/dfit/percode/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor]: Factory method 'mybatisPlusInterceptor' threw exception; nested exception is java.lang.NoSuchMethodError: 'net.sf.jsqlparser.statement.select.SelectExpressionItem net.sf.jsqlparser.statement.select.SelectExpressionItem.withAlias(net.sf.jsqlparser.expression.Alias)'
2025-06-06 13:49:36.155 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:49:36.160 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:49:36.165 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-06 13:49:36.193 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-06 13:50:17.742 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:50:17.744 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 15136 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:50:17.745 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:50:19.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:50:19.553 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:50:19.575 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:50:19.581 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:50:19.581 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:50:19.591 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:50:19.599 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:50:19.600 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:50:19.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-06 13:50:19.628 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:50:19.630 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:50:19.652 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 13:50:20.754 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:50:20.765 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:50:20.765 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:50:20.766 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:50:20.949 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:50:20.950 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3081 ms
2025-06-06 13:50:21.014 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:50:21.076 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:50:21.272 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:50:22.184 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:50:22.288 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:50:22.572 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:50:22.782 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:50:23.649 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:50:23.661 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:50:23.791 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataModuleController': Unsatisfied dependency expressed through field 'dataModuleService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'TDataModuleServiceImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'TDataModuleMapper' defined in file [G:\fushun\permissionCode\target\classes\com\dfit\percode\mapper\TDataModuleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration': Bean instantiation via constructor failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration]: Constructor threw exception; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mybatisPlusInterceptor' defined in class path resource [com/dfit/percode/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor]: Factory method 'mybatisPlusInterceptor' threw exception; nested exception is java.lang.NoSuchMethodError: 'net.sf.jsqlparser.statement.select.SelectExpressionItem net.sf.jsqlparser.statement.select.SelectExpressionItem.withAlias(net.sf.jsqlparser.expression.Alias)'
2025-06-06 13:50:23.791 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:50:23.798 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:50:23.800 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-06 13:50:23.827 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-06 13:55:49.785 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:55:49.785 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12980 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:55:49.787 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:55:51.786 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:55:51.790 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:55:51.828 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:55:51.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:55:51.838 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:55:51.852 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:55:51.865 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:55:51.866 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:55:51.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-06 13:55:51.913 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:55:51.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:55:51.935 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-06 13:55:52.909 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:55:52.925 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:55:52.926 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:55:52.926 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:55:53.155 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:55:53.155 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3297 ms
2025-06-06 13:55:53.230 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:55:53.291 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:55:53.497 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:55:54.342 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:55:54.423 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:55:54.682 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:55:54.942 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:55:55.975 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:55:55.989 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:55:57.220 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:55:59.873 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:55:59.891 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:56:00.559 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.74 seconds (JVM running for 13.566)
2025-06-06 13:56:11.592 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:56:11.592 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:56:11.595 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-06 13:56:12.027 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:56:12.027 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 13:58:38.631 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:58:38.642 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 13:58:50.045 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 8264 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 13:58:50.047 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 13:58:50.052 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 13:58:52.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:58:52.306 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:58:52.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 13:58:52.336 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:58:52.337 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 13:58:52.348 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 13:58:52.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:58:52.359 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 13:58:52.374 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-06 13:58:52.389 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 13:58:52.391 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 13:58:52.414 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 13:58:53.878 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 13:58:53.897 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 13:58:53.898 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 13:58:53.898 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 13:58:54.186 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 13:58:54.186 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3966 ms
2025-06-06 13:58:54.256 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 13:58:54.314 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 13:58:54.497 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 13:58:55.296 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 13:58:55.357 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 13:58:55.546 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 13:58:55.727 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 13:58:56.879 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 13:58:56.894 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 13:58:58.198 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 13:59:01.456 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 13:59:01.475 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 13:59:02.155 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.411 seconds (JVM running for 15.241)
2025-06-06 13:59:07.407 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 13:59:07.408 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 13:59:07.412 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 13:59:07.827 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 13:59:07.827 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:00:21.809 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:00:21.814 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:00:36.516 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 4532 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:00:36.520 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:00:36.537 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:00:38.305 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:00:38.309 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:00:38.346 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:00:38.352 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:00:38.352 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:00:38.364 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:00:38.374 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:00:38.375 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:00:38.394 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-06 14:00:38.418 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:00:38.420 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:00:38.445 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-06 14:00:40.349 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:00:40.360 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:00:40.361 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:00:40.361 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:00:40.616 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:00:40.616 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3953 ms
2025-06-06 14:00:40.694 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:00:40.773 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:00:40.949 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:00:42.032 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:00:42.112 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:00:42.332 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:00:42.561 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:00:44.022 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:00:44.065 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:00:46.113 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:00:49.395 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:00:49.413 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:00:50.162 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.923 seconds (JVM running for 17.169)
2025-06-06 14:00:54.018 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 14:00:54.019 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 14:00:54.022 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 14:00:54.591 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:00:54.592 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:00:58.554 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:00:58.554 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:01:51.690 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:01:51.698 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:02:01.918 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:02:01.919 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 19804 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:02:01.920 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:02:03.500 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:02:03.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:02:03.530 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:02:03.533 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:02:03.534 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:02:03.548 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:02:03.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:02:03.562 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:02:03.580 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-06 14:02:03.604 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:02:03.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:02:03.634 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-06 14:02:05.066 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:02:05.077 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:02:05.078 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:02:05.079 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:02:05.264 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:02:05.265 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3254 ms
2025-06-06 14:02:05.332 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:02:05.384 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:02:05.555 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:02:06.682 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:02:06.747 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:02:07.005 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:02:07.291 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:02:08.425 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:02:08.441 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:02:09.918 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:02:12.574 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:02:12.592 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:02:13.277 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.62 seconds (JVM running for 14.511)
2025-06-06 14:02:17.948 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 14:02:17.949 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 14:02:17.954 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-06 14:02:18.322 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:02:18.323 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:02:18.653 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功
2025-06-06 14:05:06.969 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:05:06.973 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:05:17.487 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 29008 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:05:17.489 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:05:17.497 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:05:18.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:05:18.955 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:05:18.980 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:05:18.986 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:05:18.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:05:18.997 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:05:19.004 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:05:19.005 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:05:19.019 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-06 14:05:19.034 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:05:19.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:05:19.059 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-06 14:05:20.364 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:05:20.375 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:05:20.376 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:05:20.376 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:05:20.691 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:05:20.692 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3104 ms
2025-06-06 14:05:20.804 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:05:20.868 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:05:21.080 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:05:22.082 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:05:22.144 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:05:22.359 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:05:22.558 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:05:23.440 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:05:23.456 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:05:25.308 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:05:28.329 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:05:28.346 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:05:28.989 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.249 seconds (JVM running for 14.919)
2025-06-06 14:05:33.562 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 14:05:33.562 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 14:05:33.565 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-06 14:05:34.080 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:05:34.080 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:05:34.601 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功
2025-06-06 14:07:19.337 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:07:19.342 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:07:29.032 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12172 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:07:29.033 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:07:29.034 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:07:30.658 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:07:30.663 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:07:30.697 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:07:30.701 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:07:30.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:07:30.718 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:07:30.728 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:07:30.729 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:07:30.751 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-06 14:07:30.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:07:30.778 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:07:30.806 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-06 14:07:32.145 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:07:32.159 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:07:32.160 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:07:32.160 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:07:32.364 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:07:32.365 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3260 ms
2025-06-06 14:07:32.426 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:07:32.474 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:07:32.633 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:07:33.488 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:07:33.562 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:07:33.800 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:07:34.025 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:07:35.177 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:07:35.188 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:07:36.431 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:07:38.957 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:07:38.976 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:07:39.637 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.507 seconds (JVM running for 13.524)
2025-06-06 14:07:43.134 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 14:07:43.134 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 14:07:43.141 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-06 14:07:43.750 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:07:43.751 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930856709698490368, 角色名称: 完整权限修改角色, 是否停用: false
2025-06-06 14:07:44.188 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功
2025-06-06 14:07:44.247 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 2
2025-06-06 14:07:44.315 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930856710013063168, 结果: 1
2025-06-06 14:07:44.376 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930856710281498624, 结果: 1
2025-06-06 14:07:44.441 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 3
2025-06-06 14:07:44.500 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930856710579294208, 结果: 1
2025-06-06 14:07:44.565 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930856710893867008, 结果: 1
2025-06-06 14:07:44.626 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930856711204245504, 结果: 1
2025-06-06 14:07:44.627 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1930856709698490368
2025-06-06 14:07:44.696 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限重新保存完成，数量: 1
2025-06-06 14:07:44.846 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限重新保存完成，数量: 2
2025-06-06 14:07:44.846 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色修改完成，角色ID: 1930856709698490368, 菜单权限数: 1, 数据权限数: 2
2025-06-06 14:10:59.605 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始删除角色，角色ID: 1930856709698490400
2025-06-06 14:11:10.342 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始删除角色，角色ID: 1930856709698490368
2025-06-06 14:11:10.412 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 检查角色是否被用户使用，角色ID: 1930856709698490368
2025-06-06 14:11:10.535 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 1
2025-06-06 14:11:10.589 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930869195290054656, 结果: 1
2025-06-06 14:11:10.650 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 2
2025-06-06 14:11:10.705 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930869195571073024, 结果: 1
2025-06-06 14:11:10.765 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930869195881451520, 结果: 1
2025-06-06 14:11:10.766 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1930856709698490368
2025-06-06 14:11:10.766 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色删除完成，角色ID: 1930856709698490368
2025-06-06 14:14:37.855 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:14:37.862 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:14:49.913 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:14:49.922 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 11364 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:14:49.923 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:14:52.370 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:14:52.374 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:14:52.420 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:14:52.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:14:52.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:14:52.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:14:52.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:14:52.454 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:14:52.482 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-06-06 14:14:52.527 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:14:52.532 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:14:52.593 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Redis repository interfaces.
2025-06-06 14:14:54.372 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:14:54.389 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:14:54.389 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:14:54.389 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:14:54.628 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:14:54.629 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4593 ms
2025-06-06 14:14:54.692 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:14:54.772 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:14:54.952 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:14:56.389 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:14:56.509 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:14:56.771 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:14:57.083 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:14:58.061 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:14:58.071 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:14:59.513 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:15:02.232 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:15:02.247 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:15:03.689 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.129 seconds (JVM running for 17.003)
2025-06-06 14:17:48.699 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 14:17:48.700 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 14:17:48.709 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-06-06 14:17:49.452 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 14:17:49.453 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 系统管理员, 是否停用: false
2025-06-06 14:18:01.518 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 14:18:01.519 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 测试管理员, 是否停用: false
2025-06-06 14:18:01.600 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 生成角色ID: 1930871783053987840
2025-06-06 14:18:01.668 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息插入成功
2025-06-06 14:18:01.809 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限保存完成，数量: 2
2025-06-06 14:18:02.088 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限保存完成，数量: 4
2025-06-06 14:18:02.089 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色新增完成，角色ID: 1930871783053987840, 菜单权限数: 2, 数据权限数: 4
2025-06-06 14:18:04.229 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 14:18:04.230 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 测试管理员, 是否停用: false
2025-06-06 14:18:24.671 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始新增角色
2025-06-06 14:18:24.671 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色名称: 测试一下, 是否停用: false
2025-06-06 14:18:24.749 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 生成角色ID: 1930871880147931136
2025-06-06 14:18:24.817 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息插入成功
2025-06-06 14:18:24.958 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限保存完成，数量: 2
2025-06-06 14:18:25.244 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限保存完成，数量: 4
2025-06-06 14:18:25.245 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色新增完成，角色ID: 1930871880147931136, 菜单权限数: 2, 数据权限数: 4
2025-06-06 14:20:10.252 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始修改角色
2025-06-06 14:20:10.253 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色ID: 1930871880147931136, 角色名称: 权限管理员, 是否停用: false
2025-06-06 14:20:10.467 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色基本信息更新成功，角色ID: 1930871880147931136
2025-06-06 14:20:10.543 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 2
2025-06-06 14:20:10.622 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930871880433143808, 结果: 1
2025-06-06 14:20:10.694 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930871880739328000, 结果: 1
2025-06-06 14:20:10.799 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 4
2025-06-06 14:20:10.899 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930871881028734976, 结果: 1
2025-06-06 14:20:10.989 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930871881292976128, 结果: 1
2025-06-06 14:20:11.064 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930871881565605888, 结果: 1
2025-06-06 14:20:11.159 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930871881880178688, 结果: 1
2025-06-06 14:20:11.159 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1930871880147931136
2025-06-06 14:20:11.309 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 菜单权限重新保存完成，数量: 2
2025-06-06 14:20:11.608 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 数据权限重新保存完成，数量: 4
2025-06-06 14:20:11.609 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色修改完成，角色ID: 1930871880147931136, 菜单权限数: 2, 数据权限数: 4
2025-06-06 14:20:32.498 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始删除角色，角色ID: 1930871880147931136
2025-06-06 14:20:32.567 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 检查角色是否被用户使用，角色ID: 1930871880147931136
2025-06-06 14:20:32.709 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的菜单权限数量: 2
2025-06-06 14:20:32.796 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930872326463819776, 结果: 1
2025-06-06 14:20:32.869 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除菜单权限关联，ID: 1930872326778392576, 结果: 1
2025-06-06 14:20:32.956 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 查询到需要删除的数据权限数量: 4
2025-06-06 14:20:33.019 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930872327097159680, 结果: 1
2025-06-06 14:20:33.087 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930872327428509696, 结果: 1
2025-06-06 14:20:33.154 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930872327759859712, 结果: 1
2025-06-06 14:20:33.229 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除数据权限关联，ID: 1930872328045072384, 结果: 1
2025-06-06 14:20:33.229 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 删除现有权限关联完成，角色ID: 1930871880147931136
2025-06-06 14:20:33.229 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色删除完成，角色ID: 1930871880147931136
2025-06-06 14:42:17.153 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:42:17.160 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 14:42:59.868 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 38452 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 14:42:59.872 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 14:42:59.910 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 14:43:01.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:43:01.997 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:43:02.038 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 14:43:02.045 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:43:02.047 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 14:43:02.064 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 14:43:02.083 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:43:02.088 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 14:43:02.121 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 JPA repository interfaces.
2025-06-06 14:43:02.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 14:43:02.157 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 14:43:02.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-06 14:43:03.830 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 14:43:03.850 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 14:43:03.850 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 14:43:03.851 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 14:43:04.152 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 14:43:04.153 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4146 ms
2025-06-06 14:43:04.230 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-06 14:43:04.282 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 14:43:04.494 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 14:43:05.922 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 14:43:06.107 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 14:43:06.459 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 14:43:06.674 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 14:43:07.860 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 14:43:07.874 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 14:43:09.903 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 14:43:12.520 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 14:43:12.568 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 14:43:13.366 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.966 seconds (JVM running for 17.62)
2025-06-06 15:31:18.980 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 15:31:19.001 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 15:58:07.472 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 31556 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 15:58:07.474 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 15:58:07.474 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 15:58:10.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 15:58:10.520 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 15:58:10.551 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 15:58:10.557 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 15:58:10.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 15:58:10.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 15:58:10.585 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 15:58:10.587 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 15:58:10.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-06 15:58:10.657 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 15:58:10.660 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 15:58:10.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-06 15:58:12.821 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 15:58:12.842 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 15:58:12.843 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 15:58:12.843 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 15:58:13.276 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 15:58:13.276 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5698 ms
2025-06-06 15:58:13.661 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 15:58:14.037 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 15:58:15.087 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 15:58:15.286 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 15:58:15.669 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 15:58:16.037 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 15:58:17.200 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 15:58:17.262 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 15:58:19.902 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 15:58:24.302 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 15:58:24.333 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 15:58:26.582 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.861 seconds (JVM running for 23.919)
2025-06-06 16:00:42.109 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 16:00:42.109 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 16:00:42.112 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-06 16:30:16.456 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 16:30:16.535 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-06 16:30:30.384 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 37692 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-06 16:30:30.386 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-06 16:30:30.389 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-06 16:30:34.827 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:30:34.834 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:30:34.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-06 16:30:34.890 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:30:34.892 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-06 16:30:34.911 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-06 16:30:34.926 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:30:34.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-06 16:30:34.955 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-06 16:30:34.989 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-06 16:30:34.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-06 16:30:35.025 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-06 16:30:36.738 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-06 16:30:36.755 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-06 16:30:36.756 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-06 16:30:36.756 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-06 16:30:37.182 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-06 16:30:37.182 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6703 ms
2025-06-06 16:30:37.551 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-06 16:30:39.429 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-06 16:30:41.817 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-06 16:30:41.910 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-06 16:30:42.170 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-06 16:30:42.589 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-06 16:30:44.206 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-06 16:30:44.253 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 16:30:45.796 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-06 16:30:49.900 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-06 16:30:49.922 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-06 16:30:51.470 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.017 seconds (JVM running for 26.763)
2025-06-06 16:32:43.388 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-06 16:32:43.391 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-06 16:32:43.395 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-06 16:32:43.840 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-06 16:32:43.841 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: null, 包含已删除: false, 最大层级: 0
2025-06-06 16:32:46.387 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功，根部门数量: 1
2025-06-06 16:51:34.159 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-06 16:51:34.170 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
