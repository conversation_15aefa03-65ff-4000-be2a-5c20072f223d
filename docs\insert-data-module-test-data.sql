-- 数据模块测试数据插入脚本
-- 为t_data_module表插入测试数据

-- 清理可能存在的测试数据
DELETE FROM t_data_module WHERE module_identifier IN (
    'user_data_module', 
    'order_data_module', 
    'product_data_module', 
    'finance_data_module', 
    'report_data_module'
);

-- 插入数据模块测试数据
INSERT INTO t_data_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) VALUES
(1001, '用户数据模块', 'user_data_module', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1002, '订单数据模块', 'order_data_module', 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1003, '商品数据模块', 'product_data_module', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1004, '财务数据模块', 'finance_data_module', 4, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1005, '报表数据模块', 'report_data_module', 5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 验证插入结果
SELECT 
    id,
    module_name as "模块名称",
    module_identifier as "模块标识",
    order_info as "排序",
    is_del as "是否删除",
    create_time as "创建时间"
FROM t_data_module 
WHERE is_del = false 
ORDER BY order_info;
