### 搜索部门接口测试

### 1. 按组织名称模糊搜索（测试用户的用例）
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "organName": "总"
}

### 2. 按组织名称模糊搜索
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "organName": "研发"
}

### 3. 按父部门ID精确搜索
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "preId": 1001
}

### 3. 组合搜索（名称+父部门）
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "organName": "开发",
  "preId": 1002
}

### 4. 包含已删除部门的搜索
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "organName": "研发",
  "includeDeleted": true
}

### 5. 搜索所有部门（空条件）
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "includeDeleted": false
}

### 6. 只搜索根部门
POST http://localhost:8080/t-org-structure/search
Content-Type: application/json

{
  "preId": null,
  "includeDeleted": false
}
