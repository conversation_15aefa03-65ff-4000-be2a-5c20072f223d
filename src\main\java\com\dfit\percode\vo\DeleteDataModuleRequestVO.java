package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除数据模块请求VO类
 * 按照前端格式要求设计（驼峰命名）
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteDataModuleRequestVO", description = "删除数据模块请求参数")
public class DeleteDataModuleRequestVO {

    @ApiModelProperty(value = "模块ID", example = "1001")
    private String moduleId;

    @ApiModelProperty(value = "模块标识符", example = "user_data_module")
    private String moduleIdentifier;

    @ApiModelProperty(value = "是否强制删除", required = false, example = "false")
    private Boolean forceDelete = false;
}
