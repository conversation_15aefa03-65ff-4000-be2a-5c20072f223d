package com.dfit.percode.util;

import cn.dev33.satoken.stp.StpUtil;
import com.dfit.percode.config.SuperAdminConfig;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.vo.UserDetailResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 超级管理员工具类
 * 提供超级管理员相关的通用方法和工具函数
 * 支持用户信息提取、身份判断、操作日志记录等功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Component
public class SuperAdminUtil {

    @Autowired
    private SuperAdminConfig superAdminConfig;

    @Autowired
    private CustomJwtUtil customJwtUtil;

    @Autowired
    private UserMapper userMapper;

    /**
     * 从token中提取用户ID
     * 支持自定义JWT和Sa-Token两种方式
     *
     * @param authorization Authorization头内容
     * @return 用户ID，提取失败返回null
     */
    public Long getUserIdFromToken(String authorization) {
        if (authorization == null || authorization.trim().isEmpty()) {
            return null;
        }

        // 尝试从自定义JWT中提取用户ID
        try {
            if (customJwtUtil.validateCustomToken(authorization)) {
                Long userId = customJwtUtil.getUserIdFromCustomToken(authorization);
                if (userId != null) {
                    log.debug("从自定义JWT成功提取用户ID: {}", userId);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("从自定义JWT提取用户ID失败: {}", e.getMessage());
        }

        // 尝试从Sa-Token中提取用户ID
        try {
            String token = authorization;
            if (authorization.startsWith("Bearer ")) {
                token = authorization.substring(7);
            }

            Object loginIdObj = StpUtil.getLoginIdByToken(token);
            if (loginIdObj != null) {
                Long userId = Long.valueOf(loginIdObj.toString());
                log.debug("从Sa-Token成功提取用户ID: {}", userId);
                return userId;
            }
        } catch (Exception e) {
            log.debug("从Sa-Token提取用户ID失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 根据用户ID从数据库查询用户信息
     * 包括登录账号、用户名等基本信息
     *
     * @param userId 用户ID
     * @return 用户信息Map，查询失败返回空Map
     */
    public Map<String, Object> getUserInfoFromDatabase(Long userId) {
        Map<String, Object> userInfo = new HashMap<>();

        if (userId == null) {
            return userInfo;
        }

        try {
            // 查询用户基本信息
            UserDetailResponseVO user = userMapper.findUserDetailById(userId);
            if (user != null) {
                userInfo.put("userId", userId);
                userInfo.put("loginAccount", user.getAccount());
                userInfo.put("username", user.getUserName());
                userInfo.put("realName", user.getUserName()); // 使用userName作为realName
                userInfo.put("isDisable", user.getIsDisable());
                userInfo.put("organAffiliation", user.getOrganAffiliation());
                userInfo.put("organName", user.getOrganName());

                log.debug("成功查询用户信息，用户ID: {}, 登录账号: {}", userId, user.getAccount());
            } else {
                log.warn("未找到用户信息，用户ID: {}", userId);
            }
        } catch (Exception e) {
            log.error("查询用户信息失败，用户ID: {}", userId, e);
        }

        return userInfo;
    }

    /**
     * 判断指定用户ID是否为超级管理员
     *
     * @param userId 用户ID
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdmin(Long userId) {
        if (!superAdminConfig.getEnabled() || userId == null) {
            return false;
        }

        return superAdminConfig.isSuperAdmin(userId);
    }

    /**
     * 综合判断是否为超级管理员
     * 支持用户ID、登录账号、用户名多种方式
     *
     * @param userId 用户ID，可为null
     * @param loginAccount 登录账号，可为null
     * @param username 用户名，可为null
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdmin(Long userId, String loginAccount, String username) {
        if (!superAdminConfig.getEnabled()) {
            return false;
        }

        return superAdminConfig.isSuperAdmin(userId, loginAccount, username);
    }

    /**
     * 记录超级管理员访问日志
     * 包含详细的访问信息和环境信息
     *
     * @param userId 用户ID
     * @param request HTTP请求对象
     * @param action 操作描述
     */
    public void logSuperAdminAccess(Long userId, HttpServletRequest request, String action) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String uri = request.getRequestURI();
            String method = request.getMethod();
            String ip = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");

            // 获取用户基本信息
            Map<String, Object> userInfo = getUserInfoFromDatabase(userId);
            String loginAccount = (String) userInfo.get("loginAccount");

            log.info("🔥 [超级管理员操作] 时间: {}, 用户ID: {}, 账号: {}, 操作: {}, URI: {}, Method: {}, IP: {}, UserAgent: {}",
                    timestamp, userId, loginAccount, action, uri, method, ip, userAgent);

        } catch (Exception e) {
            log.error("记录超级管理员访问日志失败", e);
        }
    }

    /**
     * 记录超级管理员访问日志（简化版本）
     *
     * @param userId 用户ID
     * @param uri 访问URI
     * @param action 操作描述
     */
    public void logSuperAdminAccess(Long userId, String uri, String action) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

            // 获取用户基本信息
            Map<String, Object> userInfo = getUserInfoFromDatabase(userId);
            String loginAccount = (String) userInfo.get("loginAccount");

            log.info("🔥 [超级管理员操作] 时间: {}, 用户ID: {}, 账号: {}, 操作: {}, URI: {}",
                    timestamp, userId, loginAccount, action, uri);

        } catch (Exception e) {
            log.error("记录超级管理员访问日志失败", e);
        }
    }

    /**
     * 获取客户端真实IP地址
     * 支持代理环境下的IP获取
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    public String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        String proxyClientIp = request.getHeader("Proxy-Client-IP");
        if (proxyClientIp != null && !proxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(proxyClientIp)) {
            return proxyClientIp;
        }

        String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
        if (wlProxyClientIp != null && !wlProxyClientIp.isEmpty() && !"unknown".equalsIgnoreCase(wlProxyClientIp)) {
            return wlProxyClientIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 检查超级管理员配置是否有效
     *
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isConfigValid() {
        return superAdminConfig.isConfigValid();
    }

    /**
     * 获取超级管理员配置摘要信息
     *
     * @return 配置摘要信息
     */
    public String getConfigSummary() {
        return superAdminConfig.getConfigSummary();
    }

    /**
     * 验证token并提取用户信息
     * 综合验证token有效性并提取用户信息
     *
     * @param authorization Authorization头内容
     * @return 包含用户信息的Map，验证失败返回空Map
     */
    public Map<String, Object> validateTokenAndGetUserInfo(String authorization) {
        Map<String, Object> result = new HashMap<>();

        try {
            Long userId = getUserIdFromToken(authorization);
            if (userId != null) {
                Map<String, Object> userInfo = getUserInfoFromDatabase(userId);
                if (!userInfo.isEmpty()) {
                    result.putAll(userInfo);
                    result.put("isSuperAdmin", isSuperAdmin(userId));
                    result.put("tokenValid", true);
                }
            }
        } catch (Exception e) {
            log.debug("验证token并提取用户信息失败: {}", e.getMessage());
            result.put("tokenValid", false);
        }

        return result;
    }
}
