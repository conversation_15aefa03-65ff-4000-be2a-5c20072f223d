package com.dfit.percode.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * 自定义Long类型反序列化器
 * 用于防止JavaScript中Long类型精度丢失问题
 * 支持将字符串和数字都转换为Long类型
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
public class LongDeserializer extends JsonDeserializer<Long> {
    
    @Override
    public Long deserialize(JsonParser p, DeserializationContext ctxt) 
            throws IOException, JsonProcessingException {
        
        String value = p.getValueAsString();
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Long.valueOf(value.trim());
        } catch (NumberFormatException e) {
            throw new IOException("无法将值 '" + value + "' 转换为Long类型", e);
        }
    }
}
