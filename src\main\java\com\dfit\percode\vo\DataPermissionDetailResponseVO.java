package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据权限详情响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataPermissionDetailResponseVO", description = "数据权限详情响应数据")
public class DataPermissionDetailResponseVO {
    
    @ApiModelProperty(value = "数据权限ID", example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "数据权限名称", example = "用户基础数据")
    private String name;
    
    @ApiModelProperty(value = "父级ID", example = "0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;
    
    @ApiModelProperty(value = "模块标识", example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "模块名称", example = "用户数据模块")
    private String moduleName;
    
    @ApiModelProperty(value = "数据类型", example = "1")
    private Integer dataType;
    
    @ApiModelProperty(value = "数据标识", example = "user_basic_data")
    private String dataIdentifier;
    
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "是否禁用", example = "false")
    private Boolean isDisable;
    
    @ApiModelProperty(value = "是否删除", example = "false")
    private Boolean isDel;
    
    @ApiModelProperty(value = "创建时间", example = "2023-05-15 14:30:00")
    private String createTime;
    
    @ApiModelProperty(value = "修改时间", example = "2023-05-15 14:30:00")
    private String modifyTime;
}
