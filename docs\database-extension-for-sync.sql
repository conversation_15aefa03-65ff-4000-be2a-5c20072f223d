-- 数据同步表结构扩展SQL
-- 为支持外部系统数据同步，扩展当前系统表结构

-- =====================================================
-- 1. 扩展 t_org_structure 表（部门表）
-- =====================================================

-- 添加外部系统字段到部门表
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS dept_uuid VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS org_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS parent_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS full_name VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS is_history INTEGER DEFAULT 0;
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS description TEXT;
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS fax VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS web_address VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS org_manager VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS post_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS org_type VARCHAR(50);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS org_level INTEGER;
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS org_path VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS full_org_code VARCHAR(500);

-- 添加同步相关字段
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS external_id INTEGER;
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS external_update_time TIMESTAMP;
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS sync_status VARCHAR(20) DEFAULT 'SYNCED';
ALTER TABLE t_org_structure ADD COLUMN IF NOT EXISTS last_sync_time TIMESTAMP;

-- 添加字段注释
COMMENT ON COLUMN t_org_structure.dept_uuid IS '外部系统部门唯一标识符';
COMMENT ON COLUMN t_org_structure.org_code IS '外部系统组织代码';
COMMENT ON COLUMN t_org_structure.parent_code IS '外部系统父级部门代码';
COMMENT ON COLUMN t_org_structure.full_name IS '部门全称';
COMMENT ON COLUMN t_org_structure.is_history IS '是否历史部门，0=否，1=是';
COMMENT ON COLUMN t_org_structure.description IS '部门描述';
COMMENT ON COLUMN t_org_structure.fax IS '传真';
COMMENT ON COLUMN t_org_structure.web_address IS '网站地址';
COMMENT ON COLUMN t_org_structure.org_manager IS '部门经理';
COMMENT ON COLUMN t_org_structure.post_code IS '邮政编码';
COMMENT ON COLUMN t_org_structure.org_type IS '组织类型，如公司、部门、科室等';
COMMENT ON COLUMN t_org_structure.org_level IS '组织层级，如1=集团、2=公司、3=部门、4=科室';
COMMENT ON COLUMN t_org_structure.org_path IS '组织路径，存储从根节点到当前节点的完整路径';
COMMENT ON COLUMN t_org_structure.full_org_code IS '完整组织代码，包含所有上级组织代码';
COMMENT ON COLUMN t_org_structure.external_id IS '外部系统中的原始ID';
COMMENT ON COLUMN t_org_structure.external_update_time IS '外部系统中的更新时间';
COMMENT ON COLUMN t_org_structure.sync_status IS '同步状态：SYNCED=已同步，PENDING=待同步，ERROR=同步失败';
COMMENT ON COLUMN t_org_structure.last_sync_time IS '最后同步时间';

-- =====================================================
-- 2. 扩展 t_user 表（用户表）
-- =====================================================

-- 添加外部系统字段到用户表
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS mdm_id VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS mdm_hrdwnm VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS employee_code VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS gender VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS mobile VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS id_card VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS birth_date DATE;
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS email VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS org_type VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS org_level1 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS org_level2 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS org_level3 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS wechat VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS tel VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS note TEXT;
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS employee_status VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS user_type VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS id_name VARCHAR(255);

-- 添加同步相关字段
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS external_id BIGINT;
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS external_org_code VARCHAR(255);
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS external_update_time TIMESTAMP;
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS sync_status VARCHAR(20) DEFAULT 'SYNCED';
ALTER TABLE t_user ADD COLUMN IF NOT EXISTS last_sync_time TIMESTAMP;

-- 添加字段注释
COMMENT ON COLUMN t_user.mdm_id IS '外部MDM系统中的唯一标识符';
COMMENT ON COLUMN t_user.mdm_hrdwnm IS 'MDM系统中的硬件标识';
COMMENT ON COLUMN t_user.employee_code IS '员工编号';
COMMENT ON COLUMN t_user.gender IS '性别，1=男，2=女';
COMMENT ON COLUMN t_user.mobile IS '手机号码';
COMMENT ON COLUMN t_user.id_card IS '身份证号码';
COMMENT ON COLUMN t_user.birth_date IS '出生日期';
COMMENT ON COLUMN t_user.email IS '电子邮箱';
COMMENT ON COLUMN t_user.org_type IS '组织类型';
COMMENT ON COLUMN t_user.org_level1 IS '组织层级1';
COMMENT ON COLUMN t_user.org_level2 IS '组织层级2';
COMMENT ON COLUMN t_user.org_level3 IS '组织层级3';
COMMENT ON COLUMN t_user.wechat IS '微信号';
COMMENT ON COLUMN t_user.tel IS '电话号码';
COMMENT ON COLUMN t_user.note IS '备注';
COMMENT ON COLUMN t_user.employee_status IS '员工状态，A=在职，其他=离职';
COMMENT ON COLUMN t_user.user_type IS '用户类型';
COMMENT ON COLUMN t_user.id_name IS '身份证和姓名组合';
COMMENT ON COLUMN t_user.external_id IS '外部系统中的原始ID';
COMMENT ON COLUMN t_user.external_org_code IS '外部系统中的组织代码';
COMMENT ON COLUMN t_user.external_update_time IS '外部系统中的更新时间';
COMMENT ON COLUMN t_user.sync_status IS '同步状态：SYNCED=已同步，PENDING=待同步，ERROR=同步失败';
COMMENT ON COLUMN t_user.last_sync_time IS '最后同步时间';

-- =====================================================
-- 3. 创建部门子表（对应外部系统的department_child）
-- =====================================================

CREATE TABLE IF NOT EXISTS t_department_child (
    id BIGINT PRIMARY KEY,
    dept_id BIGINT NOT NULL, -- 关联t_org_structure表的id
    guid VARCHAR(255), -- 外部系统的全局唯一标识符
    dept_uuid VARCHAR(255), -- 关联的部门UUID
    source_system VARCHAR(255), -- 来源系统
    source_data_nm VARCHAR(255), -- 来源系统数据标识
    udef1 VARCHAR(255), -- 自定义字段1
    udef2 VARCHAR(255), -- 自定义字段2
    udef3 VARCHAR(255), -- 自定义字段3
    udef4 VARCHAR(255), -- 自定义字段4
    udef5 VARCHAR(255), -- 自定义字段5
    udef6 VARCHAR(255), -- 自定义字段6
    external_id INTEGER, -- 外部系统中的原始ID
    sync_status VARCHAR(20) DEFAULT 'SYNCED', -- 同步状态
    last_sync_time TIMESTAMP, -- 最后同步时间
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE t_department_child IS '部门子表 - 存储部门在其他系统中的标识信息';
COMMENT ON COLUMN t_department_child.dept_id IS '关联t_org_structure表的部门ID';
COMMENT ON COLUMN t_department_child.guid IS '外部系统的全局唯一标识符';
COMMENT ON COLUMN t_department_child.dept_uuid IS '关联的部门UUID';
COMMENT ON COLUMN t_department_child.source_system IS '来源系统';
COMMENT ON COLUMN t_department_child.source_data_nm IS '来源系统数据标识';
COMMENT ON COLUMN t_department_child.udef1 IS '自定义字段1';
COMMENT ON COLUMN t_department_child.udef2 IS '自定义字段2';
COMMENT ON COLUMN t_department_child.udef3 IS '自定义字段3';
COMMENT ON COLUMN t_department_child.udef4 IS '自定义字段4';
COMMENT ON COLUMN t_department_child.udef5 IS '自定义字段5';
COMMENT ON COLUMN t_department_child.udef6 IS '自定义字段6';

-- 部门子表索引
CREATE INDEX IF NOT EXISTS idx_department_child_dept_id ON t_department_child(dept_id);
CREATE INDEX IF NOT EXISTS idx_department_child_dept_uuid ON t_department_child(dept_uuid);
CREATE INDEX IF NOT EXISTS idx_department_child_guid ON t_department_child(guid);
CREATE INDEX IF NOT EXISTS idx_department_child_sync_status ON t_department_child(sync_status);

-- =====================================================
-- 4. 创建员工岗位关联表（对应外部系统的employee_position）
-- =====================================================

CREATE TABLE IF NOT EXISTS t_employee_position (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 关联t_user表的id
    guid VARCHAR(255), -- 外部系统的全局唯一标识符
    employee_mdm_id VARCHAR(255), -- 关联的员工MDM ID
    position_code VARCHAR(255), -- 岗位代码
    org_code VARCHAR(255), -- 组织代码
    department_code VARCHAR(255), -- 部门代码
    is_primary VARCHAR(255), -- 是否主岗位，1=是，0=否
    status VARCHAR(255), -- 状态，D=在职，U=默认
    is_active VARCHAR(255), -- 是否激活
    position_detail_code VARCHAR(255), -- 岗位详细代码
    external_id BIGINT, -- 外部系统中的原始ID
    sync_status VARCHAR(20) DEFAULT 'SYNCED', -- 同步状态
    last_sync_time TIMESTAMP, -- 最后同步时间
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE t_employee_position IS '员工岗位关联表 - 存储员工的岗位信息';
COMMENT ON COLUMN t_employee_position.user_id IS '关联t_user表的用户ID';
COMMENT ON COLUMN t_employee_position.guid IS '外部系统的全局唯一标识符';
COMMENT ON COLUMN t_employee_position.employee_mdm_id IS '关联的员工MDM ID';
COMMENT ON COLUMN t_employee_position.position_code IS '岗位代码';
COMMENT ON COLUMN t_employee_position.org_code IS '组织代码';
COMMENT ON COLUMN t_employee_position.department_code IS '部门代码';
COMMENT ON COLUMN t_employee_position.is_primary IS '是否主岗位，1=是，0=否';
COMMENT ON COLUMN t_employee_position.status IS '状态，D=在职，U=默认';
COMMENT ON COLUMN t_employee_position.is_active IS '是否激活';
COMMENT ON COLUMN t_employee_position.position_detail_code IS '岗位详细代码';

-- =====================================================
-- 5. 创建索引优化查询性能
-- =====================================================

-- 部门表索引
CREATE INDEX IF NOT EXISTS idx_org_structure_dept_uuid ON t_org_structure(dept_uuid);
CREATE INDEX IF NOT EXISTS idx_org_structure_org_code ON t_org_structure(org_code);
CREATE INDEX IF NOT EXISTS idx_org_structure_external_id ON t_org_structure(external_id);
CREATE INDEX IF NOT EXISTS idx_org_structure_sync_status ON t_org_structure(sync_status);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_user_mdm_id ON t_user(mdm_id);
CREATE INDEX IF NOT EXISTS idx_user_employee_code ON t_user(employee_code);
CREATE INDEX IF NOT EXISTS idx_user_external_id ON t_user(external_id);
CREATE INDEX IF NOT EXISTS idx_user_external_org_code ON t_user(external_org_code);
CREATE INDEX IF NOT EXISTS idx_user_sync_status ON t_user(sync_status);
CREATE INDEX IF NOT EXISTS idx_user_mobile ON t_user(mobile);
CREATE INDEX IF NOT EXISTS idx_user_email ON t_user(email);

-- 员工岗位关联表索引
CREATE INDEX IF NOT EXISTS idx_employee_position_user_id ON t_employee_position(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_position_mdm_id ON t_employee_position(employee_mdm_id);
CREATE INDEX IF NOT EXISTS idx_employee_position_org_code ON t_employee_position(org_code);
CREATE INDEX IF NOT EXISTS idx_employee_position_dept_code ON t_employee_position(department_code);
CREATE INDEX IF NOT EXISTS idx_employee_position_guid ON t_employee_position(guid);
CREATE INDEX IF NOT EXISTS idx_employee_position_sync_status ON t_employee_position(sync_status);

-- =====================================================
-- 5.1 创建员工职称表（对应外部系统的employee_title）
-- =====================================================

CREATE TABLE IF NOT EXISTS t_employee_title (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 关联t_user表的id
    guid VARCHAR(255), -- 外部系统的全局唯一标识符
    employee_mdm_id VARCHAR(255), -- 关联的员工MDM ID
    title_code VARCHAR(255), -- 职称代码
    title_type VARCHAR(255), -- 职称类型
    title_level VARCHAR(255), -- 职称级别
    title_name VARCHAR(255), -- 职称名称
    status VARCHAR(255), -- 状态
    title_category VARCHAR(255), -- 职称类别
    external_id BIGINT, -- 外部系统中的原始ID
    sync_status VARCHAR(20) DEFAULT 'SYNCED', -- 同步状态
    last_sync_time TIMESTAMP, -- 最后同步时间
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE t_employee_title IS '员工职称表 - 存储员工的职称信息';
COMMENT ON COLUMN t_employee_title.user_id IS '关联t_user表的用户ID';
COMMENT ON COLUMN t_employee_title.guid IS '外部系统的全局唯一标识符';
COMMENT ON COLUMN t_employee_title.employee_mdm_id IS '关联的员工MDM ID';
COMMENT ON COLUMN t_employee_title.title_code IS '职称代码';
COMMENT ON COLUMN t_employee_title.title_type IS '职称类型';
COMMENT ON COLUMN t_employee_title.title_level IS '职称级别';
COMMENT ON COLUMN t_employee_title.title_name IS '职称名称';
COMMENT ON COLUMN t_employee_title.status IS '状态';
COMMENT ON COLUMN t_employee_title.title_category IS '职称类别';

-- 员工职称表索引
CREATE INDEX IF NOT EXISTS idx_employee_title_user_id ON t_employee_title(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_title_mdm_id ON t_employee_title(employee_mdm_id);
CREATE INDEX IF NOT EXISTS idx_employee_title_guid ON t_employee_title(guid);
CREATE INDEX IF NOT EXISTS idx_employee_title_sync_status ON t_employee_title(sync_status);

-- =====================================================
-- 5.2 创建员工系统标识表（对应外部系统的employee_system）
-- =====================================================

CREATE TABLE IF NOT EXISTS t_employee_system (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL, -- 关联t_user表的id
    guid VARCHAR(255), -- 外部系统的全局唯一标识符
    employee_mdm_id VARCHAR(255), -- 关联的员工MDM ID
    system_code VARCHAR(255), -- 系统代码
    system_data_id VARCHAR(255), -- 系统数据ID
    org_code VARCHAR(255), -- 组织代码
    department_code VARCHAR(255), -- 部门代码
    employee_code VARCHAR(255), -- 员工编号
    login_account VARCHAR(50), -- 登录账号
    external_id BIGINT, -- 外部系统中的原始ID
    sync_status VARCHAR(20) DEFAULT 'SYNCED', -- 同步状态
    last_sync_time TIMESTAMP, -- 最后同步时间
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

COMMENT ON TABLE t_employee_system IS '员工系统标识表 - 存储员工在其他系统中的标识信息';
COMMENT ON COLUMN t_employee_system.user_id IS '关联t_user表的用户ID';
COMMENT ON COLUMN t_employee_system.guid IS '外部系统的全局唯一标识符';
COMMENT ON COLUMN t_employee_system.employee_mdm_id IS '关联的员工MDM ID';
COMMENT ON COLUMN t_employee_system.system_code IS '系统代码';
COMMENT ON COLUMN t_employee_system.system_data_id IS '系统数据ID';
COMMENT ON COLUMN t_employee_system.org_code IS '组织代码';
COMMENT ON COLUMN t_employee_system.department_code IS '部门代码';
COMMENT ON COLUMN t_employee_system.employee_code IS '员工编号';
COMMENT ON COLUMN t_employee_system.login_account IS '登录账号';

-- 员工系统标识表索引
CREATE INDEX IF NOT EXISTS idx_employee_system_user_id ON t_employee_system(user_id);
CREATE INDEX IF NOT EXISTS idx_employee_system_mdm_id ON t_employee_system(employee_mdm_id);
CREATE INDEX IF NOT EXISTS idx_employee_system_guid ON t_employee_system(guid);
CREATE INDEX IF NOT EXISTS idx_employee_system_login_account ON t_employee_system(login_account);
CREATE INDEX IF NOT EXISTS idx_employee_system_sync_status ON t_employee_system(sync_status);

-- =====================================================
-- 6. 创建同步日志表
-- =====================================================

CREATE TABLE IF NOT EXISTS t_sync_log (
    id BIGINT PRIMARY KEY,
    sync_type VARCHAR(20) NOT NULL, -- DEPARTMENT, EMPLOYEE, EMPLOYEE_POSITION
    sync_action VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    external_id VARCHAR(255),
    internal_id BIGINT,
    sync_status VARCHAR(20) NOT NULL, -- SUCCESS, FAILED, PENDING
    error_message TEXT,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    external_data JSONB, -- 存储外部系统的原始数据
    processed_data JSONB -- 存储处理后的数据
);

COMMENT ON TABLE t_sync_log IS '数据同步日志表';
COMMENT ON COLUMN t_sync_log.sync_type IS '同步类型：DEPARTMENT=部门，EMPLOYEE=员工，EMPLOYEE_POSITION=员工岗位';
COMMENT ON COLUMN t_sync_log.sync_action IS '同步操作：INSERT=新增，UPDATE=更新，DELETE=删除';
COMMENT ON COLUMN t_sync_log.external_id IS '外部系统ID';
COMMENT ON COLUMN t_sync_log.internal_id IS '内部系统ID';
COMMENT ON COLUMN t_sync_log.sync_status IS '同步状态：SUCCESS=成功，FAILED=失败，PENDING=待处理';
COMMENT ON COLUMN t_sync_log.error_message IS '错误信息';
COMMENT ON COLUMN t_sync_log.external_data IS '外部系统原始数据（JSON格式）';
COMMENT ON COLUMN t_sync_log.processed_data IS '处理后的数据（JSON格式）';

-- 同步日志表索引
CREATE INDEX IF NOT EXISTS idx_sync_log_type ON t_sync_log(sync_type);
CREATE INDEX IF NOT EXISTS idx_sync_log_status ON t_sync_log(sync_status);
CREATE INDEX IF NOT EXISTS idx_sync_log_time ON t_sync_log(sync_time);
CREATE INDEX IF NOT EXISTS idx_sync_log_external_id ON t_sync_log(external_id);
