package com.dfit.percode.sync.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工职称批量插入DTO
 * 用于性能优化的批量操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class EmployeeTitleBatch {
    
    private Long id;
    private Long userId;
    private String guid;
    private String employeeMdmId;
    private String titleCode;
    private String titleType;
    private String titleLevel;
    private String titleName;
    private String status;
    private String titleCategory;
    private Long externalId;
    private String syncStatus;
    private LocalDateTime lastSyncTime;
    
    /**
     * 构造方法
     */
    public EmployeeTitleBatch(Long id, Long userId, String guid, String employeeMdmId,
                             String titleCode, String titleType, String titleLevel,
                             String titleName, String status, String titleCategory,
                             Long externalId, String syncStatus, LocalDateTime lastSyncTime) {
        this.id = id;
        this.userId = userId;
        this.guid = guid;
        this.employeeMdmId = employeeMdmId;
        this.titleCode = titleCode;
        this.titleType = titleType;
        this.titleLevel = titleLevel;
        this.titleName = titleName;
        this.status = status;
        this.titleCategory = titleCategory;
        this.externalId = externalId;
        this.syncStatus = syncStatus;
        this.lastSyncTime = lastSyncTime;
    }
}
