package com.dfit.percode.sync.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工岗位批量插入DTO
 * 用于性能优化的批量操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class EmployeePositionBatch {
    
    private Long id;
    private Long userId;
    private String guid;
    private String employeeMdmId;
    private String positionCode;
    private String orgCode;
    private String departmentCode;
    private String isPrimary;
    private String status;
    private String isActive;
    private String positionDetailCode;
    private Long externalId;
    private String syncStatus;
    private LocalDateTime lastSyncTime;
    
    /**
     * 构造方法
     */
    public EmployeePositionBatch(Long id, Long userId, String guid, String employeeMdmId,
                                String positionCode, String orgCode, String departmentCode,
                                String isPrimary, String status, String isActive,
                                String positionDetailCode, Long externalId,
                                String syncStatus, LocalDateTime lastSyncTime) {
        this.id = id;
        this.userId = userId;
        this.guid = guid;
        this.employeeMdmId = employeeMdmId;
        this.positionCode = positionCode;
        this.orgCode = orgCode;
        this.departmentCode = departmentCode;
        this.isPrimary = isPrimary;
        this.status = status;
        this.isActive = isActive;
        this.positionDetailCode = positionDetailCode;
        this.externalId = externalId;
        this.syncStatus = syncStatus;
        this.lastSyncTime = lastSyncTime;
    }
}
