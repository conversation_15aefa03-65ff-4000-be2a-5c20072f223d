package com.dfit.percode.util;

import java.io.BufferedReader;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OrgStructureRepairUtil {

    static class DepartmentNode {
        String id;
        String name;
        String fullName;
        DepartmentNode parent;
        List<DepartmentNode> children = new ArrayList<>();

        DepartmentNode(String id, String name, String fullName) {
            this.id = id;
            this.name = name;
            this.fullName = fullName;
        }

        @Override
        public String toString() {
            return "DepartmentNode{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", fullName='" + fullName + '\'' +
                    ", parentId=" + (parent != null ? parent.id : "null") +
                    '}';
        }
    }

    public static void main(String[] args) {
        String inputFile = "department_sync_test.sql";
        String outputFile = "docs/org_structure_repair.sql";

        try {
            List<DepartmentNode> nodes = parseSqlFile(inputFile);
            buildTree(nodes);
            generateRepairSql(nodes, outputFile);
            System.out.println("Repair script generated successfully at: " + outputFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static List<DepartmentNode> parseSqlFile(String filePath) throws IOException {
        List<DepartmentNode> nodes = new ArrayList<>();
        Pattern insertPattern = Pattern.compile("INSERT INTO `department_sync_test` VALUES \\((.*?)\\);");
        // A more robust pattern to handle the specific structure of the VALUES clause
        Pattern valuesPattern = Pattern.compile("^\\s*\\d+,\\s*'([^']*)',\\s*'([^']*)',\\s*'[^']*',\\s*'[^']*',\\s*'([^']*)',\\s*[^,]+,\\s*'([^']*)'");

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(filePath), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                Matcher insertMatcher = insertPattern.matcher(line);
                if (insertMatcher.find()) {
                    String valuesPart = insertMatcher.group(1);
                    Matcher valuesMatcher = valuesPattern.matcher(valuesPart);
                    if (valuesMatcher.find()) {
                        String userPredef14 = valuesMatcher.group(4);
                        if (!"D".equals(userPredef14)) {
                            String orgCode = valuesMatcher.group(1);
                            String orgName = valuesMatcher.group(2);
                            String fullName = valuesMatcher.group(3);
                            nodes.add(new DepartmentNode(orgCode, orgName, fullName));
                        }
                    }
                }
            }
        }
        return nodes;
    }

    private static void buildTree(List<DepartmentNode> nodes) {
        Map<String, DepartmentNode> nodeMap = new HashMap<>();
        for (DepartmentNode node : nodes) {
            nodeMap.put(node.id, node);
        }

        // Sort nodes by fullName length in descending order to ensure parents are found first
        nodes.sort(Comparator.comparingInt(n -> n.fullName.length()));

        for (DepartmentNode node : nodes) {
            DepartmentNode parent = findParent(node, nodeMap);
            if (parent != null) {
                node.parent = parent;
                parent.children.add(node);
            }
        }
    }

    private static DepartmentNode findParent(DepartmentNode currentNode, Map<String, DepartmentNode> nodeMap) {
        DepartmentNode bestMatchParent = null;
        int maxMatchLength = -1;

        for (DepartmentNode potentialParent : nodeMap.values()) {
            if (currentNode.id.equals(potentialParent.id)) {
                continue;
            }

            if (currentNode.fullName.startsWith(potentialParent.fullName) && potentialParent.fullName.length() > maxMatchLength) {
                maxMatchLength = potentialParent.fullName.length();
                bestMatchParent = potentialParent;
            }
        }
        return bestMatchParent;
    }

    private static void generateRepairSql(List<DepartmentNode> nodes, String outputFile) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outputFile), "UTF-8"))) {
            writer.write("-- SQL Script to Repair Organization Structure\n\n");
            writer.write("-- Step 1: Clean up existing synchronized data\n");
            writer.write("UPDATE \"public\".\"t_org_structure\" SET \"is_del\" = true WHERE \"data_source\" = 2;\n");
            writer.write("DELETE FROM \"public\".\"t_org_structure\" WHERE \"data_source\" = 2 AND \"is_del\" = true;\n\n");

            writer.write("-- Step 2: Insert the corrected organization structure\n");
            for (DepartmentNode node : nodes) {
                String parentId = (node.parent != null) ? "'" + node.parent.id + "'" : "0";
                String organName = node.name.replace("'", "''"); // Escape single quotes for SQL
                
                String sql = String.format(
                    "INSERT INTO \"public\".\"t_org_structure\" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) VALUES ('%s', '%s', %s, 'f', 2, NOW(), NOW());\n",
                    node.id, organName, parentId
                );
                writer.write(sql);
            }
        }
    }
}