# 数据模块管理接口测试文档

## 📋 接口概述

本文档提供数据模块管理的3个核心接口的测试用例和示例。

### 🔧 接口列表
1. `POST /data-modules/list` - 查询数据模块列表
2. `POST /data-modules/add` - 新增数据模块
3. `POST /data-modules/delete` - 删除数据模块

## 🧪 测试用例

### 1. 新增数据模块接口测试

**接口地址**: `POST http://localhost:8080/data-modules/add`

**测试用例1: 正常新增**
```json
{
    "moduleName": "用户数据模块",
    "moduleIdentifier": "user_data_module",
    "orderInfo": 1
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": null
}
```

**测试用例2: 模块标识重复**
```json
{
    "moduleName": "重复模块",
    "moduleIdentifier": "user_data_module",
    "orderInfo": 2
}
```

**预期响应**:
```json
{
    "code": 500,
    "message": "新增数据模块失败：模块标识已存在",
    "data": null
}
```

### 2. 查询数据模块列表接口测试

**接口地址**: `POST http://localhost:8080/data-modules/list`

**测试用例1: 分页查询**
```json
{
    "pageNum": 1,
    "pageSize": 10
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "SUCCESS",
    "total": 1,
    "data": [
        {
            "id": "1930307870503604224",
            "moduleName": "用户数据模块",
            "moduleIdentifier": "user_data_module",
            "orderInfo": 1,
            "createTime": "2023-05-15 14:30:00",
            "modifyTime": "2023-05-15 14:30:00"
        }
    ]
}
```

**测试用例2: 不分页查询**
```json
{}
```

### 3. 删除数据模块接口测试

**接口地址**: `POST http://localhost:8080/data-modules/delete`

**测试用例1: 正常删除**
```json
{
    "moduleId": "1930307870503604224"
}
```

**预期响应**:
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": null
}
```

**测试用例2: 删除不存在的模块**
```json
{
    "moduleId": "9999999999999999999"
}
```

**预期响应**:
```json
{
    "code": 500,
    "message": "删除数据模块失败：数据模块不存在",
    "data": null
}
```

## 🔄 完整测试流程

### 步骤1: 新增测试数据
```bash
# 新增第一个模块
curl -X POST http://localhost:8080/data-modules/add \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "用户数据模块",
    "moduleIdentifier": "user_data_module",
    "orderInfo": 1
  }'

# 新增第二个模块
curl -X POST http://localhost:8080/data-modules/add \
  -H "Content-Type: application/json" \
  -d '{
    "moduleName": "订单数据模块",
    "moduleIdentifier": "order_data_module",
    "orderInfo": 2
  }'
```

### 步骤2: 查询列表验证
```bash
curl -X POST http://localhost:8080/data-modules/list \
  -H "Content-Type: application/json" \
  -d '{
    "pageNum": 1,
    "pageSize": 10
  }'
```

### 步骤3: 删除测试
```bash
# 删除第二个模块
curl -X POST http://localhost:8080/data-modules/delete \
  -H "Content-Type: application/json" \
  -d '{
    "moduleId": "模块ID"
  }'
```

## ✅ 验证要点

### 功能验证
- [x] 新增模块成功
- [x] 模块标识唯一性检查
- [x] 分页查询正常
- [x] 删除模块成功
- [x] 删除前检查使用情况

### 数据验证
- [x] ID使用雪花算法生成
- [x] 创建时间自动设置
- [x] 逻辑删除机制
- [x] 排序序号正确

### 异常处理
- [x] 重复标识异常
- [x] 模块不存在异常
- [x] 模块被使用异常

## 📝 注意事项

1. **ID格式**: 所有ID都使用雪花算法生成的19位数字
2. **时间格式**: 时间字段格式为 "yyyy-MM-dd HH:mm:ss"
3. **逻辑删除**: 删除操作为逻辑删除，设置is_del=true
4. **排序规则**: 按order_info升序，create_time降序排列
5. **异常处理**: 所有异常都会返回500状态码和详细错误信息
