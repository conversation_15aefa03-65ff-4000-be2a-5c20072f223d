/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:49
*/


-- ----------------------------
-- Table structure for t_data_operate
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_operate";
CREATE TABLE "public"."t_data_operate" (
  "id" int8 NOT NULL DEFAULT nextval('t_data_operate_id_seq'::regclass),
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int2,
  "operate_type" int8,
  "is_del" bool DEFAULT false,
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_operate"."module_identifier" IS '模块标识符，与系统模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_operate"."data_type" IS '数据类型，1为条';
COMMENT ON COLUMN "public"."t_data_operate"."operate_type" IS '操作类型，1为新增2为修改3为删除';
COMMENT ON COLUMN "public"."t_data_operate"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_operate"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_data_operate"."modify_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_data_operate"."data_identifier" IS '数据标识,与数据权限管理列表中data_identifier一致';
COMMENT ON TABLE "public"."t_data_operate" IS '数据操作表';

-- ----------------------------
-- Records of t_data_operate
-- ----------------------------
INSERT INTO "public"."t_data_operate" VALUES (1936404204939681793, 'knowledge', 1, 1, 'f', '2025-06-21 20:41:53.765729', '2025-06-21 20:41:53.765735', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936404205057122306, 'knowledge', 1, 2, 'f', '2025-06-21 20:41:53.793925', '2025-06-21 20:41:53.79393', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936404205174562818, 'knowledge', 1, 3, 'f', '2025-06-21 20:41:53.821935', '2025-06-21 20:41:53.82194', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936404205296197633, 'knowledge', 1, 4, 'f', '2025-06-21 20:41:53.850174', '2025-06-21 20:41:53.850179', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936420749057236993, 'employee_data_module', 1, 1, 'f', '2025-06-21 21:47:38.190019', '2025-06-21 21:47:38.190026', 'employee_basic_info');
INSERT INTO "public"."t_data_operate" VALUES (1936420749178871809, 'employee_data_module', 1, 3, 'f', '2025-06-21 21:47:38.219259', '2025-06-21 21:47:38.219265', 'employee_basic_info');
INSERT INTO "public"."t_data_operate" VALUES (1936420749417947138, 'employee_data_module', 1, 1, 'f', '2025-06-21 21:47:38.276868', '2025-06-21 21:47:38.276873', 'employee_salary_info1');
INSERT INTO "public"."t_data_operate" VALUES (1936420749539581953, 'employee_data_module', 1, 2, 'f', '2025-06-21 21:47:38.305775', '2025-06-21 21:47:38.30578', 'employee_salary_info1');
INSERT INTO "public"."t_data_operate" VALUES (1936420749661216770, 'employee_data_module', 1, 3, 'f', '2025-06-21 21:47:38.334698', '2025-06-21 21:47:38.334702', 'employee_salary_info1');
INSERT INTO "public"."t_data_operate" VALUES (1936420749782851586, 'employee_data_module', 1, 4, 'f', '2025-06-21 21:47:38.363609', '2025-06-21 21:47:38.363614', 'employee_salary_info1');
INSERT INTO "public"."t_data_operate" VALUES (1936420750026121217, 'employee_data_module', 1, 1, 'f', '2025-06-21 21:47:38.42115', '2025-06-21 21:47:38.421155', 'employee_performance_data');
INSERT INTO "public"."t_data_operate" VALUES (1936420750143561730, 'employee_data_module', 1, 2, 'f', '2025-06-21 21:47:38.450013', '2025-06-21 21:47:38.450019', 'employee_performance_data');
INSERT INTO "public"."t_data_operate" VALUES (1936420750265196545, 'employee_data_module', 1, 3, 'f', '2025-06-21 21:47:38.478921', '2025-06-21 21:47:38.478925', 'employee_performance_data');
INSERT INTO "public"."t_data_operate" VALUES (1936420750386831361, 'employee_data_module', 1, 4, 'f', '2025-06-21 21:47:38.507878', '2025-06-21 21:47:38.507883', 'employee_performance_data');
INSERT INTO "public"."t_data_operate" VALUES (1936420750630100994, 'employee_data_module', 1, 1, 'f', '2025-06-21 21:47:38.565574', '2025-06-21 21:47:38.56558', 'employee_attendance_record');
INSERT INTO "public"."t_data_operate" VALUES (1936420750751735810, 'employee_data_module', 1, 2, 'f', '2025-06-21 21:47:38.594452', '2025-06-21 21:47:38.594457', 'employee_attendance_record');
INSERT INTO "public"."t_data_operate" VALUES (1936420750873370626, 'employee_data_module', 1, 3, 'f', '2025-06-21 21:47:38.623358', '2025-06-21 21:47:38.623363', 'employee_attendance_record');
INSERT INTO "public"."t_data_operate" VALUES (1936420750995005442, 'employee_data_module', 1, 4, 'f', '2025-06-21 21:47:38.652246', '2025-06-21 21:47:38.652252', 'employee_attendance_record');
INSERT INTO "public"."t_data_operate" VALUES (1934509720429666305, 'department_data_module', 1, 1, 'f', '2025-06-16 15:13:53.434492', '2025-06-16 15:13:53.434492', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509720840708097, 'department_data_module', 1, 2, 'f', '2025-06-16 15:13:53.571312', '2025-06-16 15:13:53.571312', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509721230778370, 'department_data_module', 1, 3, 'f', '2025-06-16 15:13:53.661475', '2025-06-16 15:13:53.661475', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934509721620848641, 'department_data_module', 1, 4, 'f', '2025-06-16 15:13:53.752497', '2025-06-16 15:13:53.752497', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1934608862434934786, 'project_data_module', 1, 4, 'f', '2025-06-16 21:47:50.753765', '2025-06-16 21:47:50.753787', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935990202145288193, 'employee_data_module', 1, 1, 'f', '2025-06-20 17:16:47.805193', '2025-06-20 17:16:47.805201', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935990202300477441, 'employee_data_module', 1, 2, 'f', '2025-06-20 17:16:47.844269', '2025-06-20 17:16:47.844275', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935990202422112258, 'employee_data_module', 1, 3, 'f', '2025-06-20 17:16:47.874302', '2025-06-20 17:16:47.874307', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1935990202547941378, 'employee_data_module', 1, 4, 'f', '2025-06-20 17:16:47.904087', '2025-06-20 17:16:47.904092', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936018751652737025, 'customer_data_module', 1, 2, 'f', '2025-06-20 19:10:14.540567', '2025-06-20 19:10:14.540571', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936018751791149058, 'customer_data_module', 1, 4, 'f', '2025-06-20 19:10:14.573333', '2025-06-20 19:10:14.573337', NULL);
INSERT INTO "public"."t_data_operate" VALUES (1936703738941034497, 'knowledge', 1, 2, 'f', '2025-06-22 16:32:08.234435', '2025-06-22 16:32:08.234443', 'kn_process');
INSERT INTO "public"."t_data_operate" VALUES (1936703739117195265, 'knowledge', 1, 4, 'f', '2025-06-22 16:32:08.279631', '2025-06-22 16:32:08.279636', 'kn_process');
INSERT INTO "public"."t_data_operate" VALUES (1936703739435962369, 'knowledge', 1, 3, 'f', '2025-06-22 16:32:08.355603', '2025-06-22 16:32:08.355608', 'art');
INSERT INTO "public"."t_data_operate" VALUES (1936703739599540226, 'knowledge', 1, 4, 'f', '2025-06-22 16:32:08.393706', '2025-06-22 16:32:08.39371', 'art');
INSERT INTO "public"."t_data_operate" VALUES (1936703739914113026, 'knowledge', 1, 1, 'f', '2025-06-22 16:32:08.469596', '2025-06-22 16:32:08.469601', 'article');
INSERT INTO "public"."t_data_operate" VALUES (1936703740077690881, 'knowledge', 1, 2, 'f', '2025-06-22 16:32:08.507708', '2025-06-22 16:32:08.507713', 'article');
INSERT INTO "public"."t_data_operate" VALUES (1936703740237074434, 'knowledge', 1, 3, 'f', '2025-06-22 16:32:08.546529', '2025-06-22 16:32:08.546533', 'article');
INSERT INTO "public"."t_data_operate" VALUES (1936703740396457986, 'knowledge', 1, 4, 'f', '2025-06-22 16:32:08.584544', '2025-06-22 16:32:08.584549', 'article');
INSERT INTO "public"."t_data_operate" VALUES (1936703740715225090, 'knowledge', 1, 4, 'f', '2025-06-22 16:32:08.660564', '2025-06-22 16:32:08.660568', 'standard_file');
INSERT INTO "public"."t_data_operate" VALUES (1936703741033992194, 'knowledge', 1, 1, 'f', '2025-06-22 16:32:08.736397', '2025-06-22 16:32:08.736402', 'process_file');
INSERT INTO "public"."t_data_operate" VALUES (1936703741193375746, 'knowledge', 1, 2, 'f', '2025-06-22 16:32:08.774542', '2025-06-22 16:32:08.774546', 'process_file');
INSERT INTO "public"."t_data_operate" VALUES (1936703741356953601, 'knowledge', 1, 4, 'f', '2025-06-22 16:32:08.8126', '2025-06-22 16:32:08.812604', 'process_file');

-- ----------------------------
-- Primary Key structure for table t_data_operate
-- ----------------------------
ALTER TABLE "public"."t_data_operate" ADD CONSTRAINT "t_data_operate_pkey" PRIMARY KEY ("id");
