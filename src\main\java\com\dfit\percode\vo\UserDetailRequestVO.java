package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 获取用户详情请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持单个用户查询和批量用户查询
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserDetailRequestVO", description = "获取用户详情请求参数")
public class UserDetailRequestVO {

    @ApiModelProperty(value = "用户ID（单个查询）", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户ID数组（批量查询，按传入顺序返回）", example = "[1, 2, 3]")
    private List<Long> userIds;
}
