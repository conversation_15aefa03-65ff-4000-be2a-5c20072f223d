package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除菜单模块请求VO类
 * 按照前端格式要求设计（驼峰命名）
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteMenuModuleRequestVO", description = "删除菜单模块请求参数")
public class DeleteMenuModuleRequestVO {

    @ApiModelProperty(value = "模块标识", required = true, example = "system_management")
    private String moduleIdentifier;

    @ApiModelProperty(value = "是否强制删除", required = false, example = "false")
    private Boolean forceDelete = false;
}
