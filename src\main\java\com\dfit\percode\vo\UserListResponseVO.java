package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户列表分页查询响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 包含分页信息和用户列表数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListResponseVO", description = "用户列表分页查询响应")
public class UserListResponseVO {
    
    @ApiModelProperty(value = "用户列表数据")
    private List<UserListItemVO> records;
    
    @ApiModelProperty(value = "总记录数", example = "100")
    private Long total;
    
    @ApiModelProperty(value = "当前页码", example = "1")
    private Integer pageNum;
    
    @ApiModelProperty(value = "每页大小", example = "10")
    private Integer pageSize;
    
    @ApiModelProperty(value = "总页数", example = "10")
    private Integer totalPages;
    
    @ApiModelProperty(value = "是否有下一页", example = "true")
    private Boolean hasNext;
    
    @ApiModelProperty(value = "是否有上一页", example = "false")
    private Boolean hasPrevious;
}
