package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 角色菜单权限对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_roles_menu_permission")
@ApiModel(value = "TRolesMenuPermission对象", description = "角色菜单权限对应表")
public class TRolesMenuPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("模块标识符")
    private String moduleIdentifier;

    @ApiModelProperty("菜单id，对应菜单管理列表表中的id")
    private Long menuId;

    private Boolean isDel;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;
}
