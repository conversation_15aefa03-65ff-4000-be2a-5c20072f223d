2025-07-10 01:46:34.982 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-10 01:46:34.995 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 28052 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-10 01:46:35.077 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-10 01:46:45.197 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 01:46:45.215 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-10 01:46:45.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 115 ms. Found 0 Elasticsearch repository interfaces.
2025-07-10 01:46:45.415 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 01:46:45.419 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-10 01:46:45.456 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-10 01:46:45.513 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 01:46:45.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-10 01:46:45.599 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 JPA repository interfaces.
2025-07-10 01:46:45.709 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-10 01:46:45.716 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-10 01:46:45.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Redis repository interfaces.
2025-07-10 01:46:49.328 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-10 01:46:49.389 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-10 01:46:49.392 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-10 01:46:49.392 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-10 01:46:49.928 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-10 01:46:49.928 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 14047 ms
2025-07-10 01:46:50.469 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-10 01:46:51.127 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-10 01:46:52.798 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-10 01:46:53.120 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-10 01:46:53.967 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-10 01:46:55.006 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-10 01:46:56.290 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-10 01:46:56.356 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 01:46:56.402 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-10 01:46:56.402 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-10 01:46:56.406 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-10 01:46:56.406 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-10 01:47:03.611 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-10 01:47:13.613 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-10 01:47:13.685 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-10 01:47:16.120 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 44.724 seconds (JVM running for 50.231)
2025-07-10 02:05:01.995 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-10 02:05:02.097 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
