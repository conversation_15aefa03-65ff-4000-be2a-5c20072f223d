# 数据库初始化指南

## 📋 快速开始

### 1. 创建数据库表结构

首先执行建表语句（从开发文档中复制）：

```sql
-- 按顺序执行以下表的创建语句
-- 1. t_org_structure（组织架构表）
-- 2. t_user（用户表）
-- 3. t_role（角色表）
-- 4. t_perm_user_role（用户角色关联表）
-- 5. t_menu_module（菜单模块表）
-- 6. t_menu_permission（菜单权限表）
-- 7. t_data_module（数据模块表）
-- 8. t_data_operate_list（数据操作类型表）
-- 9. t_data_permission（数据权限表）
-- 10. t_roles_menu_permission（角色菜单权限关联表）
-- 11. t_roles_data_permission（角色数据权限关联表）
```

### 2. 插入测试数据

执行测试数据脚本：

```bash
# 方式1：直接在数据库客户端执行
# 复制 docs/test-data.sql 文件内容到数据库客户端执行

# 方式2：使用命令行（PostgreSQL）
psql -U postgres -d your_database_name -f docs/test-data.sql
```

## 🎯 测试账户

### 管理员账户
| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 超级管理员 | 拥有所有权限，用于系统管理 |
| zhangsan | password123 | 系统管理员 | 拥有用户管理和权限管理权限 |

### 普通用户账户
| 用户名 | 密码 | 角色 | 部门 | 说明 |
|--------|------|------|------|------|
| lisi | password123 | 部门管理员 | 研发组 | 拥有部门管理权限 |
| wangwu | password123 | 普通用户 | 测试组 | 拥有基本查看权限 |
| zhaoliu | password123 | 普通用户 | 运维组 | 拥有基本查看权限 |
| qianqi | password123 | 部门管理员 | 销售组 | 拥有部门管理权限 |
| sunba | password123 | 普通用户 | 推广组 | 拥有基本查看权限 |
| zhoujiu | password123 | 只读用户 | 人事部 | 只有查看权限 |
| wushi | password123 | 只读用户 | 财务部 | 只有查看权限 |

### 特殊状态账户
| 用户名 | 密码 | 状态 | 说明 |
|--------|------|------|------|
| zhengshiyi | password123 | 已停用 | 用于测试停用用户功能 |

## 📊 数据统计

### 组织架构
- **总部门数**: 10个
- **层级结构**: 3层（总公司 → 一级部门 → 二级部门）
- **主要部门**: 技术部、市场部、人事部、财务部

### 用户数据
- **总用户数**: 10个
- **正常用户**: 9个
- **停用用户**: 1个
- **分布**: 各部门均有用户分布

### 角色权限
- **总角色数**: 6个
- **正常角色**: 5个
- **停用角色**: 1个（测试角色）
- **权限层级**: 超级管理员 → 系统管理员 → 部门管理员 → 普通用户 → 只读用户

### 菜单权限
- **菜单模块**: 4个（系统管理、用户管理、权限管理、数据管理）
- **菜单项**: 13个
- **权限标识**: 完整的权限标识符体系

### 数据权限
- **数据模块**: 3个（用户数据、部门数据、财务数据）
- **操作类型**: 5个（查看、新增、编辑、删除、导出）
- **数据权限**: 6个具体权限项

## 🧪 测试场景

### 1. 角色列表查询测试
```bash
# 使用我们刚实现的接口
POST /roles/getRoleList
{
    "currentPage": 1,
    "pageSize": 10
}

# 预期结果：返回6个角色，包含用户关联数量
```

### 2. 用户权限测试
```bash
# 测试不同角色的权限差异
# 1. 使用admin登录 - 应该看到所有功能
# 2. 使用zhangsan登录 - 应该看到用户管理和权限管理
# 3. 使用lisi登录 - 应该看到部门管理功能
# 4. 使用wangwu登录 - 应该只看到基本查看功能
```

### 3. 组织架构测试
```bash
# 测试部门层级关系
# 1. 查看总公司下的一级部门
# 2. 查看技术部下的二级部门
# 3. 测试用户与部门的关联关系
```

## 🔧 数据维护

### 添加新用户
```sql
-- 1. 插入用户基本信息
INSERT INTO t_user (id, user_name, origin_id, organ_affiliation, account, password, is_disable, is_del, create_time, modify_time)
VALUES (新ID, '用户名', '原始ID', 部门ID, '账户', '密码', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 2. 分配角色
INSERT INTO t_perm_user_role (id, user_id, role_id, order_info, is_del, create_time, modify_time)
VALUES (新ID, 用户ID, 角色ID, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
```

### 添加新角色
```sql
-- 1. 创建角色
INSERT INTO t_role (id, role_name, order_info, is_disable, is_del, create_time, modify_time)
VALUES (新ID, '角色名称', 排序, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 2. 分配菜单权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time)
VALUES (新ID, 角色ID, '模块标识', 菜单ID, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 3. 分配数据权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time)
VALUES (新ID, 角色ID, '模块标识', 数据类型, 数据ID, 操作类型, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
```

### 清理测试数据
```sql
-- 如需清理所有测试数据，按以下顺序删除
DELETE FROM t_roles_data_permission WHERE id >= 11001;
DELETE FROM t_roles_menu_permission WHERE id >= 10001;
DELETE FROM t_data_permission WHERE id >= 9001;
DELETE FROM t_data_operate_list WHERE id >= 8001;
DELETE FROM t_data_module WHERE id >= 7001;
DELETE FROM t_menu_permission WHERE id >= 6001;
DELETE FROM t_menu_module WHERE id >= 5001;
DELETE FROM t_perm_user_role WHERE id >= 4001;
DELETE FROM t_role WHERE id >= 3001;
DELETE FROM t_user WHERE id >= 2001;
DELETE FROM t_org_structure WHERE id >= 1001;
```

## ⚠️ 注意事项

1. **密码安全**: 测试数据中的密码都是明文，生产环境请使用加密
2. **ID生成**: 测试数据使用简单数字ID，生产环境建议使用雪花算法
3. **数据一致性**: 删除数据时注意外键关联关系
4. **权限测试**: 建议在测试环境中验证权限配置的正确性
5. **备份恢复**: 重要操作前请备份数据库

## 🚀 下一步

数据初始化完成后，您可以：

1. **测试角色列表接口**: 使用我们刚实现的 `POST /roles/getRoleList` 接口
2. **继续开发其他接口**: 新增角色、修改角色、删除角色等
3. **前端联调**: 使用测试数据进行前后端联调
4. **权限验证**: 测试不同角色的权限控制是否正确
