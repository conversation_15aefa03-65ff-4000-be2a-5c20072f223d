package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 纯部门树结构 VO
 * 用于高性能的部门树查询，不包含用户信息
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@ApiModel(value = "DepartmentTreeOnlyVO", description = "纯部门树结构数据")
public class DepartmentTreeOnlyVO {
    
    @ApiModelProperty(value = "部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "部门名称")
    private String organName;
    
    @ApiModelProperty(value = "父部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;
    
    @ApiModelProperty(value = "排序序号")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "部门层级（从1开始）")
    private Integer level;
    
    @ApiModelProperty(value = "该部门的用户数量")
    private Integer userCount = 0;
    
    @ApiModelProperty(value = "该部门及所有子部门的用户总数")
    private Integer totalUserCount = 0;
    
    @ApiModelProperty(value = "是否有子部门")
    private Boolean hasChildren = false;
    
    @ApiModelProperty(value = "子部门数量")
    private Integer childDepartmentCount = 0;
    
    @ApiModelProperty(value = "子部门列表")
    private List<DepartmentTreeOnlyVO> children;
    
    @ApiModelProperty(value = "完整路径（如：总公司/技术部/研发组）")
    private String fullPath;
    
    @ApiModelProperty(value = "是否为叶子节点（没有子部门）")
    private Boolean isLeaf = true;
}
