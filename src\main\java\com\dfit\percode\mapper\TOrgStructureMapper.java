package com.dfit.percode.mapper;

import com.dfit.percode.entity.TOrgStructure;
import com.dfit.percode.userPerm.RoleEntity;
import com.dfit.percode.vo.SearchOrgStructureRequestVO;
import com.dfit.percode.vo.SearchOrgStructureResponseVO;
import com.dfit.percode.vo.AddOrgStructureResponseVO;
import com.dfit.percode.vo.UpdateOrgStructureResponseVO;
import com.dfit.percode.vo.DeleteOrgStructureResponseVO;
import com.dfit.percode.vo.OrgStructureTreeResponseVO;
import com.dfit.percode.vo.MoveOrgStructureResponseVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dfit.percode.userPerm.UserData;
import com.dfit.percode.userPerm.UserTree;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 组织架构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface TOrgStructureMapper extends BaseMapper<TOrgStructure> {

    @Select("  select id as id , organ_name as organName \n" +
            "        from t_org_structure tos where (pre_id = 0 OR pre_id IS NULL) AND (is_del = false OR is_del IS NULL) order by order_info ")
    List<UserTree> findAllDepartments();

    @Select("  select id as id , organ_name as organName\n" +
            "        from t_org_structure tos where pre_id = #{id} order by order_info ")
    List<UserTree> findByParentId(Long departmentId);

    @Select(" select id as id , user_name as userName " +
            " from t_user tku where organ_affiliation  = #{id} and (is_del = false OR is_del IS NULL) order by user_name ")
    List<UserData> findUserByDepartId(Long departmentId);

    @Select("select id as id , role_name as roleName  from t_role tr  where (is_del = false OR is_del IS NULL)  ")
    List<RoleEntity> roleSelected();

    /**
     * 搜索部门
     * 支持按组织名称模糊搜索、父部门ID精确匹配、是否包含已删除部门
     *
     * @param organName 组织名称（模糊搜索）
     * @param preId 父部门ID（精确匹配）
     * @param includeDeleted 是否包含已删除的部门
     * @return 搜索结果列表
     */
    @Select("<script>" +
            "SELECT " +
            "    tos.id AS id, " +
            "    tos.organ_name AS organName, " +
            "    tos.pre_id AS preId, " +
            "    parent.organ_name AS parentName, " +
            "    tos.order_info AS orderInfo, " +
            "    tos.data_source AS dataSource, " +
            "    tos.is_del AS isDel " +
            "FROM t_org_structure tos " +
            "LEFT JOIN t_org_structure parent ON tos.pre_id = parent.id " +
            "WHERE 1=1 " +
            "<if test='organName != null and organName != \"\"'>" +
            "    AND tos.organ_name LIKE CONCAT('%', #{organName}, '%') " +
            "</if>" +
            "<if test='preId != null'>" +
            "    AND tos.pre_id = #{preId} " +
            "</if>" +
            "<if test='includeDeleted == null or !includeDeleted'>" +
            "    AND (tos.is_del = false OR tos.is_del IS NULL) " +
            "</if>" +
            "ORDER BY tos.order_info ASC, tos.id ASC" +
            "</script>")
    List<SearchOrgStructureResponseVO> searchOrgStructure(@Param("organName") String organName,
                                                         @Param("preId") Long preId,
                                                         @Param("includeDeleted") Boolean includeDeleted);

    /**
     * 新增部门
     * 插入新的组织架构记录
     *
     * @param id 部门ID
     * @param organName 组织名称
     * @param preId 父部门ID
     * @param orderInfo 排序序号
     */
    @Insert("INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, data_source, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{organName}, #{preId}, #{orderInfo}, #{dataSource}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertOrgStructure(@Param("id") Long id,
                           @Param("organName") String organName,
                           @Param("preId") Long preId,
                           @Param("orderInfo") Integer orderInfo,
                           @Param("dataSource") Integer dataSource);

    /**
     * 检查组织名称在同一父部门下是否已存在
     * 用于验证部门名称的唯一性
     *
     * @param organName 组织名称
     * @param preId 父部门ID
     * @return 存在的记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_org_structure " +
            "WHERE organ_name = #{organName} " +
            "<choose>" +
            "  <when test='preId != null'>" +
            "    AND pre_id = #{preId} " +
            "  </when>" +
            "  <otherwise>" +
            "    AND pre_id IS NULL " +
            "  </otherwise>" +
            "</choose>" +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    int checkOrgNameExists(@Param("organName") String organName, @Param("preId") Long preId);

    /**
     * 根据部门ID查询部门信息（包含父部门名称）
     * 用于新增后返回完整信息
     *
     * @param id 部门ID
     * @return 部门详细信息
     */
    @Select("SELECT " +
            "    tos.id AS id, " +
            "    tos.organ_name AS organName, " +
            "    tos.pre_id AS preId, " +
            "    parent.organ_name AS parentName, " +
            "    tos.order_info AS orderInfo, " +
            "    tos.data_source AS dataSource " +
            "FROM t_org_structure tos " +
            "LEFT JOIN t_org_structure parent ON tos.pre_id = parent.id " +
            "WHERE tos.id = #{id}")
    AddOrgStructureResponseVO getOrgStructureById(@Param("id") Long id);

    /**
     * 修改部门信息
     * 支持修改部门名称、排序序号等信息
     *
     * @param id 部门ID
     * @param organName 新的组织名称
     * @param orderInfo 新的排序序号
     */
    @Update("UPDATE t_org_structure SET " +
            "organ_name = #{organName}, " +
            "order_info = #{orderInfo}, " +
            "data_source = #{dataSource}, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id} AND (is_del = false OR is_del IS NULL)")
    int updateOrgStructure(@Param("id") Long id,
                          @Param("organName") String organName,
                          @Param("orderInfo") Integer orderInfo,
                          @Param("dataSource") Integer dataSource);

    /**
     * 检查修改时的名称唯一性
     * 排除当前部门ID，检查同一父部门下是否有重复名称
     *
     * @param organName 组织名称
     * @param preId 父部门ID
     * @param excludeId 要排除的部门ID（当前修改的部门）
     * @return 存在的记录数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_org_structure " +
            "WHERE organ_name = #{organName} " +
            "AND id != #{excludeId} " +
            "<choose>" +
            "  <when test='preId != null'>" +
            "    AND pre_id = #{preId} " +
            "  </when>" +
            "  <otherwise>" +
            "    AND pre_id IS NULL " +
            "  </otherwise>" +
            "</choose>" +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    int checkOrgNameExistsForUpdate(@Param("organName") String organName,
                                   @Param("preId") Long preId,
                                   @Param("excludeId") Long excludeId);

    /**
     * 根据部门ID查询修改后的部门信息（包含父部门名称）
     * 用于修改后返回完整信息
     *
     * @param id 部门ID
     * @return 部门详细信息
     */
    @Select("SELECT " +
            "    tos.id AS id, " +
            "    tos.organ_name AS organName, " +
            "    tos.pre_id AS preId, " +
            "    parent.organ_name AS parentName, " +
            "    tos.order_info AS orderInfo, " +
            "    tos.data_source AS dataSource " +
            "FROM t_org_structure tos " +
            "LEFT JOIN t_org_structure parent ON tos.pre_id = parent.id " +
            "WHERE tos.id = #{id}")
    UpdateOrgStructureResponseVO getUpdatedOrgStructureById(@Param("id") Long id);

    /**
     * 逻辑删除部门
     * 将is_del字段设置为true，并更新modify_time
     *
     * @param id 部门ID
     * @return 影响的行数
     */
    @Update("UPDATE t_org_structure SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{id} AND (is_del = false OR is_del IS NULL)")
    int deleteOrgStructure(@Param("id") Long id);

    /**
     * 批量逻辑删除部门
     * 将多个部门的is_del字段设置为true
     *
     * @param ids 部门ID列表
     * @return 影响的行数
     */
    @Update("<script>" +
            "UPDATE t_org_structure SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "  #{id}" +
            "</foreach>" +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    int batchDeleteOrgStructure(@Param("ids") List<Long> ids);

    /**
     * 查询所有根部门（用于构建部门树）
     * 查询父ID为NULL的根节点部门
     *
     * @param includeDeleted 是否包含已删除的部门
     * @return 根部门列表
     */
    @Select("<script>" +
            "SELECT " +
            "    id, " +
            "    organ_name AS organName, " +
            "    pre_id AS preId, " +
            "    order_info AS orderInfo " +
            "FROM t_org_structure " +
            "WHERE (pre_id = 0 OR pre_id IS NULL) " +
            "<if test='includeDeleted == null or includeDeleted == false'>" +
            "  AND (is_del = false OR is_del IS NULL) " +
            "</if>" +
            "ORDER BY order_info ASC, id ASC" +
            "</script>")
    List<OrgStructureTreeResponseVO> findRootOrgStructures(@Param("includeDeleted") Boolean includeDeleted);

    /**
     * 根据父部门ID查询子部门（用于构建部门树）
     *
     * @param parentId 父部门ID
     * @param includeDeleted 是否包含已删除的部门
     * @return 子部门列表
     */
    @Select("<script>" +
            "SELECT " +
            "    id, " +
            "    organ_name AS organName, " +
            "    pre_id AS preId, " +
            "    order_info AS orderInfo " +
            "FROM t_org_structure " +
            "WHERE pre_id = #{parentId} " +
            "<if test='includeDeleted == null or includeDeleted == false'>" +
            "  AND (is_del = false OR is_del IS NULL) " +
            "</if>" +
            "ORDER BY order_info ASC, id ASC" +
            "</script>")
    List<OrgStructureTreeResponseVO> findChildOrgStructures(@Param("parentId") Long parentId,
                                                           @Param("includeDeleted") Boolean includeDeleted);

    /**
     * 获取指定父部门下的最大排序序号
     * 用于计算新移动部门的排序位置
     *
     * @param parentId 父部门ID（null表示根级别）
     * @return 最大排序序号，如果没有子部门则返回0
     */
    @Select("<script>" +
            "SELECT COALESCE(MAX(order_info), 0) " +
            "FROM t_org_structure " +
            "WHERE " +
            "<choose>" +
            "  <when test='parentId == null'>" +
            "    pre_id IS NULL " +
            "  </when>" +
            "  <otherwise>" +
            "    pre_id = #{parentId} " +
            "  </otherwise>" +
            "</choose>" +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    Integer getMaxOrderInfoByParentId(@Param("parentId") Long parentId);

    /**
     * 更新部门的父级关系和排序信息
     * 用于移动部门操作
     *
     * @param orgId 部门ID
     * @param newParentId 新父部门ID
     * @param newOrderInfo 新排序序号
     * @return 影响的行数
     */
    @Update("UPDATE t_org_structure SET " +
            "pre_id = #{newParentId}, " +
            "order_info = #{newOrderInfo}, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{orgId} " +
            "AND (is_del = false OR is_del IS NULL)")
    int updateOrgParentAndOrder(@Param("orgId") Long orgId,
                               @Param("newParentId") Long newParentId,
                               @Param("newOrderInfo") Integer newOrderInfo);

    /**
     * 调整指定父部门下其他部门的排序序号
     * 当插入新部门到指定位置时，需要将后面的部门序号后移
     *
     * @param parentId 父部门ID
     * @param fromOrderInfo 从此序号开始调整（包含）
     * @param excludeOrgId 排除的部门ID（移动的部门本身）
     * @return 影响的行数
     */
    @Update("<script>" +
            "UPDATE t_org_structure SET " +
            "order_info = order_info + 1, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE " +
            "<choose>" +
            "  <when test='parentId == null'>" +
            "    pre_id IS NULL " +
            "  </when>" +
            "  <otherwise>" +
            "    pre_id = #{parentId} " +
            "  </otherwise>" +
            "</choose>" +
            "AND order_info >= #{fromOrderInfo} " +
            "AND id != #{excludeOrgId} " +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    int adjustOrderInfoForMove(@Param("parentId") Long parentId,
                              @Param("fromOrderInfo") Integer fromOrderInfo,
                              @Param("excludeOrgId") Long excludeOrgId);

    /**
     * 递归查询所有子部门ID
     * 使用PostgreSQL的WITH RECURSIVE CTE查询所有子部门
     *
     * @param parentId 父部门ID
     * @return 所有子部门ID列表
     */
    @Select("WITH RECURSIVE child_orgs AS (" +
            "  SELECT id FROM t_org_structure " +
            "  WHERE pre_id = #{parentId} AND (is_del = false OR is_del IS NULL) " +
            "  UNION ALL " +
            "  SELECT tos.id FROM t_org_structure tos " +
            "  INNER JOIN child_orgs co ON tos.pre_id = co.id " +
            "  WHERE tos.is_del = false OR tos.is_del IS NULL " +
            ") " +
            "SELECT id FROM child_orgs")
    List<Long> findAllChildOrgIds(@Param("parentId") Long parentId);

    /**
     * 一次性查询所有部门数据（用于构建部门树的性能优化）
     * 批量查询所有部门，避免N+1查询问题
     *
     * @param includeDeleted 是否包含已删除的部门
     * @return 所有部门列表
     */
    @Select("<script>" +
            "SELECT " +
            "    id, " +
            "    organ_name AS organName, " +
            "    pre_id AS preId, " +
            "    order_info AS orderInfo " +
            "FROM t_org_structure " +
            "<if test='includeDeleted == null or includeDeleted == false'>" +
            "WHERE (is_del = false OR is_del IS NULL) " +
            "</if>" +
            "ORDER BY order_info ASC, id ASC" +
            "</script>")
    List<OrgStructureTreeResponseVO> findAllOrgStructuresForTree(@Param("includeDeleted") Boolean includeDeleted);

    /**
     * 检查部门是否有用户关联
     * 检查是否有用户属于该部门或其子部门
     *
     * @param orgIds 部门ID列表
     * @return 关联用户数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_user " +
            "WHERE organ_affiliation IN " +
            "<foreach collection='orgIds' item='orgId' open='(' separator=',' close=')'>" +
            "  #{orgId}" +
            "</foreach>" +
            "AND (is_del = false OR is_del IS NULL)" +
            "</script>")
    int checkUsersInOrgs(@Param("orgIds") List<Long> orgIds);

    /**
     * 根据部门ID查询部门基本信息
     * 用于删除前获取部门信息
     *
     * @param id 部门ID
     * @return 部门基本信息
     */
    @Select("SELECT id AS deletedId, organ_name AS deletedName FROM t_org_structure " +
            "WHERE id = #{id} AND (is_del = false OR is_del IS NULL)")
    DeleteOrgStructureResponseVO getOrgBasicInfo(@Param("id") Long id);

    /**
     * 根据部门ID查询部门基本信息（用于构建路径）
     * 只查询必要字段，避免时间字段的兼容性问题
     *
     * @param id 部门ID
     * @return 部门基本信息
     */
    @Select("SELECT id, organ_name AS organName, pre_id AS preId FROM t_org_structure " +
            "WHERE id = #{id} AND (is_del = false OR is_del IS NULL)")
    TOrgStructure getOrgForPath(@Param("id") Long id);
}
