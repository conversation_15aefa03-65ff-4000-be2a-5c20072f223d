### 修改部门接口测试

### 1. 修改部门名称（基本重命名）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1002,
  "organName": "技术研发部",
  "orderInfo": 1
}

### 2. 只修改部门名称（不修改排序）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1003,
  "organName": "市场营销部"
}

### 3. 只修改排序序号（不修改名称）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1004,
  "organName": "人力资源部",
  "orderInfo": 5
}

### 4. 同时修改名称和排序
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1005,
  "organName": "财务管理部",
  "orderInfo": 3
}

### 5. 测试重复名称（应该失败）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1006,
  "organName": "技术研发部",
  "orderInfo": 2
}

### 6. 测试不存在的部门ID（应该失败）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 99999,
  "organName": "不存在的部门",
  "orderInfo": 1
}

### 7. 测试空名称（应该失败）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": 1007,
  "organName": "",
  "orderInfo": 1
}

### 8. 测试null的部门ID（应该失败）
POST http://localhost:8080/t-org-structure/update
Content-Type: application/json

{
  "id": null,
  "organName": "测试部门",
  "orderInfo": 1
}
