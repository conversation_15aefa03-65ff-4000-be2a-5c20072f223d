# 删除菜单接口测试文档

## 接口信息
- **接口路径**: `POST /menus/deleteMenu`
- **接口描述**: 逻辑删除菜单项，包含子菜单和权限使用检查
- **请求方式**: POST
- **Content-Type**: application/json

## 请求参数

### 请求体格式
```json
{
    "menuId": "1930307870503604224"
}
```

### 参数说明
| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| menuId | String | 是 | 菜单ID | "1930307870503604224" |

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "删除成功",
    "data": null
}
```

### 失败响应示例

#### 1. 菜单不存在
```json
{
    "code": 500,
    "message": "删除菜单失败：指定的菜单不存在",
    "data": null
}
```

#### 2. 菜单存在子菜单
```json
{
    "code": 500,
    "message": "删除菜单失败：该菜单存在子菜单，请先删除子菜单",
    "data": null
}
```

#### 3. 菜单被角色权限使用
```json
{
    "code": 500,
    "message": "删除菜单失败：该菜单正在被角色权限使用，无法删除",
    "data": null
}
```

## 测试用例

### 测试用例1：删除叶子节点菜单（成功）
**测试目标**: 删除没有子菜单且未被使用的菜单项

**前置条件**:
1. 菜单ID存在且有效
2. 该菜单没有子菜单
3. 该菜单未被角色权限使用

**测试步骤**:
1. 发送POST请求到 `/menus/deleteMenu`
2. 请求体包含有效的menuId

**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": "1930307870503604230"
  }'
```

**预期结果**:
- 响应状态码: 200
- 响应体: `{"code": 200, "message": "删除成功", "data": null}`
- 数据库中该菜单的is_del字段被设置为true

### 测试用例2：删除不存在的菜单（失败）
**测试目标**: 验证删除不存在菜单的错误处理

**测试步骤**:
1. 发送POST请求到 `/menus/deleteMenu`
2. 请求体包含不存在的menuId

**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": "9999999999999999999"
  }'
```

**预期结果**:
- 响应状态码: 200
- 响应体: `{"code": 500, "message": "删除菜单失败：指定的菜单不存在", "data": null}`

### 测试用例3：删除有子菜单的父级菜单（失败）
**测试目标**: 验证删除有子菜单的父级菜单时的错误处理

**前置条件**:
1. 菜单ID存在且有效
2. 该菜单存在子菜单

**测试步骤**:
1. 发送POST请求到 `/menus/deleteMenu`
2. 请求体包含有子菜单的父级菜单ID

**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": "1930307870503604223"
  }'
```

**预期结果**:
- 响应状态码: 200
- 响应体: `{"code": 500, "message": "删除菜单失败：该菜单存在子菜单，请先删除子菜单", "data": null}`

### 测试用例4：删除被角色权限使用的菜单（失败）
**测试目标**: 验证删除被角色权限使用的菜单时的错误处理

**前置条件**:
1. 菜单ID存在且有效
2. 该菜单被角色权限使用（在t_roles_menu_permission表中有记录）

**测试步骤**:
1. 发送POST请求到 `/menus/deleteMenu`
2. 请求体包含被角色权限使用的菜单ID

**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": "1930307870503604225"
  }'
```

**预期结果**:
- 响应状态码: 200
- 响应体: `{"code": 500, "message": "删除菜单失败：该菜单正在被角色权限使用，无法删除", "data": null}`

### 测试用例5：参数验证测试
**测试目标**: 验证请求参数的有效性

#### 5.1 menuId为空
**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": null
  }'
```

#### 5.2 menuId为空字符串
**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{
    "menuId": ""
  }'
```

#### 5.3 缺少menuId参数
**请求示例**:
```bash
curl -X POST "http://localhost:8080/menus/deleteMenu" \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 业务逻辑验证

### 验证点1：逻辑删除
- 删除操作应该是逻辑删除，即设置is_del=true
- 不应该物理删除数据库记录
- modify_time字段应该更新为当前时间

### 验证点2：子菜单检查
- 系统应该检查待删除菜单是否有子菜单
- 如果有子菜单，应该阻止删除操作
- 错误信息应该明确提示用户先删除子菜单

### 验证点3：权限使用检查
- 系统应该检查菜单是否被角色权限使用
- 如果被使用，应该阻止删除操作
- 错误信息应该明确提示菜单正在被使用

### 验证点4：事务控制
- 删除操作应该在事务中执行
- 如果发生异常，应该回滚所有操作
- 确保数据一致性

## 数据库验证SQL

### 验证菜单是否被逻辑删除
```sql
SELECT id, name, is_del, modify_time 
FROM t_menu_permission 
WHERE id = '1930307870503604230';
```

### 验证子菜单关系
```sql
SELECT id, name, pre_id 
FROM t_menu_permission 
WHERE pre_id = '1930307870503604223' AND is_del = false;
```

### 验证角色权限使用情况
```sql
SELECT COUNT(*) as usage_count
FROM t_roles_menu_permission 
WHERE menu_permission_id = '1930307870503604225' AND is_del = false;
```

## 注意事项

1. **测试环境准备**: 确保测试数据库中有足够的测试数据
2. **数据备份**: 在执行删除测试前备份重要数据
3. **权限验证**: 确保测试用户有执行删除操作的权限
4. **并发测试**: 考虑多用户同时删除同一菜单的情况
5. **性能测试**: 验证删除操作的响应时间是否在可接受范围内

## 自动化测试脚本

可以使用以下工具进行自动化测试：
- **Postman**: 创建测试集合，包含所有测试用例
- **JUnit**: 编写单元测试和集成测试
- **RestAssured**: 编写API自动化测试

---

*测试文档版本: v1.0*
*创建时间: 2024年12月19日*
*最后更新: 2024年12月19日*
