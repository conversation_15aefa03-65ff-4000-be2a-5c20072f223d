package com.dfit.percode.sync.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 增量数据同步请求参数
 * 用于定时任务和手动触发的增量同步
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "IncrementalSyncRequest", description = "增量数据同步请求参数")
public class IncrementalSyncRequest {

    @ApiModelProperty(value = "开始时间", required = true, example = "2025-01-19 00:00:00")
    @NotBlank(message = "开始时间不能为空")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", 
             message = "开始时间格式必须为 yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @ApiModelProperty(value = "结束时间", required = true, example = "2025-01-19 23:59:59")
    @NotBlank(message = "结束时间不能为空")
    @Pattern(regexp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", 
             message = "结束时间格式必须为 yyyy-MM-dd HH:mm:ss")
    private String endDate;

    @ApiModelProperty(value = "同步类型", example = "incremental", notes = "incremental=增量同步, full=完整同步")
    private String syncType = "incremental";

    @ApiModelProperty(value = "是否强制同步", example = "false", notes = "true=强制同步所有数据, false=只同步变更数据")
    private Boolean forceSync = false;

    @ApiModelProperty(value = "同步模块", example = "all", notes = "all=全部模块, department=仅部门, employee=仅员工")
    private String syncModule = "all";

    @ApiModelProperty(value = "备注说明", example = "每日定时增量同步")
    private String remark;
}
