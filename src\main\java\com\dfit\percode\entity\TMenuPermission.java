package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 菜单管理列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_menu_permission")
@ApiModel(value = "TMenuPermission对象", description = "菜单管理列表")
public class TMenuPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("父ID，根节点为空")
    private Long preId;

    @ApiModelProperty("模块标识 与菜单模块表中模块标识一致")
    private Integer moduleIdentifier;

    private String orderInfo;

    private String isDisable;

    @ApiModelProperty("菜单类型，1目录 2菜单 3按钮")
    private Integer menuType;

    @ApiModelProperty("路由地址")
    private String routeAddress;

    @ApiModelProperty("组件路径")
    private String componentPath;

    @ApiModelProperty("权限标识")
    private String permissionIdentifier;

    @ApiModelProperty("路由参数，用于动态路由传参")
    private String routeParam;

    private Integer isDel;

    private String createTime;

    private String modifyTime;
}
