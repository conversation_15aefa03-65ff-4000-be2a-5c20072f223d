-- =====================================================
-- 数据库修改脚本：允许扩展表保存孤儿记录
-- 目的：允许员工扩展数据在没有对应用户时也能保存
-- 执行时间：2025年6月9日
-- =====================================================

-- 修改员工岗位表，允许user_id为空
ALTER TABLE t_employee_position 
ALTER COLUMN user_id DROP NOT NULL;

-- 修改员工职称表，允许user_id为空
ALTER TABLE t_employee_title 
ALTER COLUMN user_id DROP NOT NULL;

-- 修改员工系统标识表，允许user_id为空
ALTER TABLE t_employee_system 
ALTER COLUMN user_id DROP NOT NULL;

-- 添加注释说明修改原因
COMMENT ON COLUMN t_employee_position.user_id IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';
COMMENT ON COLUMN t_employee_title.user_id IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';
COMMENT ON COLUMN t_employee_system.user_id IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- 创建用于关联孤儿记录的存储过程
CREATE OR REPLACE FUNCTION link_orphan_employee_records()
RETURNS INTEGER AS $$
DECLARE
    linked_count INTEGER := 0;
    position_count INTEGER := 0;
    title_count INTEGER := 0;
    system_count INTEGER := 0;
BEGIN
    -- 关联员工岗位表的孤儿记录
    UPDATE t_employee_position 
    SET user_id = (
        SELECT u.id 
        FROM t_user u 
        WHERE u.mdm_id = t_employee_position.employee_mdm_id 
        AND u.is_del = false
    )
    WHERE user_id IS NULL 
    AND employee_mdm_id IS NOT NULL
    AND EXISTS (
        SELECT 1 FROM t_user u 
        WHERE u.mdm_id = t_employee_position.employee_mdm_id 
        AND u.is_del = false
    );
    
    GET DIAGNOSTICS position_count = ROW_COUNT;
    
    -- 关联员工职称表的孤儿记录
    UPDATE t_employee_title 
    SET user_id = (
        SELECT u.id 
        FROM t_user u 
        WHERE u.mdm_id = t_employee_title.employee_mdm_id 
        AND u.is_del = false
    )
    WHERE user_id IS NULL 
    AND employee_mdm_id IS NOT NULL
    AND EXISTS (
        SELECT 1 FROM t_user u 
        WHERE u.mdm_id = t_employee_title.employee_mdm_id 
        AND u.is_del = false
    );
    
    GET DIAGNOSTICS title_count = ROW_COUNT;
    
    -- 关联员工系统标识表的孤儿记录
    UPDATE t_employee_system 
    SET user_id = (
        SELECT u.id 
        FROM t_user u 
        WHERE u.mdm_id = t_employee_system.employee_mdm_id 
        AND u.is_del = false
    )
    WHERE user_id IS NULL 
    AND employee_mdm_id IS NOT NULL
    AND EXISTS (
        SELECT 1 FROM t_user u 
        WHERE u.mdm_id = t_employee_system.employee_mdm_id 
        AND u.is_del = false
    );
    
    GET DIAGNOSTICS system_count = ROW_COUNT;
    
    linked_count := position_count + title_count + system_count;
    
    -- 记录关联结果
    RAISE NOTICE '孤儿记录关联完成: 岗位表=% 条, 职称表=% 条, 系统表=% 条, 总计=% 条', 
                 position_count, title_count, system_count, linked_count;
    
    RETURN linked_count;
END;
$$ LANGUAGE plpgsql;

-- 创建查询孤儿记录的视图
CREATE OR REPLACE VIEW v_orphan_employee_records AS
SELECT 
    'position' as record_type,
    id,
    employee_mdm_id,
    NULL::VARCHAR as title_name,
    NULL::VARCHAR as system_code,
    create_time
FROM t_employee_position 
WHERE user_id IS NULL AND employee_mdm_id IS NOT NULL

UNION ALL

SELECT 
    'title' as record_type,
    id,
    employee_mdm_id,
    title_name,
    NULL::VARCHAR as system_code,
    create_time
FROM t_employee_title 
WHERE user_id IS NULL AND employee_mdm_id IS NOT NULL

UNION ALL

SELECT 
    'system' as record_type,
    id,
    employee_mdm_id,
    NULL::VARCHAR as title_name,
    system_code,
    create_time
FROM t_employee_system 
WHERE user_id IS NULL AND employee_mdm_id IS NOT NULL

ORDER BY create_time DESC;

-- 添加视图注释
COMMENT ON VIEW v_orphan_employee_records IS '孤儿记录查询视图 - 显示所有未关联到用户的员工扩展数据';

-- 执行完成提示
SELECT 'Database modification completed: Extended tables now allow orphan records' as status;
