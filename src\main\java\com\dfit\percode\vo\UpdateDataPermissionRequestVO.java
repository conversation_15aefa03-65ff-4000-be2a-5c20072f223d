package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改数据权限请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UpdateDataPermissionRequestVO", description = "修改数据权限请求参数")
public class UpdateDataPermissionRequestVO {
    
    @ApiModelProperty(value = "数据权限ID", required = true, example = "1930307870503604224")
    private String id;
    
    @ApiModelProperty(value = "数据权限名称", required = true, example = "用户基础数据")
    private String name;
    
    @ApiModelProperty(value = "父级ID，根节点为0", required = true, example = "0")
    private Long preId;
    
    @ApiModelProperty(value = "模块标识", required = true, example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "数据类型", required = true, example = "1")
    private Integer dataType;
    
    @ApiModelProperty(value = "数据标识", required = true, example = "user_basic_data")
    private String dataIdentifier;
    
    @ApiModelProperty(value = "排序序号", required = false, example = "1")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "是否禁用", required = false, example = "false")
    private Boolean isDisable;
}
