2025-06-21 02:00:00.002 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行每日增量数据同步任务 ===
2025-06-21 02:00:00.014 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - 同步时间范围: 2025-06-20 00:00:00 - 2025-06-20 23:59:59
2025-06-21 02:00:00.721 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始执行增量数据同步，时间范围: 2025-06-20 00:00:00 - 2025-06-20 23:59:59
2025-06-21 02:00:00.727 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 采用增量同步策略，只处理时间范围内的变更数据
2025-06-21 02:00:00.727 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2025-06-20T00:00 - 2025-06-20T23:59:59
2025-06-21 02:00:00.730 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 同步策略: 建议策略：单次同步（23小时 ≤ 72小时阈值）
2025-06-21 02:00:00.730 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2025-06-20T00:00 - 2025-06-20T23:59:59
2025-06-21 02:00:00.751 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2025-06-20%2000:00:00&endDate=2025-06-20%2023:59:59
2025-06-21 02:00:00.751 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部系统获取部门数据（外部系统已内置重试和分片机制）
2025-06-21 18:32:50.967 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行员工部门归属更新任务 ===
2025-06-21 18:32:51.952 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始更新员工部门归属
2025-06-21 18:32:53.828 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 员工岗位数据统计: 总岗位=0, 有org_code=0, 无org_code=0, 主岗位=0, 有user_id=0, 无user_id=0
2025-06-21 18:32:53.900 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 用户数据统计: 总用户=0, 无部门归属=0
2025-06-21 18:32:53.982 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 岗位数据样本（前10条）:
2025-06-21 18:32:54.060 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - JOIN查询测试结果（前10条）:
2025-06-21 18:32:54.124 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 找到 0 个需要更新部门归属的用户
2025-06-21 18:32:54.124 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 员工部门归属更新汇总: 成功更新=0 个, 未找到部门=0 个, org_code为空=0 个
2025-06-21 18:32:54.220 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 员工部门归属更新任务执行成功 ===
2025-06-21 20:04:30.454 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-21 20:04:30.868 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-21 20:04:59.846 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 23556 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-21 20:04:59.849 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-21 20:04:59.861 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-21 20:05:05.411 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 20:05:05.414 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-21 20:05:05.445 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Elasticsearch repository interfaces.
2025-06-21 20:05:05.451 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 20:05:05.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-21 20:05:05.465 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-21 20:05:05.475 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 20:05:05.475 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-21 20:05:05.492 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-21 20:05:05.514 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-21 20:05:05.519 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-21 20:05:05.540 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-21 20:05:08.567 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-21 20:05:08.590 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-21 20:05:08.591 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-21 20:05:08.592 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-21 20:05:09.286 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-21 20:05:09.286 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9321 ms
2025-06-21 20:05:09.398 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-21 20:05:09.718 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-21 20:05:10.872 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-21 20:05:11.036 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-21 20:05:11.620 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-21 20:05:11.906 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-21 20:05:12.421 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-21 20:05:12.437 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-21 20:05:16.006 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-21 20:05:22.968 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-21 20:05:22.982 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-21 20:05:23.996 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 25.298 seconds (JVM running for 29.668)
2025-06-21 20:06:17.359 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-21 20:06:17.359 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-21 20:06:17.362 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-21 20:34:00.047 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-21 20:34:00.055 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
