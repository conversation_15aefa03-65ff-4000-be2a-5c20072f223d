package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 搜索部门请求VO类
 * 按照数据库字段设计
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "SearchOrgStructureRequestVO", description = "搜索部门请求参数")
public class SearchOrgStructureRequestVO {
    
    @ApiModelProperty(value = "组织名称（模糊搜索）", example = "研发")
    private String organName;
    
    @ApiModelProperty(value = "父部门ID（精确匹配）", example = "1001")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long preId;
    
    @ApiModelProperty(value = "是否包含已删除的部门", example = "false")
    private Boolean includeDeleted;
}
