package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户列表项VO类
 * 按照前端格式要求设计（驼峰命名）
 * 用于分页查询结果显示
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListItemVO", description = "用户列表项信息")
public class UserListItemVO {

    @ApiModelProperty(value = "用户ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户姓名", example = "张三")
    private String userName;

    @ApiModelProperty(value = "登录账号", example = "admin")
    private String account;

    @ApiModelProperty(value = "部门名称", example = "科研室")
    private String department;

    @ApiModelProperty(value = "已分配的角色名称列表")
    private List<String> roleName;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "创建时间", example = "2025-01-20 10:00:00")
    private String createTime;
}
