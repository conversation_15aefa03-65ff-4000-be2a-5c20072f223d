import java.sql.*;

public class SimpleCleanDuplicates {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== 简单清理重复部门名称 ===");
        
        try {
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            conn.setAutoCommit(false);
            
            // 使用一个简单的SQL删除重复记录，保留ID最小的
            String deleteSql = 
                "DELETE FROM t_org_structure " +
                "WHERE data_source = 2 " +
                "AND id NOT IN (" +
                "  SELECT MIN(id) " +
                "  FROM t_org_structure " +
                "  WHERE data_source = 2 " +
                "  GROUP BY organ_name" +
                ")";
            
            PreparedStatement deleteStmt = conn.prepareStatement(deleteSql);
            int deletedCount = deleteStmt.executeUpdate();
            deleteStmt.close();
            
            System.out.println("删除了 " + deletedCount + " 条重复记录");
            
            // 提交事务
            conn.commit();
            
            // 验证结果
            PreparedStatement verifyStmt = conn.prepareStatement(
                "SELECT COUNT(*) as total, " +
                "COUNT(DISTINCT organ_name) as unique_names " +
                "FROM t_org_structure WHERE data_source = 2"
            );
            ResultSet verifyRs = verifyStmt.executeQuery();
            
            if (verifyRs.next()) {
                int total = verifyRs.getInt("total");
                int unique = verifyRs.getInt("unique_names");
                
                System.out.println("清理后统计:");
                System.out.println("  总记录数: " + total);
                System.out.println("  唯一名称数: " + unique);
                
                if (total == unique) {
                    System.out.println("✅ 清理成功，无重复记录");
                } else {
                    System.out.println("⚠️  仍有重复: " + (total - unique));
                }
            }
            verifyRs.close();
            verifyStmt.close();
            
            conn.close();
            
        } catch (SQLException e) {
            System.err.println("清理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
