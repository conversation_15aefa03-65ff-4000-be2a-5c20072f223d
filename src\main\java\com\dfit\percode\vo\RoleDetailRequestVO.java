package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色详情查询请求VO类
 * 用于查询角色的完整信息，包括基本信息和权限关联
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleDetailRequestVO", description = "角色详情查询请求参数")
public class RoleDetailRequestVO {
    
    @ApiModelProperty(value = "角色ID", required = true, example = "123456789")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;
}
