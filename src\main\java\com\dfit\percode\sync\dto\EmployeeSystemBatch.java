package com.dfit.percode.sync.dto;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 员工系统标识批量插入DTO
 * 用于性能优化的批量操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class EmployeeSystemBatch {
    
    private Long id;
    private Long userId;
    private String guid;
    private String employeeMdmId;
    private String systemCode;
    private String systemDataId;
    private String orgCode;
    private String departmentCode;
    private String employeeCode;
    private String loginAccount;
    private Long externalId;
    private String syncStatus;
    private LocalDateTime lastSyncTime;
    
    /**
     * 构造方法
     */
    public EmployeeSystemBatch(Long id, Long userId, String guid, String employeeMdmId,
                              String systemCode, String systemDataId, String orgCode,
                              String departmentCode, String employeeCode, String loginAccount,
                              Long externalId, String syncStatus, LocalDateTime lastSyncTime) {
        this.id = id;
        this.userId = userId;
        this.guid = guid;
        this.employeeMdmId = employeeMdmId;
        this.systemCode = systemCode;
        this.systemDataId = systemDataId;
        this.orgCode = orgCode;
        this.departmentCode = departmentCode;
        this.employeeCode = employeeCode;
        this.loginAccount = loginAccount;
        this.externalId = externalId;
        this.syncStatus = syncStatus;
        this.lastSyncTime = lastSyncTime;
    }
}
