package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增菜单模块请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddMenuModuleRequestVO", description = "新增菜单模块请求参数")
public class AddMenuModuleRequestVO {
    
    @ApiModelProperty(value = "模块名称", required = true, example = "用户管理")
    private String moduleName;
    
    @ApiModelProperty(value = "模块标识", required = true, example = "user_management")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "排序序号", required = true, example = "1")
    private Integer orderInfo;
}
