### 数据同步定时任务测试用例
### 测试数据同步定时任务的各种功能

### 1. 获取定时任务状态
GET {{baseUrl}}/data-sync/task-status
Content-Type: application/json

### 2. 手动触发增量同步（同步最近1天的数据）
POST {{baseUrl}}/data-sync/manual-incremental/1
Content-Type: application/json

### 3. 手动触发增量同步（同步最近3天的数据）
POST {{baseUrl}}/data-sync/manual-incremental/3
Content-Type: application/json

### 4. 手动触发增量同步（同步最近7天的数据）
POST {{baseUrl}}/data-sync/manual-incremental/7
Content-Type: application/json

### 5. 执行增量数据同步（指定时间范围）
POST {{baseUrl}}/data-sync/incremental
Content-Type: application/json

{
  "startDate": "2025-01-19 00:00:00",
  "endDate": "2025-01-19 23:59:59",
  "syncType": "incremental",
  "forceSync": false,
  "syncModule": "all",
  "remark": "手动测试增量同步"
}

### 6. 执行增量数据同步（只同步部门数据）
POST {{baseUrl}}/data-sync/incremental
Content-Type: application/json

{
  "startDate": "2025-01-19 00:00:00",
  "endDate": "2025-01-19 23:59:59",
  "syncType": "incremental",
  "forceSync": false,
  "syncModule": "department",
  "remark": "只同步部门数据"
}

### 7. 执行增量数据同步（只同步员工数据）
POST {{baseUrl}}/data-sync/incremental
Content-Type: application/json

{
  "startDate": "2025-01-19 00:00:00",
  "endDate": "2025-01-19 23:59:59",
  "syncType": "incremental",
  "forceSync": false,
  "syncModule": "employee",
  "remark": "只同步员工数据"
}

### 8. 执行强制增量同步
POST {{baseUrl}}/data-sync/incremental
Content-Type: application/json

{
  "startDate": "2025-01-19 00:00:00",
  "endDate": "2025-01-19 23:59:59",
  "syncType": "incremental",
  "forceSync": true,
  "syncModule": "all",
  "remark": "强制同步所有数据"
}

### 9. 更新员工部门归属
POST {{baseUrl}}/data-sync/updateEmployeeDepartmentAffiliation
Content-Type: application/json

### 10. 清理重复数据
POST {{baseUrl}}/data-sync/cleanDuplicateData
Content-Type: application/json

### 11. 获取同步状态
GET {{baseUrl}}/data-sync/status
Content-Type: application/json

### 12. 测试外部系统连接
GET {{baseUrl}}/data-sync/test-connection
Content-Type: application/json

### 13. 执行完整数据同步（指定时间范围）
POST {{baseUrl}}/data-sync/full?startDate=2025-01-19 00:00:00&endDate=2025-01-19 23:59:59
Content-Type: application/json

### 14. 同步部门数据（指定时间范围）
POST {{baseUrl}}/data-sync/departments?startDate=2025-01-19 00:00:00&endDate=2025-01-19 23:59:59
Content-Type: application/json

### 15. 同步员工数据（指定时间范围）
POST {{baseUrl}}/data-sync/employees?startDate=2025-01-19 00:00:00&endDate=2025-01-19 23:59:59
Content-Type: application/json

### 变量定义
@baseUrl = http://localhost:8080

### 定时任务说明
###
### 1. 每日增量同步：每天凌晨2点执行
###    - 同步前一天的数据变更（00:00:00 - 23:59:59）
###    - Cron表达式：0 0 2 * * ?
###
### 2. 每周完整同步：每周日凌晨1点执行
###    - 同步过去7天的所有数据，确保数据完整性
###    - Cron表达式：0 0 1 ? * SUN
###
### 3. 部门归属更新：每天上午9点执行
###    - 确保新同步的员工岗位数据能正确关联到部门
###    - Cron表达式：0 0 9 * * ?
###
### 4. 孤儿记录关联：每天下午6点执行
###    - 处理之前同步失败的扩展数据记录
###    - Cron表达式：0 0 18 * * ?
###
### 5. 每月数据清理：每月1号凌晨3点执行
###    - 清理重复数据和过期的同步记录
###    - Cron表达式：0 0 3 1 * ?

### 测试场景说明
###
### 场景1：日常增量同步测试
### - 使用接口5测试指定时间范围的增量同步
### - 使用接口2测试手动触发最近1天的增量同步
###
### 场景2：模块化同步测试
### - 使用接口6测试只同步部门数据
### - 使用接口7测试只同步员工数据
###
### 场景3：强制同步测试
### - 使用接口8测试强制同步所有数据
###
### 场景4：数据维护测试
### - 使用接口9测试员工部门归属更新
### - 使用接口10测试重复数据清理
###
### 场景5：系统状态监控
### - 使用接口1获取定时任务状态
### - 使用接口11获取同步状态
### - 使用接口12测试外部系统连接

### 预期结果
###
### 成功响应格式：
### {
###   "code": 200,
###   "message": "SUCCESS",
###   "data": "具体的返回数据"
### }
###
### 失败响应格式：
### {
###   "code": 500,
###   "message": "具体的错误信息",
###   "data": null
### }

### 注意事项
###
### 1. 定时任务会自动执行，无需手动干预
### 2. 手动触发的同步操作会立即执行
### 3. 时间格式必须为：yyyy-MM-dd HH:mm:ss
### 4. 建议在测试环境中验证定时任务功能
### 5. 生产环境中请谨慎使用强制同步功能
