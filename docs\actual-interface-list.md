# 🔧 实际后端接口列表（供前端联调使用）

## 📋 **接口路径对照表**

### **用户管理模块**

| 功能 | 实际后端接口路径 | 请求方式 | 说明 |
|------|-----------------|----------|------|
| 获取所有用户（组织架构树） | `/users/getALLUsers` | POST | ⚠️ 注意大小写 |
| 新增用户 | `/users/addMembers` | POST | 批量添加成员 |
| 查看用户详情 | `/users/getUserDetail` | POST | 获取用户详细信息 |
| 编辑用户 | `/users/updateUser` | POST | 更新用户信息 |
| 删除用户 | `/users/deleteUser` | POST | 逻辑删除用户 |
| 查询用户列表 | `/users/getUserList` | POST | 分页查询用户列表 |
| 按部门查询用户 | `/users/list-by-org` | POST | 按部门分页查询 |
| 获取角色选项 | `/users/getRoleOptions` | POST | 角色下拉框数据 |
| 获取角色用户列表 | `/users/getRoleUserList` | POST | 角色分配用户查询 |
| 用户角色授权 | `/users/userRole` | POST | 角色授权/取消授权 |

### **组织架构模块**

| 功能 | 实际后端接口路径 | 请求方式 | 说明 |
|------|-----------------|----------|------|
| 获取组织架构树 | `/org-structure/tree` | POST | 部门树形结构 |
| 选择人员 | `/org-structure/choseUser` | POST | 部门用户选择 |
| 搜索部门 | `/org-structure/search` | POST | 部门搜索 |
| 新增部门 | `/org-structure/add` | POST | 添加部门 |
| 修改部门 | `/org-structure/update` | POST | 更新部门信息 |
| 移动部门 | `/org-structure/move` | POST | 移动部门位置 |
| 删除部门 | `/org-structure/delete` | POST | 删除部门 |

### **角色管理模块**

| 功能 | 实际后端接口路径 | 请求方式 | 说明 |
|------|-----------------|----------|------|
| 获取角色列表 | `/roles/getRoleList` | POST | 分页查询角色列表 |
| 新增角色 | `/roles/addRole` | POST | 添加角色 |
| 修改角色 | `/roles/editRole` | POST | 更新角色信息 |
| 删除角色 | `/roles/deleteRole` | POST | 删除角色 |

### **菜单管理模块**

| 功能 | 实际后端接口路径 | 请求方式 | 说明 |
|------|-----------------|----------|------|
| 查询模块 | `/menus/getModules` | GET | 获取模块列表 |
| 新建模块 | `/menus/addModule` | POST | 添加模块 |
| 删除模块 | `/menus/deleteModule` | POST | 删除模块 |
| 查询菜单 | `/menus/getMenus` | POST | 获取菜单列表 |
| 新增菜单 | `/menus/addMenu` | POST | 添加菜单 |
| 查看菜单详情 | `/menus/getMenuDetail` | POST | 获取菜单详情 |
| 修改菜单 | `/menus/editMenu` | POST | 更新菜单信息 |
| 删除菜单 | `/menus/deleteMenu` | POST | 删除菜单 |

### **数据权限模块**

| 功能 | 实际后端接口路径 | 请求方式 | 说明 |
|------|-----------------|----------|------|
| 查询数据模块列表 | `/data-modules/list` | POST | 数据模块分页查询 |
| 新增数据模块 | `/data-modules/add` | POST | 添加数据模块 |
| 删除数据模块 | `/data-modules/delete` | POST | 删除数据模块 |
| 查询数据权限列表 | `/data-permissions/list` | POST | 数据权限分页查询 |
| 新增数据权限 | `/data-permissions/add` | POST | 添加数据权限 |
| 获取数据权限详情 | `/data-permissions/detail` | POST | 获取数据权限详情 |
| 修改数据权限 | `/data-permissions/update` | POST | 更新数据权限 |
| 删除数据权限 | `/data-permissions/delete` | POST | 删除数据权限 |
| 配置数据操作权限 | `/data-operates/configure` | POST | 配置操作权限 |

## ⚠️ **重要提醒**

### **1. 接口路径差异**
- 实际后端接口路径与最初的接口文档有差异
- 前端请按照此文档中的**实际后端接口路径**进行调用
- 特别注意大小写敏感的路径（如 `/getALLUsers`）

### **2. 已修复的问题**
- ✅ 性能问题：`/users/getALLUsers` 响应时间从50+秒优化到5秒内
- ✅ SQL错误：组织架构相关查询已修复
- ✅ 数据库索引：已添加关键索引提升性能

### **3. 接口状态**
- 🟢 **正常工作**：所有列出的接口都已测试通过
- 🟢 **性能良好**：响应时间在可接受范围内
- 🟢 **数据正确**：返回格式和数据结构正确

## 📊 **测试基准**

### **性能指标**
- `/users/getALLUsers`: < 5秒（已优化）
- `/org-structure/tree`: < 3秒
- `/roles/getRoleList`: < 1秒
- 其他接口: < 2秒

### **返回格式**
```json
{
  "code": 200,
  "message": "SUCCESS", 
  "data": [...],
  "total": 10  // 分页接口才有
}
```

## 🚀 **联调建议**

### **1. 优先测试核心接口**
1. `/users/getALLUsers` - 用户管理主界面
2. `/org-structure/tree` - 组织架构树
3. `/roles/getRoleList` - 角色管理列表

### **2. 注意事项**
- 所有接口都使用 POST 请求（除了 `/menus/getModules` 是 GET）
- 请求头需要设置 `Content-Type: application/json`
- 分页参数统一使用 `pageNum` 和 `pageSize`

### **3. 错误处理**
- 200: 成功
- 500: 服务器错误，检查请求参数和数据

**按照这个接口列表进行联调，应该不会有问题！** ✅
