package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 菜单列表响应VO类
 * 按照前端设计的格式要求（snake_case命名）
 * 只包含前端需要的字段
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MenuListResponseVO", description = "菜单列表响应数据（前端格式）")
public class MenuListResponseVO {
    
    @ApiModelProperty(value = "菜单ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "菜单名称", example = "首页")
    private String name;
    
    @ApiModelProperty(value = "权限标识", example = "home:view")
    private String permission_identifier;
    
    @ApiModelProperty(value = "组件路径", example = "/views/home/<USER>")
    private String component_path;
    
    @ApiModelProperty(value = "是否禁用", example = "true")
    private Boolean is_disable;
    
    @ApiModelProperty(value = "创建时间", example = "2023-05-10 09:30:00")
    private String create_time;
    
    @ApiModelProperty(value = "子菜单列表")
    private List<MenuListResponseVO> children;
}
