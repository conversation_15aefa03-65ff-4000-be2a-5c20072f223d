<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dfit.percode.mapper.UserMapper">

    <!-- 分页查询用户列表 -->
    <select id="findUserListPage" parameterType="com.dfit.percode.vo.UserListRequestVO"
            resultType="com.dfit.percode.vo.UserListItemVO">
        SELECT DISTINCT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            o.organ_name AS department,
            u.is_disable AS isDisable,
            u.create_time AS createTime
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false
        LEFT JOIN t_role r ON pur.role_id = r.id AND r.is_del = false
        WHERE u.is_del = false
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="userState != null">
            AND u.is_disable = #{userState}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
        ORDER BY u.create_time DESC
        LIMIT #{pageSize} OFFSET #{currentPage}
    </select>

    <!-- 查询用户列表总数 -->
    <select id="countUserList" parameterType="com.dfit.percode.vo.UserListRequestVO"
            resultType="java.lang.Long">
        SELECT COUNT(DISTINCT u.id)
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false
        LEFT JOIN t_role r ON pur.role_id = r.id AND r.is_del = false
        WHERE u.is_del = false
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="userState != null">
            AND u.is_disable = #{userState}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
    </select>

    <!-- 查询所有用户列表（不分页） -->
    <select id="findUserListAll" parameterType="com.dfit.percode.vo.UserListRequestVO"
            resultType="com.dfit.percode.vo.UserListItemVO">
        SELECT DISTINCT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            o.organ_name AS department,
            u.is_disable AS isDisable,
            u.create_time AS createTime
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false
        LEFT JOIN t_role r ON pur.role_id = r.id AND r.is_del = false
        WHERE u.is_del = false
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="userState != null">
            AND u.is_disable = #{userState}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
        ORDER BY u.create_time DESC
    </select>

    <!-- 分页查询角色的用户分配列表 - 只显示已授权用户 -->
    <select id="findRoleUserListPage" parameterType="com.dfit.percode.vo.RoleUserListRequestVO"
            resultType="com.dfit.percode.vo.RoleUserListItemVO">
        SELECT DISTINCT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            o.organ_name AS department,
            u.is_disable AS isDisable,
            r.role_name AS roleName,
            u.create_time AS createTime,
            -- 已授权用户，isAssigned 固定为 true
            true AS isAssigned,
            -- 关联表的is_del状态（用于前端按钮显示）
            pur.is_del AS canAssign
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
        INNER JOIN t_role r ON pur.role_id = r.id
        WHERE u.is_del = false
        AND pur.is_del = false
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="isDisable != null">
            AND u.is_disable = #{isDisable}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
        ORDER BY u.create_time DESC
        LIMIT #{pageSize} OFFSET #{currentPage}
    </select>

    <!-- 查询角色用户分配列表总数 - 只统计已授权用户 -->
    <select id="countRoleUserList" parameterType="com.dfit.percode.vo.RoleUserListRequestVO"
            resultType="java.lang.Long">
        SELECT COUNT(DISTINCT u.id)
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
        WHERE u.is_del = false
        AND pur.is_del = false
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="isDisable != null">
            AND u.is_disable = #{isDisable}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
    </select>

    <!-- 分页查询指定角色的未授权用户列表 -->
    <select id="findUnauthorizedUsersPage" parameterType="com.dfit.percode.vo.UnauthorizedUsersRequestVO"
            resultType="com.dfit.percode.vo.UnauthorizedUserItemVO">
        SELECT DISTINCT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            o.organ_name AS department,
            u.is_disable AS userState,
            u.create_time AS createTime
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
        WHERE u.is_del = false
        AND (pur.id IS NULL OR pur.is_del = true)
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="userState != null">
            AND u.is_disable = #{userState}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
        ORDER BY u.create_time DESC
        LIMIT #{pageSize} OFFSET #{currentPage}
    </select>

    <!-- 查询指定角色的未授权用户总数 -->
    <select id="countUnauthorizedUsers" parameterType="com.dfit.percode.vo.UnauthorizedUsersRequestVO"
            resultType="java.lang.Long">
        SELECT COUNT(DISTINCT u.id)
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
        WHERE u.is_del = false
        AND (pur.id IS NULL OR pur.is_del = true)
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="userState != null">
            AND u.is_disable = #{userState}
        </if>
        <if test="startTime != null and startTime != ''">
            AND u.create_time >= CAST(#{startTime} AS TIMESTAMP)
        </if>
        <if test="endTime != null and endTime != ''">
            AND u.create_time &lt;= CAST(#{endTime} AS TIMESTAMP)
        </if>
    </select>

    <!-- 层级懒加载查询部门和用户 -->
    <select id="findUnauthorizedUsersTreeOptimized" resultType="java.util.Map">
        <choose>
            <!-- 查询顶级部门 -->
            <when test="parentId == null">
                SELECT
                    d.id,
                    d.organ_name AS organName,
                    null AS userName,
                    null AS isDisable
                FROM t_org_structure d
                WHERE (d.pre_id = 0 OR d.pre_id IS NULL) AND d.is_del = false
                ORDER BY d.order_info
            </when>
            <!-- 查询指定部门下的直属部门和用户 -->
            <otherwise>
                SELECT
                    d.id,
                    d.organ_name AS organName,
                    null AS userName,
                    null AS isDisable
                FROM t_org_structure d
                WHERE d.pre_id = #{parentId} AND d.is_del = false

                UNION ALL

                SELECT
                    u.id,
                    null AS organName,
                    u.user_name AS userName,
                    u.is_disable AS isDisable
                FROM t_user u
                WHERE u.organ_affiliation = #{parentId} AND u.is_del = false

                ORDER BY organName, userName
            </otherwise>
        </choose>
    </select>

    <!-- 使用 RECURSIVE CTE 查询部门树和未授权用户（分页优化版本） -->
    <select id="findUnauthorizedUsersTreeOptimizedWithPaging" resultType="java.util.Map">
        WITH RECURSIVE dept_tree AS (
            -- 递归起点：查询所有根部门
            SELECT
                id AS deptId,
                organ_name AS deptName,
                pre_id AS parentId,
                1 AS level
            FROM t_org_structure
            WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false

            UNION ALL

            -- 递归部分：查询子部门
            SELECT
                os.id AS deptId,
                os.organ_name AS deptName,
                os.pre_id AS parentId,
                dt.level + 1 AS level
            FROM t_org_structure os
            INNER JOIN dept_tree dt ON os.pre_id = dt.deptId
            WHERE os.is_del = false
        ),
        unauthorized_users AS (
            -- 查询未授权用户（带分页）
            SELECT
                u.id AS userId,
                u.user_name AS userName,
                u.account AS account,
                u.organ_affiliation AS deptId,
                u.is_disable AS userState,
                u.create_time AS createTime,
                ROW_NUMBER() OVER (ORDER BY u.organ_affiliation, u.user_name) AS rn
            FROM t_user u
            WHERE u.is_del = false
            AND NOT EXISTS (
                SELECT 1 FROM t_perm_user_role pur
                WHERE pur.user_id = u.id
                AND pur.role_id = #{roleId}
                AND pur.is_del = false
            )
            <if test="userName != null and userName != ''">
                AND u.user_name LIKE CONCAT('%', #{userName}, '%')
            </if>
            <if test="userState != null">
                AND u.is_disable = #{userState}
            </if>
        )
        SELECT
            dt.deptId,
            dt.deptName,
            dt.parentId,
            dt.level,
            uu.userId,
            uu.userName,
            uu.account,
            uu.userState,
            uu.createTime
        FROM dept_tree dt
        LEFT JOIN unauthorized_users uu ON uu.deptId = dt.deptId
        <if test="pageSize != null and pageSize > 0">
            AND uu.rn BETWEEN #{offset} + 1 AND #{offset} + #{pageSize}
        </if>
        ORDER BY dt.level, dt.deptId, uu.userName
    </select>

    <!-- 查询指定部门下的直属未授权用户（懒加载优化版本） -->
    <select id="findUnauthorizedUsersByDepartment" resultType="java.util.Map">
        SELECT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            u.is_disable AS userState,
            u.create_time AS createTime
        FROM t_user u
        WHERE u.organ_affiliation = #{departmentId}
        AND u.is_del = false
        AND NOT EXISTS (
            SELECT 1 FROM t_perm_user_role pur
            WHERE pur.user_id = u.id
            AND pur.role_id = #{roleId}
            AND pur.is_del = false
        )
        ORDER BY u.user_name
    </select>

    <!-- 按部门分页查询用户列表 -->
    <select id="findUserListByOrgPage" parameterType="com.dfit.percode.vo.UserListByOrgRequestVO"
            resultType="com.dfit.percode.vo.UserListByOrgItemVO">
        SELECT
            u.id AS userId,
            u.user_name AS userName,
            u.account AS account,
            u.user_name AS realName,
            u.organ_affiliation AS orgId,
            o.organ_name AS organName,
            u.is_disable AS isDisable,
            u.create_time AS createTime
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        WHERE u.is_del = false
        <if test="orgId != null">
            <choose>
                <when test="includeSubOrgs != null and includeSubOrgs">
                    <!-- 包含子部门：使用递归CTE查询所有子部门 -->
                    AND u.organ_affiliation IN (
                        WITH RECURSIVE org_tree AS (
                            -- 起始部门
                            SELECT id FROM t_org_structure WHERE id = #{orgId} AND is_del = false
                            UNION ALL
                            -- 递归查找子部门
                            SELECT os.id FROM t_org_structure os
                            INNER JOIN org_tree ot ON os.pre_id = ot.id
                            WHERE os.is_del = false
                        )
                        SELECT id FROM org_tree
                    )
                </when>
                <otherwise>
                    <!-- 只查询当前部门 -->
                    AND u.organ_affiliation = #{orgId}
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="status != null">
            AND u.is_disable = #{status}
        </if>
        ORDER BY u.create_time DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{pageNum}
        </if>
    </select>

    <!-- 按部门查询用户列表总数 -->
    <select id="countUserListByOrg" parameterType="com.dfit.percode.vo.UserListByOrgRequestVO"
            resultType="java.lang.Long">
        SELECT COUNT(u.id)
        FROM t_user u
        LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
        WHERE u.is_del = false
        <if test="orgId != null">
            <choose>
                <when test="includeSubOrgs != null and includeSubOrgs">
                    <!-- 包含子部门：使用递归CTE查询所有子部门 -->
                    AND u.organ_affiliation IN (
                        WITH RECURSIVE org_tree AS (
                            -- 起始部门
                            SELECT id FROM t_org_structure WHERE id = #{orgId} AND is_del = false
                            UNION ALL
                            -- 递归查找子部门
                            SELECT os.id FROM t_org_structure os
                            INNER JOIN org_tree ot ON os.pre_id = ot.id
                            WHERE os.is_del = false
                        )
                        SELECT id FROM org_tree
                    )
                </when>
                <otherwise>
                    <!-- 只查询当前部门 -->
                    AND u.organ_affiliation = #{orgId}
                </otherwise>
            </choose>
        </if>
        <if test="userName != null and userName != ''">
            AND u.user_name LIKE CONCAT('%', #{userName}, '%')
        </if>
        <if test="status != null">
            AND u.is_disable = #{status}
        </if>
    </select>

</mapper>
