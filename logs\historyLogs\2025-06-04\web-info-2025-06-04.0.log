2025-06-04 01:03:48.509 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 01:03:48.555 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 10:31:07.952 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 34508 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 10:31:07.954 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 10:31:10.407 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:31:10.413 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:31:10.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 10:31:10.446 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:31:10.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:31:10.455 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 10:31:10.470 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:31:10.472 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 10:31:10.502 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-04 10:31:10.526 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:31:10.528 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 10:31:10.546 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-04 10:31:12.168 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 10:31:12.272 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 10:31:12.273 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 10:31:12.273 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 10:31:12.586 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 10:31:12.587 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4510 ms
2025-06-04 10:31:12.720 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 10:31:12.836 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 10:31:13.068 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 10:31:14.488 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 10:31:14.654 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 10:31:15.047 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 10:31:15.482 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 10:31:18.000 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 10:31:18.019 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:31:20.922 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 10:31:26.412 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 10:31:26.452 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 10:31:26.983 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.156 seconds (JVM running for 22.928)
2025-06-04 10:32:08.995 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 10:32:08.996 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 10:32:08.998 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 10:34:50.006 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:34:50.036 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 10:35:02.514 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 6808 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 10:35:02.516 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 10:35:03.880 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:35:03.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:35:03.908 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 10:35:03.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:35:03.915 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:35:03.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 10:35:03.931 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:35:03.932 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 10:35:03.944 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-04 10:35:03.962 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:35:03.965 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 10:35:03.981 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-04 10:35:04.860 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 10:35:04.874 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 10:35:04.875 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 10:35:04.875 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 10:35:05.087 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 10:35:05.088 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2487 ms
2025-06-04 10:35:05.141 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 10:35:05.190 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 10:35:05.401 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 10:35:06.240 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 10:35:06.306 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 10:35:06.508 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 10:35:06.668 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 10:35:07.765 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 10:35:07.786 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:35:09.130 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 10:35:11.628 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 10:35:11.647 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 10:35:12.359 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.978 seconds (JVM running for 12.752)
2025-06-04 10:35:23.517 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 10:35:23.517 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 10:35:23.518 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-04 10:49:51.425 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:49:51.476 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 10:50:06.419 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 34676 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 10:50:06.423 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 10:50:08.932 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:50:08.939 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:50:09.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 10:50:09.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:50:09.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:50:09.036 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 10:50:09.059 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:50:09.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 10:50:09.091 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-04 10:50:09.126 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:50:09.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 10:50:09.219 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-04 10:50:11.131 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 10:50:11.152 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 10:50:11.153 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 10:50:11.153 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 10:50:11.458 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 10:50:11.458 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4901 ms
2025-06-04 10:50:11.559 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 10:50:11.692 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 10:50:11.979 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 10:50:13.109 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 10:50:13.238 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 10:50:13.918 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 10:50:14.440 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 10:50:16.172 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 10:50:16.206 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:50:18.396 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 10:50:22.518 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 10:50:22.558 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 10:50:23.191 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.776 seconds (JVM running for 21.859)
2025-06-04 10:51:16.173 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 10:51:16.174 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 10:51:16.177 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-04 10:58:59.393 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:58:59.408 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 10:59:14.739 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 39512 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 10:59:14.742 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 10:59:17.435 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:59:17.449 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:59:17.511 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 10:59:17.522 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:59:17.525 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 10:59:17.543 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 10:59:17.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:59:17.573 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 10:59:17.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-04 10:59:17.653 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 10:59:17.657 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 10:59:17.703 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-04 10:59:19.922 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 10:59:19.945 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 10:59:19.947 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 10:59:19.948 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 10:59:20.479 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 10:59:20.479 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5589 ms
2025-06-04 10:59:20.582 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 10:59:20.738 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 10:59:21.303 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 10:59:22.282 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 10:59:22.392 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 10:59:22.728 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 10:59:23.023 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 10:59:24.213 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 10:59:24.228 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 10:59:26.024 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 10:59:30.142 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 10:59:30.195 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 10:59:30.959 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.836 seconds (JVM running for 21.891)
2025-06-04 10:59:43.047 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 10:59:43.049 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 10:59:43.055 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-04 11:07:16.134 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:07:16.150 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 11:17:16.701 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 14128 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 11:17:16.708 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 11:17:20.205 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:17:20.213 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:17:20.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 11:17:20.262 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:17:20.265 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:17:20.286 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 11:17:20.315 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:17:20.316 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 11:17:20.341 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-04 11:17:20.374 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:17:20.376 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 11:17:20.405 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-04 11:17:22.851 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 11:17:22.875 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 11:17:22.877 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 11:17:22.879 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 11:17:23.629 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 11:17:23.629 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6742 ms
2025-06-04 11:17:23.770 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 11:17:23.887 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 11:17:24.162 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 11:17:25.148 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 11:17:25.263 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 11:17:25.568 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 11:17:25.860 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 11:17:27.312 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 11:17:27.335 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:17:29.120 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 11:17:33.221 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 11:17:33.257 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 11:17:34.058 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.425 seconds (JVM running for 22.558)
2025-06-04 11:17:54.311 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 11:17:54.312 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 11:17:54.315 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-04 11:20:25.671 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:20:25.693 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 11:20:51.602 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 15664 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 11:20:51.606 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 11:20:56.383 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:20:56.395 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:20:56.467 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 11:20:56.499 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:20:56.506 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:20:56.569 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 62 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 11:20:56.630 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:20:56.634 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 11:20:56.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 99 ms. Found 0 JPA repository interfaces.
2025-06-04 11:20:56.965 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:20:56.984 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 11:20:57.128 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 59 ms. Found 0 Redis repository interfaces.
2025-06-04 11:21:01.997 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 11:21:02.034 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 11:21:02.035 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 11:21:02.036 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 11:21:02.346 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 11:21:02.346 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 10558 ms
2025-06-04 11:21:02.463 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 11:21:02.656 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 11:21:03.072 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 11:21:04.384 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 11:21:04.653 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 11:21:05.165 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 11:21:05.788 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 11:21:07.701 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 11:21:07.741 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:21:11.739 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 11:21:16.693 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 11:21:16.726 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 11:21:17.998 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 28.103 seconds (JVM running for 31.891)
2025-06-04 11:21:32.858 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 11:21:32.859 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 11:21:32.864 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-04 11:32:59.301 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:32:59.306 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 11:33:11.885 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12496 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 11:33:11.888 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 11:33:13.485 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:33:13.488 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:33:13.515 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 11:33:13.520 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:33:13.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:33:13.531 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 11:33:13.546 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:33:13.547 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 11:33:13.559 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 11:33:13.584 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:33:13.586 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 11:33:13.603 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-04 11:33:15.361 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 11:33:15.374 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 11:33:15.376 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 11:33:15.377 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 11:33:15.701 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 11:33:15.702 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3702 ms
2025-06-04 11:33:15.821 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 11:33:15.901 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 11:33:16.219 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 11:33:17.070 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 11:33:17.133 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 11:33:17.387 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 11:33:17.668 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 11:33:18.500 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 11:33:18.513 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:33:20.108 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 11:33:22.656 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 11:33:22.672 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 11:33:23.050 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.221 seconds (JVM running for 14.736)
2025-06-04 11:34:02.951 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 11:34:02.952 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 11:34:02.953 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-04 11:58:15.999 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:58:16.059 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 11:58:25.770 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 23572 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 11:58:25.773 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 11:58:27.094 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:58:27.098 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:58:27.117 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 11:58:27.122 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:58:27.122 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 11:58:27.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 11:58:27.140 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:58:27.141 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 11:58:27.153 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 11:58:27.166 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 11:58:27.168 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 11:58:27.184 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-04 11:58:28.228 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 11:58:28.246 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 11:58:28.248 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 11:58:28.249 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 11:58:28.481 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 11:58:28.481 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2640 ms
2025-06-04 11:58:28.524 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 11:58:28.591 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 11:58:28.750 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 11:58:30.157 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 11:58:30.553 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 11:58:30.886 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 11:58:31.230 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 11:58:32.240 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 11:58:32.251 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 11:58:33.333 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 11:58:35.703 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 11:58:35.717 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 11:58:36.100 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.316 seconds (JVM running for 13.035)
2025-06-04 12:00:28.467 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 12:00:28.467 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 12:00:28.469 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 12:08:51.813 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 12:08:51.821 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 12:09:00.993 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 18368 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 12:09:00.994 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 12:09:02.298 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 12:09:02.302 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 12:09:02.341 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 12:09:02.351 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 12:09:02.352 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 12:09:02.365 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 12:09:02.384 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 12:09:02.386 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 12:09:02.406 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-04 12:09:02.427 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 12:09:02.428 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 12:09:02.444 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-06-04 12:09:03.301 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 12:09:03.313 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 12:09:03.314 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 12:09:03.315 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 12:09:03.517 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 12:09:03.518 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2463 ms
2025-06-04 12:09:03.575 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 12:09:03.623 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 12:09:03.764 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 12:09:04.639 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 12:09:04.717 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 12:09:04.928 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 12:09:05.104 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 12:09:06.133 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 12:09:06.153 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 12:09:07.456 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 12:09:09.820 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 12:09:09.848 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 12:09:10.299 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.184 seconds (JVM running for 11.565)
2025-06-04 12:09:15.288 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 12:09:15.289 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 12:09:15.290 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-04 14:19:09.777 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:19:09.860 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 14:19:23.048 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 31404 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 14:19:23.050 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 14:19:26.266 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:19:26.269 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:19:26.307 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 14:19:26.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:19:26.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:19:26.322 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 14:19:26.340 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:19:26.342 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 14:19:26.356 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-04 14:19:26.381 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:19:26.383 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:19:26.405 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-04 14:19:28.250 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 14:19:28.292 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 14:19:28.293 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 14:19:28.294 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 14:19:28.705 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 14:19:28.706 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5572 ms
2025-06-04 14:19:28.781 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 14:19:28.859 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 14:19:29.075 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 14:19:30.051 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 14:19:30.280 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 14:19:30.731 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 14:19:31.115 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 14:19:32.326 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 14:19:32.360 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:19:34.023 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 14:19:36.885 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 14:19:36.904 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 14:19:37.499 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.629 seconds (JVM running for 17.866)
2025-06-04 14:20:18.970 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 14:20:18.970 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 14:20:18.971 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-04 14:30:57.726 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:30:57.750 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 14:31:08.829 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 37108 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 14:31:08.831 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 14:31:10.172 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:31:10.176 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:31:10.208 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 14:31:10.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:31:10.215 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:31:10.224 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 14:31:10.233 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:31:10.235 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 14:31:10.250 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-04 14:31:10.272 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:31:10.274 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:31:10.298 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-04 14:31:11.637 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 14:31:11.657 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 14:31:11.658 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 14:31:11.659 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 14:31:12.006 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 14:31:12.007 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3101 ms
2025-06-04 14:31:12.062 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 14:31:12.113 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 14:31:12.303 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 14:31:13.096 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 14:31:13.175 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 14:31:13.411 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 14:31:13.562 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 14:31:14.235 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 14:31:14.247 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:31:15.177 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 14:31:17.562 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 14:31:17.581 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 14:31:17.985 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.056 seconds (JVM running for 12.135)
2025-06-04 14:35:36.086 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:35:36.090 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 14:35:43.524 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27464 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 14:35:43.525 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 14:35:44.887 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:35:44.892 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:35:44.910 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 14:35:44.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:35:44.915 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:35:44.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 14:35:44.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:35:44.942 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 14:35:44.953 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 14:35:44.968 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:35:44.971 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:35:44.988 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 Redis repository interfaces.
2025-06-04 14:35:45.909 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 14:35:45.922 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 14:35:45.923 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 14:35:45.924 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 14:35:46.203 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 14:35:46.203 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2606 ms
2025-06-04 14:35:46.278 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 14:35:46.341 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 14:35:46.503 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 14:35:47.377 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 14:35:47.438 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 14:35:47.646 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 14:35:47.806 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 14:35:48.751 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 14:35:48.771 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:35:50.208 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 14:35:52.438 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 14:35:52.477 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 14:35:52.840 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.2 seconds (JVM running for 11.608)
2025-06-04 14:36:14.262 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 14:36:14.263 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 14:36:14.265 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 14:46:26.595 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:46:26.602 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 14:46:34.909 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 22604 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 14:46:34.911 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 14:46:36.224 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:46:36.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:46:36.265 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 14:46:36.270 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:46:36.271 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 14:46:36.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 14:46:36.294 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:46:36.295 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 14:46:36.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 14:46:36.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 14:46:36.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 14:46:36.357 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-04 14:46:37.196 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 14:46:37.205 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 14:46:37.206 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 14:46:37.207 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 14:46:37.388 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 14:46:37.389 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2385 ms
2025-06-04 14:46:37.441 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 14:46:37.491 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 14:46:37.620 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 14:46:38.457 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 14:46:38.525 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 14:46:38.705 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 14:46:38.842 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 14:46:39.591 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 14:46:39.604 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 14:46:40.706 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 14:46:42.633 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 14:46:42.651 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 14:46:43.025 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 8.858 seconds (JVM running for 10.24)
2025-06-04 14:46:54.199 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 14:46:54.199 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 14:46:54.201 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 15:20:22.394 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 15:20:22.400 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 15:20:31.818 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 26588 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 15:20:31.820 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 15:20:33.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 15:20:33.158 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 15:20:33.190 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 15:20:33.197 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 15:20:33.198 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 15:20:33.208 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 15:20:33.223 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 15:20:33.225 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 15:20:33.238 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 15:20:33.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 15:20:33.262 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 15:20:33.284 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-04 15:20:34.200 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 15:20:34.213 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 15:20:34.216 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 15:20:34.216 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 15:20:34.413 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 15:20:34.413 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2519 ms
2025-06-04 15:20:34.470 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 15:20:34.510 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 15:20:34.652 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 15:20:35.449 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 15:20:35.528 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 15:20:35.760 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 15:20:35.949 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 15:20:36.846 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 15:20:36.864 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 15:20:37.791 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 15:20:39.654 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 15:20:39.668 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 15:20:40.039 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.114 seconds (JVM running for 10.386)
2025-06-04 15:21:08.978 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 15:21:08.978 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 15:21:08.981 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-04 15:21:17.434 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-04 15:21:17.435 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-04 15:21:17.603 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 1
2025-06-04 15:21:17.722 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 1
2025-06-04 15:21:17.723 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-04 15:22:06.130 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-04 15:22:06.130 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 2
2025-06-04 15:22:06.196 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 2
2025-06-04 15:22:06.377 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 4
2025-06-04 15:22:06.377 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-04 16:25:49.588 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 16:25:49.608 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 16:25:59.426 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27664 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 16:25:59.428 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 16:26:00.663 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 16:26:00.667 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 16:26:00.690 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 16:26:00.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 16:26:00.697 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 16:26:00.707 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 16:26:00.718 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 16:26:00.719 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 16:26:00.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-04 16:26:00.752 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 16:26:00.753 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 16:26:00.773 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-04 16:26:01.837 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 16:26:01.848 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 16:26:01.850 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 16:26:01.852 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 16:26:02.055 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 16:26:02.055 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2545 ms
2025-06-04 16:26:02.108 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 16:26:02.159 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 16:26:02.324 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 16:26:03.110 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 16:26:03.175 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 16:26:03.366 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 16:26:03.508 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 16:26:04.152 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 16:26:04.164 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 16:26:05.136 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 16:26:07.085 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 16:26:07.100 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 16:26:07.526 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 8.911 seconds (JVM running for 10.357)
2025-06-04 16:26:16.035 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 16:26:16.036 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 16:26:16.039 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-04 17:25:47.442 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 17:25:47.455 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 17:25:55.646 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 28524 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 17:25:55.649 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 17:25:56.956 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 17:25:56.959 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 17:25:56.982 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 17:25:56.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 17:25:56.988 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 17:25:56.995 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 17:25:57.004 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 17:25:57.006 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 17:25:57.017 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-04 17:25:57.032 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 17:25:57.033 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 17:25:57.050 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-04 17:25:57.975 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 17:25:57.986 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 17:25:57.986 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 17:25:57.987 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 17:25:58.217 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 17:25:58.217 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2494 ms
2025-06-04 17:25:58.293 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 17:25:58.344 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 17:25:58.488 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 17:25:59.289 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 17:25:59.371 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 17:25:59.692 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 17:25:59.986 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 17:26:00.774 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 17:26:00.792 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 17:26:01.733 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 17:26:03.854 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 17:26:03.870 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 17:26:04.455 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.738 seconds (JVM running for 11.37)
2025-06-04 17:26:25.858 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 17:26:25.858 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 17:26:25.861 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 17:26:25.986 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色选项列表
2025-06-04 17:26:26.179 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取到角色选项数量: 2
2025-06-04 23:23:46.318 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 23:23:46.348 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-04 23:23:57.575 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 25844 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-04 23:23:57.577 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-04 23:23:59.312 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 23:23:59.318 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-04 23:23:59.348 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-04 23:23:59.356 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 23:23:59.357 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-04 23:23:59.369 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-04 23:23:59.383 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 23:23:59.385 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-04 23:23:59.405 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-04 23:23:59.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-04 23:23:59.427 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-04 23:23:59.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-04 23:24:01.173 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-04 23:24:01.185 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-04 23:24:01.186 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-04 23:24:01.186 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-04 23:24:01.514 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-04 23:24:01.514 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3861 ms
2025-06-04 23:24:01.592 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-04 23:24:01.661 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-04 23:24:01.948 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-04 23:24:02.794 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-04 23:24:02.864 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-04 23:24:03.107 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-04 23:24:03.290 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-04 23:24:04.014 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-04 23:24:04.025 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-04 23:24:05.136 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-04 23:24:07.449 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-04 23:24:07.478 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-04 23:24:07.904 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.559 seconds (JVM running for 13.09)
2025-06-04 23:24:22.645 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-04 23:24:22.646 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-04 23:24:22.648 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-04 23:28:08.997 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-04 23:28:08.997 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 2001, 页码: 1, 页大小: 10
2025-06-04 23:28:09.327 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 10
2025-06-04 23:28:09.392 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 12
2025-06-04 23:28:09.393 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
