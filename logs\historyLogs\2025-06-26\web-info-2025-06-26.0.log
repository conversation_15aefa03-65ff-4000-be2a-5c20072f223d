2025-06-26 10:10:18.755 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 5480 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-26 10:10:18.770 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-26 10:10:18.793 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 10:10:21.602 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:10:21.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-26 10:10:21.635 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-26 10:10:21.641 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:10:21.642 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-26 10:10:21.659 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-26 10:10:21.670 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:10:21.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 10:10:21.703 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-26 10:10:21.730 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:10:21.732 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:10:21.762 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-26 10:10:23.700 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-26 10:10:23.717 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-26 10:10:23.719 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:10:23.720 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 10:10:24.207 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:10:24.207 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5276 ms
2025-06-26 10:10:24.363 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-26 10:10:24.628 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:10:25.636 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 10:10:25.791 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 10:10:26.208 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 10:10:26.666 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-26 10:10:27.215 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 10:10:27.236 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 10:10:30.425 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 10:10:34.063 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-26 10:10:34.085 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-26 10:10:35.595 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.171 seconds (JVM running for 22.119)
2025-06-26 10:14:01.923 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:14:01.923 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:14:01.930 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-06-26 10:14:02.836 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-26 10:14:06.854 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 4016ms
2025-06-26 10:16:22.283 [http-nio-8285-exec-5] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-06-26 10:16:22.306 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: test
2025-06-26 10:16:22.307 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 当前Token值: Bearer eyJ0eXAiOiJKV...
2025-06-26 10:16:22.307 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - Token是否有效: false
2025-06-26 10:16:22.307 [http-nio-8285-exec-5] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：用户未登录或Token已过期，异常类型: NotLoginException, 异常消息: token 无效：Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE5MzY2NDAzNjc2MTcyNDkyODAsInJuU3RyIjoid3FPdUhFTUpxUHdDZkdNdzR6ZHRDdWQ5N1J2Y1JURHgifQ.4kcpLuahIU1bvQ3Ob0fL0yb0bJQt_RnTb0exMlKziDc
2025-06-26 10:21:30.311 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 10:21:30.470 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-26 10:21:51.507 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 18156 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-26 10:21:51.509 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-26 10:21:51.515 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-26 10:21:54.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:21:54.119 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-26 10:21:54.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces.
2025-06-26 10:21:54.203 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:21:54.204 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-26 10:21:54.226 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-26 10:21:54.241 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:21:54.242 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-26 10:21:54.277 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
2025-06-26 10:21:54.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-26 10:21:54.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-26 10:21:54.356 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-26 10:21:55.836 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-26 10:21:55.868 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-26 10:21:55.868 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-26 10:21:55.869 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-26 10:21:56.310 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-26 10:21:56.311 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4721 ms
2025-06-26 10:21:56.436 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-26 10:21:56.690 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-26 10:21:57.613 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-26 10:21:57.718 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-26 10:21:57.995 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-26 10:21:58.274 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-26 10:21:58.842 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-26 10:21:58.904 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-26 10:22:02.965 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-26 10:22:05.854 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-26 10:22:05.873 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-26 10:22:07.243 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.634 seconds (JVM running for 20.338)
2025-06-26 10:22:21.070 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-26 10:22:21.070 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-26 10:22:21.077 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-06-26 10:22:21.726 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-26 10:22:25.340 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功，账号: 123, 用户ID: 1936640367617249280, 耗时: 3611ms
2025-06-26 10:22:54.757 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-06-26 10:22:54.770 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: test
2025-06-26 10:22:54.771 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 当前Token值: eyJ0eXAiOiJKV1QiLCJh...
2025-06-26 10:22:54.772 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - Token是否有效: true
2025-06-26 10:22:54.773 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: test
2025-06-26 10:22:54.843 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-06-26 10:22:54.972 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有菜单权限数量: 4
2025-06-26 10:22:54.972 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937827951659847680, name=test, preId=0, menuType=1, routeAddress=/demo/test1
2025-06-26 10:22:54.972 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937828169650409472, name=test1, preId=1937827951659847680, menuType=2, routeAddress=/demo/test1
2025-06-26 10:22:54.972 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937828280837214208, name=test2, preId=1937827951659847680, menuType=2, routeAddress=/demo/test2
2025-06-26 10:22:54.973 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 查询到菜单: id=1937829398061387776, name=btn1, preId=1937828169650409472, menuType=3, routeAddress=
2025-06-26 10:22:54.973 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 3, 按钮权限数量: 1
2025-06-26 10:22:54.974 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 1, 按钮数量: 1, 耗时: 201ms
2025-06-26 10:22:54.974 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功，用户ID: 1936640367617249280, 菜单数量: 1, 按钮数量: 1, 耗时: 203ms
