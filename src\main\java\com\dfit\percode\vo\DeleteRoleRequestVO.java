package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除角色请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteRoleRequestVO", description = "删除角色请求参数")
public class DeleteRoleRequestVO {
    
    @ApiModelProperty(value = "角色ID", required = true, example = "1930806593885179907")
    @NotBlank(message = "角色ID不能为空")
    private String id;
}
