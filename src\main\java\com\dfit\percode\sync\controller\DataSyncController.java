package com.dfit.percode.sync.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.sync.service.DataSyncService;
import com.dfit.percode.sync.service.ExternalDataService;
import com.dfit.percode.sync.task.DataSyncScheduledTask;
import com.dfit.percode.sync.vo.IncrementalSyncRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 数据同步控制器
 * 提供数据同步相关的API接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@RequestMapping("/sync")
@Api(tags = "数据同步管理")
@Slf4j
public class DataSyncController {

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private ExternalDataService externalDataService;

    @Autowired
    private DataSyncScheduledTask dataSyncScheduledTask;

    /**
     * 执行完整数据同步
     * 同步指定时间范围内的所有部门和员工数据
     *
     * @param startDate 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDate 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 同步结果
     */
    @PostMapping("/full")
    @ApiOperation(value = "执行完整数据同步", notes = "从外部系统同步指定时间范围内的所有部门和员工数据到当前系统")
    public BaseResult performFullSync(
            @RequestParam(value = "startDate", required = false)
            @ApiParam(value = "开始时间", example = "2024-01-01 00:00:00")
            String startDate,
            @RequestParam(value = "endDate", required = false)
            @ApiParam(value = "结束时间", example = "2024-01-02 00:00:00")
            String endDate) {
        BaseResult result = new BaseResult();

        try {
            log.info("开始执行完整数据同步，时间范围: {} - {}", startDate, endDate);

            if (startDate != null && endDate != null) {
                // 使用指定时间范围同步
                dataSyncService.performFullSync(startDate, endDate);
            } else {
                // 使用默认时间范围同步（最近一天）
                dataSyncService.performFullSync();
            }

            result.setCode(200);
            result.setMessage("完整数据同步执行成功");
            result.setData("同步完成，时间范围: " + startDate + " - " + endDate);

            log.info("完整数据同步执行成功");
        } catch (Exception e) {
            log.error("完整数据同步执行失败", e);
            result.setCode(500);
            result.setMessage("数据同步失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 仅同步部门数据
     * 支持指定时间范围同步
     *
     * @param startDate 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDate 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 同步结果
     */
    @PostMapping("/departments")
    @ApiOperation(value = "同步部门数据", notes = "从外部系统同步指定时间范围内的部门数据到当前系统")
    public BaseResult syncDepartments(
            @RequestParam(value = "startDate", required = false)
            @ApiParam(value = "开始时间", example = "2024-01-01 00:00:00")
            String startDate,
            @RequestParam(value = "endDate", required = false)
            @ApiParam(value = "结束时间", example = "2024-01-02 00:00:00")
            String endDate) {
        BaseResult result = new BaseResult();

        try {
            log.info("开始同步部门数据，时间范围: {} - {}", startDate, endDate);

            if (startDate != null && endDate != null) {
                // 使用指定时间范围同步
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime start = LocalDateTime.parse(startDate, formatter);
                LocalDateTime end = LocalDateTime.parse(endDate, formatter);
                dataSyncService.syncDepartments(start, end);
            } else {
                // 使用默认时间范围同步（最近一天）
                dataSyncService.syncDepartments();
            }

            result.setCode(200);
            result.setMessage("部门数据同步成功");
            result.setData("同步完成，时间范围: " + startDate + " - " + endDate);

            log.info("部门数据同步成功");
        } catch (Exception e) {
            log.error("部门数据同步失败", e);
            result.setCode(500);
            result.setMessage("部门数据同步失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 仅同步员工数据
     * 支持指定时间范围同步
     *
     * @param startDate 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endDate 结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 同步结果
     */
    @PostMapping("/employees")
    @ApiOperation(value = "同步员工数据", notes = "从外部系统同步指定时间范围内的员工数据到当前系统（包含扩展数据）")
    public BaseResult syncEmployees(
            @RequestParam(value = "startDate", required = false)
            @ApiParam(value = "开始时间", example = "2024-01-01 00:00:00")
            String startDate,
            @RequestParam(value = "endDate", required = false)
            @ApiParam(value = "结束时间", example = "2024-01-02 00:00:00")
            String endDate) {
        BaseResult result = new BaseResult();

        try {
            log.info("开始同步员工数据（包含扩展数据），时间范围: {} - {}", startDate, endDate);

            if (startDate != null && endDate != null) {
                // 使用指定时间范围同步
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                LocalDateTime start = LocalDateTime.parse(startDate, formatter);
                LocalDateTime end = LocalDateTime.parse(endDate, formatter);

                // 同步员工数据（包含扩展数据，一次性完成）
                dataSyncService.syncEmployees(start, end);

                // 根据主岗位更新员工的部门归属
                dataSyncService.updateEmployeeDepartmentAffiliation();
            } else {
                // 使用默认时间范围同步（最近一天）

                // 同步员工数据（包含扩展数据，一次性完成）
                dataSyncService.syncEmployees();

                // 根据主岗位更新员工的部门归属
                dataSyncService.updateEmployeeDepartmentAffiliation();
            }

            result.setCode(200);
            result.setMessage("员工数据同步成功（包含扩展数据）");
            result.setData("同步完成，时间范围: " + startDate + " - " + endDate);

            log.info("员工数据同步成功（包含扩展数据）");
        } catch (Exception e) {
            log.error("员工数据同步失败", e);
            result.setCode(500);
            result.setMessage("员工数据同步失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 测试外部系统连接
     *
     * @return 连接测试结果
     */
    @GetMapping("/test-connection")
    @ApiOperation(value = "测试外部系统连接", notes = "测试与外部同步系统的网络连接")
    public BaseResult testConnection() {
        BaseResult result = new BaseResult();
        
        try {
            boolean isConnected = externalDataService.testConnection();
            
            if (isConnected) {
                result.setCode(200);
                result.setMessage("外部系统连接正常");
                result.setData("连接成功");
            } else {
                result.setCode(500);
                result.setMessage("外部系统连接失败");
                result.setData("连接失败");
            }
        } catch (Exception e) {
            log.error("连接测试失败", e);
            result.setCode(500);
            result.setMessage("连接测试异常: " + e.getMessage());
            result.setData(null);
        }
        
        return result;
    }

    /**
     * 清理重复的员工扩展数据
     *
     * @return 清理结果
     */
    @PostMapping("/cleanDuplicateData")
    @ApiOperation(value = "清理重复数据", notes = "清理员工岗位、职称、系统标识等扩展数据中的重复记录")
    public BaseResult cleanDuplicateData() {
        BaseResult result = new BaseResult();

        try {
            log.info("开始清理重复的员工扩展数据");
            dataSyncService.cleanDuplicateEmployeeData();

            result.setCode(200);
            result.setMessage("重复数据清理成功");
            result.setData("清理完成");

            log.info("重复数据清理成功");
        } catch (Exception e) {
            log.error("重复数据清理失败", e);
            result.setCode(500);
            result.setMessage("重复数据清理失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 执行增量数据同步
     *
     * @param request 增量同步请求参数
     * @return 同步结果
     */
    @PostMapping("/incremental")
    @ApiOperation(value = "执行增量数据同步", notes = "同步指定时间范围内的变更数据")
    public BaseResult performIncrementalSync(@RequestBody IncrementalSyncRequest request) {
        BaseResult result = new BaseResult();

        try {
            log.info("开始执行增量数据同步，时间范围: {} - {}", request.getStartDate(), request.getEndDate());
            dataSyncService.performIncrementalSync(request.getStartDate(), request.getEndDate());

            result.setCode(200);
            result.setMessage("增量数据同步成功");
            result.setData("同步完成");

            log.info("增量数据同步成功");
        } catch (Exception e) {
            log.error("增量数据同步失败", e);
            result.setCode(500);
            result.setMessage("增量数据同步失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 更新员工部门归属
     *
     * @return 更新结果
     */
    @PostMapping("/updateEmployeeDepartmentAffiliation")
    @ApiOperation(value = "更新员工部门归属", notes = "根据员工主岗位信息更新用户表中的部门归属字段")
    public BaseResult updateEmployeeDepartmentAffiliation() {
        BaseResult result = new BaseResult();

        try {
            log.info("开始更新员工部门归属");
            dataSyncService.updateEmployeeDepartmentAffiliation();

            result.setCode(200);
            result.setMessage("员工部门归属更新成功");
            result.setData("更新完成");

            log.info("员工部门归属更新成功");
        } catch (Exception e) {
            log.error("员工部门归属更新失败", e);
            result.setCode(500);
            result.setMessage("员工部门归属更新失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

    /**
     * 获取同步状态信息
     *
     * @return 同步状态
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取同步状态", notes = "获取当前数据同步的状态信息")
    public BaseResult getSyncStatus() {
        BaseResult result = new BaseResult();

        try {
            // TODO: 实现同步状态查询逻辑
            // 可以查询同步日志表，返回最近的同步记录

            result.setCode(200);
            result.setMessage("获取同步状态成功");
            result.setData("状态查询功能待实现");

        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            result.setCode(500);
            result.setMessage("获取同步状态失败: " + e.getMessage());
            result.setData(null);
        }

        return result;
    }

//    /**
//     * 手动触发增量同步（指定天数）
//     *
//     * @param days 同步最近几天的数据
//     * @return 同步结果
//     */
//    @PostMapping("/manual-incremental/{days}")
//    @ApiOperation(value = "手动触发增量同步", notes = "手动触发指定天数的增量数据同步")
//    public BaseResult manualIncrementalSync(@PathVariable("days") int days) {
//        BaseResult result = new BaseResult();
//
//        try {
//            log.info("开始手动触发增量同步，天数: {}", days);
//            dataSyncScheduledTask.manualIncrementalSync(days);
//
//            result.setCode(200);
//            result.setMessage("手动增量同步成功");
//            result.setData("同步完成，天数: " + days);
//
//            log.info("手动增量同步成功");
//        } catch (Exception e) {
//            log.error("手动增量同步失败", e);
//            result.setCode(500);
//            result.setMessage("手动增量同步失败: " + e.getMessage());
//            result.setData(null);
//        }
//
//        return result;
//    }

//    /**
//     * 获取定时任务状态
//     *
//     * @return 定时任务状态信息
//     */
//    @GetMapping("/task-status")
//    @ApiOperation(value = "获取定时任务状态", notes = "获取数据同步定时任务的状态信息")
//    public BaseResult getTaskStatus() {
//        BaseResult result = new BaseResult();
//
//        try {
//            String taskStatus = dataSyncScheduledTask.getTaskStatus();
//
//            result.setCode(200);
//            result.setMessage("获取定时任务状态成功");
//            result.setData(taskStatus);
//
//        } catch (Exception e) {
//            log.error("获取定时任务状态失败", e);
//            result.setCode(500);
//            result.setMessage("获取定时任务状态失败: " + e.getMessage());
//            result.setData(null);
//        }
//
//        return result;
//    }
}
