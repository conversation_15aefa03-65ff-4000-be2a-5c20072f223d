package com.dfit.percode.mapper;

import com.dfit.percode.entity.TRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dfit.percode.userPerm.TRoleEntity;
import com.dfit.percode.vo.RoleListRequestVO;
import com.dfit.percode.vo.RoleListItemVO;
import com.dfit.percode.vo.RoleDetailResponseVO;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 角色信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface TRoleMapper extends BaseMapper<TRole> {

    @Insert("INSERT INTO t_role (id, role_name, order_info, is_disable, is_del, create_time, modify_time) " +
            "VALUES(#{id}, #{roleName}, #{orderInfo}, #{isDisable}, #{isDel}, #{createTime}, #{modifyTime}) ")
    void insertTRole(TRoleEntity tRoleEntity);

    @Insert("INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, " +
            "is_del, create_time, modify_time)" +
            " VALUES(#{id}, #{roleId}, #{moduleIdentifier}, #{menuId}, #{isDel}, #{createTime}, #{modifyTime}) ")
    void insertRoleMenuPerm(TRoleEntity tRoleEntity);

    /**
     * 分页查询角色列表
     * 支持角色名称模糊搜索、状态筛选、删除状态筛选
     *
     * @param request 查询请求参数
     * @return 角色列表
     */
    @Select("<script>" +
            "SELECT " +
            "    r.id, " +
            "    r.role_name AS roleName, " +
            "    r.order_info AS orderInfo, " +
            "    r.is_disable AS isDisable, " +
            "    CASE " +
            "        WHEN r.is_disable = true THEN '停用' " +
            "        ELSE '启用' " +
            "    END AS statusText, " +
            "    r.create_time AS createTime, " +
            "    r.modify_time AS modifyTime, " +
            "    COALESCE(user_counts.userCount, 0) AS userCount " +
            "FROM t_role r " +
            "LEFT JOIN ( " +
            "    SELECT " +
            "        role_id, " +
            "        COUNT(DISTINCT user_id) AS userCount " +
            "    FROM t_perm_user_role " +
            "    WHERE is_del = false " +
            "    GROUP BY role_id " +
            ") user_counts ON r.id = user_counts.role_id " +
            "WHERE 1=1 " +
            "<if test='includeDeleted == null or includeDeleted == false'>" +
            "  AND (r.is_del = false OR r.is_del IS NULL) " +
            "</if>" +
            "<if test='roleName != null and roleName != \"\"'>" +
            "  AND r.role_name ILIKE '%' || #{roleName} || '%' " +
            "</if>" +
            "<if test='isDisable != null'>" +
            "  AND r.is_disable = #{isDisable} " +
            "</if>" +
            "ORDER BY r.order_info ASC, r.id ASC " +
            "LIMIT #{pageSize} OFFSET #{currentPage}" +
            "</script>")
    List<RoleListItemVO> findRoleListPage(RoleListRequestVO request);

    /**
     * 查询角色列表总数
     * 用于分页计算
     *
     * @param request 查询请求参数
     * @return 总记录数
     */
    @Select("<script>" +
            "SELECT COUNT(1) " +
            "FROM t_role r " +
            "WHERE 1=1 " +
            "<if test='includeDeleted == null or includeDeleted == false'>" +
            "  AND (r.is_del = false OR r.is_del IS NULL) " +
            "</if>" +
            "<if test='roleName != null and roleName != \"\"'>" +
            "  AND r.role_name ILIKE '%' || #{roleName} || '%' " +
            "</if>" +
            "<if test='isDisable != null'>" +
            "  AND r.is_disable = #{isDisable} " +
            "</if>" +
            "</script>")
    Long countRoleList(RoleListRequestVO request);

    /**
     * 查询角色基本信息
     *
     * @param roleId 角色ID
     * @return 角色基本信息
     */
    @Select("SELECT " +
            "r.id as \"roleId\", " +
            "r.role_name as \"roleName\", " +
            "r.order_info as \"orderInfo\", " +
            "r.is_disable as \"isDisable\", " +
            "r.create_time as \"createTime\", " +
            "r.modify_time as \"modifyTime\" " +
            "FROM t_role r " +
            "WHERE r.id = #{roleId} AND (r.is_del = false OR r.is_del IS NULL)")
    RoleDetailResponseVO.RoleInfo getRoleBasicInfo(@Param("roleId") Long roleId);

    /**
     * 查询角色已关联的菜单权限ID列表
     *
     * @param roleId 角色ID
     * @return 菜单权限ID列表
     */
    @Select("SELECT menu_id " +
            "FROM t_roles_menu_permission " +
            "WHERE role_id = #{roleId} AND is_del = false " +
            "ORDER BY menu_id")
    List<Long> getRoleMenuPermissions(@Param("roleId") Long roleId);

    /**
     * 查询角色已关联的数据权限列表，包含权限ID和操作类型
     *
     * @param roleId 角色ID
     * @return 数据权限列表，包含权限ID和操作类型
     */
    @Select("SELECT DISTINCT data_id " +
            "FROM t_roles_data_permission " +
            "WHERE role_id = #{roleId} AND is_del = false " +
            "ORDER BY data_id")
    List<Long> getRoleDataPermissionIds(@Param("roleId") Long roleId);

    /**
     * 查询角色指定数据权限的操作类型列表
     *
     * @param roleId 角色ID
     * @param dataId 数据权限ID
     * @return 操作类型列表
     */
    @Select("SELECT operate_type " +
            "FROM t_roles_data_permission " +
            "WHERE role_id = #{roleId} AND data_id = #{dataId} AND is_del = false " +
            "ORDER BY operate_type")
    List<Integer> getRoleDataPermissionOperateTypes(@Param("roleId") Long roleId, @Param("dataId") Long dataId);
}
