package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 成员信息VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持多角色分配和新用户创建
 *
 * 使用方式：
 * - 新增用户：userId为null，提供userName、account、password、organAffiliation等信息
 * - 现有用户角色分配：userId不为null，主要用于角色关联
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MemberInfoVO", description = "成员信息")
public class MemberInfoVO {

    @ApiModelProperty("用户ID（新增用户时为null，现有用户角色分配时必填）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("账号（用于登录）")
    private String account;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("组织归属ID（对应组织架构表中的id）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organAffiliation;

    @ApiModelProperty("角色列表")
    private List<RoleInfoVO> roles;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean isDisable;
}
