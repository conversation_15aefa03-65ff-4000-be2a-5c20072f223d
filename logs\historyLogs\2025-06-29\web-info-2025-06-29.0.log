2025-06-29 19:15:02.570 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 39244 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-29 19:15:02.575 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-29 19:15:02.595 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-29 19:15:04.856 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:15:04.861 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-29 19:15:04.887 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-29 19:15:04.890 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:15:04.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-29 19:15:04.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-29 19:15:04.911 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:15:04.912 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-29 19:15:04.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-29 19:15:04.971 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:15:04.973 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 19:15:04.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-29 19:15:06.071 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-29 19:15:06.086 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-29 19:15:06.087 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 19:15:06.087 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 19:15:06.343 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 19:15:06.344 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3672 ms
2025-06-29 19:15:06.462 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-29 19:15:06.650 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-29 19:15:07.651 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-29 19:15:07.765 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-29 19:15:08.047 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-29 19:15:08.265 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-29 19:15:08.686 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-29 19:15:08.699 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-29 19:15:10.912 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-29 19:15:13.754 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-29 19:15:13.782 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-29 19:15:14.475 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.841 seconds (JVM running for 16.056)
2025-06-29 19:15:31.570 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 19:15:31.570 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 19:15:31.571 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-29 19:15:31.952 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-29 19:15:33.902 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: 123, 用户ID: 1936640367617249280, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 1948ms
2025-06-29 19:23:05.979 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-29 19:23:05.994 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-29 19:23:14.356 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 19468 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-29 19:23:14.358 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-29 19:23:14.364 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-29 19:23:15.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:23:15.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-29 19:23:15.466 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-29 19:23:15.470 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:23:15.471 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-29 19:23:15.481 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-29 19:23:15.488 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:23:15.488 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-29 19:23:15.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-29 19:23:15.519 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-29 19:23:15.520 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-29 19:23:15.539 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-29 19:23:16.144 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-29 19:23:16.152 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-29 19:23:16.153 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-29 19:23:16.153 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-29 19:23:16.352 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-29 19:23:16.352 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1952 ms
2025-06-29 19:23:16.438 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-29 19:23:16.551 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-29 19:23:17.409 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-29 19:23:17.457 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-29 19:23:17.576 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-29 19:23:17.698 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-29 19:23:18.008 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-29 19:23:18.018 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-29 19:23:19.783 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-29 19:23:21.162 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-29 19:23:21.173 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-29 19:23:21.696 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 8.038 seconds (JVM running for 9.905)
2025-06-29 19:23:32.942 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-29 19:23:32.942 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-29 19:23:32.943 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-29 19:23:33.254 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-06-29 19:23:34.746 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: 123, 用户ID: 1936640367617249280, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 1490ms
