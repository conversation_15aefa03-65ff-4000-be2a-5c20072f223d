@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 权限管理系统 - Windows测试环境部署脚本
REM 使用方法: deploy-test-windows.bat

echo ==========================================
echo 权限管理系统 - Windows测试环境部署
echo ==========================================

REM 配置变量
set APP_NAME=permission-system
set PROFILE=test
set JAR_NAME=permissionCode-0.0.1-SNAPSHOT.jar
set APP_PORT=8285
set JVM_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC

REM 检查Java环境
echo [INFO] 检查Java环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Java未安装或未配置到PATH
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr /i "version"') do (
    set JAVA_VERSION=%%i
    goto :java_found
)
:java_found
echo [INFO] Java版本: %JAVA_VERSION%

REM 检查JAR文件是否存在
if not exist "%JAR_NAME%" (
    echo [ERROR] JAR文件不存在: %JAR_NAME%
    echo [INFO] 请先编译项目: mvn clean package -DskipTests
    pause
    exit /b 1
)

REM 停止现有进程
echo [INFO] 检查并停止现有进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
    set PID=%%i
    set PID=!PID:"=!
    echo [WARN] 发现运行中的进程 PID: !PID!
    echo [INFO] 正在停止进程...
    taskkill /pid !PID! /f >nul 2>&1
    timeout /t 3 >nul
)

REM 检查端口占用
echo [INFO] 检查端口 %APP_PORT% 占用情况...
netstat -an | findstr ":%APP_PORT% " >nul
if not errorlevel 1 (
    echo [ERROR] 端口 %APP_PORT% 已被占用
    netstat -an | findstr ":%APP_PORT%"
    pause
    exit /b 1
) else (
    echo [INFO] 端口 %APP_PORT% 可用
)

REM 备份旧版本
if exist "%JAR_NAME%.old" (
    del "%JAR_NAME%.old" >nul 2>&1
)
if exist "%JAR_NAME%" (
    echo [INFO] 备份旧版本...
    copy "%JAR_NAME%" "%JAR_NAME%.old" >nul
)

REM 创建日志目录
if not exist "logs" (
    mkdir logs
)

REM 启动应用
echo [INFO] 启动应用...
start /b java %JVM_OPTS% ^
    -Dspring.profiles.active=%PROFILE% ^
    -Dfile.encoding=UTF-8 ^
    -Duser.timezone=Asia/Shanghai ^
    -jar %JAR_NAME% ^
    > logs\app.log 2>&1

REM 等待应用启动
echo [INFO] 等待应用启动...
set /a count=0
:check_loop
timeout /t 2 >nul
set /a count+=1

REM 检查应用是否启动（通过检查日志文件中的启动标识）
if exist "logs\app.log" (
    findstr /c:"Started Application" logs\app.log >nul 2>&1
    if not errorlevel 1 (
        echo [INFO] 应用启动成功！
        goto :success
    )
)

if %count% lss 30 (
    echo|set /p="."
    goto :check_loop
)

echo.
echo [ERROR] 应用启动失败或超时
echo [INFO] 请检查日志: type logs\app.log
pause
exit /b 1

:success
echo.
echo [INFO] ========== 部署状态 ==========
echo [INFO] 应用名称: %APP_NAME%
echo [INFO] 环境配置: %PROFILE%
echo [INFO] 端口: %APP_PORT%
echo [INFO] 日志目录: logs\
echo.
echo [INFO] 访问地址: http://localhost:%APP_PORT%
echo [INFO] Swagger文档: http://localhost:%APP_PORT%/swagger-ui.html
echo.
echo [INFO] 常用命令:
echo [INFO]   查看日志: type logs\app.log
echo [INFO]   查看进程: tasklist ^| findstr java
echo [INFO]   停止应用: 运行 stop-app.bat
echo.
echo [INFO] 部署完成！
pause
