2025-06-22 14:14:41.248 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.dfit.percode.mapper.UserMapper.findUsersByDepartmentId] is ignored, because it exists, maybe from xml file
2025-06-22 14:39:42.882 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.dfit.percode.mapper.UserMapper.findUsersByDepartmentId] is ignored, because it exists, maybe from xml file
2025-06-22 14:42:10.859 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.dfit.percode.mapper.UserMapper.findUsersByDepartmentId] is ignored, because it exists, maybe from xml file
2025-06-22 15:12:20.893 [main] ERROR com.baomidou.mybatisplus.core.MybatisConfiguration - mapper[com.dfit.percode.mapper.UserMapper.findUsersByDepartmentId] is ignored, because it exists, maybe from xml file
2025-06-22 15:16:00.766 [http-nio-8285-exec-6] ERROR c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单不存在，菜单ID: 1930307870503604200
2025-06-22 15:16:19.895 [http-nio-8285-exec-7] ERROR c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单不存在，菜单ID: 1935949462509850600
2025-06-22 15:16:46.511 [http-nio-8285-exec-10] ERROR c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单不存在，菜单ID: 1935949462509850600
