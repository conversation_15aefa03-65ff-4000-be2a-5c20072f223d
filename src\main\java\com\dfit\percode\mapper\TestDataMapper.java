package com.dfit.percode.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;

/**
 * 测试数据Mapper接口
 * 用于插入和清理测试数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface TestDataMapper {

    /**
     * 插入菜单模块测试数据
     */
    @Insert("INSERT INTO t_menu_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) VALUES " +
            "(1001, '系统管理', 'system_management', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(1002, '权限管理', 'permission_management', 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(1003, '业务管理', 'business_management', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(1004, '报表中心', 'report_center', 4, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(1005, '监控中心', 'monitor_center', 5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertMenuModules();

    /**
     * 插入系统管理模块菜单
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES " +
            "(2001, '系统管理', 0, 'system_management', 1, false, 1, '/system', '', 'system', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2002, '用户管理', 2001, 'system_management', 1, false, 2, '/system/user', '/views/system/user/index', 'system:user:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2003, '新增用户', 2002, 'system_management', 1, false, 3, '', '', 'system:user:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2004, '编辑用户', 2002, 'system_management', 2, false, 3, '', '', 'system:user:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2005, '删除用户', 2002, 'system_management', 3, false, 3, '', '', 'system:user:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2006, '重置密码', 2002, 'system_management', 4, false, 3, '', '', 'system:user:reset', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2007, '部门管理', 2001, 'system_management', 2, false, 2, '/system/dept', '/views/system/dept/index', 'system:dept:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2008, '新增部门', 2007, 'system_management', 1, false, 3, '', '', 'system:dept:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2009, '编辑部门', 2007, 'system_management', 2, false, 3, '', '', 'system:dept:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2010, '删除部门', 2007, 'system_management', 3, false, 3, '', '', 'system:dept:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2011, '系统配置', 2001, 'system_management', 3, false, 2, '/system/config', '/views/system/config/index', 'system:config:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(2012, '修改配置', 2011, 'system_management', 1, false, 3, '', '', 'system:config:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertSystemMenus();

    /**
     * 插入权限管理模块菜单
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES " +
            "(3001, '权限管理', 0, 'permission_management', 1, false, 1, '/permission', '', 'permission', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3002, '角色管理', 3001, 'permission_management', 1, false, 2, '/permission/role', '/views/permission/role/index', 'permission:role:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3003, '新增角色', 3002, 'permission_management', 1, false, 3, '', '', 'permission:role:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3004, '编辑角色', 3002, 'permission_management', 2, false, 3, '', '', 'permission:role:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3005, '删除角色', 3002, 'permission_management', 3, false, 3, '', '', 'permission:role:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3006, '分配权限', 3002, 'permission_management', 4, false, 3, '', '', 'permission:role:assign', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3007, '菜单管理', 3001, 'permission_management', 2, false, 2, '/permission/menu', '/views/permission/menu/index', 'permission:menu:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3008, '新增菜单', 3007, 'permission_management', 1, false, 3, '', '', 'permission:menu:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3009, '编辑菜单', 3007, 'permission_management', 2, false, 3, '', '', 'permission:menu:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3010, '删除菜单', 3007, 'permission_management', 3, false, 3, '', '', 'permission:menu:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3011, '数据权限', 3001, 'permission_management', 3, false, 2, '/permission/data', '/views/permission/data/index', 'permission:data:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(3012, '配置数据权限', 3011, 'permission_management', 1, false, 3, '', '', 'permission:data:config', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertPermissionMenus();

    /**
     * 插入业务管理模块菜单
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES " +
            "(4001, '业务管理', 0, 'business_management', 1, false, 1, '/business', '', 'business', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4002, '订单管理', 4001, 'business_management', 1, false, 2, '/business/order', '/views/business/order/index', 'business:order:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4003, '新增订单', 4002, 'business_management', 1, false, 3, '', '', 'business:order:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4004, '编辑订单', 4002, 'business_management', 2, false, 3, '', '', 'business:order:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4005, '删除订单', 4002, 'business_management', 3, false, 3, '', '', 'business:order:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4006, '审核订单', 4002, 'business_management', 4, false, 3, '', '', 'business:order:audit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4007, '客户管理', 4001, 'business_management', 2, false, 2, '/business/customer', '/views/business/customer/index', 'business:customer:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4008, '新增客户', 4007, 'business_management', 1, false, 3, '', '', 'business:customer:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4009, '编辑客户', 4007, 'business_management', 2, false, 3, '', '', 'business:customer:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4010, '删除客户', 4007, 'business_management', 3, false, 3, '', '', 'business:customer:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4011, '产品管理', 4001, 'business_management', 3, false, 2, '/business/product', '/views/business/product/index', 'business:product:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4012, '新增产品', 4011, 'business_management', 1, false, 3, '', '', 'business:product:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4013, '编辑产品', 4011, 'business_management', 2, false, 3, '', '', 'business:product:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4014, '删除产品', 4011, 'business_management', 3, false, 3, '', '', 'business:product:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4015, '产品上架', 4011, 'business_management', 4, false, 3, '', '', 'business:product:online', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(4016, '产品下架', 4011, 'business_management', 5, false, 3, '', '', 'business:product:offline', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertBusinessMenus();

    /**
     * 插入报表中心模块菜单
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES " +
            "(5001, '报表中心', 0, 'report_center', 1, false, 1, '/report', '', 'report', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5002, '销售报表', 5001, 'report_center', 1, false, 2, '/report/sales', '/views/report/sales/index', 'report:sales:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5003, '导出销售报表', 5002, 'report_center', 1, false, 3, '', '', 'report:sales:export', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5004, '财务报表', 5001, 'report_center', 2, false, 2, '/report/finance', '/views/report/finance/index', 'report:finance:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5005, '导出财务报表', 5004, 'report_center', 1, false, 3, '', '', 'report:finance:export', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5006, '用户统计', 5001, 'report_center', 3, false, 2, '/report/user', '/views/report/user/index', 'report:user:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(5007, '导出用户统计', 5006, 'report_center', 1, false, 3, '', '', 'report:user:export', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertReportMenus();

    /**
     * 插入监控中心模块菜单（包含禁用状态的菜单用于测试）
     */
    @Insert("INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES " +
            "(6001, '监控中心', 0, 'monitor_center', 1, false, 1, '/monitor', '', 'monitor', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6002, '系统监控', 6001, 'monitor_center', 1, false, 2, '/monitor/system', '/views/monitor/system/index', 'monitor:system:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6003, '日志监控', 6001, 'monitor_center', 2, false, 2, '/monitor/log', '/views/monitor/log/index', 'monitor:log:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6004, '清理日志', 6003, 'monitor_center', 1, false, 3, '', '', 'monitor:log:clean', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6005, '性能监控', 6001, 'monitor_center', 3, false, 2, '/monitor/performance', '/views/monitor/performance/index', 'monitor:performance:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6006, '在线用户', 6001, 'monitor_center', 4, true, 2, '/monitor/online', '/views/monitor/online/index', 'monitor:online:view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP), " +
            "(6007, '强制下线', 6006, 'monitor_center', 1, true, 3, '', '', 'monitor:online:offline', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertMonitorMenus();

    /**
     * 清理菜单权限测试数据
     */
    @Delete("DELETE FROM t_menu_permission WHERE id BETWEEN 2001 AND 6999")
    void cleanMenuPermissions();

    /**
     * 清理菜单模块测试数据
     */
    @Delete("DELETE FROM t_menu_module WHERE id BETWEEN 1001 AND 1999")
    void cleanMenuModules();
}
