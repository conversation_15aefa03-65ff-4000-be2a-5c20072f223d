package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色列表响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 包含分页信息和角色列表数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleListResponseVO", description = "角色列表响应数据")
public class RoleListResponseVO {
    
    @ApiModelProperty(value = "角色列表数据")
    private List<RoleListItemVO> records;
    
    @ApiModelProperty(value = "总记录数")
    private Long total;
    
    @ApiModelProperty(value = "当前页码")
    private Integer currentPage;
    
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;
    
    @ApiModelProperty(value = "总页数")
    private Integer totalPages;
}
