package com.dfit.orgsync.service;

import com.dfit.orgsync.dto.DepartmentSyncDto;
import com.dfit.orgsync.dto.OrganizationTreeNode;
import com.dfit.orgsync.dto.SyncResult;
import com.dfit.orgsync.mapper.OrganizationSyncMapper;
import com.dfit.orgsync.util.HierarchyParser;
import com.dfit.orgsync.util.SqlFileParser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 组织架构同步服务
 * 负责将department_sync_test.sql中的数据转换并同步到t_org_structure表
 */
@Service
@Transactional
@Slf4j
public class OrganizationSyncService {
    
    @Autowired
    private SqlFileParser sqlFileParser;
    
    @Autowired
    private HierarchyParser hierarchyParser;
    
    @Autowired
    private OrganizationSyncMapper organizationSyncMapper;
    
    /**
     * 同步组织架构数据
     * 
     * @param sqlFilePath SQL文件路径
     * @return 同步结果
     */
    public SyncResult syncOrganizationData(String sqlFilePath) {
        log.info("开始同步组织架构数据，文件路径: {}", sqlFilePath);
        
        SyncResult result = new SyncResult();
        result.setStartTime(LocalDateTime.now());
        
        try {
            // 阶段1: 解析SQL文件
            log.info("=== 阶段1: 解析SQL文件 ===");
            List<DepartmentSyncDto> rawData = sqlFileParser.parseSqlFile(sqlFilePath);
            result.setRawDataCount(rawData.size());
            log.info("解析完成，原始数据: {} 条", rawData.size());
            
            // 阶段2: 数据清洗和去重
            log.info("=== 阶段2: 数据清洗和去重 ===");
            List<DepartmentSyncDto> cleanData = cleanAndDeduplicateData(rawData);
            result.setCleanDataCount(cleanData.size());
            log.info("清洗完成，有效数据: {} 条", cleanData.size());
            
            // 阶段3: 构建层级树
            log.info("=== 阶段3: 构建层级树 ===");
            Map<String, OrganizationTreeNode> treeNodes = buildHierarchyTree(cleanData);
            result.setTreeNodeCount(treeNodes.size());
            log.info("层级树构建完成，节点数: {}", treeNodes.size());
            
            // 阶段4: 分配ID
            log.info("=== 阶段4: 分配ID ===");
            List<OrganizationTreeNode> insertNodes = assignIds(treeNodes);
            result.setInsertNodeCount(insertNodes.size());
            log.info("ID分配完成，准备插入: {} 条记录", insertNodes.size());
            
            // 阶段5: 数据库操作
            log.info("=== 阶段5: 数据库操作 ===");
            performDatabaseOperations(insertNodes, result);
            log.info("数据库操作完成，插入: {} 条记录", result.getInsertedCount());
            
            // 阶段6: 数据验证
            log.info("=== 阶段6: 数据验证 ===");
            validateData(result);
            log.info("数据验证完成");
            
            result.setEndTime(LocalDateTime.now());
            result.setSuccess(true);
            
            log.info("组织架构数据同步完成: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("组织架构数据同步失败", e);
            result.setEndTime(LocalDateTime.now());
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            throw new RuntimeException("数据同步失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 数据清洗和去重
     * 按fullName去重，保留最新记录
     */
    private List<DepartmentSyncDto> cleanAndDeduplicateData(List<DepartmentSyncDto> rawData) {
        log.info("开始数据清洗和去重...");
        
        // 按fullName去重，保留最新记录
        Map<String, DepartmentSyncDto> uniqueData = new LinkedHashMap<>();
        int duplicateCount = 0;
        int invalidCount = 0;
        
        for (DepartmentSyncDto dto : rawData) {
            // 检查数据有效性
            if (!dto.isValid()) {
                invalidCount++;
                continue;
            }
            
            String fullName = dto.getFullName().trim();
            DepartmentSyncDto existing = uniqueData.get(fullName);
            
            if (existing == null) {
                // 新记录
                uniqueData.put(fullName, dto);
            } else {
                // 重复记录，保留更新时间较新的
                duplicateCount++;
                if (dto.getUpdateTime() != null && existing.getUpdateTime() != null && 
                    dto.getUpdateTime().isAfter(existing.getUpdateTime())) {
                    uniqueData.put(fullName, dto);
                    log.debug("替换重复记录: {}", fullName);
                }
            }
        }
        
        List<DepartmentSyncDto> result = new ArrayList<>(uniqueData.values());
        
        log.info("数据清洗完成: 原始={}, 无效={}, 重复={}, 最终={}", 
            rawData.size(), invalidCount, duplicateCount, result.size());
        
        return result;
    }

    /**
     * 构建层级树
     * 根据fullName解析层级结构，构建完整的组织架构树
     */
    private Map<String, OrganizationTreeNode> buildHierarchyTree(List<DepartmentSyncDto> cleanData) {
        log.info("开始构建层级树...");

        Map<String, OrganizationTreeNode> allNodes = new LinkedHashMap<>();
        Map<String, OrganizationTreeNode> rootNodes = new LinkedHashMap<>();
        int processedCount = 0;

        for (DepartmentSyncDto dto : cleanData) {
            String fullName = dto.getFullName();
            List<String> hierarchy = hierarchyParser.parseHierarchy(fullName);

            if (hierarchy.isEmpty()) {
                log.warn("无法解析层级结构: {}", fullName);
                continue;
            }

            OrganizationTreeNode currentParent = null;

            // 逐级创建或获取节点
            for (int i = 0; i < hierarchy.size(); i++) {
                String levelName = hierarchy.get(i);
                int level = i + 1;

                // 检查节点是否已存在
                OrganizationTreeNode node = allNodes.get(levelName);
                if (node == null) {
                    // 创建新节点
                    String organName = levelName;
                    String nodeFullName = (i == hierarchy.size() - 1) ? fullName : levelName;

                    node = new OrganizationTreeNode(organName, nodeFullName, level);
                    allNodes.put(levelName, node);

                    // 建立父子关系
                    if (currentParent != null) {
                        currentParent.addChild(node);
                    } else {
                        // 根节点
                        rootNodes.put(levelName, node);
                    }

                    log.debug("创建节点: {} (级别: {}, 父节点: {})",
                        levelName, level, currentParent != null ? currentParent.getOrganName() : "无");
                }

                currentParent = node;
            }

            processedCount++;
            if (processedCount % 100 == 0) {
                log.info("层级树构建进度: {}/{}", processedCount, cleanData.size());
            }
        }

        log.info("层级树构建完成: 总节点数={}, 根节点数={}", allNodes.size(), rootNodes.size());

        // 输出根节点信息
        log.info("根节点列表:");
        rootNodes.keySet().forEach(rootName -> log.info("  - {}", rootName));

        return allNodes;
    }

    /**
     * 分配ID
     * 为所有节点分配唯一的数字ID，并设置父子关系
     */
    private List<OrganizationTreeNode> assignIds(Map<String, OrganizationTreeNode> treeNodes) {
        log.info("开始分配ID...");

        // 获取当前最大ID
        Long maxId = organizationSyncMapper.getMaxId();
        AtomicLong currentId = new AtomicLong(Math.max(maxId, 2000000000L));

        log.info("起始ID: {}", currentId.get());

        List<OrganizationTreeNode> insertNodes = new ArrayList<>();

        // 找出所有根节点并排序
        List<OrganizationTreeNode> rootNodes = treeNodes.values().stream()
            .filter(node -> node.getParent() == null)
            .sorted(Comparator.comparing(OrganizationTreeNode::getOrganName))
            .collect(Collectors.toList());

        log.info("开始为 {} 个根节点分配ID", rootNodes.size());

        // 递归分配ID
        for (OrganizationTreeNode root : rootNodes) {
            assignNodeIds(root, currentId, insertNodes);
        }

        log.info("ID分配完成: 分配了{}个ID, 最大ID={}", insertNodes.size(), currentId.get());
        return insertNodes;
    }

    /**
     * 递归分配节点ID
     */
    private void assignNodeIds(OrganizationTreeNode node, AtomicLong currentId, List<OrganizationTreeNode> insertNodes) {
        // 分配ID
        node.setId(currentId.incrementAndGet());
        node.setPreId(node.getParent() != null ? node.getParent().getId() : 0L);
        node.setOrderInfo(1); // 可以根据需要调整排序

        insertNodes.add(node);

        log.debug("分配ID: {} -> ID={}, PreID={}",
            node.getOrganName(), node.getId(), node.getPreId());

        // 对子节点排序并递归分配ID
        node.getChildren().sort(Comparator.comparing(OrganizationTreeNode::getOrganName));
        for (OrganizationTreeNode child : node.getChildren()) {
            assignNodeIds(child, currentId, insertNodes);
        }
    }

    /**
     * 执行数据库操作
     * 删除现有同步数据，插入新数据
     */
    private void performDatabaseOperations(List<OrganizationTreeNode> insertNodes, SyncResult result) {
        log.info("开始数据库操作...");

        // 删除现有同步数据
        int deletedCount = organizationSyncMapper.deleteByDataSource(2);
        log.info("删除现有同步数据: {} 条", deletedCount);
        result.setDeletedCount(deletedCount);

        // 分批插入新数据
        int batchSize = 100;
        int totalBatches = (insertNodes.size() + batchSize - 1) / batchSize;
        int insertedCount = 0;

        log.info("开始分批插入数据，批次大小: {}, 总批次: {}", batchSize, totalBatches);

        for (int i = 0; i < insertNodes.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, insertNodes.size());
            List<OrganizationTreeNode> batch = insertNodes.subList(i, endIndex);

            try {
                int batchInserted = organizationSyncMapper.batchInsert(batch);
                insertedCount += batchInserted;

                int batchNum = i / batchSize + 1;
                log.info("批次 {}/{} 完成，本批插入: {}, 累计插入: {}/{}",
                    batchNum, totalBatches, batchInserted, insertedCount, insertNodes.size());

            } catch (Exception e) {
                log.error("批次 {} 插入失败", i / batchSize + 1, e);
                throw new RuntimeException("数据库插入失败: " + e.getMessage(), e);
            }
        }

        result.setInsertedCount(insertedCount);
        log.info("数据库操作完成: 删除 {} 条，插入 {} 条记录", deletedCount, insertedCount);
    }

    /**
     * 数据验证
     * 验证插入数据的完整性和正确性
     */
    private void validateData(SyncResult result) {
        log.info("开始数据验证...");

        try {
            // 验证记录数量
            int finalCount = organizationSyncMapper.countByDataSource(2);
            result.setFinalCount(finalCount);
            log.info("最终记录数: {}", finalCount);

            // 验证层级结构
            List<Map<String, Object>> levelStats = organizationSyncMapper.validateHierarchy(2);
            result.setLevelStats(levelStats);

            log.info("层级分布:");
            for (Map<String, Object> stat : levelStats) {
                log.info("  第{}级: {}个部门", stat.get("level"), stat.get("count"));
            }

            // 检查孤立节点
            List<OrganizationTreeNode> orphanNodes = organizationSyncMapper.findOrphanNodes(2);
            result.setOrphanNodeCount(orphanNodes.size());

            if (orphanNodes.size() > 0) {
                log.warn("发现 {} 个孤立节点:", orphanNodes.size());
                orphanNodes.forEach(node ->
                    log.warn("  孤立节点: {} (ID: {}, PreID: {})",
                        node.getOrganName(), node.getId(), node.getPreId()));
            } else {
                log.info("✓ 无孤立节点");
            }

            // 检查重复名称
            List<Map<String, Object>> duplicateNames = organizationSyncMapper.findDuplicateNames(2);
            result.setDuplicateNameCount(duplicateNames.size());

            if (duplicateNames.size() > 0) {
                log.warn("发现 {} 个重复名称:", duplicateNames.size());
                duplicateNames.forEach(duplicate ->
                    log.warn("  重复名称: {} (数量: {})",
                        duplicate.get("organ_name"), duplicate.get("count")));
            } else {
                log.info("✓ 无重复名称");
            }

            // 验证一级部门
            List<OrganizationTreeNode> level1Departments = organizationSyncMapper.findLevel1Departments(2);
            Map<String, Object> level1Stats = new HashMap<>();
            level1Stats.put("expected", hierarchyParser.getLevel1Departments().size());
            level1Stats.put("actual", level1Departments.size());
            level1Stats.put("departments", level1Departments.stream()
                .map(OrganizationTreeNode::getOrganName)
                .collect(Collectors.toList()));
            result.setLevel1Stats(level1Stats);

            log.info("一级部门验证: 预期={}, 实际={}",
                level1Stats.get("expected"), level1Stats.get("actual"));

        } catch (Exception e) {
            log.error("数据验证失败", e);
            throw new RuntimeException("数据验证失败: " + e.getMessage(), e);
        }

        log.info("数据验证完成");
    }
}
