-- 数据同步表结构扩展SQL (PostgreSQL版本)
-- 为支持外部系统数据同步，扩展当前系统表结构

-- =====================================================
-- 1. 扩展 t_org_structure 表（部门表）
-- =====================================================

-- 添加外部系统字段到部门表
DO $$
BEGIN
    -- 检查并添加字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'dept_uuid') THEN
        ALTER TABLE t_org_structure ADD COLUMN dept_uuid VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_code') THEN
        ALTER TABLE t_org_structure ADD COLUMN org_code VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'parent_code') THEN
        ALTER TABLE t_org_structure ADD COLUMN parent_code VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'full_name') THEN
        ALTER TABLE t_org_structure ADD COLUMN full_name VARCHAR(500);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'is_history') THEN
        ALTER TABLE t_org_structure ADD COLUMN is_history INTEGER DEFAULT 0;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'description') THEN
        ALTER TABLE t_org_structure ADD COLUMN description TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'fax') THEN
        ALTER TABLE t_org_structure ADD COLUMN fax VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'web_address') THEN
        ALTER TABLE t_org_structure ADD COLUMN web_address VARCHAR(500);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_manager') THEN
        ALTER TABLE t_org_structure ADD COLUMN org_manager VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'post_code') THEN
        ALTER TABLE t_org_structure ADD COLUMN post_code VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_type') THEN
        ALTER TABLE t_org_structure ADD COLUMN org_type VARCHAR(50);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_level') THEN
        ALTER TABLE t_org_structure ADD COLUMN org_level INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_path') THEN
        ALTER TABLE t_org_structure ADD COLUMN org_path VARCHAR(500);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'full_org_code') THEN
        ALTER TABLE t_org_structure ADD COLUMN full_org_code VARCHAR(500);
    END IF;
    
    -- 添加同步相关字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'external_id') THEN
        ALTER TABLE t_org_structure ADD COLUMN external_id INTEGER;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'external_update_time') THEN
        ALTER TABLE t_org_structure ADD COLUMN external_update_time TIMESTAMP;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'sync_status') THEN
        ALTER TABLE t_org_structure ADD COLUMN sync_status VARCHAR(20) DEFAULT 'SYNCED';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'last_sync_time') THEN
        ALTER TABLE t_org_structure ADD COLUMN last_sync_time TIMESTAMP;
    END IF;
END $$;

-- 添加字段注释
DO $$
BEGIN
    -- 部门表字段注释
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'dept_uuid') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.dept_uuid IS ''外部系统部门唯一标识符''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_code') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.org_code IS ''外部系统组织代码''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'parent_code') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.parent_code IS ''外部系统父级部门代码''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'full_name') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.full_name IS ''部门全称''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'is_history') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.is_history IS ''是否历史部门，0=否，1=是''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'description') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.description IS ''部门描述''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'fax') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.fax IS ''传真''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'web_address') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.web_address IS ''网站地址''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_manager') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.org_manager IS ''部门经理''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'post_code') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.post_code IS ''邮政编码''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_type') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.org_type IS ''组织类型，如公司、部门、科室等''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_level') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.org_level IS ''组织层级，如1=集团、2=公司、3=部门、4=科室''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'org_path') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.org_path IS ''组织路径，存储从根节点到当前节点的完整路径''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'full_org_code') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.full_org_code IS ''完整组织代码，包含所有上级组织代码''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'external_id') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.external_id IS ''外部系统中的原始ID''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'external_update_time') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.external_update_time IS ''外部系统中的更新时间''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'sync_status') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.sync_status IS ''同步状态：SYNCED=已同步，PENDING=待同步，ERROR=同步失败''';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_org_structure' AND column_name = 'last_sync_time') THEN
        EXECUTE 'COMMENT ON COLUMN t_org_structure.last_sync_time IS ''最后同步时间''';
    END IF;
END $$;

-- =====================================================
-- 2. 扩展 t_user 表（用户表）
-- =====================================================

-- 添加外部系统字段到用户表
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'mdm_id') THEN
        ALTER TABLE t_user ADD COLUMN mdm_id VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'mdm_hrdwnm') THEN
        ALTER TABLE t_user ADD COLUMN mdm_hrdwnm VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'employee_code') THEN
        ALTER TABLE t_user ADD COLUMN employee_code VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'gender') THEN
        ALTER TABLE t_user ADD COLUMN gender VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'mobile') THEN
        ALTER TABLE t_user ADD COLUMN mobile VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'id_card') THEN
        ALTER TABLE t_user ADD COLUMN id_card VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'birth_date') THEN
        ALTER TABLE t_user ADD COLUMN birth_date DATE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'email') THEN
        ALTER TABLE t_user ADD COLUMN email VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'org_type') THEN
        ALTER TABLE t_user ADD COLUMN org_type VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'org_level1') THEN
        ALTER TABLE t_user ADD COLUMN org_level1 VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'org_level2') THEN
        ALTER TABLE t_user ADD COLUMN org_level2 VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'org_level3') THEN
        ALTER TABLE t_user ADD COLUMN org_level3 VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'wechat') THEN
        ALTER TABLE t_user ADD COLUMN wechat VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'tel') THEN
        ALTER TABLE t_user ADD COLUMN tel VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'note') THEN
        ALTER TABLE t_user ADD COLUMN note TEXT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'employee_status') THEN
        ALTER TABLE t_user ADD COLUMN employee_status VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'user_type') THEN
        ALTER TABLE t_user ADD COLUMN user_type VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'id_name') THEN
        ALTER TABLE t_user ADD COLUMN id_name VARCHAR(255);
    END IF;

    -- 添加同步相关字段
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'external_id') THEN
        ALTER TABLE t_user ADD COLUMN external_id BIGINT;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'external_org_code') THEN
        ALTER TABLE t_user ADD COLUMN external_org_code VARCHAR(255);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'external_update_time') THEN
        ALTER TABLE t_user ADD COLUMN external_update_time TIMESTAMP;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'sync_status') THEN
        ALTER TABLE t_user ADD COLUMN sync_status VARCHAR(20) DEFAULT 'SYNCED';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 't_user' AND column_name = 'last_sync_time') THEN
        ALTER TABLE t_user ADD COLUMN last_sync_time TIMESTAMP;
    END IF;
END $$;
