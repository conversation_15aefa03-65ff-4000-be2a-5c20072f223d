#!/bin/bash

# 权限管理系统 - 测试环境部署脚本
# 使用方法: ./deploy-test.sh

echo "=========================================="
echo "权限管理系统 - 测试环境部署"
echo "=========================================="

# 配置变量
APP_NAME="permission-system"
PROFILE="test"
JAR_NAME="percode-0.0.1-SNAPSHOT.jar"
APP_PORT="8285"
JVM_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未配置到PATH"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "Java版本: $JAVA_VERSION"
}

# 停止现有进程
stop_app() {
    log_info "检查并停止现有进程..."
    
    # 查找进程
    PID=$(ps aux | grep "$JAR_NAME" | grep -v grep | awk '{print $2}')
    
    if [ -n "$PID" ]; then
        log_warn "发现运行中的进程 PID: $PID"
        log_info "正在停止进程..."
        kill -15 $PID
        
        # 等待进程优雅关闭
        sleep 10
        
        # 检查进程是否还在运行
        if ps -p $PID > /dev/null; then
            log_warn "进程未能优雅关闭，强制终止..."
            kill -9 $PID
        fi
        
        log_info "进程已停止"
    else
        log_info "未发现运行中的进程"
    fi
}

# 备份旧版本
backup_old() {
    if [ -f "$JAR_NAME" ]; then
        BACKUP_NAME="${JAR_NAME}.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "备份旧版本: $BACKUP_NAME"
        mv "$JAR_NAME" "$BACKUP_NAME"
    fi
}

# 检查端口占用
check_port() {
    log_info "检查端口 $APP_PORT 占用情况..."
    
    if netstat -tuln | grep ":$APP_PORT " > /dev/null; then
        log_error "端口 $APP_PORT 已被占用"
        netstat -tuln | grep ":$APP_PORT"
        exit 1
    else
        log_info "端口 $APP_PORT 可用"
    fi
}

# 启动应用
start_app() {
    log_info "启动应用..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动命令
    nohup java $JVM_OPTS \
        -Dspring.profiles.active=$PROFILE \
        -Dfile.encoding=UTF-8 \
        -Duser.timezone=Asia/Shanghai \
        -jar $JAR_NAME \
        > logs/app.log 2>&1 &
    
    APP_PID=$!
    echo $APP_PID > app.pid
    
    log_info "应用已启动，PID: $APP_PID"
    log_info "日志文件: logs/app.log"
}

# 健康检查
health_check() {
    log_info "等待应用启动..."
    
    for i in {1..30}; do
        sleep 2
        
        if curl -s "http://localhost:$APP_PORT/actuator/health" > /dev/null 2>&1; then
            log_info "应用启动成功！"
            log_info "访问地址: http://localhost:$APP_PORT"
            log_info "Swagger文档: http://localhost:$APP_PORT/swagger-ui.html"
            return 0
        fi
        
        echo -n "."
    done
    
    echo ""
    log_error "应用启动失败或超时"
    log_info "请检查日志: tail -f logs/app.log"
    return 1
}

# 显示状态
show_status() {
    echo ""
    log_info "========== 部署状态 =========="
    log_info "应用名称: $APP_NAME"
    log_info "环境配置: $PROFILE"
    log_info "端口: $APP_PORT"
    log_info "PID文件: app.pid"
    log_info "日志目录: logs/"
    echo ""
    log_info "常用命令:"
    log_info "  查看日志: tail -f logs/app.log"
    log_info "  查看进程: ps aux | grep $JAR_NAME"
    log_info "  停止应用: kill \$(cat app.pid)"
    echo ""
}

# 主流程
main() {
    # 检查JAR文件是否存在
    if [ ! -f "$JAR_NAME" ]; then
        log_error "JAR文件不存在: $JAR_NAME"
        log_info "请先编译项目: mvn clean package -DskipTests"
        exit 1
    fi
    
    check_java
    stop_app
    backup_old
    check_port
    start_app
    
    if health_check; then
        show_status
        log_info "部署完成！"
    else
        log_error "部署失败！"
        exit 1
    fi
}

# 执行主流程
main "$@"
