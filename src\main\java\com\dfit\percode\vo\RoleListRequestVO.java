package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色列表查询请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持分页和多条件搜索
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleListRequestVO", description = "角色列表查询请求参数")
public class RoleListRequestVO {
    
    @ApiModelProperty(value = "当前页码（从1开始）", required = true, example = "1")
    private Integer currentPage;

    @ApiModelProperty(value = "每页大小", required = true, example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "角色名称（模糊搜索）", example = "管理员")
    private String roleName;

    @ApiModelProperty(value = "角色状态：false-启用，true-停用，null-全部", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "是否包含已删除的角色", example = "false")
    private Boolean includeDeleted = false;
}
