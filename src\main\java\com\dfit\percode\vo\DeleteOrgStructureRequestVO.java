package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除部门请求VO类
 * 支持级联删除子部门
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteOrgStructureRequestVO", description = "删除部门请求参数")
public class DeleteOrgStructureRequestVO {
    
    @ApiModelProperty(value = "部门ID", example = "1002", required = true)
    // @NotNull(message = "部门ID不能为空")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long id;
    
    @ApiModelProperty(value = "是否级联删除子部门", example = "true")
    private Boolean cascadeDelete = true;
}
