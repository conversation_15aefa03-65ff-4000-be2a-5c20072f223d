# Windows系统部署指南

## 🪟 Windows环境要求

### 系统要求
- **操作系统**: Windows Server 2012+ 或 Windows 10+
- **Java版本**: JDK 8+ (推荐 JDK 11)
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘**: 最低 5GB 可用空间

### 软件准备
1. **Java JDK**: 确保已安装并配置环境变量
2. **数据库客户端**: 用于连接PostgreSQL数据库
3. **文本编辑器**: 用于修改配置文件

## 🚀 部署方式

### 方式1: 使用批处理脚本部署（推荐）

#### 1. 准备文件
```
部署目录/
├── percode-0.0.1-SNAPSHOT.jar     # 应用JAR包
├── deploy-test-windows.bat        # 部署脚本
├── stop-app.bat                   # 停止脚本
└── logs/                          # 日志目录（自动创建）
```

#### 2. 执行部署
```cmd
# 双击运行或在命令行执行
deploy-test-windows.bat
```

#### 3. 停止应用
```cmd
# 双击运行或在命令行执行
stop-app.bat
```

### 方式2: 手动命令行部署

#### 1. 打开命令提示符
```cmd
# 以管理员身份运行 cmd
# 切换到部署目录
cd C:\path\to\your\app
```

#### 2. 启动应用
```cmd
java -Xms512m -Xmx1024m ^
  -Dspring.profiles.active=test ^
  -Dfile.encoding=UTF-8 ^
  -Duser.timezone=Asia/Shanghai ^
  -jar percode-0.0.1-SNAPSHOT.jar
```

#### 3. 后台运行（可选）
```cmd
# 使用start命令在后台运行
start /b java -Xms512m -Xmx1024m ^
  -Dspring.profiles.active=test ^
  -Dfile.encoding=UTF-8 ^
  -Duser.timezone=Asia/Shanghai ^
  -jar percode-0.0.1-SNAPSHOT.jar > logs\app.log 2>&1
```

### 方式3: 安装为Windows服务

#### 1. 下载NSSM工具
- 访问: https://nssm.cc/download
- 下载适合系统的版本（32位或64位）
- 解压并将 `nssm.exe` 放到应用目录

#### 2. 安装服务
```cmd
# 运行安装脚本
install-service.bat

# 或手动安装
nssm install PermissionSystem "C:\Program Files\Java\jdk-11\bin\java.exe"
```

#### 3. 服务管理
```cmd
# 启动服务
net start PermissionSystem

# 停止服务
net stop PermissionSystem

# 查看服务状态
sc query PermissionSystem
```

## 🔧 配置说明

### 环境变量设置
```cmd
# 设置JAVA_HOME（如果未设置）
set JAVA_HOME=C:\Program Files\Java\jdk-11

# 添加到PATH
set PATH=%JAVA_HOME%\bin;%PATH%
```

### 防火墙配置
```cmd
# 开放应用端口（以管理员身份运行）
netsh advfirewall firewall add rule name="Permission System" dir=in action=allow protocol=TCP localport=8285
```

### 端口检查
```cmd
# 检查端口占用
netstat -an | findstr :8285

# 查看进程
tasklist | findstr java
```

## 📋 部署检查清单

### 部署前检查
- [ ] Java环境已安装并配置
- [ ] 配置文件已修改（数据库地址等）
- [ ] 防火墙端口已开放
- [ ] 数据库连接正常

### 部署后验证
- [ ] 应用进程正常运行
- [ ] 端口监听正常
- [ ] 日志无错误信息
- [ ] 接口访问正常

### 验证命令
```cmd
# 1. 检查进程
tasklist | findstr java

# 2. 检查端口
netstat -an | findstr :8285

# 3. 检查日志
type logs\app.log

# 4. 测试接口
curl http://localhost:8285/actuator/health
# 或在浏览器访问: http://localhost:8285/swagger-ui.html
```

## 🔍 故障排查

### 常见问题

#### 1. Java环境问题
```cmd
# 检查Java版本
java -version

# 检查JAVA_HOME
echo %JAVA_HOME%

# 如果提示"java不是内部或外部命令"
# 需要安装JDK并配置环境变量
```

#### 2. 端口被占用
```cmd
# 查看端口占用
netstat -ano | findstr :8285

# 结束占用进程
taskkill /pid <PID> /f

# 或修改配置文件中的端口
```

#### 3. 内存不足
```cmd
# 减少JVM内存参数
-Xms256m -Xmx512m
```

#### 4. 中文乱码
```cmd
# 设置控制台编码
chcp 65001

# 确保JVM参数包含
-Dfile.encoding=UTF-8
```

#### 5. 数据库连接失败
```cmd
# 测试数据库连接
telnet 数据库IP 5432

# 检查配置文件
type src\main\resources\application-test.yml
```

## 📝 日志管理

### 日志文件位置
```
logs/
├── app.log              # 应用日志
├── service.log          # 服务日志（如果安装为服务）
└── service-error.log    # 服务错误日志
```

### 日志查看命令
```cmd
# 查看最新日志
type logs\app.log

# 实时查看日志（PowerShell）
Get-Content logs\app.log -Wait -Tail 50

# 查找错误日志
findstr /i "error" logs\app.log
```

## 🔐 安全建议

1. **运行权限**: 使用专用用户账户运行应用
2. **防火墙**: 只开放必要的端口
3. **日志监控**: 定期检查应用日志
4. **定期更新**: 及时更新Java和应用版本

## 📞 技术支持

### 收集故障信息
```cmd
# 系统信息
systeminfo

# Java版本
java -version

# 网络连接
netstat -an | findstr :8285

# 进程信息
tasklist | findstr java

# 错误日志
type logs\app.log | findstr /i "error"
```

遇到问题时，请提供以上信息以便快速定位问题。
