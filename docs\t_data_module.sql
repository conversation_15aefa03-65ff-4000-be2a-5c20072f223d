/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:59
*/


-- ----------------------------
-- Table structure for t_data_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_module";
CREATE TABLE "public"."t_data_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_data_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_data_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_data_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_data_module"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_data_module" IS '数据权限模块信息表';

-- ----------------------------
-- Records of t_data_module
-- ----------------------------
INSERT INTO "public"."t_data_module" VALUES (10001, '员工数据模块', 'employee_data_module', 1, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10002, '部门数据模块', 'department_data_module', 2, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10003, '项目数据模块', 'project_data_module', 3, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10004, '客户数据模块', 'customer_data_module', 4, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10005, '合同数据模块', 'contract_data_module', 5, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (10006, '报表数据模块', 'report_data_module', 6, 'f', '2025-06-11 01:58:49.149105', '2025-06-11 01:58:49.149105');
INSERT INTO "public"."t_data_module" VALUES (1932720361229651968, 'test', 'aaa', 11, 'f', '2025-06-11 16:43:36.965141', '2025-06-20 17:15:40.979536');
INSERT INTO "public"."t_data_module" VALUES (1935155973312352256, 'sdf', 'sdf', 1, 'f', '2025-06-18 10:01:52.140474', '2025-06-20 17:51:49.834569');
INSERT INTO "public"."t_data_module" VALUES (1936196593585885184, '知识库', 'knowledge', 1, 'f', '2025-06-21 06:56:55.33955', '2025-06-21 06:56:55.33955');

-- ----------------------------
-- Primary Key structure for table t_data_module
-- ----------------------------
ALTER TABLE "public"."t_data_module" ADD CONSTRAINT "t_data_module_pk" PRIMARY KEY ("id");
