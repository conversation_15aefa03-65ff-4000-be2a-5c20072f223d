# 数据操作权限管理接口测试文档
# 测试配置数据操作权限的功能

### 1. 获取数据操作权限配置 - 正常获取
GET http://localhost:8080/data-operates/config?dataIdentifier=user_basic_data&moduleIdentifier=user_data_module

### 2. 获取数据操作权限配置 - 测试不存在的数据标识
GET http://localhost:8080/data-operates/config?dataIdentifier=nonexistent_data&moduleIdentifier=user_data_module

### 3. 获取数据操作权限配置 - 测试空参数
GET http://localhost:8080/data-operates/config?dataIdentifier=&moduleIdentifier=user_data_module

### 4. 配置数据操作权限 - 配置全部操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_basic_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 3]
}

### 5. 配置数据操作权限 - 配置部分操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_profile_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2]
}

### 6. 配置数据操作权限 - 清空操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_auth_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": []
}

### 7. 配置数据操作权限 - 测试无效操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_basic_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 5]
}

### 8. 配置数据操作权限 - 测试不存在的数据权限
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "nonexistent_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 3]
}

### 9. 配置数据操作权限 - 测试必填字段验证
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 3]
}

### 10. 重新获取配置验证修改结果
GET http://localhost:8080/data-operates/config?dataIdentifier=user_basic_data&moduleIdentifier=user_data_module
