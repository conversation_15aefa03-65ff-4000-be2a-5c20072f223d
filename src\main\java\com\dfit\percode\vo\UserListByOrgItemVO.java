package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 按部门查询用户列表项VO类
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserListByOrgItemVO", description = "按部门查询用户列表项")
public class UserListByOrgItemVO {

    @ApiModelProperty(value = "用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "登录账号")
    private String account;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "所属部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;

    @ApiModelProperty(value = "所属部门名称")
    private String organName;

    @ApiModelProperty(value = "完整部门路径")
    private String orgPath;

    @ApiModelProperty(value = "职位")
    private String position;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "用户状态（false-正常，true-禁用）")
    private Boolean isDisable;

    @ApiModelProperty(value = "角色名称列表")
    private List<String> roleName;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    @ApiModelProperty(value = "最后登录时间")
    private String lastLoginTime;
}
