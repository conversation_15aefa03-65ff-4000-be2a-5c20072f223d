# 数据模块管理接口测试
# 使用VS Code REST Client插件运行
# 或者复制到Postman中测试

### 0. 插入测试数据 - 快速创建5个测试模块
POST http://localhost:8080/data-modules/insertTestData
Content-Type: application/json

{}

### 1. 新增数据模块 - 用户数据模块
POST http://localhost:8080/data-modules/add
Content-Type: application/json

{
  "moduleName": "用户数据模块",
  "moduleIdentifier": "user_data_module",
  "orderInfo": 1
}

### 2. 新增数据模块 - 订单数据模块
POST http://localhost:8080/data-modules/add
Content-Type: application/json

{
  "moduleName": "订单数据模块",
  "moduleIdentifier": "order_data_module",
  "orderInfo": 2
}

### 3. 新增数据模块 - 商品数据模块
POST http://localhost:8080/data-modules/add
Content-Type: application/json

{
  "moduleName": "商品数据模块",
  "moduleIdentifier": "product_data_module",
  "orderInfo": 3
}

### 4. 查询数据模块列表 - 分页查询
POST http://localhost:8080/data-modules/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### 5. 查询数据模块列表 - 不分页
POST http://localhost:8080/data-modules/list
Content-Type: application/json

{}

### 6. 测试重复标识 - 应该失败
POST http://localhost:8080/data-modules/add
Content-Type: application/json

{
  "moduleName": "重复模块",
  "moduleIdentifier": "user_data_module",
  "orderInfo": 4
}

### 7. 删除数据模块 - 测试删除功能（使用实际ID）
POST http://localhost:8080/data-modules/delete
Content-Type: application/json

{
  "moduleId": "1930548900859613184"
}

### 8. 测试删除不存在的模块 - 应该失败
POST http://localhost:8080/data-modules/delete
Content-Type: application/json

{
  "moduleId": "9999999999999999999"
}

### 9. 测试原有接口 - 验证兼容性
POST http://localhost:8080/t-data-module/dataModule
Content-Type: application/json

{}
