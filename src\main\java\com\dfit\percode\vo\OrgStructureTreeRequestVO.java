package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取部门结构树请求VO类
 * 用于移动部门时获取可选择的部门树
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "OrgStructureTreeRequestVO", description = "获取部门结构树请求参数")
public class OrgStructureTreeRequestVO {
    
    @ApiModelProperty(value = "要排除的部门ID（移动部门时排除自身和子部门）", example = "1002")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long excludeOrgId;
    
    @ApiModelProperty(value = "是否包含已删除的部门", example = "false")
    private Boolean includeDeleted = false;
    
    @ApiModelProperty(value = "最大层级深度（0表示不限制）", example = "0")
    private Integer maxLevel = 0;
}
