# Augment-VIP 项目使用指南

## 项目概述
Augment-VIP 是一个用于管理和清理 VS Code 数据库的工具包，专门为 Augment VIP 用户设计。

**主要功能：**
1. **数据库清理**：从 VS Code 数据库中移除 Augment 相关条目
2. **遥测 ID 修改**：为 VS Code 生成随机遥测 ID 以增强隐私
3. **跨平台支持**：支持 Windows、macOS 和 Linux
4. **安全操作**：在进行任何更改前创建备份

## Python 版本兼容性
- **要求**：Python 3.6 或更高版本
- **用户当前版本**：Python 3.13 ✅ **完全兼容**

## 安装步骤

### 第一次安装（只需要做一次）
```bash
# 1. 进入项目目录
cd /g/IDEA/augmentVIP/augment-vip

# 2. 运行安装脚本（这会创建虚拟环境并安装依赖）
python install.py
```

安装脚本会自动：
- 检查 Python 版本
- 创建虚拟环境 `.venv`
- 安装所需依赖
- 询问是否立即运行工具

## 使用方法

### 方法一：激活虚拟环境后使用
```bash
# 1. 进入项目目录
cd /g/IDEA/augmentVIP/augment-vip

# 2. 激活虚拟环境（Windows）
.venv\Scripts\activate

# 3. 使用工具
augment-vip clean          # 清理数据库
augment-vip modify-ids     # 修改遥测ID
augment-vip all           # 运行所有工具

# 4. 使用完毕后，退出虚拟环境（可选）
deactivate
```

### 方法二：直接使用（推荐）
```bash
# 进入项目目录
cd /g/IDEA/augmentVIP/augment-vip

# 直接使用虚拟环境中的命令（Windows）
.venv\Scripts\augment-vip clean        # 清理数据库
.venv\Scripts\augment-vip modify-ids   # 修改遥测ID  
.venv\Scripts\augment-vip all          # 运行所有工具
```

## 便捷使用脚本

### 创建 run.bat 文件（推荐）
在项目目录下创建一个 `run.bat` 文件，内容如下：

```batch
@echo off
cd /d "%~dp0"
echo 选择要执行的操作：
echo 1. 清理数据库
echo 2. 修改遥测ID
echo 3. 运行所有工具
echo 4. 退出
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    .venv\Scripts\augment-vip clean
) else if "%choice%"=="2" (
    .venv\Scripts\augment-vip modify-ids
) else if "%choice%"=="3" (
    .venv\Scripts\augment-vip all
) else if "%choice%"=="4" (
    exit
) else (
    echo 无效选择
)
pause
```

创建后，双击 `run.bat` 即可方便使用。

## 工具功能详解

### 1. 数据库清理 (clean)
- **功能**：从 VS Code 数据库中移除包含 "augment" 的条目
- **安全性**：操作前会自动创建数据库备份
- **支持的数据库**：自动检测 VS Code 在不同操作系统下的数据库位置

### 2. 遥测 ID 修改 (modify-ids)
- **功能**：修改 VS Code 的 machineId 和 devDeviceId
- **生成方式**：
  - machineId：64位随机十六进制字符串
  - devDeviceId：随机 UUID v4
- **安全性**：修改前会备份原始 storage.json 文件

### 3. 运行所有工具 (all)
- **功能**：依次执行数据库清理和遥测 ID 修改
- **适用场景**：一次性完成所有操作

## 项目结构
```
augment-vip/
├── .venv/                  # 虚拟环境（安装后生成）
├── augment_vip/            # 主程序包
│   ├── __init__.py         # 包初始化
│   ├── cli.py              # 命令行界面
│   ├── db_cleaner.py       # 数据库清理功能
│   ├── id_modifier.py      # ID修改功能
│   └── utils.py            # 工具函数
├── install.py              # Python安装脚本
├── install.sh              # Bash安装脚本
├── requirements.txt        # 依赖列表
└── setup.py                # 包设置脚本
```

## 常见问题

### Q: 后续使用时需要重新安装吗？
A: 不需要。安装只需要做一次，后续直接使用命令即可。

### Q: 如何确认工具是否正常工作？
A: 运行任何命令后，工具会显示详细的操作日志和结果报告。

### Q: 备份文件在哪里？
A: 工具会在原文件同目录下创建带时间戳的备份文件。

### Q: 可以撤销操作吗？
A: 可以，使用备份文件恢复原始状态。

## 使用建议
1. **首次使用**：建议先运行 `augment-vip clean` 测试
2. **定期使用**：可以定期运行清理工具保持 VS Code 整洁
3. **备份重要**：虽然工具会自动备份，但建议您也手动备份重要配置

---
**创建时间**：2025年1月
**适用版本**：Python 3.6+
**测试环境**：Windows 10/11, Python 3.13 