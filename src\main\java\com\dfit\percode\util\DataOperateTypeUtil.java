package com.dfit.percode.util;

/**
 * 数据操作类型工具类
 * 提供数据权限操作类型的常量定义和data_operate_id生成方法
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public class DataOperateTypeUtil {

    /** 下载权限 */
    public static final int DOWNLOAD = 1;

    /** 修改权限 */
    public static final int UPDATE = 2;

    /** 删除权限 */
    public static final int DELETE = 3;

    /** 查看权限 */
    public static final int VIEW = 4;

    /**
     * 生成data_operate_id
     * 格式：数据id.操作类型
     *
     * @param dataId 数据ID
     * @param operateType 操作类型（1-下载，2-修改，3-删除，4-查看）
     * @return data_operate_id字符串，如"11001.1"，如果参数为null则返回null
     */
    public static String generateDataOperateId(Long dataId, Integer operateType) {
        if (dataId == null || operateType == null) {
            return null;
        }
        return dataId + "." + operateType;
    }

    /**
     * 验证操作类型是否有效
     *
     * @param operateType 操作类型
     * @return 是否为有效的操作类型（1-4）
     */
    public static boolean isValidOperateType(Integer operateType) {
        return operateType != null && operateType >= VIEW && operateType <= DELETE;
    }

    /**
     * 获取操作类型的描述
     *
     * @param operateType 操作类型
     * @return 操作类型描述
     */
    public static String getOperateTypeDescription(Integer operateType) {
        if (operateType == null) {
            return "未知";
        }
        switch (operateType) {
            case DOWNLOAD:
                return "下载";
            case UPDATE:
                return "修改";
            case DELETE:
                return "删除";
            case VIEW:
                return "查看";
            default:
                return "未知";
        }
    }
}
