### 调试数据类型转换问题
### 查看查询返回的原始数据类型

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试数据类型转换（查看日志中的原始数据类型）
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 预期的日志输出：
### "原始数据: orgId=5001 (Long), orgName=科技集团总公司, userId=6001 (Long)"
### 或者可能是：
### "原始数据: orgId=5001 (Integer), orgName=科技集团总公司, userId=6001 (Integer)"
### 或者：
### "原始数据: orgId=5001 (BigInteger), orgName=科技集团总公司, userId=6001 (BigInteger)"

### 根据日志结果分析：

### 情况1：如果显示的是 Long 类型
### 说明类型转换没问题，问题可能在其他地方

### 情况2：如果显示的是 Integer 类型  
### 说明需要从 Integer 转换为 Long，convertToLong方法应该能处理

### 情况3：如果显示的是 BigInteger 类型
### 说明是PostgreSQL的bigint类型，需要特殊处理

### 情况4：如果显示的是其他类型
### 需要在convertToLong方法中添加对应的处理逻辑

### 注意观察：
### 1. orgId 的数据类型
### 2. userId 的数据类型  
### 3. 是否有数据为null
### 4. 数据值是否正确（应该是5001, 5002等）

### 如果数据类型正确但还是处理失败，可能的原因：
### 1. 数据库字段映射错误
### 2. SQL查询结果集处理有问题
### 3. MyBatis结果映射配置问题

### 下一步调试：
### 根据日志中显示的数据类型，调整convertToLong方法
### 或者检查SQL查询和字段映射是否正确
