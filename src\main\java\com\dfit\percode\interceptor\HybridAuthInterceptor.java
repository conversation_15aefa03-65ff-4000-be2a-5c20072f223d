package com.dfit.percode.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.dfit.percode.common.SecurityConstants;
import com.dfit.percode.config.SuperAdminConfig;
import com.dfit.percode.util.CustomJwtUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 混合认证拦截器
 * 支持同时验证自定义JWT token和Sa-Token
 * 优先使用自定义JWT，fallback到Sa-Token验证，确保认证的灵活性
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Slf4j
@Component
public class HybridAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private CustomJwtUtil customJwtUtil;

    @Autowired
    private SuperAdminConfig superAdminConfig;

    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 请求处理前的混合认证拦截
     * 优先验证自定义JWT token，fallback到Sa-Token验证
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @return true表示继续处理，false表示中断处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        String method = request.getMethod();

        log.debug("混合认证拦截器 - URI: {}, Method: {}", requestURI, method);

        // 1. 检查是否在白名单中
        if (isInWhiteList(requestURI)) {
            log.debug("请求路径在白名单中，跳过认证: {}", requestURI);
            return true;
        }

        // 2. 获取Authorization头
        String authorization = request.getHeader("Authorization");
        if (authorization == null || authorization.trim().isEmpty()) {
            log.warn("混合认证失败：缺少Authorization头, URI: {}", requestURI);
            return handleAuthFailure(response, "缺少Authorization头");
        }

        // 3. 超级管理员判断（优先级最高）
        if (checkSuperAdmin(authorization, request, requestURI)) {
            return true; // 超级管理员直接放行
        }

        // 4. 混合认证策略：优先自定义JWT，fallback到Sa-Token
        Long userId = attemptHybridAuth(authorization, requestURI);

        if (userId != null) {
            // 认证成功，将用户ID存储到request attribute中
            request.setAttribute("userId", userId);
            request.setAttribute("authMethod", getLastAuthMethod());
            log.debug("混合认证成功，用户ID: {}, 认证方式: {}, URI: {}", userId, getLastAuthMethod(), requestURI);
            return true;
        } else {
            log.warn("混合认证失败：所有认证方式都失败, URI: {}", requestURI);
            return handleAuthFailure(response, "认证失败，请重新登录");
        }
    }

    /**
     * 检查请求路径是否在白名单中
     *
     * @param requestURI 请求URI
     * @return 是否在白名单中
     */
    private boolean isInWhiteList(String requestURI) {
        // 复用SecurityConstants的白名单配置
        return Arrays.stream(SecurityConstants.WHITE_LIST)
                .anyMatch(pattern -> pathMatcher.match(pattern, requestURI));
    }

    /**
     * 尝试混合认证
     * 优先使用自定义JWT，fallback到Sa-Token验证
     *
     * @param authorization Authorization头内容
     * @param requestURI    请求URI
     * @return 用户ID，认证失败返回null
     */
    private Long attemptHybridAuth(String authorization, String requestURI) {
        // 策略1：尝试自定义JWT认证
        Long userId = attemptCustomJwtAuth(authorization, requestURI);
        if (userId != null) {
            lastAuthMethod = "CustomJWT";
            return userId;
        }

        // 策略2：fallback到Sa-Token认证
        userId = attemptSaTokenAuth(authorization, requestURI);
        if (userId != null) {
            lastAuthMethod = "Sa-Token";
            return userId;
        }

        lastAuthMethod = "Failed";
        return null;
    }

    private String lastAuthMethod = "Unknown";

    private String getLastAuthMethod() {
        return lastAuthMethod;
    }

    /**
     * 尝试自定义JWT认证
     *
     * @param authorization Authorization头内容
     * @param requestURI    请求URI
     * @return 用户ID，认证失败返回null
     */
    private Long attemptCustomJwtAuth(String authorization, String requestURI) {
        try {
            // 验证自定义JWT token
            if (customJwtUtil.validateCustomToken(authorization)) {
                Long userId = customJwtUtil.getUserIdFromCustomToken(authorization);
                if (userId != null) {
                    log.debug("自定义JWT认证成功，用户ID: {}, URI: {}", userId, requestURI);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("自定义JWT认证失败: {}, URI: {}", e.getMessage(), requestURI);
        }
        return null;
    }

    /**
     * 尝试Sa-Token认证
     *
     * @param authorization Authorization头内容
     * @param requestURI    请求URI
     * @return 用户ID，认证失败返回null
     */
    private Long attemptSaTokenAuth(String authorization, String requestURI) {
        try {
            // 设置当前请求的token到Sa-Token上下文
            if (authorization.startsWith("Bearer ")) {
                String token = authorization.substring(7);
                // 尝试通过Sa-Token验证
                Object loginIdObj = StpUtil.getLoginIdByToken(token);
                if (loginIdObj != null) {
                    Long userId = Long.valueOf(loginIdObj.toString());
                    log.debug("Sa-Token认证成功，用户ID: {}, URI: {}", userId, requestURI);
                    return userId;
                }
            }
        } catch (Exception e) {
            log.debug("Sa-Token认证失败: {}, URI: {}", e.getMessage(), requestURI);
        }
        return null;
    }

    /**
     * 检查是否为超级管理员
     * 优先级最高，超级管理员直接放行，跳过所有权限检查
     *
     * @param authorization Authorization头内容
     * @param request HTTP请求对象
     * @param requestURI 请求URI
     * @return true表示是超级管理员，false表示不是
     */
    private boolean checkSuperAdmin(String authorization, HttpServletRequest request, String requestURI) {
        try {
            // 检查超级管理员功能是否启用
            if (!superAdminConfig.isConfigValid() || !superAdminConfig.getEnabled()) {
                return false;
            }

            // 尝试从token中获取用户ID
            Long userId = extractUserIdFromToken(authorization);
            if (userId == null) {
                return false;
            }

            // 判断是否为超级管理员
            boolean isSuperAdmin = superAdminConfig.isSuperAdmin(userId);

            if (isSuperAdmin) {
                // 设置超级管理员标识
                request.setAttribute("userId", userId);
                request.setAttribute("authMethod", "SuperAdmin");
                request.setAttribute("isSuperAdmin", true);

                // 记录超级管理员访问日志
                log.info(" 超级管理员访问 - 用户ID: {}, URI: {}, Method: {}, IP: {}",
                        userId, requestURI, request.getMethod(), getClientIpAddress(request));

                return true;
            }

        } catch (Exception e) {
            log.debug("超级管理员检查异常: {}, URI: {}", e.getMessage(), requestURI);
        }

        return false;
    }

    /**
     * 处理认证失败
     *
     * @param response HTTP响应
     * @param message  错误消息
     * @return false，中断请求处理
     */
    private boolean handleAuthFailure(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");

        Map<String, Object> result = new HashMap<>();
        result.put("code", 401);
        result.put("message", message);
        result.put("data", null);
        result.put("timestamp", System.currentTimeMillis());

        String jsonResponse = objectMapper.writeValueAsString(result);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();

        return false;
    }

    /**
     * 请求处理后的拦截
     * 记录认证相关的性能信息
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 清理认证相关的临时数据
        String authMethod = (String) request.getAttribute("authMethod");
        Long userId = (Long) request.getAttribute("userId");

        if (userId != null && authMethod != null) {
            log.debug("请求完成，用户ID: {}, 认证方式: {}, URI: {}", userId, authMethod, request.getRequestURI());
        }

        if (ex != null) {
            log.error("混合认证拦截器处理异常 - URI: {}, Error: {}", request.getRequestURI(), ex.getMessage());
        }
    }

    /**
     * 从token中提取用户ID
     * 支持自定义JWT和Sa-Token两种方式
     *
     * @param authorization Authorization头内容
     * @return 用户ID，提取失败返回null
     */
    private Long extractUserIdFromToken(String authorization) {
        // 尝试从自定义JWT中提取用户ID
        try {
            if (customJwtUtil.validateCustomToken(authorization)) {
                return customJwtUtil.getUserIdFromCustomToken(authorization);
            }
        } catch (Exception e) {
            log.debug("从自定义JWT提取用户ID失败: {}", e.getMessage());
        }

        // 尝试从Sa-Token中提取用户ID
        try {
            if (authorization.startsWith("Bearer ")) {
                String token = authorization.substring(7);
                Object loginIdObj = StpUtil.getLoginIdByToken(token);
                if (loginIdObj != null) {
                    return Long.valueOf(loginIdObj.toString());
                }
            }
        } catch (Exception e) {
            log.debug("从Sa-Token提取用户ID失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
