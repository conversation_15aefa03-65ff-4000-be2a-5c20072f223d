package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 角色用户分配列表响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 包含分页信息和用户列表数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleUserListResponseVO", description = "角色用户分配列表响应")
public class RoleUserListResponseVO {
    
    @ApiModelProperty("用户列表数据")
    private List<RoleUserListItemVO> records;

    @ApiModelProperty("总记录数")
    private Long total;

    @ApiModelProperty("当前页码")
    private Integer pageNum;

    @ApiModelProperty("每页大小")
    private Integer pageSize;

    @ApiModelProperty("总页数")
    private Integer totalPages;

    @ApiModelProperty("是否有上一页")
    private Boolean hasPrevious;

    @ApiModelProperty("是否有下一页")
    private Boolean hasNext;
}
