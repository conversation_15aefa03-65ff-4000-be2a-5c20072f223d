package com.dfit.percode.common;
import java.io.*;
import java.net.ServerSocket;
import java.net.Socket;

public class FileReceiver {
    public static void main(String[] args) {
        int port = 12345;
        String saveDirectory = "path/to/save/"; // 替换为实际保存目录

        try (ServerSocket serverSocket = new ServerSocket(port)) {
            System.out.println("服务器已启动，等待连接...");

            while (true) {
                try (Socket socket = serverSocket.accept();
                     InputStream inputStream = socket.getInputStream();
                     DataInputStream dataInputStream = new DataInputStream(inputStream)) {

                    // 接收文件名和长度
                    String fileName = dataInputStream.readUTF();
                    long fileLength = dataInputStream.readLong();

                    // 创建保存文件的路径
                    File directory = new File(saveDirectory);
                    if (!directory.exists()) {
                        directory.mkdirs();
                    }
                    File saveFile = new File(directory, fileName);

                    // 接收文件内容
                    try (FileOutputStream fileOutputStream = new FileOutputStream(saveFile)) {
                        byte[] buffer = new byte[4096];
                        long totalBytesRead = 0;
                        int bytesRead;

                        while (totalBytesRead < fileLength &&
                                (bytesRead = dataInputStream.read(buffer, 0,
                                        (int) Math.min(buffer.length, fileLength - totalBytesRead))) != -1) {
                            fileOutputStream.write(buffer, 0, bytesRead);
                            totalBytesRead += bytesRead;
                        }
                    }

                    System.out.println("文件接收成功: " + fileName);

                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
