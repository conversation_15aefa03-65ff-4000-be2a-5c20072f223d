package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除菜单请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持逻辑删除菜单项
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteMenuRequestVO", description = "删除菜单请求参数")
public class DeleteMenuRequestVO {

    @ApiModelProperty(value = "菜单ID", required = true, example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongDeserializer.class)
    private Long id;

    @ApiModelProperty(value = "是否强制删除", required = false, example = "false")
    private Boolean forceDelete = false;
}
