package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITestDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 测试数据控制器
 * 用于插入测试数据，方便功能测试
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@RequestMapping("/test")
@Api(tags = "测试数据管理")
public class TestDataController {

    @Autowired
    private ITestDataService testDataService;

    /**
     * 插入菜单测试数据
     * 包含5个模块和完整的菜单树结构
     *
     * @return 插入结果
     */
    @RequestMapping(value = "/insertMenuData", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "插入菜单测试数据", notes = "插入完整的菜单模块和菜单权限测试数据")
    public BaseResult insertMenuTestData() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层插入测试数据
            testDataService.insertMenuTestData();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("菜单测试数据插入成功");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("插入菜单测试数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 清理测试数据
     * 删除所有测试插入的数据
     *
     * @return 清理结果
     */
    @RequestMapping(value = "/cleanTestData", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "清理测试数据", notes = "删除所有测试插入的菜单数据")
    public BaseResult cleanTestData() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层清理测试数据
            testDataService.cleanTestData();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("测试数据清理成功");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("清理测试数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
