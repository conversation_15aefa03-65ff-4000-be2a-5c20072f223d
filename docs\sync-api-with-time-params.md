# 数据同步API接口文档（支持时间范围参数）

## 📋 **接口概述**

所有数据同步接口现在都支持时间范围参数，可以同步指定时间段内的数据变更。

### **时间参数格式**
- **格式**：`yyyy-MM-dd HH:mm:ss`
- **示例**：`2024-01-01 00:00:00`
- **参数**：
  - `startDate`：开始时间（可选）
  - `endDate`：结束时间（可选）
- **默认行为**：如果不提供时间参数，默认同步最近一天的数据

## 🔄 **数据同步接口列表**

### **1. 执行完整数据同步**

**接口地址**：`POST /sync/full`

**请求参数**：
```http
POST /sync/full?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json
```

**参数说明**：
- `startDate`（可选）：开始时间
- `endDate`（可选）：结束时间

**响应示例**：
```json
{
  "code": 200,
  "message": "完整数据同步执行成功",
  "data": "同步完成，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00"
}
```

### **2. 同步部门数据**

**接口地址**：`POST /sync/departments`

**请求参数**：
```http
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json
```

**响应示例**：
```json
{
  "code": 200,
  "message": "部门数据同步成功",
  "data": "同步完成，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00"
}
```

### **3. 同步员工数据**

**接口地址**：`POST /sync/employees`

**请求参数**：
```http
POST /sync/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json
```

**响应示例**：
```json
{
  "code": 200,
  "message": "员工数据同步成功",
  "data": "同步完成，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00"
}
```

### **4. 测试外部系统连接**

**接口地址**：`GET /sync/test-connection`

**请求参数**：无

**响应示例**：
```json
{
  "code": 200,
  "message": "外部系统连接正常",
  "data": "连接成功"
}
```

### **5. 获取同步状态**

**接口地址**：`GET /sync/status`

**请求参数**：无

**响应示例**：
```json
{
  "code": 200,
  "message": "获取同步状态成功",
  "data": "状态查询功能待实现"
}
```

## 🧪 **使用示例**

### **示例1：同步今天的数据**
```http
POST /sync/full?startDate=2024-01-20 00:00:00&endDate=2024-01-20 23:59:59
```

### **示例2：同步最近一周的数据**
```http
POST /sync/full?startDate=2024-01-14 00:00:00&endDate=2024-01-20 23:59:59
```

### **示例3：使用默认时间范围（最近一天）**
```http
POST /sync/full
```

### **示例4：只同步部门数据（指定时间）**
```http
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
```

## 📝 **外部系统API调用**

当您调用同步接口时，系统会自动调用外部系统的API：

### **部门数据API**
```
GET {external.system.base-url}/api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
```

### **员工数据API**
```
GET {external.system.base-url}/api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
```

## ⚙️ **配置说明**

在`application.yml`中配置外部系统地址：

```yaml
external:
  system:
    base-url: http://localhost:8080  # 外部系统地址
    api:
      departments: /api/data/departments
      employees: /api/data/employees
    timeout:
      connect: 5000
      read: 30000
```

## ⚠️ **注意事项**

1. **时间格式**：必须严格按照`yyyy-MM-dd HH:mm:ss`格式
2. **时间范围**：建议不要设置过大的时间范围，避免数据量过大
3. **网络超时**：大量数据同步可能需要较长时间，注意超时设置
4. **数据一致性**：同步过程中建议避免对相关表进行其他操作
5. **错误处理**：如果同步失败，可以查看日志了解具体原因

## 🔍 **错误排查**

### **常见错误**

1. **时间格式错误**
```json
{
  "code": 500,
  "message": "数据同步失败: Text '2024-1-1' could not be parsed at index 5"
}
```
**解决**：使用正确的时间格式`2024-01-01 00:00:00`

2. **外部系统连接失败**
```json
{
  "code": 500,
  "message": "数据同步失败: Connection refused"
}
```
**解决**：检查外部系统是否启动，网络是否通畅

3. **数据库错误**
```json
{
  "code": 500,
  "message": "数据同步失败: relation 't_sync_log' does not exist"
}
```
**解决**：执行数据库扩展SQL文件

## 📊 **同步流程**

1. **解析时间参数** → 验证格式
2. **调用外部API** → 获取指定时间范围的数据
3. **数据转换** → 将外部格式转换为内部格式
4. **保存数据** → 插入到对应的数据库表
5. **记录日志** → 记录同步状态和结果
6. **返回结果** → 返回同步成功或失败信息

现在您可以通过Swagger UI测试这些带时间参数的接口了！
