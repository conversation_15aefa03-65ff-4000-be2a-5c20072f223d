package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色列表项VO类
 * 按照前端格式要求设计（驼峰命名）
 * 对应角色管理页面表格的每一行数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleListItemVO", description = "角色列表项数据")
public class RoleListItemVO {
    
    @ApiModelProperty(value = "角色ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "角色名称")
    private String roleName;
    
    @ApiModelProperty(value = "排序序号")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "是否停用：false-启用，true-停用")
    private Boolean isDisable;
    
    @ApiModelProperty(value = "角色状态显示文本")
    private String statusText;
    
    @ApiModelProperty(value = "创建时间")
    private String createTime;
    
    @ApiModelProperty(value = "修改时间")
    private String modifyTime;
    
    @ApiModelProperty(value = "关联用户数量")
    private Integer userCount = 0;
    
    @ApiModelProperty(value = "是否可删除（有关联用户时不可删除）")
    private Boolean canDelete = true;
}
