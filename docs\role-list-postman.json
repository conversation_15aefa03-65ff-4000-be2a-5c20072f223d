{"info": {"name": "角色列表接口测试", "description": "角色管理系统 - 角色列表查询接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}], "item": [{"name": "1. 基础分页查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('响应格式正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('total');", "});", "", "pm.test('返回成功', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.message).to.eql('SUCCESS');", "});", "", "pm.test('数据格式正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "    pm.expect(jsonData.total).to.be.a('number');", "    ", "    if (jsonData.data.length > 0) {", "        const firstRole = jsonData.data[0];", "        pm.expect(firstRole).to.have.property('id');", "        pm.expect(firstRole).to.have.property('roleName');", "        pm.expect(firstRole).to.have.property('isDisable');", "        pm.expect(firstRole).to.have.property('statusText');", "        pm.expect(firstRole).to.have.property('userCount');", "        pm.expect(firstRole).to.have.property('canDelete');", "    }", "});"]}}]}, {"name": "2. 角色名称模糊搜索", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10,\n  \"roleName\": \"管理\"\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('搜索结果正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    ", "    // 检查返回的角色名称都包含'管理'", "    jsonData.data.forEach(function(role) {", "        pm.expect(role.roleName).to.include('管理');", "    });", "});"]}}]}, {"name": "3. 按状态筛选（启用）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10,\n  \"isDisable\": false\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('只返回启用状态的角色', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    ", "    // 检查所有返回的角色都是启用状态", "    jsonData.data.forEach(function(role) {", "        pm.expect(role.isDisable).to.eql(false);", "        pm.expect(role.statusText).to.eql('启用');", "    });", "});"]}}]}, {"name": "4. 按状态筛选（停用）", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10,\n  \"isDisable\": true\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('只返回停用状态的角色', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    ", "    // 检查所有返回的角色都是停用状态", "    jsonData.data.forEach(function(role) {", "        pm.expect(role.isDisable).to.eql(true);", "        pm.expect(role.statusText).to.eql('停用');", "    });", "});"]}}]}, {"name": "5. 组合条件查询", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10,\n  \"roleName\": \"管理\",\n  \"isDisable\": false\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('组合条件查询正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    ", "    // 检查返回的角色同时满足两个条件", "    jsonData.data.forEach(function(role) {", "        pm.expect(role.roleName).to.include('管理');", "        pm.expect(role.isDisable).to.eql(false);", "    });", "});"]}}]}, {"name": "6. 分页测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 3\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('分页功能正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    ", "    // 检查返回的数据量不超过页大小", "    pm.expect(jsonData.data.length).to.be.at.most(3);", "    ", "    // 检查总数大于等于返回的数据量", "    pm.expect(jsonData.total).to.be.at.least(jsonData.data.length);", "});"]}}]}, {"name": "7. 空结果场景", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPage\": 1,\n  \"pageSize\": 10,\n  \"roleName\": \"不存在的角色名称\"\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('状态码为200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('空结果处理正确', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.eql(200);", "    pm.expect(jsonData.data).to.be.an('array');", "    pm.expect(jsonData.data.length).to.eql(0);", "    pm.expect(jsonData.total).to.eql(0);", "});"]}}]}, {"name": "8. 错误场景 - 缺少必填参数", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"roleName\": \"管理员\"\n}"}, "url": {"raw": "{{baseUrl}}/roles/getRoleList", "host": ["{{baseUrl}}"], "path": ["roles", "getRoleList"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('正确处理错误场景', function () {", "    // 这个测试预期会返回错误状态码", "    const jsonData = pm.response.json();", "    ", "    // 检查是否正确返回了错误", "    pm.expect(jsonData.code).to.not.eql(200);", "    pm.expect(jsonData.message).to.be.a('string');", "});"]}}]}]}