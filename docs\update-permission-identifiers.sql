-- 权限标识符格式更新脚本
-- 将现有的 module:action 格式更新为 module_object_operation 格式
-- 执行前请备份数据库！

-- ================================
-- 更新菜单权限标识符
-- ================================

-- 系统管理模块
UPDATE t_menu_permission SET permission_identifier = 'system_management_view' WHERE permission_identifier = 'system';
UPDATE t_menu_permission SET permission_identifier = 'system_user_view' WHERE permission_identifier = 'system:user:view';
UPDATE t_menu_permission SET permission_identifier = 'system_user_add' WHERE permission_identifier = 'system:user:add';
UPDATE t_menu_permission SET permission_identifier = 'system_user_edit' WHERE permission_identifier = 'system:user:edit';
UPDATE t_menu_permission SET permission_identifier = 'system_user_delete' WHERE permission_identifier = 'system:user:delete';
UPDATE t_menu_permission SET permission_identifier = 'system_config_view' WHERE permission_identifier = 'system:config:view';
UPDATE t_menu_permission SET permission_identifier = 'system_log_view' WHERE permission_identifier = 'system:log:view';
UPDATE t_menu_permission SET permission_identifier = 'system_setting_view' WHERE permission_identifier = 'system:setting';
UPDATE t_menu_permission SET permission_identifier = 'system_monitor_view' WHERE permission_identifier = 'system:monitor';
UPDATE t_menu_permission SET permission_identifier = 'system_log_view' WHERE permission_identifier = 'system:log';

-- 权限管理模块
UPDATE t_menu_permission SET permission_identifier = 'permission_management_view' WHERE permission_identifier = 'permission';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_view' WHERE permission_identifier = 'permission:role:view';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_add' WHERE permission_identifier = 'permission:role:add';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_edit' WHERE permission_identifier = 'permission:role:edit';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_delete' WHERE permission_identifier = 'permission:role:delete';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_assign' WHERE permission_identifier = 'permission:role:assign';
UPDATE t_menu_permission SET permission_identifier = 'permission_menu_view' WHERE permission_identifier = 'permission:menu:view';
UPDATE t_menu_permission SET permission_identifier = 'permission_menu_add' WHERE permission_identifier = 'permission:menu:add';
UPDATE t_menu_permission SET permission_identifier = 'permission_menu_edit' WHERE permission_identifier = 'permission:menu:edit';
UPDATE t_menu_permission SET permission_identifier = 'permission_menu_delete' WHERE permission_identifier = 'permission:menu:delete';
UPDATE t_menu_permission SET permission_identifier = 'permission_data_view' WHERE permission_identifier = 'permission:data:view';
UPDATE t_menu_permission SET permission_identifier = 'permission_data_config' WHERE permission_identifier = 'permission:data:config';
UPDATE t_menu_permission SET permission_identifier = 'permission_role_view' WHERE permission_identifier = 'permission:role';
UPDATE t_menu_permission SET permission_identifier = 'permission_menu_view' WHERE permission_identifier = 'permission:menu';
UPDATE t_menu_permission SET permission_identifier = 'permission_dept_view' WHERE permission_identifier = 'permission:dept';

-- 业务管理模块
UPDATE t_menu_permission SET permission_identifier = 'business_management_view' WHERE permission_identifier = 'business';
UPDATE t_menu_permission SET permission_identifier = 'business_order_view' WHERE permission_identifier = 'business:order:view';
UPDATE t_menu_permission SET permission_identifier = 'business_order_add' WHERE permission_identifier = 'business:order:add';
UPDATE t_menu_permission SET permission_identifier = 'business_order_edit' WHERE permission_identifier = 'business:order:edit';
UPDATE t_menu_permission SET permission_identifier = 'business_order_delete' WHERE permission_identifier = 'business:order:delete';
UPDATE t_menu_permission SET permission_identifier = 'business_customer_view' WHERE permission_identifier = 'business:customer:view';
UPDATE t_menu_permission SET permission_identifier = 'business_customer_add' WHERE permission_identifier = 'business:customer:add';
UPDATE t_menu_permission SET permission_identifier = 'business_product_view' WHERE permission_identifier = 'business:product:view';

-- 报表中心模块
UPDATE t_menu_permission SET permission_identifier = 'report_center_view' WHERE permission_identifier = 'report';
UPDATE t_menu_permission SET permission_identifier = 'report_sales_view' WHERE permission_identifier = 'report:sales:view';
UPDATE t_menu_permission SET permission_identifier = 'report_sales_export' WHERE permission_identifier = 'report:sales:export';
UPDATE t_menu_permission SET permission_identifier = 'report_finance_view' WHERE permission_identifier = 'report:finance:view';
UPDATE t_menu_permission SET permission_identifier = 'report_finance_export' WHERE permission_identifier = 'report:finance:export';

-- 监控中心模块
UPDATE t_menu_permission SET permission_identifier = 'monitor_center_view' WHERE permission_identifier = 'monitor';
UPDATE t_menu_permission SET permission_identifier = 'monitor_system_view' WHERE permission_identifier = 'monitor:system:view';
UPDATE t_menu_permission SET permission_identifier = 'monitor_log_view' WHERE permission_identifier = 'monitor:log:view';
UPDATE t_menu_permission SET permission_identifier = 'monitor_performance_view' WHERE permission_identifier = 'monitor:performance:view';

-- 用户管理模块（test-data.sql中的数据）
UPDATE t_menu_permission SET permission_identifier = 'user_list_view' WHERE permission_identifier = 'user:list';
UPDATE t_menu_permission SET permission_identifier = 'user_item_add' WHERE permission_identifier = 'user:add';
UPDATE t_menu_permission SET permission_identifier = 'user_item_edit' WHERE permission_identifier = 'user:edit';
UPDATE t_menu_permission SET permission_identifier = 'user_item_delete' WHERE permission_identifier = 'user:delete';

-- 数据管理模块（test-data.sql中的数据）
UPDATE t_menu_permission SET permission_identifier = 'data_permission_view' WHERE permission_identifier = 'data:permission';
UPDATE t_menu_permission SET permission_identifier = 'data_import_execute' WHERE permission_identifier = 'data:import';
UPDATE t_menu_permission SET permission_identifier = 'data_export_execute' WHERE permission_identifier = 'data:export';

-- ================================
-- 验证更新结果
-- ================================

-- 查看更新后的权限标识符
SELECT id, name, permission_identifier
FROM t_menu_permission
WHERE is_del = false
ORDER BY id;

-- 检查是否还有旧格式的权限标识符
SELECT id, name, permission_identifier
FROM t_menu_permission
WHERE permission_identifier LIKE '%:%'
AND is_del = false;

-- 统计权限标识符格式
SELECT
    CASE
        WHEN permission_identifier LIKE '%_%_%' THEN '新格式(module_object_operation)'
        WHEN permission_identifier LIKE '%:%' THEN '旧格式(module:action)'
        ELSE '其他格式'
    END as format_type,
    COUNT(*) as count
FROM t_menu_permission
WHERE is_del = false
GROUP BY
    CASE
        WHEN permission_identifier LIKE '%_%_%' THEN '新格式(module_object_operation)'
        WHEN permission_identifier LIKE '%:%' THEN '旧格式(module:action)'
        ELSE '其他格式'
    END;

-- ================================
-- 更新数据权限标识符
-- ================================

-- 用户数据权限
UPDATE t_data_permission SET data_identifier = 'user_data_view' WHERE data_identifier = 'user_basic_data';
UPDATE t_data_permission SET data_identifier = 'user_profile_view' WHERE data_identifier = 'user_profile_data';
UPDATE t_data_permission SET data_identifier = 'user_auth_view' WHERE data_identifier = 'user_auth_data';
UPDATE t_data_permission SET data_identifier = 'user_data_view' WHERE data_identifier = 'user_view';
UPDATE t_data_permission SET data_identifier = 'user_data_add' WHERE data_identifier = 'user_add';
UPDATE t_data_permission SET data_identifier = 'user_data_edit' WHERE data_identifier = 'user_edit';

-- 订单数据权限
UPDATE t_data_permission SET data_identifier = 'order_data_view' WHERE data_identifier = 'order_basic_data';
UPDATE t_data_permission SET data_identifier = 'order_detail_view' WHERE data_identifier = 'order_detail_data';
UPDATE t_data_permission SET data_identifier = 'order_payment_view' WHERE data_identifier = 'order_payment_data';

-- 产品数据权限
UPDATE t_data_permission SET data_identifier = 'product_data_view' WHERE data_identifier = 'product_basic_data';
UPDATE t_data_permission SET data_identifier = 'product_inventory_view' WHERE data_identifier = 'product_inventory_data';

-- 部门数据权限
UPDATE t_data_permission SET data_identifier = 'dept_data_view' WHERE data_identifier = 'dept_view';
UPDATE t_data_permission SET data_identifier = 'dept_data_manage' WHERE data_identifier = 'dept_manage';

-- 财务数据权限
UPDATE t_data_permission SET data_identifier = 'finance_data_view' WHERE data_identifier = 'finance_view';
UPDATE t_data_permission SET data_identifier = 'finance_data_export' WHERE data_identifier = 'finance_export';

-- ================================
-- 验证数据权限更新结果
-- ================================

-- 查看更新后的数据权限标识符
SELECT id, name, data_identifier
FROM t_data_permission
WHERE is_del = false
ORDER BY id;

-- 检查是否还有旧格式的数据权限标识符
SELECT id, name, data_identifier
FROM t_data_permission
WHERE data_identifier NOT LIKE '%_%_%'
AND is_del = false;

-- 统计数据权限标识符格式
SELECT
    CASE
        WHEN data_identifier LIKE '%_%_%' THEN '新格式(module_object_operation)'
        ELSE '其他格式'
    END as format_type,
    COUNT(*) as count
FROM t_data_permission
WHERE is_del = false
GROUP BY
    CASE
        WHEN data_identifier LIKE '%_%_%' THEN '新格式(module_object_operation)'
        ELSE '其他格式'
    END;
