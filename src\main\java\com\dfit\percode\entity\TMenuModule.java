package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 菜单模块信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_menu_module")
@ApiModel(value = "TMenuModule对象", description = "菜单模块信息表")
public class TMenuModule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("模块标志，用于区分模块的内部标识")
    private String moduleIdentifier;

    private Integer orderInfo;

    private Integer isDel;

    private String createTime;

    private String modifyTime;
}
