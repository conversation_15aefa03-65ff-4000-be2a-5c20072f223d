package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 角色数据权限对应表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Getter
@Setter
@TableName("t_roles_data_permission")
@ApiModel(value = "TRolesDataPermission对象", description = "角色数据权限对应表")
public class TRolesDataPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("模块标识符")
    private String moduleIdentifier;

    @ApiModelProperty("数据类型，1检签")
    private Integer dataType;

    @ApiModelProperty("数据id，对应数据权限管理列表中的id")
    private Long dataId;

    @ApiModelProperty("操作类型1与数据权限操作类型中operate_type一致")
    private Integer operateType;

    @ApiModelProperty("数据id与操作类型通过.拼接，格式：数据id.操作类型")
    private String dataOperateId;

    @ApiModelProperty("是否删除，默认为false")
    private Boolean isDel;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;
}
