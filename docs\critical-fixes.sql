-- =====================================================
-- 🚨 紧急修复：部署前必须执行的关键修复
-- =====================================================

-- 1. 验证数据库中的根部门数据
SELECT id, organ_name, pre_id, is_del FROM t_org_structure WHERE pre_id = 0;
-- 预期：应该返回 5001 科技集团总公司

-- 2. 验证用户表数据
SELECT COUNT(*) FROM t_user WHERE is_del = false;
-- 预期：应该返回 12

-- 3. 验证用户表字段类型
SELECT id, user_name, organ_affiliation, is_del FROM t_user WHERE is_del = false LIMIT 5;
-- 预期：is_del 应该是 boolean 类型

-- 4. 验证部门用户关联
SELECT 
    u.id, 
    u.user_name, 
    u.organ_affiliation,
    o.organ_name
FROM t_user u
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
WHERE u.is_del = false
ORDER BY u.organ_affiliation
LIMIT 10;
-- 预期：所有用户都应该有对应的部门

-- =====================================================
-- 如果上述查询有问题，说明需要修复代码中的SQL
-- =====================================================

/*
发现的问题：

1. TOrgStructureMapper.java 第35行：
   错误：where pre_id = '0' 
   正确：where pre_id = 0

2. TOrgStructureMapper.java 第43行：
   错误：from t_kb_user tku where ... and is_del = '0'
   正确：from t_user tku where ... and is_del = false

3. TOrgStructureMapper.java 第253行：
   错误：WHERE pre_id IS NULL
   正确：WHERE (pre_id = 0 OR pre_id IS NULL)

这些问题会导致：
- 部门树查询返回空
- 用户查询失败
- 接口报错或返回空数据
*/
