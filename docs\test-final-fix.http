### 测试最终修复的 /users/getAllUsers 接口
### 使用原始查询方法 + 修复的根部门查询条件 + 数据库索引

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试修复后的 getAllUsers 接口
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 预期结果：
### 1. 接口正常返回部门树结构
### 2. 响应时间显著提升（应该在几秒内，不再是50+秒）
### 3. 日志显示"使用原始查询方法（已优化根部门查询条件）"
### 4. 返回完整的部门和用户数据

### 成功的响应格式：
/*
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": 5001,
      "organName": "科技集团总公司",
      "children": [
        {
          "id": 6001,
          "userName": "张总经理"
        },
        {
          "id": 5002,
          "organName": "技术研发中心",
          "children": [
            {
              "id": 6002,
              "userName": "李技术总监"
            },
            {
              "id": 5007,
              "organName": "前端开发部",
              "children": [
                {
                  "id": 6004,
                  "userName": "赵前端经理"
                },
                {
                  "id": 6007,
                  "userName": "周前端开发"
                }
              ]
            },
            {
              "id": 5008,
              "organName": "后端开发部",
              "children": [
                {
                  "id": 6005,
                  "userName": "钱后端经理"
                },
                {
                  "id": 6008,
                  "userName": "吴后端开发"
                }
              ]
            },
            {
              "id": 5009,
              "organName": "测试质量部",
              "children": [
                {
                  "id": 6006,
                  "userName": "孙测试经理"
                },
                {
                  "id": 6009,
                  "userName": "郑测试工程师"
                }
              ]
            },
            {
              "id": 5010,
              "organName": "运维安全部",
              "children": [
                {
                  "id": 6010,
                  "userName": "王运维工程师"
                }
              ]
            }
          ]
        },
        {
          "id": 5003,
          "organName": "产品运营中心",
          "children": [
            {
              "id": 6003,
              "userName": "王产品总监"
            },
            {
              "id": 5013,
              "organName": "数据分析部",
              "children": [
                {
                  "id": 6012,
                  "userName": "陈实习生"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
*/

### 2. 如果还有性能问题，请执行数据库索引
### 在数据库中执行以下SQL：

/*
-- 创建关键索引（如果还没有创建）
CREATE INDEX idx_user_organ_affiliation ON t_user(organ_affiliation);
CREATE INDEX idx_org_structure_pre_id ON t_org_structure(pre_id);
*/

### 3. 性能对比
### 修复前：50+ 秒
### 修复后：应该在 5 秒以内

### 4. 问题解决总结：
### ✅ 修复根部门查询条件：(pre_id = 0 OR pre_id IS NULL)
### ✅ 添加数据库索引：提升查询性能
### ✅ 使用稳定的原始查询方法：避免复杂的优化逻辑
### ✅ 保留异常处理和日志记录

### 5. 如果接口正常工作，可以考虑后续优化：
### - 添加缓存机制（Redis）
### - 实现增量更新
### - 前端分页加载
### - 异步数据加载

### 注意事项：
### 1. 确保数据库索引已创建
### 2. 重启应用确保代码修改生效
### 3. 观察响应时间和日志输出
### 4. 验证返回的数据结构是否正确
