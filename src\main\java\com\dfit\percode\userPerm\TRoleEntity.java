package com.dfit.percode.userPerm;

import lombok.Data;

/**
 * 角色实体类（旧版本格式）
 * 用于兼容旧的角色管理接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class TRoleEntity {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 是否停用（字符串格式）
     */
    private String isDisable;
    
    /**
     * 排序序号
     */
    private Integer orderInfo;
    
    /**
     * 创建时间
     */
    private String createTime;
    
    /**
     * 修改时间
     */
    private String modifyTime;
}
