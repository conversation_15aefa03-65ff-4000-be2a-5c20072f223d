<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dfit.percode.mapper.TDataModuleMapper">

    <!-- 分页查询数据模块列表 -->
    <select id="getDataModuleList" parameterType="com.dfit.percode.vo.DataModuleListRequestVO"
            resultType="com.dfit.percode.vo.DataModuleListResponseVO">
        SELECT
            id,
            module_name as moduleName,
            module_identifier as moduleIdentifier,
            order_info as orderInfo,
            create_time as createTime,
            modify_time as modifyTime
        FROM t_data_module
        WHERE is_del = false
        ORDER BY order_info ASC, create_time DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{pageNum}
        </if>
    </select>

    <!-- 获取数据模块总数 -->
    <select id="getDataModuleTotal" parameterType="com.dfit.percode.vo.DataModuleListRequestVO"
            resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_data_module
        WHERE is_del = false
    </select>

</mapper>
