package com.dfit.percode.common;

public class SecurityConstants {
    // 统一的鉴权白名单配置
    // 所有拦截器都使用此配置，确保一致性
    public static final String[] WHITE_LIST = {
            // 认证相关接口
            "/auth/login",            // 登录接口

            // 用户管理相关接口（供外部系统调用）
            "/users/getUserList",     // 用户列表查询接口
            "/users/list-by-org",     // 按组织查询用户列表接口
            "/users/getUserDetail",   // 用户详情查询接口（支持单个和批量查询）

            // 组织架构相关接口（供外部系统调用）
            "/org-structure/tree",    // 组织架构树接口

            // 管理相关接口
            "/manage/**",             // 管理相关接口白名单

            // 文档和API相关
            "/swagger-ui/**",         // Swagger UI
            "/swagger-ui.html",       // Swagger UI首页
            "/doc.html",              // API文档页面
            "/swagger-resources/**",  // Swagger资源
            "/v2/api-docs",           // API文档v2
            "/v3/api-docs",           // API文档v3

            // 静态资源
            "/webjars/**",            // 静态资源
            "/favicon.ico",           // 网站图标

            // 文件上传相关
            "/uploadFile/apiUploadFiles",     // 文件上传接口
            "/uploadFile/upload-byte-stream", // 字节流上传接口

            // 系统相关
            "/error"                  // 错误页面

            // 备用配置（注释保留）
//            "/employee/login",
//            "/api/auth/register",
//            "/api/auth/captcha",
//            "/api/auth/logout",
//            "/api/auth/refresh",
//            "/camunda",
//            "/leave",
//            "/file"
    };
}
