package com.dfit.percode.service.impl;

import com.dfit.percode.mapper.MenuModuleMapper;
import com.dfit.percode.service.IMenuModuleService;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.vo.AddMenuModuleRequestVO;
import com.dfit.percode.vo.AddMenuRequestVO;
import com.dfit.percode.vo.DeleteMenuModuleRequestVO;
import com.dfit.percode.vo.DeleteMenuRequestVO;
import com.dfit.percode.vo.MenuDetailRequestVO;
import com.dfit.percode.vo.MenuDetailResponseVO;
import com.dfit.percode.vo.MenuListRequestVO;
import com.dfit.percode.vo.MenuModuleListResponseVO;
import com.dfit.percode.vo.MenuTreeResponseVO;
import com.dfit.percode.vo.ModuleInfoVO;
import com.dfit.percode.vo.ModuleMenuTreeResponseVO;
import com.dfit.percode.vo.UpdateMenuRequestVO;
import com.dfit.percode.vo.CheckIdentifierRequestVO;
import com.dfit.percode.vo.CheckIdentifierResponseVO;
import com.dfit.percode.vo.response.RoleUsageVO;
import com.dfit.percode.vo.response.UsageInfoVO;
import com.dfit.percode.vo.response.UsageCheckResponseVO;
import com.dfit.percode.exception.UsageConflictException;
import com.dfit.percode.util.IdentifierValidator;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 菜单模块相关服务实现类
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class MenuModuleServiceImpl implements IMenuModuleService {

    @Autowired
    private MenuModuleMapper menuModuleMapper;

    /**
     * 新增菜单模块
     * 按照前端弹窗设计实现
     *
     * @param request 新增菜单模块请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMenuModule(AddMenuModuleRequestVO request) {
        log.info("开始新增菜单模块");
        log.info("模块名称: {}, 模块标识: {}, 排序: {}",
                request.getModuleName(), request.getModuleIdentifier(), request.getOrderInfo());

        // 1. 检查模块标识是否已存在
        int existsCount = menuModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (existsCount > 0) {
            log.error("模块标识已存在: {}", request.getModuleIdentifier());
            throw new RuntimeException("模块标识已存在，请使用其他标识");
        }

        // 2. 生成ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long moduleId = idGenerator.generateId();

        // 3. 插入菜单模块记录
        menuModuleMapper.insertMenuModule(moduleId, request.getModuleName(),
                request.getModuleIdentifier(), request.getOrderInfo());

        log.info("菜单模块新增成功，ID: {}", moduleId);
    }

    /**
     * 获取菜单模块列表
     * 返回所有未删除的菜单模块，按排序字段排序
     *
     * @return 菜单模块列表
     */
    @Override
    public List<MenuModuleListResponseVO> getMenuModules() {
        log.info("开始获取菜单模块列表");

        // 调用Mapper获取菜单模块列表
        List<MenuModuleListResponseVO> moduleList = menuModuleMapper.getMenuModules();

        log.info("菜单模块列表查询完成，共{}条记录", moduleList.size());
        return moduleList;
    }

    /**
     * 删除菜单模块
     * 逻辑删除菜单模块，设置is_del为true
     *
     * @param request 删除菜单模块请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenuModule(DeleteMenuModuleRequestVO request) {
        log.info("开始删除菜单模块");
        log.info("模块标识: {}", request.getModuleIdentifier());

        // 1. 检查模块是否存在
        int existsCount = menuModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (existsCount == 0) {
            log.error("菜单模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("菜单模块不存在");
        }

        // 2. 检查模块是否已被菜单权限使用
        int usedCount = menuModuleMapper.checkModuleUsedByPermissions(request.getModuleIdentifier());
        if (usedCount > 0) {
            log.error("菜单模块正在被使用，无法删除，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("该模块正在被菜单权限使用，无法删除");
        }

        // 3. 逻辑删除菜单模块
        menuModuleMapper.deleteMenuModuleByIdentifier(request.getModuleIdentifier());

        log.info("菜单模块删除成功，模块标识: {}", request.getModuleIdentifier());
    }

    /**
     * 新增菜单
     * 在指定模块下新增菜单项，支持目录、菜单、按钮三种类型
     *
     * @param request 新增菜单请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMenu(AddMenuRequestVO request) {
        log.info("开始新增菜单");
        log.info("菜单名称: {}, 模块标识: {}, 菜单类型: {}",
                request.getName(), request.getModuleIdentifier(), request.getMenuType());

        // 1. 验证模块是否存在
        int moduleExists = menuModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (moduleExists == 0) {
            log.error("模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("指定的模块不存在");
        }

        // 2. 验证父级菜单是否存在（如果不是根节点）
        if (request.getPreId() != null && request.getPreId() > 0) {
            int parentExists = menuModuleMapper.checkMenuExists(request.getPreId());
            if (parentExists == 0) {
                log.error("父级菜单不存在，父级ID: {}", request.getPreId());
                throw new RuntimeException("指定的父级菜单不存在");
            }
        }

        // 3. 生成ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long menuId = idGenerator.generateId();

        // 4. 插入菜单记录
        menuModuleMapper.insertMenu(
            menuId,
            request.getName(),
            request.getPreId() != null ? request.getPreId() : 0L,
            request.getModuleIdentifier(),
            request.getOrderInfo(),
            request.getIsDisable() != null ? request.getIsDisable() : false,
            request.getMenuType(),
            request.getRouteAddress(),
            request.getComponentPath(),
            request.getPermissionIdentifier(),
            request.getRouteParam()
        );

        log.info("菜单新增成功，ID: {}", menuId);
    }

    /**
     * 获取菜单详情
     * 根据菜单ID获取菜单的详细信息，用于编辑表单回显
     *
     * @param request 获取菜单详情请求参数
     * @return 菜单详情数据
     */
    @Override
    public MenuDetailResponseVO getMenuDetail(MenuDetailRequestVO request) {
        log.info("开始获取菜单详情，菜单ID: {}", request.getMenuId());

        // 1. 验证菜单ID是否为空
        if (request.getMenuId() == null) {
            log.error("菜单ID不能为空");
            throw new RuntimeException("菜单ID不能为空");
        }

        // 2. 查询菜单详情
        MenuDetailResponseVO menuDetail = menuModuleMapper.getMenuDetailById(request.getMenuId());

        // 3. 验证菜单是否存在
        if (menuDetail == null) {
            log.error("菜单不存在，菜单ID: {}", request.getMenuId());
            throw new RuntimeException("菜单不存在");
        }

        log.info("菜单详情获取成功，菜单名称: {}", menuDetail.getName());
        return menuDetail;
    }

    /**
     * 查询菜单列表
     * 返回树形结构的菜单数据，支持按模块筛选
     *
     * @param request 查询菜单列表请求参数
     * @return 菜单树形结构数据
     */
    @Override
    public List<MenuTreeResponseVO> getMenus(MenuListRequestVO request) {
        log.info("开始查询菜单列表");
        log.info("模块标识: {}, 菜单名称: {}, 包含禁用菜单: {}",
                request.getModuleIdentifier(), request.getName(), request.getIsDisable());

        // 1. 查询根节点菜单（pre_id = 0）
        List<MenuTreeResponseVO> rootMenus = menuModuleMapper.getRootMenus(
            request.getModuleIdentifier(),
            request.getName(),
            request.getIsDisable()
        );

        // 2. 递归构建子菜单树
        for (MenuTreeResponseVO rootMenu : rootMenus) {
            buildMenuTree(rootMenu, request.getModuleIdentifier(), request.getName(), request.getIsDisable());
        }

        log.info("菜单列表查询成功，根节点数量: {}", rootMenus.size());
        return rootMenus;
    }

    /**
     * 递归构建菜单树
     * 为每个菜单节点查找并设置子菜单
     *
     * @param parentMenu 父级菜单
     * @param moduleIdentifier 模块标识
     * @param menuName 菜单名称
     * @param includeDisabled 是否包含禁用菜单
     */
    private void buildMenuTree(MenuTreeResponseVO parentMenu, String moduleIdentifier, String menuName, Boolean includeDisabled) {
        // 查询子菜单
        List<MenuTreeResponseVO> children = menuModuleMapper.getChildMenus(
            parentMenu.getId(),
            moduleIdentifier,
            menuName,
            includeDisabled
        );

        if (!children.isEmpty()) {
            // 递归构建子菜单的子菜单
            for (MenuTreeResponseVO child : children) {
                buildMenuTree(child, moduleIdentifier, menuName, includeDisabled);
            }
            parentMenu.setChildren(children);
        }
    }

    /**
     * 查询菜单列表（优化版本）
     * 使用一次性查询+内存构建替代递归查询，大幅提升性能
     * 参考TOrgStructureServiceImpl的buildOptimizedOrgTree实现模式
     *
     * @param request 查询菜单列表请求参数
     * @return 菜单树形结构数据
     */
    @Override
    public List<MenuTreeResponseVO> getMenusOptimized(MenuListRequestVO request) {
        log.info("开始查询菜单列表（优化版本）");
        log.info("模块标识: {}, 菜单名称: {}, 包含禁用菜单: {}",
                request.getModuleIdentifier(), request.getName(), request.getIsDisable());
        long startTime = System.currentTimeMillis();

        try {
            // 第1步：一次性查询所有符合条件的菜单数据
            log.debug("第1步：一次性查询所有符合条件的菜单数据");
            List<MenuTreeResponseVO> allMenus = menuModuleMapper.getAllMenusForTree(
                request.getModuleIdentifier(),
                request.getName(),
                request.getIsDisable()
            );
            log.info("查询到菜单总数: {}", allMenus.size());

            if (allMenus.isEmpty()) {
                log.info("没有找到任何菜单，返回空列表");
                return allMenus;
            }

            // 第2步：在内存中构建父子关系映射
            log.debug("第2步：在内存中构建父子关系映射");
            Map<Long, List<MenuTreeResponseVO>> parentChildMap = new HashMap<>();
            Map<Long, MenuTreeResponseVO> menuMap = new HashMap<>();

            // 构建映射关系
            for (MenuTreeResponseVO menu : allMenus) {
                menuMap.put(menu.getId(), menu);

                Long parentId = menu.getPreId();
                parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
            }
            log.debug("父子关系映射构建完成，父节点数量: {}", parentChildMap.size());

            // 第3步：找出根节点（pre_id = 0）
            log.debug("第3步：找出根节点");
            List<MenuTreeResponseVO> rootMenus = parentChildMap.get(0L);
            if (rootMenus == null) {
                rootMenus = new ArrayList<>();
            }
            log.debug("根节点数量: {}", rootMenus.size());

            // 第4步：使用递归算法在内存中构建树形结构
            log.debug("第4步：在内存中构建树形结构");
            for (MenuTreeResponseVO rootMenu : rootMenus) {
                buildOptimizedMenuTree(rootMenu, parentChildMap);
            }

            long endTime = System.currentTimeMillis();
            log.info("菜单列表查询成功（优化版本），根节点数量: {}，耗时: {}ms，预计性能提升: 90%+",
                    rootMenus.size(), (endTime - startTime));

            return rootMenus;

        } catch (Exception e) {
            log.error("查询菜单列表失败（优化版本）", e);
            throw new RuntimeException("查询菜单列表失败（优化版本）: " + e.getMessage(), e);
        }
    }

    /**
     * 优化的递归构建菜单树形结构（内存操作，无数据库查询）
     * 使用预构建的映射关系，避免重复数据库查询
     * 参考TOrgStructureServiceImpl的buildOptimizedOrgTree实现模式
     *
     * @param parentMenu 父菜单节点
     * @param parentChildMap 父子关系映射
     */
    private void buildOptimizedMenuTree(MenuTreeResponseVO parentMenu,
                                       Map<Long, List<MenuTreeResponseVO>> parentChildMap) {
        // 获取子菜单列表
        List<MenuTreeResponseVO> children = parentChildMap.get(parentMenu.getId());

        if (children != null && !children.isEmpty()) {
            // 递归构建子菜单树
            for (MenuTreeResponseVO child : children) {
                buildOptimizedMenuTree(child, parentChildMap);
            }
            parentMenu.setChildren(children);
        }
    }

    /**
     * 查询菜单列表（模块级优化版本）
     * 返回按模块分组的菜单树结构，外层为模块信息，内层保持现有菜单树结构不变
     *
     * @param request 查询菜单列表请求参数
     * @return 模块级菜单树结构数据
     */
    public List<ModuleMenuTreeResponseVO> getMenusOptimizedWithModules(MenuListRequestVO request) {
        log.info("开始查询菜单列表（模块级优化版本）");
        log.info("模块标识: {}, 菜单名称: {}, 包含禁用菜单: {}",
                request.getModuleIdentifier(), request.getName(), request.getIsDisable());
        long startTime = System.currentTimeMillis();

        try {
            List<ModuleMenuTreeResponseVO> result = new ArrayList<>();

            // 第1步：查询所有模块
            log.debug("第1步：查询所有模块");
            List<ModuleInfoVO> allModules = menuModuleMapper.findAllModules();
            log.info("查询到模块总数: {}", allModules.size());

            // 第2步：为每个模块构建菜单树
            for (ModuleInfoVO module : allModules) {
                // 如果指定了模块标识，只处理该模块
                if (request.getModuleIdentifier() != null && !request.getModuleIdentifier().isEmpty()
                    && !module.getModuleIdentifier().equals(request.getModuleIdentifier())) {
                    continue;
                }

                log.debug("处理模块: {} - {}", module.getModuleIdentifier(), module.getModuleName());

                // 第3步：查询该模块下的所有菜单
                List<MenuTreeResponseVO> allMenus = menuModuleMapper.getAllMenusForTree(
                    module.getModuleIdentifier(),
                    request.getName(),
                    request.getIsDisable()
                );

                if (!allMenus.isEmpty()) {
                    // 第4步：在内存中构建父子关系映射
                    Map<Long, List<MenuTreeResponseVO>> parentChildMap = new HashMap<>();
                    for (MenuTreeResponseVO menu : allMenus) {
                        Long parentId = menu.getPreId();
                        parentChildMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(menu);
                    }

                    // 第5步：找出根节点（pre_id = 0）
                    List<MenuTreeResponseVO> rootMenus = parentChildMap.get(0L);
                    if (rootMenus == null) {
                        rootMenus = new ArrayList<>();
                    }

                    // 第6步：构建树形结构
                    for (MenuTreeResponseVO rootMenu : rootMenus) {
                        buildOptimizedMenuTree(rootMenu, parentChildMap);
                    }

                    // 第7步：创建模块包装器
                    ModuleMenuTreeResponseVO moduleWrapper = ModuleMenuTreeResponseVO.create(
                        module.getModuleIdentifier(),
                        module.getModuleName(),
                        module.getOrderInfo(),
                        rootMenus
                    );

                    result.add(moduleWrapper);
                    log.debug("模块 {} 处理完成，菜单数量: {}", module.getModuleName(), rootMenus.size());
                }
            }

            long endTime = System.currentTimeMillis();
            log.info("菜单列表查询成功（模块级优化版本），模块数量: {}，耗时: {}ms",
                    result.size(), (endTime - startTime));

            return result;

        } catch (Exception e) {
            log.error("查询菜单列表失败（模块级优化版本）", e);
            throw new RuntimeException("查询菜单列表失败（模块级优化版本）: " + e.getMessage(), e);
        }
    }

    /**
     * 修改菜单
     * 支持修改菜单的所有字段，包括父级关系变更
     * 包含完整的数据验证和层级关系检查
     *
     * @param request 修改菜单请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMenu(UpdateMenuRequestVO request) {
        log.info("开始修改菜单");
        log.info("菜单ID: {}, 菜单名称: {}, 父级ID: {}, 模块标识: {}",
                request.getId(), request.getName(), request.getPreId(), request.getModuleIdentifier());

        // 1. 验证菜单是否存在
        int menuExists = menuModuleMapper.checkMenuExists(request.getId());
        if (menuExists == 0) {
            log.error("菜单不存在，菜单ID: {}", request.getId());
            throw new RuntimeException("指定的菜单不存在");
        }

        // 2. 验证模块是否存在
        int moduleExists = menuModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (moduleExists == 0) {
            log.error("模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("指定的模块不存在");
        }

        // 3. 验证父级菜单关系
        if (request.getPreId() != null && request.getPreId() > 0) {
            // 检查父级菜单是否存在
            int parentExists = menuModuleMapper.checkMenuExists(request.getPreId());
            if (parentExists == 0) {
                log.error("父级菜单不存在，父级ID: {}", request.getPreId());
                throw new RuntimeException("指定的父级菜单不存在");
            }

            // 检查是否会形成循环引用（不能将自己设置为自己的父级或子级）
            if (request.getPreId().equals(request.getId())) {
                log.error("不能将菜单设置为自己的父级，菜单ID: {}", request.getId());
                throw new RuntimeException("不能将菜单设置为自己的父级");
            }

            // 检查是否会将父级设置为自己的子级（防止循环引用）
            if (isDescendant(request.getId(), request.getPreId())) {
                log.error("不能将菜单的子级设置为父级，会形成循环引用，菜单ID: {}, 父级ID: {}",
                        request.getId(), request.getPreId());
                throw new RuntimeException("不能将菜单的子级设置为父级，会形成循环引用");
            }
        }

        // 4. 执行更新操作
        menuModuleMapper.updateMenu(
            request.getId(),
            request.getName(),
            request.getPreId() != null ? request.getPreId() : 0L,
            request.getModuleIdentifier(),
            request.getOrderInfo(),
            request.getIsDisable() != null ? request.getIsDisable() : false,
            request.getMenuType(),
            request.getRouteAddress(),
            request.getComponentPath(),
            request.getPermissionIdentifier(),
            request.getRouteParam()
        );

        log.info("菜单修改成功，菜单ID: {}", request.getId());
    }

    /**
     * 删除菜单
     * 逻辑删除菜单项，设置is_del为true
     * 包含子菜单检查，防止删除有子菜单的父级菜单
     *
     * @param request 删除菜单请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMenu(DeleteMenuRequestVO request) {
        log.info("开始删除菜单");
        log.info("菜单ID: {}", request.getId());

        // 1. 验证菜单是否存在
        int menuExists = menuModuleMapper.checkMenuExists(request.getId());
        if (menuExists == 0) {
            log.error("菜单不存在，菜单ID: {}", request.getId());
            throw new RuntimeException("指定的菜单不存在");
        }

        // 2. 检查是否有子菜单
        List<Long> childIds = menuModuleMapper.getChildMenuIds(request.getId());
        if (!childIds.isEmpty()) {
            log.error("菜单存在子菜单，无法删除，菜单ID: {}, 子菜单数量: {}",
                     request.getId(), childIds.size());
            throw new RuntimeException("该菜单存在子菜单，请先删除子菜单");
        }

        // 3. 检查菜单是否已被角色权限使用
        int usedCount = menuModuleMapper.checkMenuUsedByRoles(request.getId());
        if (usedCount > 0) {
            log.error("菜单正在被角色权限使用，无法删除，菜单ID: {}", request.getId());
            throw new RuntimeException("该菜单正在被角色权限使用，无法删除");
        }

        // 4. 逻辑删除菜单
        menuModuleMapper.deleteMenuById(request.getId());

        log.info("菜单删除成功，菜单ID: {}", request.getId());
    }

    /**
     * 检查目标菜单是否是指定菜单的子级（递归检查）
     * 用于防止在修改父级关系时形成循环引用
     *
     * @param parentId 父级菜单ID
     * @param targetId 目标菜单ID
     * @return 如果目标菜单是父级菜单的子级则返回true
     */
    private boolean isDescendant(Long parentId, Long targetId) {
        // 查询父级菜单的所有直接子菜单
        List<Long> childIds = menuModuleMapper.getChildMenuIds(parentId);

        for (Long childId : childIds) {
            // 如果直接子菜单中包含目标菜单，则目标菜单是父级的子级
            if (childId.equals(targetId)) {
                return true;
            }
            // 递归检查子菜单的子菜单
            if (isDescendant(childId, targetId)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查权限标识符是否重复
     * 支持菜单权限和数据权限的标识符重复检测
     * 包含完整的输入验证和格式检查
     *
     * @param request 检查权限标识符请求参数
     * @return 检查结果
     */
    @Override
    public CheckIdentifierResponseVO checkIdentifier(CheckIdentifierRequestVO request) {
        log.info("开始检查权限标识符重复");
        log.info("标识符: {}, 类型: {}, 排除ID: {}",
                request.getIdentifier(), request.getType(), request.getExcludeId());

        try {
            // 1. 输入预处理和基本验证
            String cleanIdentifier = IdentifierValidator.cleanIdentifier(request.getIdentifier());

            // 检查是否为空
            if (IdentifierValidator.isEmpty(cleanIdentifier)) {
                return CheckIdentifierResponseVO.validationError(
                    "权限标识符不能为空",
                    IdentifierValidator.getFormatRules(request.getType())
                );
            }

            // 检查长度
            if (cleanIdentifier.length() > 100) {
                return CheckIdentifierResponseVO.validationError(
                    "权限标识符长度不能超过100个字符",
                    IdentifierValidator.getFormatRules(request.getType())
                );
            }

            // 2. 格式验证
            if (!IdentifierValidator.isValidFormat(cleanIdentifier, request.getType())) {
                String suggestion = IdentifierValidator.generateSuggestion(cleanIdentifier, request.getType());
                CheckIdentifierResponseVO response = CheckIdentifierResponseVO.validationError(
                    "权限标识符格式不正确",
                    IdentifierValidator.getFormatRules(request.getType())
                );
                response.setSuggestion(suggestion);
                return response;
            }

            // 3. 重复检测
            boolean isDuplicate = false;
            if ("menu".equals(request.getType())) {
                int count = menuModuleMapper.checkMenuPermissionIdentifierExists(
                    cleanIdentifier, request.getExcludeId());
                log.info("菜单权限重复检测结果 - 标识符: {}, 排除ID: {}, 重复数量: {}",
                    cleanIdentifier, request.getExcludeId(), count);
                isDuplicate = count > 0;
            } else if ("data".equals(request.getType())) {
                int count = menuModuleMapper.checkDataPermissionIdentifierExists(
                    cleanIdentifier, request.getExcludeId());
                log.info("数据权限重复检测结果 - 标识符: {}, 排除ID: {}, 重复数量: {}",
                    cleanIdentifier, request.getExcludeId(), count);
                isDuplicate = count > 0;
            }

            // 4. 构建响应
            if (isDuplicate) {
                String suggestion = IdentifierValidator.generateSuggestion(cleanIdentifier, request.getType());
                return CheckIdentifierResponseVO.unavailable(
                    "该权限标识符已存在，请使用其他标识符",
                    suggestion
                );
            } else {
                return CheckIdentifierResponseVO.available("该权限标识符可以使用");
            }

        } catch (Exception e) {
            log.error("检查权限标识符重复失败", e);
            return CheckIdentifierResponseVO.validationError(
                "检查权限标识符时发生错误：" + e.getMessage(),
                IdentifierValidator.getFormatRules(request.getType())
            );
        }
    }

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 删除菜单 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除菜单请求参数
     * @return 删除结果或使用情况信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Object deleteMenuV2(DeleteMenuRequestVO request) {
        log.info("开始删除菜单V2");
        log.info("菜单ID: {}, 强制删除: {}", request.getId(), request.getForceDelete());

        Long menuId = request.getId();

        // 1. 验证菜单是否存在
        int menuExists = menuModuleMapper.checkMenuExists(menuId);
        if (menuExists == 0) {
            log.error("菜单不存在，菜单ID: {}", menuId);
            throw new RuntimeException("指定的菜单不存在");
        }

        // 2. 如果不是强制删除，检查使用情况
        if (!Boolean.TRUE.equals(request.getForceDelete())) {
            // 检查是否有子菜单
            List<Long> childIds = menuModuleMapper.getChildMenuIds(menuId);
            if (!childIds.isEmpty()) {
                log.error("菜单存在子菜单，无法删除，菜单ID: {}, 子菜单数量: {}",
                         menuId, childIds.size());
                throw new RuntimeException("该菜单存在子菜单，请先删除子菜单");
            }

            // 检查角色使用情况
            List<RoleUsageVO> usedByRoles = menuModuleMapper.getRolesByMenuId(menuId);
            if (!usedByRoles.isEmpty()) {
                log.info("菜单正在被角色使用，返回使用情况，菜单ID: {}", menuId);

                // 构建使用情况响应
                UsageCheckResponseVO response = new UsageCheckResponseVO();
                response.setCanDirectDelete(false);

                UsageInfoVO usageInfo = new UsageInfoVO();
                usageInfo.setMenuId(menuId.toString());
                usageInfo.setMenuName(menuModuleMapper.getMenuNameById(menuId));
                usageInfo.setUsedByRoles(usedByRoles);
                usageInfo.setTotalRoleCount(usedByRoles.size());

                response.setUsageInfo(usageInfo);

                // 返回409状态码和使用情况信息
                throw new UsageConflictException("检测到使用情况，请确认是否强制删除", response);
            }
        }

        // 3. 执行删除（无使用情况或强制删除）
        if (Boolean.TRUE.equals(request.getForceDelete())) {
            // 强制删除：级联删除相关数据
            log.info("执行强制删除，菜单ID: {}", menuId);

            // 删除角色权限关联
            menuModuleMapper.deleteMenuRolePermissions(menuId);

            // 递归删除子菜单
            List<Long> childIds = menuModuleMapper.getChildMenuIds(menuId);
            for (Long childId : childIds) {
                DeleteMenuRequestVO childRequest = new DeleteMenuRequestVO();
                childRequest.setId(childId);
                childRequest.setForceDelete(true);
                deleteMenuV2(childRequest);
            }
        }

        // 4. 逻辑删除菜单
        menuModuleMapper.deleteMenuById(menuId);

        log.info("菜单删除成功，菜单ID: {}", menuId);
        return null; // 成功删除返回null
    }

    /**
     * 删除菜单模块 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除菜单模块请求参数
     * @return 删除结果或使用情况信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Object deleteModuleV2(DeleteMenuModuleRequestVO request) {
        log.info("开始删除菜单模块V2");
        log.info("模块标识: {}, 强制删除: {}", request.getModuleIdentifier(), request.getForceDelete());

        // 1. 检查模块是否存在
        int existsCount = menuModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (existsCount == 0) {
            log.error("菜单模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("菜单模块不存在");
        }

        // 2. 如果不是强制删除，检查使用情况
        if (!Boolean.TRUE.equals(request.getForceDelete())) {
            List<RoleUsageVO> usedByRoles = menuModuleMapper.getRolesByModuleIdentifier(request.getModuleIdentifier());
            if (!usedByRoles.isEmpty()) {
                log.info("菜单模块正在被角色使用，返回使用情况，模块标识: {}", request.getModuleIdentifier());

                // 构建使用情况响应
                UsageCheckResponseVO response = new UsageCheckResponseVO();
                response.setCanDirectDelete(false);

                UsageInfoVO usageInfo = new UsageInfoVO();
                usageInfo.setModuleIdentifier(request.getModuleIdentifier());
                usageInfo.setModuleName(menuModuleMapper.getModuleNameByIdentifier(request.getModuleIdentifier()));
                usageInfo.setUsedByRoles(usedByRoles);
                usageInfo.setTotalRoleCount(usedByRoles.size());

                response.setUsageInfo(usageInfo);

                // 返回409状态码和使用情况信息
                throw new UsageConflictException("检测到使用情况，请确认是否强制删除", response);
            }
        }

        // 3. 执行删除（无使用情况或强制删除）
        if (Boolean.TRUE.equals(request.getForceDelete())) {
            // 强制删除：级联删除相关数据
            log.info("执行强制删除，模块标识: {}", request.getModuleIdentifier());

            // 删除模块下所有菜单的角色权限关联
            menuModuleMapper.deleteModuleRolePermissions(request.getModuleIdentifier());

            // 删除模块下所有菜单
            menuModuleMapper.deleteMenusByModuleIdentifier(request.getModuleIdentifier());
        }

        // 4. 逻辑删除菜单模块
        menuModuleMapper.deleteMenuModuleByIdentifier(request.getModuleIdentifier());

        log.info("菜单模块删除成功，模块标识: {}", request.getModuleIdentifier());
        return null; // 成功删除返回null
    }
}
