2025-07-04 09:40:16.315 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 42000 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-04 09:40:16.321 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-04 09:40:16.330 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-04 09:40:19.440 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 09:40:19.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:40:19.481 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Elasticsearch repository interfaces.
2025-07-04 09:40:19.487 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 09:40:19.488 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-04 09:40:19.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-04 09:40:19.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 09:40:19.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-04 09:40:19.545 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-07-04 09:40:19.566 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-04 09:40:19.569 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-04 09:40:19.597 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-04 09:40:20.578 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-04 09:40:20.588 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-04 09:40:20.589 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-04 09:40:20.589 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-04 09:40:21.016 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-04 09:40:21.016 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4615 ms
2025-07-04 09:40:21.132 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-04 09:40:21.328 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-04 09:40:22.068 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-04 09:40:22.116 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-04 09:40:22.239 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-04 09:40:22.404 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-04 09:40:23.025 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-04 09:40:23.036 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-04 09:40:23.051 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-04 09:40:23.051 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-04 09:40:23.052 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-04 09:40:23.052 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-04 09:40:26.259 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-04 09:40:28.166 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-04 09:40:28.179 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-04 09:40:28.898 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.678 seconds (JVM running for 16.498)
2025-07-04 09:55:27.200 [http-nio-8285-exec-4] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-04 09:55:27.201 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-04 09:55:27.206 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-07-04 09:55:27.244 [http-nio-8285-exec-4] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /data-operates/configure-list, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-07-04 09:55:27.521 [http-nio-8285-exec-4] INFO  com.dfit.percode.interceptor.HybridAuthInterceptor -  超级管理员访问 - 用户ID: 1938155631131365376, URI: /data-operates/configure-list, Method: POST, IP: 0:0:0:0:0:0:0:1
2025-07-04 09:55:27.576 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.DataOperateController - 开始配置数据级别操作权限
2025-07-04 09:55:27.577 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.DataOperateController - 模块标识: PD, 数据权限数量: 1
2025-07-04 09:55:27.828 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置数据级别操作权限
2025-07-04 09:55:27.828 [http-nio-8285-exec-4] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块标识: PD, 数据权限数量: 1
2025-07-04 09:55:28.364 [http-nio-8285-exec-4] INFO  com.dfit.percode.listener.UserActionInterceptor - 请求处理完成 - URI: /data-operates/configure-list, 处理时间: 1119ms
