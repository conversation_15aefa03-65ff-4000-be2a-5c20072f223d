package com.dfit.percode.service;

import com.dfit.percode.entity.TRole;
import com.dfit.percode.userPerm.AddRoleParam;
import com.dfit.percode.vo.RoleListRequestVO;
import com.dfit.percode.vo.RoleListResponseVO;
import com.dfit.percode.vo.AddRoleRequestVO;
import com.dfit.percode.vo.AddRoleResponseVO;
import com.dfit.percode.vo.EditRoleRequestVO;
import com.dfit.percode.vo.DeleteRoleRequestVO;
import com.dfit.percode.vo.RoleDetailRequestVO;
import com.dfit.percode.vo.RoleDetailResponseVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 角色信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface ITRoleService extends IService<TRole> {

    void addNewRole(AddRoleParam arp);

    /**
     * 分页查询角色列表
     * 支持角色名称模糊搜索、状态筛选、删除状态筛选
     *
     * @param request 查询请求参数
     * @return 角色列表响应，包含分页信息
     */
    RoleListResponseVO getRoleList(RoleListRequestVO request);

    /**
     * 新增角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的设置
     *
     * @param request 新增角色请求参数
     * @return 新增角色响应，包含角色基本信息
     */
    AddRoleResponseVO addRole(AddRoleRequestVO request);

    /**
     * 修改角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的修改
     *
     * @param request 修改角色请求参数
     * @return 操作结果
     */
    void editRole(EditRoleRequestVO request);

    /**
     * 删除角色（逻辑删除）
     * 检查角色是否被用户使用，如果有关联则不能删除
     *
     * @param request 删除角色请求参数
     * @return 操作结果
     */
    void deleteRole(DeleteRoleRequestVO request);

    /**
     * 获取角色详情
     * 查询角色基本信息和已关联的权限ID列表
     * 用于角色编辑时的数据回显
     *
     * @param request 角色详情查询请求参数
     * @return 角色详情响应，包含基本信息和权限关联
     */
    RoleDetailResponseVO getRoleDetail(RoleDetailRequestVO request);

}
