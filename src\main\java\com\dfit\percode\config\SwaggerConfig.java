package com.dfit.percode.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * Swagger配置类
 * 用于生成API文档
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Configuration
@EnableOpenApi
public class SwaggerConfig {

    @Value("${sys.swagger.enable-swgger}")
    private Boolean enableSwagger;

    /**
     * 创建API文档
     *
     * @return Docket配置
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.OAS_30)
                .apiInfo(apiInfo())
                .enable(enableSwagger)
                .select()
                // 扫描指定包下的Controller（包括子包）
                .apis(RequestHandlerSelectors.basePackage("com.dfit.percode"))
                // 扫描所有路径
                .paths(PathSelectors.any())
                .build();
    }

    /**
     * API信息配置
     *
     * @return API信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("权限管理系统 API文档")
                .description("权限管理系统的RESTful API接口文档")
                .version("1.0.0")
                .contact(new Contact("开发团队", "", ""))
                .build();
    }
}
