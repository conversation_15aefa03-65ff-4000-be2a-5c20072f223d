package com.dfit.percode.config;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.Arrays;

public class LongArrayTypeHandler implements TypeHandler<long[]> {
    @Override
    public void setParameter(PreparedStatement ps, int i, long[] parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, Types.ARRAY);
        } else {
            // 将 long[] 转换为 PostgreSQL 可以接受的数组格式
            ps.setArray(i, ps.getConnection().createArrayOf("bigint", Arrays.stream(parameter).boxed().toArray(Long[]::new)));
        }
    }

    @Override
    public long[] getResult(ResultSet rs, String columnName) throws SQLException {
        return getLongArray(rs.getArray(columnName));
    }

    @Override
    public long[] getResult(ResultSet rs, int columnIndex) throws SQLException {
        return getLongArray(rs.getArray(columnIndex));
    }

    @Override
    public long[] getResult(java.sql.CallableStatement cs, int columnIndex) throws SQLException {
        return getLongArray(cs.getArray(columnIndex));
    }

    private long[] getLongArray(java.sql.Array array) throws SQLException {
        if (array == null) {
            return null;
        }
        Long[] arrayResult = (Long[]) array.getArray();
        return Arrays.stream(arrayResult).mapToLong(Long::longValue).toArray();
    }
}
