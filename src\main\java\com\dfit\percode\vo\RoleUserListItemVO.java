package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色用户分配列表项VO类
 * 按照前端格式要求设计（驼峰命名）
 * 用于显示用户信息和角色分配状态
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "RoleUserListItemVO", description = "角色用户分配列表项")
public class RoleUserListItemVO {

    @ApiModelProperty("用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("登录账号")
    private String account;

    @ApiModelProperty("部门名称")
    private String department;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty(value = "是否已分配该角色", example = "true")
    private Boolean isAssigned;

    @ApiModelProperty(value = "是否可以进行授权操作（对应关联表is_del）", example = "false")
    private Boolean canAssign;

    @ApiModelProperty("创建时间")
    private String createTime;
}
