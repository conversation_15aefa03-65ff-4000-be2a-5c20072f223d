import java.sql.*;

public class CleanSyncData {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== 清理错误的同步数据 ===");
        
        try {
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            
            // 查询现有同步数据数量
            PreparedStatement countStmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2");
            ResultSet rs = countStmt.executeQuery();
            int beforeCount = 0;
            if (rs.next()) {
                beforeCount = rs.getInt(1);
            }
            rs.close();
            countStmt.close();
            
            System.out.println("删除前同步数据数量: " + beforeCount);
            
            // 删除所有同步数据
            PreparedStatement deleteStmt = conn.prepareStatement("DELETE FROM t_org_structure WHERE data_source = 2");
            int deletedCount = deleteStmt.executeUpdate();
            deleteStmt.close();
            
            System.out.println("已删除同步数据数量: " + deletedCount);
            
            // 验证删除结果
            PreparedStatement verifyStmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2");
            ResultSet verifyRs = verifyStmt.executeQuery();
            int afterCount = 0;
            if (verifyRs.next()) {
                afterCount = verifyRs.getInt(1);
            }
            verifyRs.close();
            verifyStmt.close();
            
            System.out.println("删除后同步数据数量: " + afterCount);
            
            // 查询总记录数
            PreparedStatement totalStmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure");
            ResultSet totalRs = totalStmt.executeQuery();
            int totalCount = 0;
            if (totalRs.next()) {
                totalCount = totalRs.getInt(1);
            }
            totalRs.close();
            totalStmt.close();
            
            System.out.println("表中总记录数: " + totalCount);
            
            conn.close();
            
            if (afterCount == 0) {
                System.out.println("✅ 错误的同步数据清理完成！");
            } else {
                System.out.println("❌ 清理失败，仍有 " + afterCount + " 条同步数据");
            }
            
        } catch (SQLException e) {
            System.err.println("❌ 清理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
