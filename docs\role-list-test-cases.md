# 角色列表接口测试用例

## 📋 接口信息
- **接口路径**: `POST /roles/getRoleList`
- **接口描述**: 分页查询角色列表，支持角色名称模糊搜索、状态筛选
- **Content-Type**: `application/json`

## 🧪 测试用例

### 测试用例1：基础分页查询
**测试目的**: 验证基本的分页查询功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 10
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3001",
      "roleName": "超级管理员",
      "orderInfo": 1,
      "isDisable": false,
      "statusText": "启用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 1,
      "canDelete": false
    },
    {
      "id": "3002",
      "roleName": "系统管理员",
      "orderInfo": 2,
      "isDisable": false,
      "statusText": "启用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 1,
      "canDelete": false
    },
    {
      "id": "3003",
      "roleName": "部门管理员",
      "orderInfo": 3,
      "isDisable": false,
      "statusText": "启用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 2,
      "canDelete": false
    },
    {
      "id": "3004",
      "roleName": "普通用户",
      "orderInfo": 4,
      "isDisable": false,
      "statusText": "启用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 3,
      "canDelete": false
    },
    {
      "id": "3005",
      "roleName": "只读用户",
      "orderInfo": 5,
      "isDisable": false,
      "statusText": "启用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 2,
      "canDelete": false
    },
    {
      "id": "3006",
      "roleName": "测试角色",
      "orderInfo": 6,
      "isDisable": true,
      "statusText": "停用",
      "createTime": "2025-01-20 15:30:45",
      "modifyTime": "2025-01-20 15:30:45",
      "userCount": 0,
      "canDelete": true
    }
  ],
  "total": 6
}
```

**验证点**:
- ✅ 返回状态码为200
- ✅ 返回6个角色记录
- ✅ 每个角色包含完整字段信息
- ✅ userCount字段正确统计关联用户数量
- ✅ canDelete字段正确判断（有用户关联的不可删除）
- ✅ statusText字段正确显示中文状态

---

### 测试用例2：角色名称模糊搜索
**测试目的**: 验证角色名称模糊搜索功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 10,
    "roleName": "管理"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3001",
      "roleName": "超级管理员",
      "orderInfo": 1,
      "isDisable": false,
      "statusText": "启用",
      "userCount": 1,
      "canDelete": false
    },
    {
      "id": "3002",
      "roleName": "系统管理员",
      "orderInfo": 2,
      "isDisable": false,
      "statusText": "启用",
      "userCount": 1,
      "canDelete": false
    },
    {
      "id": "3003",
      "roleName": "部门管理员",
      "orderInfo": 3,
      "isDisable": false,
      "statusText": "启用",
      "userCount": 2,
      "canDelete": false
    }
  ],
  "total": 3
}
```

**验证点**:
- ✅ 只返回包含"管理"关键字的角色
- ✅ 总数为3（超级管理员、系统管理员、部门管理员）
- ✅ 搜索不区分大小写

---

### 测试用例3：按状态筛选（只查询启用角色）
**测试目的**: 验证按启用状态筛选功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 10,
    "isDisable": false
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3001",
      "roleName": "超级管理员",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3002",
      "roleName": "系统管理员",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3003",
      "roleName": "部门管理员",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3004",
      "roleName": "普通用户",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3005",
      "roleName": "只读用户",
      "isDisable": false,
      "statusText": "启用"
    }
  ],
  "total": 5
}
```

**验证点**:
- ✅ 只返回isDisable=false的角色
- ✅ 不包含"测试角色"（已停用）
- ✅ 总数为5

---

### 测试用例4：按状态筛选（只查询停用角色）
**测试目的**: 验证按停用状态筛选功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 10,
    "isDisable": true
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3006",
      "roleName": "测试角色",
      "orderInfo": 6,
      "isDisable": true,
      "statusText": "停用",
      "userCount": 0,
      "canDelete": true
    }
  ],
  "total": 1
}
```

**验证点**:
- ✅ 只返回isDisable=true的角色
- ✅ 只包含"测试角色"
- ✅ statusText显示"停用"
- ✅ canDelete为true（无用户关联）

---

### 测试用例5：组合条件查询
**测试目的**: 验证多条件组合查询功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 5,
    "roleName": "管理",
    "isDisable": false
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3001",
      "roleName": "超级管理员",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3002",
      "roleName": "系统管理员",
      "isDisable": false,
      "statusText": "启用"
    },
    {
      "id": "3003",
      "roleName": "部门管理员",
      "isDisable": false,
      "statusText": "启用"
    }
  ],
  "total": 3
}
```

**验证点**:
- ✅ 同时满足角色名称包含"管理"且状态为启用
- ✅ 不包含停用的角色
- ✅ 分页大小限制为5条

---

### 测试用例6：分页测试
**测试目的**: 验证分页功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 3
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3001",
      "roleName": "超级管理员",
      "orderInfo": 1
    },
    {
      "id": "3002",
      "roleName": "系统管理员",
      "orderInfo": 2
    },
    {
      "id": "3003",
      "roleName": "部门管理员",
      "orderInfo": 3
    }
  ],
  "total": 6
}
```

**第二页请求**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 2,
    "pageSize": 3
  }'
```

**第二页预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": "3004",
      "roleName": "普通用户",
      "orderInfo": 4
    },
    {
      "id": "3005",
      "roleName": "只读用户",
      "orderInfo": 5
    },
    {
      "id": "3006",
      "roleName": "测试角色",
      "orderInfo": 6
    }
  ],
  "total": 6
}
```

**验证点**:
- ✅ 第一页返回前3条记录
- ✅ 第二页返回后3条记录
- ✅ 总数始终为6
- ✅ 按orderInfo排序

---

### 测试用例7：空结果场景
**测试目的**: 验证无匹配结果的处理

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 1,
    "pageSize": 10,
    "roleName": "不存在的角色名称"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [],
  "total": 0
}
```

**验证点**:
- ✅ 返回空数组
- ✅ 总数为0
- ✅ 状态码仍为200

---

## ❌ 错误场景测试

### 错误场景1：缺少必填参数
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "管理员"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数验证失败：currentPage和pageSize为必填参数",
  "data": null
}
```

### 错误场景2：无效的分页参数
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/getRoleList \
  -H "Content-Type: application/json" \
  -d '{
    "currentPage": 0,
    "pageSize": -1
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数验证失败：页码必须大于0，页大小必须大于0",
  "data": null
}
```

## 🔧 测试工具推荐

### 1. Postman测试集合
可以将以上测试用例导入Postman创建测试集合，方便批量执行。

### 2. 自动化测试脚本
```bash
#!/bin/bash
# 角色列表接口自动化测试脚本

BASE_URL="http://localhost:8080"
ENDPOINT="/roles/getRoleList"

echo "开始测试角色列表接口..."

# 测试1：基础分页查询
echo "测试1：基础分页查询"
curl -s -X POST "$BASE_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"currentPage":1,"pageSize":10}' | jq .

# 测试2：角色名称搜索
echo "测试2：角色名称搜索"
curl -s -X POST "$BASE_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"currentPage":1,"pageSize":10,"roleName":"管理"}' | jq .

# 测试3：状态筛选
echo "测试3：状态筛选"
curl -s -X POST "$BASE_URL$ENDPOINT" \
  -H "Content-Type: application/json" \
  -d '{"currentPage":1,"pageSize":10,"isDisable":false}' | jq .

echo "测试完成！"
```

## 📊 性能测试

### 并发测试
```bash
# 使用ab工具进行并发测试
ab -n 100 -c 10 -p role_list_request.json -T application/json http://localhost:8080/roles/getRoleList
```

### 大数据量测试
建议在数据库中插入更多角色数据（如1000+条），测试分页性能。

## ✅ 验收标准

1. **功能完整性**: 所有测试用例都能正常执行
2. **数据准确性**: 返回的数据与数据库中的数据一致
3. **性能要求**: 响应时间 < 500ms
4. **并发能力**: 支持10个并发请求
5. **错误处理**: 异常情况能正确返回错误信息
