2025-06-20 14:49:52.914 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 14:49:52.922 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 41200 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 14:49:52.923 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 14:49:56.552 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 14:49:56.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 14:49:56.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 14:49:56.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 14:49:56.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 14:49:56.636 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 14:49:56.655 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 14:49:56.655 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 14:49:56.694 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-20 14:49:56.726 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 14:49:56.728 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 14:49:56.769 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-20 14:49:58.417 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 14:49:58.437 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 14:49:58.445 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 14:49:58.446 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 14:49:59.044 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 14:49:59.045 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5894 ms
2025-06-20 14:49:59.228 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 14:49:59.656 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 14:50:00.682 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 14:50:00.844 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 14:50:01.362 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 14:50:01.738 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 14:50:02.304 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 14:50:02.317 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 14:50:06.234 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 14:50:10.092 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 14:50:10.109 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 14:50:10.810 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.263 seconds (JVM running for 25.17)
2025-06-20 14:59:42.382 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 14:59:42.388 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 15:00:35.043 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 40176 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 15:00:35.046 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 15:00:35.080 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 15:00:37.698 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:00:37.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 15:00:37.747 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 15:00:37.753 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:00:37.754 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 15:00:37.772 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 15:00:37.786 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:00:37.786 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 15:00:37.813 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-20 15:00:37.845 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:00:37.847 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 15:00:37.881 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-20 15:00:39.148 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 15:00:39.170 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 15:00:39.171 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 15:00:39.171 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 15:00:39.763 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 15:00:39.776 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4621 ms
2025-06-20 15:00:40.151 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 15:00:40.415 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 15:00:41.301 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 15:00:41.396 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 15:00:41.661 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 15:00:41.889 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 15:00:42.344 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 15:00:42.365 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 15:00:45.415 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 15:00:47.762 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 15:00:47.775 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 15:00:48.597 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.609 seconds (JVM running for 17.375)
2025-06-20 15:13:11.300 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 15:13:11.309 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 15:13:25.153 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 36984 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 15:13:25.155 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 15:13:25.192 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 15:13:27.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:13:27.308 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 15:13:27.348 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 15:13:27.354 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:13:27.355 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 15:13:27.369 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 15:13:27.380 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:13:27.380 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 15:13:27.403 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-20 15:13:27.427 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 15:13:27.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 15:13:27.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-20 15:13:28.458 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 15:13:28.471 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 15:13:28.472 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 15:13:28.472 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 15:13:28.711 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 15:13:28.711 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3409 ms
2025-06-20 15:13:28.810 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 15:13:28.996 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 15:13:29.986 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 15:13:30.136 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 15:13:30.494 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 15:13:30.663 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 15:13:31.104 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 15:13:31.122 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 15:13:34.008 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 15:13:36.315 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 15:13:36.333 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 15:13:37.100 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.952 seconds (JVM running for 15.837)
2025-06-20 16:43:24.630 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 13512 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 16:43:24.632 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 16:43:24.646 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 16:43:26.309 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 16:43:26.311 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 16:43:26.334 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 16:43:26.338 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 16:43:26.339 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 16:43:26.349 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 16:43:26.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 16:43:26.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 16:43:26.373 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-20 16:43:26.388 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 16:43:26.390 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 16:43:26.413 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-20 16:43:27.296 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 16:43:27.310 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 16:43:27.311 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 16:43:27.311 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 16:43:27.683 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 16:43:27.684 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2990 ms
2025-06-20 16:43:27.828 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 16:43:28.077 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 16:43:29.104 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 16:43:29.178 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 16:43:29.320 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 16:43:29.482 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 16:43:29.834 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 16:43:29.846 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 16:43:31.991 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 16:43:34.131 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 16:43:34.146 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 16:43:34.941 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.089 seconds (JVM running for 14.642)
2025-06-20 16:59:49.533 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 16:59:49.538 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 17:00:12.779 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 19208 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 17:00:12.781 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 17:00:12.786 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 17:00:14.400 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 17:00:14.404 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 17:00:14.445 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 17:00:14.451 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 17:00:14.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 17:00:14.465 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 17:00:14.475 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 17:00:14.476 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 17:00:14.499 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-20 17:00:14.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 17:00:14.523 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 17:00:14.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-20 17:00:15.664 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 17:00:15.676 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 17:00:15.677 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 17:00:15.677 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 17:00:15.954 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 17:00:15.955 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3103 ms
2025-06-20 17:00:16.046 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 17:00:16.216 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 17:00:17.148 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 17:00:17.200 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 17:00:17.331 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 17:00:17.532 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 17:00:17.938 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 17:00:17.953 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 17:00:20.336 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 17:00:22.122 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 17:00:22.139 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 17:00:23.087 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.214 seconds (JVM running for 14.673)
2025-06-20 19:14:51.672 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:14:51.707 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 19:15:20.161 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 1524 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 19:15:20.164 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 19:15:20.174 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 19:15:23.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:15:23.574 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:15:23.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 19:15:23.611 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:15:23.612 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:15:23.626 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 19:15:23.636 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:15:23.637 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 19:15:23.654 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-20 19:15:23.710 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:15:23.712 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 19:15:23.735 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-20 19:15:24.995 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 19:15:25.011 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 19:15:25.012 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 19:15:25.012 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 19:15:26.079 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 19:15:26.080 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5805 ms
2025-06-20 19:15:26.793 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 19:15:28.170 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 19:15:29.546 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 19:15:29.621 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 19:15:30.042 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 19:15:30.250 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 19:15:30.770 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 19:15:30.792 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:15:34.518 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 19:15:37.126 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 19:15:37.141 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 19:15:38.035 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.852 seconds (JVM running for 23.194)
2025-06-20 19:27:52.500 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:27:52.535 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 19:29:05.580 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 33868 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 19:29:05.583 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 19:29:05.609 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 19:29:07.571 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:29:07.574 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:29:07.601 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 19:29:07.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:29:07.608 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:29:07.620 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 19:29:07.631 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:29:07.631 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 19:29:07.652 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-20 19:29:07.673 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:29:07.675 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 19:29:07.695 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-20 19:29:08.661 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 19:29:08.673 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 19:29:08.674 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 19:29:08.674 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 19:29:09.006 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 19:29:09.006 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3320 ms
2025-06-20 19:29:09.122 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 19:29:09.345 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 19:29:10.201 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 19:29:10.334 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 19:29:10.588 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 19:29:10.773 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 19:29:11.175 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 19:29:11.190 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:29:13.914 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 19:29:16.398 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 19:29:16.412 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 19:29:17.659 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.179 seconds (JVM running for 18.461)
2025-06-20 19:41:29.016 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:41:29.030 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-20 19:43:08.412 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 24300 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-20 19:43:08.413 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-20 19:43:08.425 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-20 19:43:10.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:43:10.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:43:10.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-20 19:43:10.271 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:43:10.272 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-20 19:43:10.285 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-20 19:43:10.296 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:43:10.296 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-20 19:43:10.315 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-20 19:43:10.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-20 19:43:10.336 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-20 19:43:10.360 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-20 19:43:11.267 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-20 19:43:11.277 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-20 19:43:11.277 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-20 19:43:11.277 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-20 19:43:11.536 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-20 19:43:11.537 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3059 ms
2025-06-20 19:43:11.662 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-20 19:43:11.893 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-20 19:43:12.672 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-20 19:43:12.727 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-20 19:43:12.855 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-20 19:43:12.998 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-20 19:43:13.336 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-20 19:43:13.354 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-20 19:43:15.838 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-20 19:43:17.955 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-20 19:43:18.021 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-20 19:43:18.746 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.204 seconds (JVM running for 15.702)
2025-06-20 21:00:00.014 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-06-20 21:00:00.749 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
2025-06-20 21:00:00.855 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工岗位孤儿记录: 0 条
2025-06-20 21:00:00.921 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工职称孤儿记录: 0 条
2025-06-20 21:00:00.988 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工系统标识孤儿记录: 0 条
2025-06-20 21:00:00.988 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 孤儿记录关联汇总: 岗位表=0 条, 职称表=0 条, 系统表=0 条, 总计=0 条
2025-06-20 21:00:01.058 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 孤儿记录关联任务执行成功 ===
