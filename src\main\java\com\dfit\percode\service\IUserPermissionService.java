package com.dfit.percode.service;

import com.dfit.percode.vo.UserPermissionsResponseVO;

/**
 * 用户权限服务接口
 * 用于查询用户的菜单、按钮和数据权限
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IUserPermissionService {

    /**
     * 根据用户ID查询用户权限（支持权限类型筛选）
     * 包含菜单权限（树形结构）、按钮权限（平铺结构）和数据权限（树形结构）
     *
     * @param userId 用户ID
     * @param moduleIdentifier 模块标识符，可选，用于筛选特定模块的权限
     * @param moduleType 权限类型，可选值：menu（菜单）、data（数据）、all（全部），默认menu
     * @return 用户权限数据
     */
    UserPermissionsResponseVO getUserPermissions(Long userId, String moduleIdentifier, String moduleType);

    /**
     * 根据用户ID查询用户权限（向后兼容方法）
     * 默认查询菜单和按钮权限
     *
     * @param userId 用户ID
     * @param moduleIdentifier 模块标识符，可选，用于筛选特定模块的权限
     * @return 用户权限数据
     */
    default UserPermissionsResponseVO getUserPermissions(Long userId, String moduleIdentifier) {
        return getUserPermissions(userId, moduleIdentifier, "menu");
    }
}