import java.sql.*;

public class TestConnection {
    public static void main(String[] args) {
        String url = "****************************************";
        String user = "postgres2";
        String password = "123456";
        
        System.out.println("测试数据库连接...");
        System.out.println("URL: " + url);
        System.out.println("用户: " + user);
        
        try {
            Connection conn = DriverManager.getConnection(url, user, password);
            System.out.println("✅ 数据库连接成功！");
            
            // 测试查询
            PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure");
            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                System.out.println("t_org_structure表中现有记录数: " + rs.getInt(1));
            }
            rs.close();
            stmt.close();
            
            // 测试查询同步数据
            PreparedStatement stmt2 = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2");
            ResultSet rs2 = stmt2.executeQuery();
            if (rs2.next()) {
                System.out.println("现有同步数据记录数: " + rs2.getInt(1));
            }
            rs2.close();
            stmt2.close();
            
            conn.close();
            System.out.println("数据库连接测试完成");
            
        } catch (SQLException e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
