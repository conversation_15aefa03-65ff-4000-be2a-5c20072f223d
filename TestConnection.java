import java.sql.*;

public class TestConnection {
    public static void main(String[] args) {
        String url = "****************************************";
        String user = "postgres2";
        String password = "123456";
        
        System.out.println("测试数据库连接...");
        System.out.println("URL: " + url);
        System.out.println("用户: " + user);
        
        try {
            Connection conn = DriverManager.getConnection(url, user, password);
            System.out.println("✅ 数据库连接成功！");
            
            // 查看所有表
            System.out.println("\n=== 数据库中的所有表 ===");
            PreparedStatement tablesStmt = conn.prepareStatement(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"
            );
            ResultSet tablesRs = tablesStmt.executeQuery();
            while (tablesRs.next()) {
                System.out.println("  - " + tablesRs.getString("table_name"));
            }
            tablesRs.close();
            tablesStmt.close();

            // 查找组织架构相关的表
            System.out.println("\n=== 查找组织架构相关表 ===");
            PreparedStatement orgStmt = conn.prepareStatement(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' " +
                "AND (table_name LIKE '%org%' OR table_name LIKE '%structure%' OR table_name LIKE '%department%')"
            );
            ResultSet orgRs = orgStmt.executeQuery();
            boolean foundOrgTable = false;
            while (orgRs.next()) {
                System.out.println("  找到组织架构表: " + orgRs.getString("table_name"));
                foundOrgTable = true;
            }
            if (!foundOrgTable) {
                System.out.println("  未找到组织架构相关表");
            }
            orgRs.close();
            orgStmt.close();

            // 查询重复的部门名称
            try {
                System.out.println("\n=== 检查重复部门名称 ===");
                PreparedStatement dupStmt = conn.prepareStatement(
                    "SELECT organ_name, COUNT(*) as count " +
                    "FROM t_org_structure " +
                    "WHERE data_source = 2 " +
                    "GROUP BY organ_name " +
                    "HAVING COUNT(*) > 1 " +
                    "ORDER BY count DESC LIMIT 20"
                );

                ResultSet dupRs = dupStmt.executeQuery();
                int duplicateGroups = 0;

                while (dupRs.next()) {
                    String organName = dupRs.getString("organ_name");
                    int count = dupRs.getInt("count");
                    duplicateGroups++;
                    System.out.println(String.format("  %d. %s (重复%d次)",
                        duplicateGroups, organName, count));
                }

                if (duplicateGroups == 0) {
                    System.out.println("  ✅ 没有发现重复的部门名称");
                } else {
                    System.out.println("  ⚠️  发现 " + duplicateGroups + " 个重复的部门名称");
                }

                dupRs.close();
                dupStmt.close();

                // 查询基本统计
                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2");
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    System.out.println("\n同步数据记录数: " + rs.getInt(1));
                }
                rs.close();
                stmt.close();

            } catch (SQLException e) {
                System.out.println("查询失败: " + e.getMessage());
            }
            
            conn.close();
            System.out.println("数据库连接测试完成");
            
        } catch (SQLException e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
