import java.sql.*;

public class TestConnection {
    public static void main(String[] args) {
        String url = "****************************************";
        String user = "postgres2";
        String password = "123456";
        
        System.out.println("测试数据库连接...");
        System.out.println("URL: " + url);
        System.out.println("用户: " + user);
        
        try {
            Connection conn = DriverManager.getConnection(url, user, password);
            System.out.println("✅ 数据库连接成功！");
            
            // 查看所有表
            System.out.println("\n=== 数据库中的所有表 ===");
            PreparedStatement tablesStmt = conn.prepareStatement(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"
            );
            ResultSet tablesRs = tablesStmt.executeQuery();
            while (tablesRs.next()) {
                System.out.println("  - " + tablesRs.getString("table_name"));
            }
            tablesRs.close();
            tablesStmt.close();

            // 查找组织架构相关的表
            System.out.println("\n=== 查找组织架构相关表 ===");
            PreparedStatement orgStmt = conn.prepareStatement(
                "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' " +
                "AND (table_name LIKE '%org%' OR table_name LIKE '%structure%' OR table_name LIKE '%department%')"
            );
            ResultSet orgRs = orgStmt.executeQuery();
            boolean foundOrgTable = false;
            while (orgRs.next()) {
                System.out.println("  找到组织架构表: " + orgRs.getString("table_name"));
                foundOrgTable = true;
            }
            if (!foundOrgTable) {
                System.out.println("  未找到组织架构相关表");
            }
            orgRs.close();
            orgStmt.close();

            // 如果有t_org_structure表，查询其内容
            try {
                PreparedStatement stmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure");
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    System.out.println("\nt_org_structure表中现有记录数: " + rs.getInt(1));

                    // 查询同步数据
                    PreparedStatement stmt2 = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = 2");
                    ResultSet rs2 = stmt2.executeQuery();
                    if (rs2.next()) {
                        System.out.println("现有同步数据记录数: " + rs2.getInt(1));

                        // 如果有同步数据，查看前几条
                        if (rs2.getInt(1) > 0) {
                            System.out.println("\n=== 前10条同步数据 ===");
                            PreparedStatement stmt3 = conn.prepareStatement(
                                "SELECT id, organ_name, pre_id FROM t_org_structure WHERE data_source = 2 ORDER BY id LIMIT 10"
                            );
                            ResultSet rs3 = stmt3.executeQuery();
                            while (rs3.next()) {
                                System.out.println(String.format("  ID: %d, 名称: %s, 父ID: %d",
                                    rs3.getLong("id"), rs3.getString("organ_name"), rs3.getLong("pre_id")));
                            }
                            rs3.close();
                            stmt3.close();
                        }
                    }
                    rs2.close();
                    stmt2.close();
                }
                rs.close();
                stmt.close();
            } catch (SQLException e) {
                System.out.println("t_org_structure表不存在或查询失败: " + e.getMessage());
            }
            
            conn.close();
            System.out.println("数据库连接测试完成");
            
        } catch (SQLException e) {
            System.err.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
