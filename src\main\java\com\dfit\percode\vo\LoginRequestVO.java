package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 登录请求VO类
 * 用于用户登录接口的请求参数
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "LoginRequestVO", description = "用户登录请求参数")
public class LoginRequestVO {

    @ApiModelProperty(value = "登录账号", example = "admin", required = true)
    @NotBlank(message = "登录账号不能为空")
    private String username;

    @ApiModelProperty(value = "登录密码", example = "123456", required = true)
    @NotBlank(message = "登录密码不能为空")
    private String password;
}