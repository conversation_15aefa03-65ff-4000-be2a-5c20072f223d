/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:01
*/


-- ----------------------------
-- Table structure for t_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_role";
CREATE TABLE "public"."t_role" (
  "id" int8 NOT NULL,
  "role_name" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_role"."id" IS '角色id';
COMMENT ON COLUMN "public"."t_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."t_role"."order_info" IS '顺序';
COMMENT ON COLUMN "public"."t_role"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_role"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_role" IS '角色信息表';

-- ----------------------------
-- Records of t_role
-- ----------------------------
INSERT INTO "public"."t_role" VALUES (1936385110190460928, '测试', 999, 'f', 'f', '2025-06-21 19:26:01.222601', '2025-06-21 19:36:18.329182');
INSERT INTO "public"."t_role" VALUES (1936403967517003776, '测试', 999, 'f', 'f', '2025-06-21 20:40:57.160111', '2025-06-21 20:41:13.673887');
INSERT INTO "public"."t_role" VALUES (1936384535235268608, '超级', 999, 'f', 'f', '2025-06-21 19:23:44.142365', '2025-06-22 09:53:14.735063');
INSERT INTO "public"."t_role" VALUES (1936027772606615552, '123', 999, 'f', 'f', '2025-06-20 19:46:05.304295', '2025-06-20 19:46:43.566784');
INSERT INTO "public"."t_role" VALUES (1936612757591953408, 'DERT', 999, 'f', 'f', '2025-06-22 10:30:36.592373', '2025-06-22 16:32:23.069245');
INSERT INTO "public"."t_role" VALUES (1936613552274149376, 'ddd', 999, 'f', 'f', '2025-06-22 10:33:46.059208', '2025-06-22 16:32:25.237367');
INSERT INTO "public"."t_role" VALUES (7001, '超级管理员', 1, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:02:27.444838');
INSERT INTO "public"."t_role" VALUES (7009, '数据分析师', 9, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:02:35.421911');
INSERT INTO "public"."t_role" VALUES (7008, '产品经理', 8, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:02:38.577888');
INSERT INTO "public"."t_role" VALUES (7007, '运维工程师', 7, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:02:41.278173');
INSERT INTO "public"."t_role" VALUES (1936191996003749888, '权限1', 999, 'f', 'f', '2025-06-21 06:38:39.212569', '2025-06-21 07:04:20.069547');
INSERT INTO "public"."t_role" VALUES (1936192165931782144, '权限2', 999, 'f', 'f', '2025-06-21 06:39:19.727126', '2025-06-21 07:04:23.213315');
INSERT INTO "public"."t_role" VALUES (1936191688116670464, '权限管理', 999, 'f', 'f', '2025-06-21 06:37:25.80696', '2025-06-21 07:04:26.395847');
INSERT INTO "public"."t_role" VALUES (1936192995749662720, '权限3', 999, 'f', 'f', '2025-06-21 06:42:37.570866', '2025-06-21 07:04:39.314892');
INSERT INTO "public"."t_role" VALUES (1932739129100079104, 'test', 999, 'f', 'f', '2025-06-11 17:58:11.591544', '2025-06-11 17:58:36.352424');
INSERT INTO "public"."t_role" VALUES (1936006861878857728, '草鸡管理员', 999, 'f', 'f', '2025-06-20 18:22:59.797437', '2025-06-21 07:04:42.093613');
INSERT INTO "public"."t_role" VALUES (7006, '测试工程师', 6, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:04:47.365168');
INSERT INTO "public"."t_role" VALUES (7005, '普通开发工程师', 5, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:04:50.024185');
INSERT INTO "public"."t_role" VALUES (7004, '高级开发工程师', 4, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:04:52.480634');
INSERT INTO "public"."t_role" VALUES (7003, '部门经理', 3, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:04:56.30484');
INSERT INTO "public"."t_role" VALUES (7002, '系统管理员', 2, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:04:58.604746');
INSERT INTO "public"."t_role" VALUES (1936703912811827200, '管理员', 999, 'f', 'f', '2025-06-22 16:32:49.69027', '2025-06-25 12:22:26.690138');
INSERT INTO "public"."t_role" VALUES (7010, '财务专员', 10, 'f', 'f', '2025-06-11 01:58:48.189059', '2025-06-21 07:05:11.687337');
INSERT INTO "public"."t_role" VALUES (1934611306124546048, 'test', 999, 'f', 'f', '2025-06-16 21:57:33.377784', '2025-06-21 07:05:14.003041');
INSERT INTO "public"."t_role" VALUES (1936319106089357312, '系统管理', 999, 'f', 'f', '2025-06-21 15:03:44.618512', '2025-06-21 15:04:04.691919');
INSERT INTO "public"."t_role" VALUES (1936350438311989248, '123', 999, 'f', 'f', '2025-06-21 17:08:14.802355', '2025-06-21 17:08:45.314967');
INSERT INTO "public"."t_role" VALUES (1936351047220072448, '权限3', 999, 'f', 'f', '2025-06-21 17:10:39.97813', '2025-06-21 17:11:12.570017');
INSERT INTO "public"."t_role" VALUES (1936369556469714944, 'test', 999, 'f', 'f', '2025-06-21 18:24:12.926154', '2025-06-21 18:25:27.064833');
INSERT INTO "public"."t_role" VALUES (1936370012377976832, 'a', 999, 'f', 'f', '2025-06-21 18:26:01.624085', '2025-06-21 18:37:29.371068');
INSERT INTO "public"."t_role" VALUES (1936383603722293248, '超级管理员', 999, 'f', 'f', '2025-06-21 19:20:02.052403', '2025-06-21 19:20:32.591212');
INSERT INTO "public"."t_role" VALUES (1936383801630527488, '超级管理员', 999, 'f', 'f', '2025-06-21 19:20:49.237325', '2025-06-21 19:23:30.133242');

-- ----------------------------
-- Primary Key structure for table t_role
-- ----------------------------
ALTER TABLE "public"."t_role" ADD CONSTRAINT "t_role_pk" PRIMARY KEY ("id");
