#!/bin/bash

# 角色列表接口测试脚本
# 使用方法: ./test-role-list.sh [服务器地址]
# 示例: ./test-role-list.sh http://localhost:8080

# 设置服务器地址
BASE_URL=${1:-"http://localhost:8080"}
ENDPOINT="/roles/getRoleList"
FULL_URL="$BASE_URL$ENDPOINT"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印标题
print_title() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 打印测试结果
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ 测试通过${NC}"
    else
        echo -e "${RED}❌ 测试失败${NC}"
    fi
    echo ""
}

# 执行HTTP请求并检查结果
test_request() {
    local test_name="$1"
    local request_data="$2"
    local expected_pattern="$3"
    
    echo -e "${YELLOW}测试: $test_name${NC}"
    echo "请求数据: $request_data"
    echo "请求URL: $FULL_URL"
    
    # 执行请求
    response=$(curl -s -X POST "$FULL_URL" \
        -H "Content-Type: application/json" \
        -d "$request_data")
    
    # 检查响应
    if [ $? -eq 0 ]; then
        echo "响应结果:"
        echo "$response" | jq . 2>/dev/null || echo "$response"
        
        # 检查是否包含预期内容
        if [[ "$response" == *"$expected_pattern"* ]]; then
            print_result 0
            return 0
        else
            echo -e "${RED}响应内容不符合预期${NC}"
            print_result 1
            return 1
        fi
    else
        echo -e "${RED}请求失败${NC}"
        print_result 1
        return 1
    fi
}

# 开始测试
print_title "角色列表接口自动化测试"
echo "测试服务器: $BASE_URL"
echo "测试接口: $ENDPOINT"
echo ""

# 检查服务器是否可达
echo -e "${YELLOW}检查服务器连接...${NC}"
if curl -s --connect-timeout 5 "$BASE_URL" > /dev/null; then
    echo -e "${GREEN}✅ 服务器连接正常${NC}"
else
    echo -e "${RED}❌ 无法连接到服务器: $BASE_URL${NC}"
    echo "请检查:"
    echo "1. 服务器是否启动"
    echo "2. 端口是否正确"
    echo "3. 防火墙设置"
    exit 1
fi
echo ""

# 测试计数器
total_tests=0
passed_tests=0

# 测试1: 基础分页查询
print_title "测试1: 基础分页查询"
test_request "基础分页查询" \
    '{"currentPage":1,"pageSize":10}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试2: 角色名称模糊搜索
print_title "测试2: 角色名称模糊搜索"
test_request "搜索包含'管理'的角色" \
    '{"currentPage":1,"pageSize":10,"roleName":"管理"}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试3: 按状态筛选（启用）
print_title "测试3: 按状态筛选（启用）"
test_request "查询启用状态的角色" \
    '{"currentPage":1,"pageSize":10,"isDisable":false}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试4: 按状态筛选（停用）
print_title "测试4: 按状态筛选（停用）"
test_request "查询停用状态的角色" \
    '{"currentPage":1,"pageSize":10,"isDisable":true}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试5: 组合条件查询
print_title "测试5: 组合条件查询"
test_request "搜索启用状态且包含'管理'的角色" \
    '{"currentPage":1,"pageSize":10,"roleName":"管理","isDisable":false}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试6: 分页测试
print_title "测试6: 分页测试"
test_request "第一页，每页3条" \
    '{"currentPage":1,"pageSize":3}' \
    '"code":200'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试7: 空结果场景
print_title "测试7: 空结果场景"
test_request "搜索不存在的角色" \
    '{"currentPage":1,"pageSize":10,"roleName":"不存在的角色名称"}' \
    '"total":0'
if [ $? -eq 0 ]; then ((passed_tests++)); fi
((total_tests++))

# 测试8: 错误场景 - 缺少必填参数
print_title "测试8: 错误场景 - 缺少必填参数"
echo -e "${YELLOW}测试: 缺少必填参数${NC}"
echo "请求数据: {\"roleName\":\"管理员\"}"
echo "请求URL: $FULL_URL"

response=$(curl -s -X POST "$FULL_URL" \
    -H "Content-Type: application/json" \
    -d '{"roleName":"管理员"}')

echo "响应结果:"
echo "$response" | jq . 2>/dev/null || echo "$response"

# 这个测试预期会失败（返回400或500），所以我们检查是否不是200
if [[ "$response" != *'"code":200'* ]]; then
    echo -e "${GREEN}✅ 正确处理了错误场景${NC}"
    ((passed_tests++))
else
    echo -e "${RED}❌ 应该返回错误，但返回了成功${NC}"
fi
((total_tests++))
echo ""

# 输出测试总结
print_title "测试总结"
echo -e "总测试数: ${BLUE}$total_tests${NC}"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$((total_tests - passed_tests))${NC}"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    exit 0
else
    echo -e "${RED}⚠️  有测试失败，请检查接口实现${NC}"
    exit 1
fi
