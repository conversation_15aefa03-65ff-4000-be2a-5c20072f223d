### 🚨 部署前关键接口测试清单
### 必须全部通过才能部署！

### 变量定义
@baseUrl = http://localhost:8080

### =====================================================
### 🔥 高风险接口（必须测试）
### =====================================================

### 1. 用户管理 - 获取所有用户（已修复）
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 2. 部门树查询 - 选择人员（刚修复）
POST {{baseUrl}}/org-structure/choseUser
Content-Type: application/json

### 3. 部门结构树查询（刚修复）
POST {{baseUrl}}/org-structure/tree
Content-Type: application/json

{
  "excludeOrgId": null,
  "includeDeleted": false,
  "maxLevel": 10
}

### 4. 菜单树查询
POST {{baseUrl}}/menus/getMenus
Content-Type: application/json

{
  "moduleIdentifier": "",
  "name": "",
  "isDisable": false
}

### 5. 用户列表分页查询
POST {{baseUrl}}/users/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### =====================================================
### 🟡 中风险接口（建议测试）
### =====================================================

### 6. 角色列表查询
POST {{baseUrl}}/roles/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### 7. 用户详情查询
POST {{baseUrl}}/users/detail
Content-Type: application/json

{
  "id": "6001"
}

### 8. 用户角色查询
POST {{baseUrl}}/users/roles
Content-Type: application/json

{
  "userId": 6001
}

### 9. 角色用户分配列表
POST {{baseUrl}}/users/getRoleUserList
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "roleId": 7001
}

### 10. 数据权限列表
POST {{baseUrl}}/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### =====================================================
### 🟢 低风险接口（可选测试）
### =====================================================

### 11. 菜单模块列表
GET {{baseUrl}}/menus/getModules
Content-Type: application/json

### 12. 角色选项
POST {{baseUrl}}/users/getRoleOptions
Content-Type: application/json

### 13. 部门搜索
POST {{baseUrl}}/org-structure/search
Content-Type: application/json

{
  "organName": "技术",
  "preId": null,
  "includeDeleted": false
}

### =====================================================
### 📋 检查清单
### =====================================================

### ✅ 必须通过的测试：
### 1. 所有高风险接口返回 200 状态码
### 2. 返回的数据结构正确，不是空数组
### 3. 响应时间在可接受范围内（< 10秒）
### 4. 没有 500 错误或异常

### ⚠️ 常见问题排查：
### 1. 如果返回空数据：检查数据库连接和数据
### 2. 如果返回 500 错误：检查日志中的异常信息
### 3. 如果响应很慢：检查是否创建了索引
### 4. 如果字段为 null：检查 SQL 查询和字段映射

### 🔧 已修复的问题：
### ✅ 用户管理 - 根部门查询条件修复
### ✅ 部门树查询 - 表名和字段类型修复
### ✅ 性能优化 - 数据库索引添加

### 📊 性能基准：
### - /users/getAllUsers: < 5秒（之前50+秒）
### - /org-structure/choseUser: < 3秒
### - /org-structure/tree: < 3秒
### - /menus/getMenus: < 2秒
### - /users/list: < 1秒

### 🚨 如果任何高风险接口失败：
### 1. 立即停止部署
### 2. 检查错误日志
### 3. 修复问题后重新测试
### 4. 确保所有测试通过后再部署

### 💡 部署后监控：
### 1. 观察接口响应时间
### 2. 监控错误日志
### 3. 检查用户反馈
### 4. 准备回滚方案
