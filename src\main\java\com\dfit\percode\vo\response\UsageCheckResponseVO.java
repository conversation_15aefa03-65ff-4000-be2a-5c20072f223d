package com.dfit.percode.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 使用情况检查响应VO
 * 用于删除功能中返回使用情况检查结果
 *
 * <AUTHOR>
 * @date 2024-06-30
 */
@Data
@ApiModel(value = "使用情况检查响应VO", description = "删除使用情况检查响应")
public class UsageCheckResponseVO {
    
    @ApiModelProperty("是否可以直接删除")
    private Boolean canDirectDelete;
    
    @ApiModelProperty("使用情况详细信息")
    private UsageInfoVO usageInfo;
}
