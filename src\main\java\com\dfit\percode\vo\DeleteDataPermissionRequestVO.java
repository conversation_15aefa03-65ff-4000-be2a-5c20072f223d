package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除数据权限请求VO类
 * 按照前端格式要求设计（驼峰命名）
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteDataPermissionRequestVO", description = "删除数据权限请求参数")
public class DeleteDataPermissionRequestVO {

    @ApiModelProperty(value = "数据权限ID", required = true, example = "1930307870503604224")
    private String dataId;

    @ApiModelProperty(value = "是否强制删除", required = false, example = "false")
    private Boolean forceDelete = false;
}
