package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.IMenuModuleService;
import com.dfit.percode.vo.AddMenuModuleRequestVO;
import com.dfit.percode.vo.AddMenuRequestVO;
import com.dfit.percode.vo.DeleteMenuModuleRequestVO;
import com.dfit.percode.vo.DeleteMenuRequestVO;
import com.dfit.percode.vo.MenuDetailRequestVO;
import com.dfit.percode.vo.MenuDetailResponseVO;
import com.dfit.percode.vo.MenuListRequestVO;
import com.dfit.percode.vo.MenuModuleListResponseVO;
import com.dfit.percode.vo.MenuTreeResponseVO;
import com.dfit.percode.vo.ModuleMenuTreeResponseVO;
import com.dfit.percode.vo.UpdateMenuRequestVO;
import com.dfit.percode.vo.CheckIdentifierRequestVO;
import com.dfit.percode.vo.CheckIdentifierResponseVO;
import com.dfit.percode.exception.UsageConflictException;
import com.dfit.percode.service.impl.MenuModuleServiceImpl;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 菜单模块管理控制器
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@RestController
@RequestMapping("/menus")
@Api(tags = "菜单管理接口")
@Slf4j
public class MenuModuleController {

    @Autowired
    private IMenuModuleService menuModuleService;

    /**
     * 新增菜单模块
     * 按照前端弹窗设计实现
     *
     * @param request 新增菜单模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/addModule", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增菜单模块", notes = "新增菜单模块，包含模块名称、标识和排序")
    public BaseResult addMenuModule(@RequestBody AddMenuModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增菜单模块
            menuModuleService.addMenuModule(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("新增菜单模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取菜单模块列表
     * 返回所有未删除的菜单模块，按排序字段排序
     *
     * @return 统一返回格式，data为菜单模块数组
     */
    @RequestMapping(value = "/getModules", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取菜单模块列表", notes = "获取所有菜单模块列表，按排序字段排序")
    public BaseResult getMenuModules() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取菜单模块列表
            List<MenuModuleListResponseVO> moduleList = menuModuleService.getMenuModules();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(moduleList);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取菜单模块列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除菜单模块
     * 逻辑删除菜单模块，设置is_del为true
     *
     * @param request 删除菜单模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteModule", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除菜单模块", notes = "逻辑删除菜单模块，设置删除标记")
    public BaseResult deleteMenuModule(@RequestBody DeleteMenuModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除菜单模块
            menuModuleService.deleteMenuModule(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("删除成功");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("删除菜单模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 新增菜单
     * 在指定模块下新增菜单项，支持目录、菜单、按钮三种类型
     *
     * @param request 新增菜单请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/addMenu", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增菜单", notes = "新增菜单项，支持目录、菜单、按钮类型")
    public BaseResult addMenu(@RequestBody AddMenuRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增菜单
            menuModuleService.addMenu(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("新增菜单失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取菜单详情
     * 根据菜单ID获取菜单的详细信息，用于编辑表单回显
     *
     * @param request 获取菜单详情请求参数
     * @return 菜单详情数据
     */
    @RequestMapping(value = "/getMenuDetail", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取菜单详情", notes = "根据菜单ID获取菜单详细信息，用于编辑表单")
    public BaseResult getMenuDetail(@RequestBody MenuDetailRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取菜单详情
            MenuDetailResponseVO menuDetail = menuModuleService.getMenuDetail(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(menuDetail);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取菜单详情失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 查询菜单列表（模块级优化版本）
     * 返回按模块分组的菜单树结构，外层为模块信息，内层保持现有菜单树结构不变
     *
     * @param request 查询菜单列表请求参数
     * @return 模块级菜单树结构数据
     */
    @RequestMapping(value = "/getMenus", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "查询菜单列表（模块级优化版本）", notes = "返回按模块分组的菜单树结构")
    public BaseResult<List<ModuleMenuTreeResponseVO>> getMenus(@RequestBody MenuListRequestVO request) {
        BaseResult<List<ModuleMenuTreeResponseVO>> baseResult = new BaseResult<>();

        try {
            log.info("开始查询菜单列表（模块级优化版本）");
            log.info("请求参数 - 模块标识: {}, 菜单名称: {}, 包含禁用菜单: {}",
                    request.getModuleIdentifier(), request.getName(), request.getIsDisable());
            long startTime = System.currentTimeMillis();

            // 调用模块级优化版本的服务层方法
            List<ModuleMenuTreeResponseVO> menuList = menuModuleService.getMenusOptimizedWithModules(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(menuList);

            long endTime = System.currentTimeMillis();
            log.info("菜单列表查询成功（模块级优化版本），模块数量: {}，接口耗时: {}ms",
                    menuList.size(), (endTime - startTime));

        } catch (Exception e) {
            // 异常处理
            log.error("查询菜单列表失败（模块级优化版本）", e);
            baseResult.setCode(500);
            baseResult.setMessage("查询菜单列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 查询菜单列表（优化版本）
     * 使用一次性查询+内存构建替代递归查询，大幅提升性能
     * 返回树形结构的菜单数据，支持按模块筛选
     *
     * @param request 查询菜单列表请求参数
     * @return 菜单树形结构数据
     */
    @RequestMapping(value = "/getMenus-optimized", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "查询菜单列表（优化版本）", notes = "使用批量查询优化性能，返回树形结构的菜单数据")
    public BaseResult getMenusOptimized(@RequestBody MenuListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始查询菜单列表（优化版本）");
            log.info("请求参数 - 模块标识: {}, 菜单名称: {}, 包含禁用菜单: {}",
                    request.getModuleIdentifier(), request.getName(), request.getIsDisable());
            long startTime = System.currentTimeMillis();

            // 调用服务层查询菜单列表（优化版本）
            List<MenuTreeResponseVO> menuList = menuModuleService.getMenusOptimized(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(menuList);

            long endTime = System.currentTimeMillis();
            log.info("菜单列表查询成功（优化版本），根节点数量: {}，接口耗时: {}ms",
                    menuList.size(), (endTime - startTime));

        } catch (Exception e) {
            // 异常处理
            log.error("查询菜单列表失败（优化版本）", e);
            baseResult.setCode(500);
            baseResult.setMessage("查询菜单列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 修改菜单
     * 支持修改菜单的所有字段，包括父级关系变更
     * 包含完整的数据验证和层级关系检查
     *
     * @param request 修改菜单请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/editMenu", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改菜单", notes = "修改菜单信息，支持父级关系变更，包含循环引用检查")
    public BaseResult updateMenu(@RequestBody UpdateMenuRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层修改菜单
            menuModuleService.updateMenu(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("修改成功");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("修改菜单失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除菜单
     * 逻辑删除菜单项，设置is_del为true
     * 包含子菜单检查，防止删除有子菜单的父级菜单
     *
     * @param request 删除菜单请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteMenu", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除菜单", notes = "逻辑删除菜单项，包含子菜单和权限使用检查")
    public BaseResult deleteMenu(@RequestBody DeleteMenuRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除菜单
            menuModuleService.deleteMenu(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("删除成功");
            baseResult.setData(null);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("删除菜单失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 检查权限标识符是否重复
     * 支持菜单权限和数据权限的标识符重复检测
     * 前端可在输入框内容变化时实时调用此接口
     *
     * @param request 检查权限标识符请求参数
     * @return 检查结果，包含是否可用、提示信息等
     */
    @RequestMapping(value = "/checkIdentifier", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "检查权限标识符", notes = "检测菜单权限和数据权限的标识符是否重复，支持实时验证")
    public BaseResult checkIdentifier(@RequestBody CheckIdentifierRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层检查权限标识符
            CheckIdentifierResponseVO result = menuModuleService.checkIdentifier(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("检查权限标识符失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    // ==================== V2版本删除功能相关接口 ====================

    /**
     * 删除菜单 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除菜单请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteMenuV2", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除菜单V2", notes = "支持两阶段删除，检查使用情况后可强制删除")
    public BaseResult deleteMenuV2(@RequestBody DeleteMenuRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除菜单V2
            MenuModuleServiceImpl serviceImpl = (MenuModuleServiceImpl) menuModuleService;
            Object result = serviceImpl.deleteMenuV2(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result);

        } catch (UsageConflictException e) {
            // 检测到使用情况，返回409状态码
            log.warn("菜单删除检测到使用情况: {}", e.getMessage());
            baseResult.setCode(409);
            baseResult.setMessage(e.getMessage());
            baseResult.setData(e.getUsageInfo());

        } catch (Exception e) {
            log.error("删除菜单失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除菜单失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除菜单模块 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除菜单模块请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteModuleV2", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除菜单模块V2", notes = "支持两阶段删除，检查使用情况后可强制删除")
    public BaseResult deleteModuleV2(@RequestBody DeleteMenuModuleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除菜单模块V2
            MenuModuleServiceImpl serviceImpl = (MenuModuleServiceImpl) menuModuleService;
            Object result = serviceImpl.deleteModuleV2(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result);

        } catch (UsageConflictException e) {
            // 检测到使用情况，返回409状态码
            log.warn("菜单模块删除检测到使用情况: {}", e.getMessage());
            baseResult.setCode(409);
            baseResult.setMessage(e.getMessage());
            baseResult.setData(e.getUsageInfo());

        } catch (Exception e) {
            log.error("删除菜单模块失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除菜单模块失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
