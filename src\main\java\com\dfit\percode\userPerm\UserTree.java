package com.dfit.percode.userPerm;

import lombok.Data;
import java.util.List;

/**
 * 用户树形结构实体类
 * 用于组织架构树形展示
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class UserTree {
    
    /**
     * 部门ID
     */
    private Long id;
    
    /**
     * 部门名称
     */
    private String organName;
    
    /**
     * 子部门列表
     */
    private List<UserTree> departChildren;
    
    /**
     * 部门下的用户列表
     */
    private List<UserData> user;
}
