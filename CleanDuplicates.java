import java.sql.*;
import java.util.*;

public class CleanDuplicates {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== 清理重复部门名称 ===");
        
        try {
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            conn.setAutoCommit(false);
            
            // 查找所有重复的部门名称
            PreparedStatement findDuplicatesStmt = conn.prepareStatement(
                "SELECT organ_name " +
                "FROM t_org_structure " +
                "WHERE data_source = 2 " +
                "GROUP BY organ_name " +
                "HAVING COUNT(*) > 1 " +
                "ORDER BY organ_name"
            );
            
            ResultSet duplicateNames = findDuplicatesStmt.executeQuery();
            List<String> duplicateList = new ArrayList<>();
            
            while (duplicateNames.next()) {
                duplicateList.add(duplicateNames.getString("organ_name"));
            }
            duplicateNames.close();
            findDuplicatesStmt.close();
            
            System.out.println("发现 " + duplicateList.size() + " 个重复的部门名称");
            
            int totalDeleted = 0;
            
            // 对每个重复的部门名称进行处理
            for (String organName : duplicateList) {
                System.out.println("\n处理重复部门: " + organName);
                
                // 获取该名称的所有记录，按ID排序
                PreparedStatement getRecordsStmt = conn.prepareStatement(
                    "SELECT id, pre_id, create_time " +
                    "FROM t_org_structure " +
                    "WHERE data_source = 2 AND organ_name = ? " +
                    "ORDER BY id"
                );
                getRecordsStmt.setString(1, organName);
                
                ResultSet records = getRecordsStmt.executeQuery();
                List<Long> ids = new ArrayList<>();
                
                while (records.next()) {
                    ids.add(records.getLong("id"));
                }
                records.close();
                getRecordsStmt.close();
                
                if (ids.size() > 1) {
                    // 保留第一个（ID最小的），删除其他的
                    Long keepId = ids.get(0);
                    System.out.println("  保留记录 ID: " + keepId);
                    
                    // 首先检查是否有子部门引用了要删除的记录
                    for (int i = 1; i < ids.size(); i++) {
                        Long deleteId = ids.get(i);
                        
                        // 检查是否有子部门
                        PreparedStatement checkChildrenStmt = conn.prepareStatement(
                            "SELECT COUNT(*) FROM t_org_structure WHERE pre_id = ? AND data_source = 2"
                        );
                        checkChildrenStmt.setLong(1, deleteId);
                        ResultSet childrenRs = checkChildrenStmt.executeQuery();
                        
                        int childrenCount = 0;
                        if (childrenRs.next()) {
                            childrenCount = childrenRs.getInt(1);
                        }
                        childrenRs.close();
                        checkChildrenStmt.close();
                        
                        if (childrenCount > 0) {
                            // 将子部门的父ID更新为保留的记录ID
                            PreparedStatement updateChildrenStmt = conn.prepareStatement(
                                "UPDATE t_org_structure SET pre_id = ? WHERE pre_id = ? AND data_source = 2"
                            );
                            updateChildrenStmt.setLong(1, keepId);
                            updateChildrenStmt.setLong(2, deleteId);
                            int updatedChildren = updateChildrenStmt.executeUpdate();
                            updateChildrenStmt.close();
                            
                            System.out.println("  更新了 " + updatedChildren + " 个子部门的父ID: " + deleteId + " -> " + keepId);
                        }
                        
                        // 删除重复记录
                        PreparedStatement deleteStmt = conn.prepareStatement(
                            "DELETE FROM t_org_structure WHERE id = ? AND data_source = 2"
                        );
                        deleteStmt.setLong(1, deleteId);
                        int deleted = deleteStmt.executeUpdate();
                        deleteStmt.close();
                        
                        if (deleted > 0) {
                            System.out.println("  删除重复记录 ID: " + deleteId);
                            totalDeleted++;
                        }
                    }
                }
            }
            
            // 提交事务
            conn.commit();
            
            System.out.println("\n=== 清理完成 ===");
            System.out.println("总共删除了 " + totalDeleted + " 条重复记录");
            
            // 验证结果
            PreparedStatement verifyStmt = conn.prepareStatement(
                "SELECT COUNT(*) as total_count, " +
                "COUNT(DISTINCT organ_name) as unique_names " +
                "FROM t_org_structure WHERE data_source = 2"
            );
            ResultSet verifyRs = verifyStmt.executeQuery();
            
            if (verifyRs.next()) {
                int totalCount = verifyRs.getInt("total_count");
                int uniqueNames = verifyRs.getInt("unique_names");
                
                System.out.println("验证结果:");
                System.out.println("  总记录数: " + totalCount);
                System.out.println("  唯一名称数: " + uniqueNames);
                
                if (totalCount == uniqueNames) {
                    System.out.println("✅ 清理成功，无重复记录");
                } else {
                    System.out.println("⚠️  仍有重复记录: " + (totalCount - uniqueNames));
                }
            }
            verifyRs.close();
            verifyStmt.close();
            
            conn.close();
            
        } catch (SQLException e) {
            System.err.println("清理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
