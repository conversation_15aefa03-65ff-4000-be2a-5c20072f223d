2025-06-05 00:04:03.443 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:04:03.451 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 00:04:14.834 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 35668 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 00:04:14.836 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 00:04:16.283 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:04:16.287 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:04:16.325 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 00:04:16.334 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:04:16.335 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:04:16.344 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 00:04:16.356 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:04:16.357 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 00:04:16.380 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-05 00:04:16.403 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:04:16.407 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 00:04:16.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-05 00:04:17.624 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 00:04:17.635 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 00:04:17.636 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 00:04:17.636 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 00:04:17.811 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 00:04:17.811 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2910 ms
2025-06-05 00:04:17.868 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 00:04:17.935 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 00:04:18.100 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 00:04:18.848 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 00:04:18.909 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 00:04:19.081 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 00:04:19.235 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 00:04:19.916 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 00:04:19.929 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:04:21.075 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 00:04:23.297 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 00:04:23.313 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 00:04:23.782 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.723 seconds (JVM running for 11.252)
2025-06-05 00:05:59.344 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 00:05:59.345 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 00:05:59.348 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-05 00:06:22.957 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始更新用户角色分配状态
2025-06-05 00:06:22.957 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户ID: 3007, 角色ID: 2001, isDel: false
2025-06-05 00:06:22.959 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 执行授权操作
2025-06-05 00:06:23.065 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 关联记录不存在，创建新记录
2025-06-05 00:06:23.134 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 新关联记录创建成功
2025-06-05 00:06:23.135 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户角色分配状态更新完成
2025-06-05 00:31:34.918 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:31:34.944 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 00:31:44.636 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 38724 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 00:31:44.638 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 00:31:46.354 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:31:46.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:31:46.394 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 00:31:46.399 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:31:46.400 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:31:46.411 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 00:31:46.428 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:31:46.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 00:31:46.444 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-05 00:31:46.462 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:31:46.465 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 00:31:46.483 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 00:31:47.630 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 00:31:47.655 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 00:31:47.656 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 00:31:47.656 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 00:31:48.028 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 00:31:48.029 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3306 ms
2025-06-05 00:31:48.131 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 00:31:48.195 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 00:31:48.459 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 00:31:50.480 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 00:31:50.545 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 00:31:50.749 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 00:31:50.920 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 00:31:51.924 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 00:31:51.941 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:31:53.367 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 00:31:57.509 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 00:31:57.526 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 00:31:58.214 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.317 seconds (JVM running for 16.288)
2025-06-05 00:32:58.836 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 00:32:58.836 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 00:32:58.837 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-05 00:33:48.060 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:33:48.068 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 00:33:56.792 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 24664 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 00:33:56.794 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 00:33:58.121 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:33:58.124 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:33:58.158 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 00:33:58.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:33:58.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:33:58.179 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 00:33:58.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:33:58.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 00:33:58.209 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-05 00:33:58.228 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:33:58.229 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 00:33:58.243 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 00:33:59.233 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 00:33:59.246 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 00:33:59.247 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 00:33:59.247 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 00:33:59.543 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 00:33:59.543 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2677 ms
2025-06-05 00:33:59.601 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 00:33:59.664 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 00:33:59.843 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 00:34:27.175 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 00:34:27.256 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 00:34:27.448 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 00:34:27.607 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 00:34:28.786 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 00:34:28.800 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:34:29.781 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 00:34:32.495 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 00:34:32.520 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 00:34:33.200 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 37.169 seconds (JVM running for 38.959)
2025-06-05 00:34:39.848 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 00:34:39.848 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 00:34:39.852 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-05 00:34:48.851 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单模块
2025-06-05 00:34:48.852 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块名称: 系统管理, 模块标识: system_management, 排序: 1
2025-06-05 00:40:50.394 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:40:50.403 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 00:40:59.517 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 33176 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 00:40:59.518 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 00:41:01.053 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:41:01.069 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:41:01.119 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 00:41:01.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:41:01.133 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:41:01.147 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 00:41:01.163 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:41:01.166 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 00:41:01.189 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 00:41:01.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:41:01.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 00:41:01.258 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-05 00:41:02.355 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 00:41:02.373 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 00:41:02.374 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 00:41:02.375 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 00:41:02.670 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 00:41:02.670 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3071 ms
2025-06-05 00:41:02.794 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 00:41:02.858 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 00:41:03.133 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 00:41:03.949 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 00:41:04.017 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 00:41:04.238 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 00:41:04.401 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 00:41:05.227 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 00:41:05.250 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:41:07.584 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 00:41:09.751 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 00:41:09.770 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 00:41:10.214 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.726 seconds (JVM running for 13.25)
2025-06-05 00:41:39.715 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 00:41:39.716 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 00:41:39.719 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 00:41:46.544 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单模块
2025-06-05 00:41:46.545 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块名称: 系统管理, 模块标识: system_management, 排序: 1
2025-06-05 00:56:37.154 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:56:37.157 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 00:56:46.718 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 39064 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 00:56:46.721 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 00:56:48.679 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:56:48.685 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:56:48.705 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 00:56:48.708 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:56:48.709 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 00:56:48.717 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 00:56:48.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:56:48.732 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 00:56:48.744 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 JPA repository interfaces.
2025-06-05 00:56:48.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 00:56:48.763 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 00:56:48.779 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 00:56:49.652 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 00:56:49.663 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 00:56:49.664 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 00:56:49.664 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 00:56:49.869 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 00:56:49.870 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3049 ms
2025-06-05 00:56:49.924 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 00:56:49.983 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 00:56:50.202 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 00:56:51.312 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 00:56:51.474 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 00:56:51.811 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 00:56:52.001 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 00:56:53.099 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 00:56:53.108 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 00:56:54.315 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 00:56:57.055 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 00:56:57.069 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 00:56:57.618 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.929 seconds (JVM running for 14.557)
2025-06-05 00:57:14.085 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 00:57:14.086 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 00:57:14.087 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-05 00:57:14.268 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单模块
2025-06-05 00:57:14.268 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块名称: 系统管理, 模块标识: system_management, 排序: 1
2025-06-05 00:57:14.430 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块新增成功，ID: 1930307870503604224
2025-06-05 01:02:50.157 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:02:50.161 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 01:02:59.424 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 11684 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 01:02:59.426 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 01:03:01.585 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:03:01.588 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:03:01.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 01:03:01.621 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:03:01.621 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:03:01.632 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 01:03:01.643 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:03:01.645 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 01:03:01.664 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 01:03:01.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:03:01.684 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 01:03:01.705 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 01:03:02.719 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 01:03:02.731 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 01:03:02.732 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:03:02.732 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 01:03:02.911 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:03:02.912 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3320 ms
2025-06-05 01:03:03.015 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 01:03:03.106 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 01:03:03.343 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 01:03:04.258 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 01:03:04.426 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 01:03:04.682 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 01:03:04.968 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 01:03:05.785 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 01:03:05.799 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:03:06.867 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 01:03:09.149 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 01:03:09.166 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 01:03:09.755 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.299 seconds (JVM running for 12.884)
2025-06-05 01:03:42.235 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 01:03:42.235 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 01:03:42.237 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 01:04:28.904 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单模块列表
2025-06-05 01:04:29.058 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块列表查询完成，共2条记录
2025-06-05 01:09:24.168 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:09:24.172 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 01:09:32.561 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 33632 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 01:09:32.563 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 01:09:33.964 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:09:33.967 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:09:33.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 01:09:33.997 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:09:33.998 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:09:34.010 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 01:09:34.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:09:34.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 01:09:34.041 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-05 01:09:34.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:09:34.062 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 01:09:34.076 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 01:09:35.002 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 01:09:35.015 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 01:09:35.016 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:09:35.016 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 01:09:35.218 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:09:35.218 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2560 ms
2025-06-05 01:09:35.272 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 01:09:35.321 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 01:09:35.478 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 01:09:36.321 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 01:09:36.409 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 01:09:36.748 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 01:09:36.979 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 01:09:37.757 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 01:09:37.767 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:09:38.716 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 01:09:40.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 01:09:40.671 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 01:09:41.189 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.635 seconds (JVM running for 11.505)
2025-06-05 01:09:52.000 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 01:09:52.000 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 01:09:52.001 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-05 01:10:12.076 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单模块
2025-06-05 01:10:12.076 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块ID: 1001, 模块标识: system_management
2025-06-05 01:11:23.146 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单模块
2025-06-05 01:11:23.147 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块ID: 1930307870503604200, 模块标识: system_management
2025-06-05 01:11:48.866 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单模块
2025-06-05 01:11:48.866 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块ID: 1930307870503604200, 模块标识: system_management
2025-06-05 01:12:08.463 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单模块
2025-06-05 01:12:08.463 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块ID: 1930307870503604200, 模块标识: system_management
2025-06-05 01:13:49.985 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单模块列表
2025-06-05 01:13:50.230 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块列表查询完成，共2条记录
2025-06-05 01:19:12.666 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:19:12.670 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 01:19:20.805 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 22464 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 01:19:20.808 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 01:19:22.103 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:19:22.106 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:19:22.133 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 01:19:22.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:19:22.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 01:19:22.152 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 01:19:22.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:19:22.165 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 01:19:22.183 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-05 01:19:22.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 01:19:22.216 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 01:19:22.246 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-05 01:19:23.630 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 01:19:23.640 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 01:19:23.641 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 01:19:23.641 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 01:19:23.851 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 01:19:23.851 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2958 ms
2025-06-05 01:19:23.909 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 01:19:23.946 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 01:19:24.134 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 01:19:25.011 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 01:19:25.075 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 01:19:25.261 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 01:19:25.421 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 01:19:26.184 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 01:19:26.195 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:19:27.489 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 01:19:29.362 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 01:19:29.377 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 01:19:29.827 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.071 seconds (JVM running for 11.733)
2025-06-05 01:19:39.114 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 01:19:39.114 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 01:19:39.116 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-05 01:19:47.226 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单模块
2025-06-05 01:19:47.227 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: system_management
2025-06-05 01:19:47.484 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块删除成功，模块标识: system_management
2025-06-05 01:23:37.085 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 01:23:37.090 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 09:07:27.580 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 31388 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 09:07:27.582 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 09:07:29.681 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:07:29.686 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:07:29.714 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 09:07:29.720 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:07:29.721 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:07:29.732 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 09:07:29.745 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:07:29.747 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 09:07:29.763 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 09:07:29.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:07:29.789 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:07:29.806 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-05 09:07:32.151 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 09:07:32.171 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 09:07:32.172 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:07:32.173 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 09:07:32.479 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:07:32.479 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4786 ms
2025-06-05 09:07:32.535 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 09:07:32.621 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 09:07:32.856 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 09:07:33.970 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 09:07:34.193 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 09:07:34.566 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 09:07:34.980 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 09:07:36.695 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 09:07:36.715 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:07:37.979 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 09:07:42.335 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 09:07:42.360 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 09:07:42.936 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.673 seconds (JVM running for 20.283)
2025-06-05 09:07:55.256 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:07:55.257 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:07:55.262 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-05 09:08:56.367 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单
2025-06-05 09:08:56.368 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单名称: 系统管理, 模块标识: system_management, 菜单类型: 1
2025-06-05 09:09:29.239 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单模块
2025-06-05 09:09:29.239 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块名称: 用户管理, 模块标识: user_management, 排序: 1
2025-06-05 09:09:29.419 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块新增成功，ID: 1930431748999614464
2025-06-05 09:12:35.175 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单模块
2025-06-05 09:12:35.176 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块名称: 权限管理, 模块标识: permission_management, 排序: 1
2025-06-05 09:12:35.291 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单模块新增成功，ID: 1930432528821391360
2025-06-05 09:12:47.570 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始新增菜单
2025-06-05 09:12:47.572 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单名称: 权限管理, 模块标识: permission_management, 菜单类型: 1
2025-06-05 09:12:47.705 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单新增成功，ID: 1930432580826566656
2025-06-05 09:26:54.426 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:26:54.460 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 09:27:06.826 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 32132 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 09:27:06.831 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 09:27:08.697 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:27:08.702 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:27:08.729 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 09:27:08.733 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:27:08.734 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:27:08.746 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 09:27:08.757 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:27:08.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 09:27:08.775 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 09:27:08.799 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:27:08.801 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:27:08.816 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 6 ms. Found 0 Redis repository interfaces.
2025-06-05 09:27:09.815 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 09:27:09.851 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 09:27:09.853 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:27:09.854 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 09:27:10.175 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:27:10.175 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3247 ms
2025-06-05 09:27:10.245 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 09:27:10.334 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 09:27:10.633 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 09:27:11.657 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 09:27:11.717 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 09:27:11.931 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 09:27:12.285 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 09:27:13.263 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 09:27:13.284 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:27:14.980 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 09:27:17.465 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 09:27:17.488 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 09:27:18.006 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.381 seconds (JVM running for 14.409)
2025-06-05 09:27:29.980 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:27:29.981 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:27:29.983 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 09:27:46.282 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1930307870503604224
2025-06-05 09:28:39.733 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1930432580826566656
2025-06-05 09:28:39.811 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单详情获取成功，菜单名称: 权限管理
2025-06-05 09:43:48.488 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:43:48.493 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 09:44:04.075 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 26600 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 09:44:04.079 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 09:44:05.967 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:44:05.970 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:44:06.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 09:44:06.005 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:44:06.007 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:44:06.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 09:44:06.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:44:06.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 09:44:06.047 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-05 09:44:06.066 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:44:06.068 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:44:06.089 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-05 09:44:07.269 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 09:44:07.294 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 09:44:07.296 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:44:07.297 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 09:44:07.675 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:44:07.676 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3440 ms
2025-06-05 09:44:07.750 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 09:44:07.826 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 09:44:08.357 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 09:45:50.234 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 32724 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 09:45:50.236 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 09:45:51.632 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:45:51.635 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:45:51.665 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 09:45:51.670 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:45:51.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:45:51.682 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 09:45:51.695 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:45:51.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 09:45:51.715 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-05 09:45:51.737 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:45:51.741 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:45:51.759 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-05 09:45:52.988 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 09:45:52.999 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 09:45:53.000 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:45:53.000 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 09:45:53.215 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:45:53.215 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2899 ms
2025-06-05 09:45:53.281 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 09:45:53.355 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 09:45:53.554 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 09:45:54.441 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 09:45:54.524 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 09:45:55.195 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 09:45:55.477 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 09:45:56.380 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 09:45:56.398 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:45:57.853 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 09:46:00.424 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 09:46:00.444 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 09:46:01.144 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.983 seconds (JVM running for 14.094)
2025-06-05 09:46:07.691 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:46:07.693 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:46:07.704 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-06-05 09:49:07.158 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表
2025-06-05 09:49:07.160 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-05 09:49:10.059 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功，根节点数量: 2
2025-06-05 09:56:22.091 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:56:22.112 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 09:56:44.299 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 25692 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 09:56:44.303 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 09:56:47.308 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:56:47.316 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:56:47.433 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 97 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 09:56:47.450 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:56:47.451 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 09:56:47.476 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 09:56:47.501 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:56:47.504 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 09:56:47.547 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 JPA repository interfaces.
2025-06-05 09:56:47.590 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:56:47.594 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:56:47.644 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-05 09:56:49.356 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 09:56:49.372 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 09:56:49.374 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:56:49.374 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 09:56:49.632 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:56:49.633 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5138 ms
2025-06-05 09:56:49.704 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 09:56:49.802 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 09:56:50.019 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 09:56:51.091 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 09:56:51.188 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 09:56:51.515 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 09:56:51.858 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 09:56:54.324 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 09:56:54.407 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 09:56:57.363 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 09:57:01.573 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 09:57:01.609 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 09:57:02.712 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.672 seconds (JVM running for 24.588)
2025-06-05 09:57:43.340 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:57:43.342 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:57:43.347 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 09:57:57.282 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 开始插入菜单测试数据
2025-06-05 09:57:57.532 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 菜单模块数据插入完成
2025-06-05 09:57:57.641 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 系统管理模块菜单插入完成
2025-06-05 09:57:57.727 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 权限管理模块菜单插入完成
2025-06-05 09:57:57.816 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 业务管理模块菜单插入完成
2025-06-05 09:57:57.912 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 报表中心模块菜单插入完成
2025-06-05 09:57:58.002 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 监控中心模块菜单插入完成
2025-06-05 09:57:58.003 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.TestDataServiceImpl - 所有菜单测试数据插入成功
2025-06-05 09:58:14.899 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表
2025-06-05 09:58:14.900 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-05 09:58:19.011 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功，根节点数量: 7
2025-06-05 10:14:52.961 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 10:14:52.973 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 10:15:10.901 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12448 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 10:15:10.905 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 10:15:12.959 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:15:12.962 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 10:15:12.996 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 10:15:13.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:15:13.002 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 10:15:13.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 10:15:13.034 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:15:13.036 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 10:15:13.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-05 10:15:13.092 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:15:13.095 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 10:15:13.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-05 10:15:14.620 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 10:15:14.638 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 10:15:14.639 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 10:15:14.639 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 10:15:14.977 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 10:15:14.978 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3956 ms
2025-06-05 10:15:15.070 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 10:15:15.162 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 10:15:15.511 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 10:15:17.570 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 10:15:17.680 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 10:15:17.958 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 10:15:18.290 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 10:15:19.588 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 10:15:19.616 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 10:15:21.882 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 10:15:24.918 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 10:15:24.939 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 10:15:25.629 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.282 seconds (JVM running for 19.497)
2025-06-05 10:16:22.008 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 10:16:22.008 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 10:16:22.014 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-05 10:37:26.777 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 10:37:26.792 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 10:37:59.679 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 32080 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 10:37:59.682 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 10:38:06.289 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:38:06.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 10:38:06.561 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 192 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 10:38:06.624 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:38:06.636 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 10:38:06.692 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 10:38:06.733 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:38:06.739 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 10:38:07.091 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 162 ms. Found 0 JPA repository interfaces.
2025-06-05 10:38:07.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 10:38:07.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 10:38:07.334 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-06-05 10:38:10.725 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 10:38:10.776 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 10:38:10.778 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 10:38:10.778 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 10:38:11.610 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 10:38:11.611 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 11661 ms
2025-06-05 10:38:11.805 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 10:38:12.012 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 10:38:12.523 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 10:38:13.912 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 10:38:14.072 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 10:38:14.727 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 10:38:15.449 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 10:38:17.793 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 10:38:17.840 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 10:38:22.524 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 10:38:29.994 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 10:38:30.037 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 10:38:31.195 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 36.276 seconds (JVM running for 40.851)
2025-06-05 10:39:45.362 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 10:39:45.370 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 10:39:45.381 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2025-06-05 10:42:11.407 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始修改菜单
2025-06-05 10:42:11.410 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 2002, 菜单名称: 用户管理（新）, 父级ID: 2001, 模块标识: system_management
2025-06-05 10:42:12.264 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单修改成功，菜单ID: 2002
2025-06-05 10:42:30.333 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始修改菜单
2025-06-05 10:42:30.334 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 2002, 菜单名称: 用户管理, 父级ID: 3001, 模块标识: permission_management
2025-06-05 10:42:31.023 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单修改成功，菜单ID: 2002
2025-06-05 11:31:29.340 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:31:29.365 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 11:31:45.149 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 40648 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 11:31:45.155 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 11:31:47.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:31:47.822 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:31:47.847 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 11:31:47.855 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:31:47.857 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:31:47.870 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 11:31:47.886 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:31:47.888 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 11:31:47.908 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-05 11:31:47.936 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:31:47.939 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:31:47.964 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-05 11:31:49.419 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 11:31:49.443 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 11:31:49.444 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 11:31:49.444 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 11:31:49.875 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 11:31:49.876 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4635 ms
2025-06-05 11:31:49.941 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 11:31:50.104 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 11:31:50.369 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 11:31:52.051 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 11:31:52.273 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 11:31:53.240 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 11:31:53.502 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 11:31:54.781 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 11:31:54.808 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:31:56.315 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 11:31:59.996 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 11:32:00.133 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 11:32:01.059 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.531 seconds (JVM running for 20.062)
2025-06-05 11:32:34.151 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 11:32:34.151 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 11:32:34.155 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-05 11:33:50.482 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单
2025-06-05 11:33:50.483 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 1930307870503604200
2025-06-05 11:39:59.405 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:39:59.427 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 11:40:08.405 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 19148 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 11:40:08.408 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 11:40:09.852 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:40:09.856 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:40:09.886 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 11:40:09.893 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:40:09.893 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:40:09.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 11:40:09.917 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:40:09.919 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 11:40:09.935 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-05 11:40:09.958 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:40:09.960 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:40:09.981 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-05 11:40:11.257 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 11:40:11.288 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 11:40:11.289 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 11:40:11.290 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 11:40:11.898 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 11:40:11.898 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3384 ms
2025-06-05 11:40:11.970 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 11:40:12.039 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 11:40:12.215 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 11:40:13.330 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 11:40:13.399 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 11:40:13.581 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 11:40:13.780 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 11:40:14.603 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 11:40:14.616 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:40:15.801 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 11:40:18.300 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 11:40:18.317 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 11:40:18.828 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.331 seconds (JVM running for 13.549)
2025-06-05 11:40:42.389 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 11:40:42.390 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 11:40:42.393 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 11:41:04.570 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单
2025-06-05 11:41:04.571 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 2003
2025-06-05 11:50:49.514 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:50:49.520 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 11:51:00.056 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 29460 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 11:51:00.059 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 11:51:01.728 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:51:01.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:51:01.760 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 11:51:01.767 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:51:01.768 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:51:01.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 11:51:01.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:51:01.788 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 11:51:01.802 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-05 11:51:01.820 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:51:01.821 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:51:01.839 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
2025-06-05 11:51:02.923 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 11:51:02.933 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 11:51:02.934 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 11:51:02.934 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 11:51:03.199 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 11:51:03.200 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3024 ms
2025-06-05 11:51:03.253 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 11:51:03.311 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 11:51:03.488 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 11:51:04.473 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 11:51:04.638 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 11:51:04.999 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 11:51:05.243 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 11:51:06.053 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 11:51:06.072 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:51:07.149 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 11:51:09.731 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 11:51:09.750 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 11:51:10.263 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.643 seconds (JVM running for 13.605)
2025-06-05 11:51:28.516 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 11:51:28.517 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 11:51:28.519 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 11:51:37.070 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单
2025-06-05 11:51:37.071 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 2003
2025-06-05 11:51:37.392 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单删除成功，菜单ID: 2003
2025-06-05 11:57:59.086 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:57:59.095 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 11:58:11.025 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 28884 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 11:58:11.027 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 11:58:13.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:58:13.579 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:58:13.618 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 11:58:13.624 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:58:13.626 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 11:58:13.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 11:58:13.652 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:58:13.655 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 11:58:13.673 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-05 11:58:13.693 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 11:58:13.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 11:58:13.728 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-05 11:58:15.288 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 11:58:15.303 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 11:58:15.304 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 11:58:15.304 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 11:58:15.576 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 11:58:15.578 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4397 ms
2025-06-05 11:58:15.644 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 11:58:15.719 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 11:58:16.010 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 11:58:17.533 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 11:58:17.631 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 11:58:17.904 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 11:58:18.221 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 11:58:19.663 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 11:58:19.687 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 11:58:21.214 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 11:58:25.493 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 11:58:25.520 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 11:58:26.584 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.022 seconds (JVM running for 19.57)
2025-06-05 11:58:54.085 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 11:58:54.086 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 11:58:54.089 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 11:59:19.667 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始删除菜单
2025-06-05 11:59:19.668 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单ID: 5003
2025-06-05 11:59:20.094 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单删除成功，菜单ID: 5003
2025-06-05 12:01:37.043 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 12:01:37.049 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 16:37:53.059 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 14332 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 16:37:53.062 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 16:37:56.986 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:37:57.022 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:37:57.103 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 16:37:57.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:37:57.117 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:37:57.144 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 16:37:57.197 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:37:57.199 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 16:37:57.258 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 JPA repository interfaces.
2025-06-05 16:37:57.367 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:37:57.373 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:37:57.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Redis repository interfaces.
2025-06-05 16:38:00.920 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 16:38:00.993 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 16:38:00.996 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 16:38:00.997 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 16:38:02.030 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 16:38:02.032 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8790 ms
2025-06-05 16:38:02.230 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 16:38:02.450 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 16:38:03.052 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 16:38:05.446 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 16:38:05.690 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 16:38:06.132 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 16:38:06.489 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 16:38:07.951 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 16:38:07.966 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:38:09.930 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 16:38:17.556 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 16:38:17.594 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 16:38:18.554 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 27.487 seconds (JVM running for 32.035)
2025-06-05 16:39:29.885 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:39:29.885 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:39:29.888 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-05 16:40:50.163 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:40:50.179 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 16:41:03.846 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 29460 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 16:41:03.848 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 16:41:05.885 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:41:05.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:41:05.939 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 16:41:05.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:41:05.952 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:41:05.972 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 16:41:05.993 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:41:05.996 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 16:41:06.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-05 16:41:06.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:41:06.046 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:41:06.072 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-05 16:41:07.849 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 16:41:07.871 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 16:41:07.872 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 16:41:07.873 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 16:41:08.182 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 16:41:08.182 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4247 ms
2025-06-05 16:41:08.237 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 16:41:08.296 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 16:41:08.456 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 16:41:09.380 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 16:41:09.480 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 16:41:09.719 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 16:41:09.917 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 16:41:11.180 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 16:41:11.200 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:41:12.616 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 16:41:15.055 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 16:41:15.076 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 16:41:15.751 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.981 seconds (JVM running for 15.094)
2025-06-05 16:41:50.381 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:41:50.382 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:41:50.387 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-05 16:42:17.906 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:42:17.910 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 16:42:29.364 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 25660 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 16:42:29.365 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 16:42:30.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:42:30.790 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:42:30.823 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 16:42:30.826 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:42:30.827 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:42:30.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 16:42:30.845 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:42:30.845 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 16:42:30.860 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 16:42:30.879 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:42:30.880 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:42:30.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-05 16:42:32.237 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 16:42:32.258 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 16:42:32.260 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 16:42:32.261 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 16:42:32.579 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 16:42:32.579 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3132 ms
2025-06-05 16:42:32.647 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 16:42:32.711 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 16:42:32.992 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 16:42:34.300 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 16:42:34.362 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 16:42:34.570 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 16:42:34.750 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 16:42:35.616 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 16:42:35.632 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:42:37.183 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 16:42:39.895 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 16:42:39.916 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 16:42:40.755 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.384 seconds (JVM running for 14.877)
2025-06-05 16:46:05.167 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:46:05.167 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:46:05.169 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 16:46:44.354 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始查询数据模块列表
2025-06-05 16:46:44.733 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块列表查询完成，共0条记录
2025-06-05 16:50:51.588 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:50:51.601 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 16:51:05.221 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 11120 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 16:51:05.224 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 16:51:06.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:51:06.908 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:51:06.947 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 16:51:06.952 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:51:06.953 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 16:51:06.962 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 16:51:06.978 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:51:06.980 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 16:51:06.998 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-05 16:51:07.023 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:51:07.026 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:51:07.055 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-05 16:51:09.632 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 16:51:09.661 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 16:51:09.663 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 16:51:09.664 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 16:51:09.920 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 16:51:09.920 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4601 ms
2025-06-05 16:51:09.975 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 16:51:10.044 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 16:51:10.239 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 16:51:11.459 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 16:51:11.553 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 16:51:11.880 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 16:51:12.186 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 16:51:13.508 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 16:51:13.527 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 16:51:15.167 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 16:51:17.799 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 16:51:17.820 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 16:51:18.425 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.252 seconds (JVM running for 16.741)
2025-06-05 16:51:26.015 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:51:26.016 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:51:26.018 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 16:51:38.412 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:51:38.414 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 用户数据模块, 模块标识: user_data_module
2025-06-05 16:51:38.681 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548054105133056
2025-06-05 16:51:39.024 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:51:39.024 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 订单数据模块, 模块标识: order_data_module
2025-06-05 16:51:39.489 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548056177119232
2025-06-05 16:51:39.556 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:51:39.556 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 商品数据模块, 模块标识: product_data_module
2025-06-05 16:51:39.703 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548058416877568
2025-06-05 16:51:39.767 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:51:39.768 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 财务数据模块, 模块标识: finance_data_module
2025-06-05 16:51:40.195 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548060497252352
2025-06-05 16:51:40.261 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:51:40.262 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 报表数据模块, 模块标识: report_data_module
2025-06-05 16:51:40.691 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548062489546752
2025-06-05 16:51:52.326 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始查询数据模块列表
2025-06-05 16:51:52.786 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块列表查询完成，共2条记录
2025-06-05 16:52:01.191 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始查询数据模块列表
2025-06-05 16:52:01.303 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块列表查询完成，共5条记录
2025-06-05 16:55:00.426 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 16:55:00.427 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 测试插入数据模块, 模块标识: one_data_module
2025-06-05 16:55:00.552 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930548900859613184
2025-06-05 16:55:10.458 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始查询数据模块列表
2025-06-05 16:55:10.530 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块列表查询完成，共6条记录
2025-06-05 16:55:41.010 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始删除数据模块
2025-06-05 16:55:41.011 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块ID: 1930548900859613184
2025-06-05 17:06:12.714 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 17:06:12.726 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 17:06:25.621 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 37220 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 17:06:25.623 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 17:06:28.325 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:06:28.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 17:06:28.364 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 17:06:28.370 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:06:28.371 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 17:06:28.381 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 17:06:28.395 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:06:28.397 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 17:06:28.418 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-05 17:06:28.443 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:06:28.446 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 17:06:28.472 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-05 17:06:29.616 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 17:06:29.632 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 17:06:29.633 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 17:06:29.634 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 17:06:29.946 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 17:06:29.946 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4170 ms
2025-06-05 17:06:29.999 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 17:06:30.057 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 17:06:30.274 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 17:06:31.299 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 17:06:31.388 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 17:06:31.620 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 17:06:31.805 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 17:06:32.736 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 17:06:32.753 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 17:06:34.430 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 17:06:37.556 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 17:06:37.571 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 17:06:38.122 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.945 seconds (JVM running for 15.803)
2025-06-05 17:07:08.825 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 17:07:08.825 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 17:07:08.828 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-05 17:07:09.067 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始删除数据模块
2025-06-05 17:07:09.068 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块ID: 1930548900859613184
2025-06-05 17:07:09.442 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块删除成功，ID: 1930548900859613184
2025-06-05 17:26:54.832 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 17:26:54.847 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-05 17:27:07.628 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 30988 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-05 17:27:07.633 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-05 17:27:10.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:27:10.168 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-05 17:27:10.197 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-05 17:27:10.201 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:27:10.202 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-05 17:27:10.215 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-05 17:27:10.225 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:27:10.225 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-05 17:27:10.239 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-05 17:27:10.259 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 17:27:10.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 17:27:10.283 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-05 17:27:11.427 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-05 17:27:11.439 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-05 17:27:11.440 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 17:27:11.441 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-05 17:27:11.676 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 17:27:11.676 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3542 ms
2025-06-05 17:27:11.741 [main] INFO  o.s.boot.web.servlet.RegistrationBean - Filter userIdFilter was not registered (possibly already registered?)
2025-06-05 17:27:11.800 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-05 17:27:11.994 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-05 17:27:13.392 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-05 17:27:13.520 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-05 17:27:13.988 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-05 17:27:14.398 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-05 17:27:15.290 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-05 17:27:15.300 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 17:27:16.526 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-05 17:27:19.425 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-05 17:27:19.444 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-05 17:27:20.088 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.224 seconds (JVM running for 17.56)
2025-06-05 17:36:21.922 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 17:36:21.923 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 17:36:21.925 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 17:36:50.245 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始新增数据模块
2025-06-05 17:36:50.246 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块名称: 测试数据模块, 模块标识: two_data_module
2025-06-05 17:36:50.492 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块新增成功，ID: 1930559428256468992
2025-06-05 17:37:00.119 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始查询数据模块列表
2025-06-05 17:37:00.275 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块列表查询完成，共6条记录
2025-06-05 17:37:13.454 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 开始删除数据模块
2025-06-05 17:37:13.454 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 模块ID: 1930559428256468992
2025-06-05 17:37:13.734 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.TDataModuleServiceImpl - 数据模块删除成功，ID: 1930559428256468992
2025-06-05 18:49:27.702 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-05 18:49:27.706 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
