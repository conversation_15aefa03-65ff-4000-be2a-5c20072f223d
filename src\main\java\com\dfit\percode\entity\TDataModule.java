package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 数据权限模块信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_data_module")
@ApiModel(value = "TDataModule对象", description = "数据权限模块信息表")
public class TDataModule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("模块标志，用于区分模块的内部标识")
    private String moduleIdentifier;

    private Integer orderInfo;

    @ApiModelProperty("是否删除，true删除，false正常")
    private Boolean isDel;

    private String createTime;

    private String modifyTime;
}
