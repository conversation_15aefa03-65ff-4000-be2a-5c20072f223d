package com.dfit.percode.sync.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部系统员工实体类
 * 对应外部系统的employee表结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExternalEmployee {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * MDM系统中的唯一标识符
     */
    private String mdmId;
    
    /**
     * MDM系统中的硬件标识
     */
    private String mdmHrdwnm;
    
    /**
     * 员工编号
     */
    private String employeeCode;
    
    /**
     * 员工姓名
     */
    private String employeeName;
    
    /**
     * 性别，1=男，2=女
     */
    private String gender;
    
    /**
     * 手机号码
     */
    private String mobile;
    
    /**
     * 员工状态，A=在职
     */
    private String status;
    
    /**
     * 身份证号码
     */
    private String idCard;
    
    /**
     * 账号
     */
    private String account;
    
    /**
     * 出生日期
     * 外部系统返回ISO格式时间戳，使用String接收后再转换
     */
    private String birthDate;
    
    /**
     * 电子邮箱
     */
    private String email;
    
    /**
     * 组织类型
     */
    private String orgType;
    
    /**
     * 组织层级1
     */
    private String orgLevel1;
    
    /**
     * 组织层级2
     */
    private String orgLevel2;
    
    /**
     * 组织层级3
     */
    private String orgLevel3;
    
    /**
     * 微信号
     */
    private String wechat;
    
    /**
     * 电话号码
     */
    private String tel;
    
    /**
     * 备注
     */
    private String note;
    
    /**
     * 是否禁用，0=否，1=是
     */
    private String isDisabled;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 组织代码
     */
    private String orgCode;

    /**
     * 身份证和姓名组合
     */
    private String idName;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 员工岗位信息（对应employee_position表）
     */
    private List<ExternalEmployeePosition> positions;

    /**
     * 员工职称信息（对应employee_title表）
     */
    private List<ExternalEmployeeTitle> titles;

    /**
     * 员工系统标识信息（对应employee_system表）
     */
    private List<ExternalEmployeeSystem> systems;
}
