# 数据同步详细日志说明

## 📋 **日志增强内容**

为了让您能清楚地看到同步过程的每个细节，我已经为数据同步功能添加了详细的日志记录。

### **🔍 新增的日志信息**

#### **1. 部门数据同步日志**
```
INFO - 正在处理部门: orgCode=TECH001, orgName=技术部
INFO - 部门数据已保存到 t_org_structure 表: id=*********, orgName=技术部
INFO - 开始同步部门子表数据，部门 TECH001 有 3 个子记录
INFO - 部门子表数据已保存到 t_department_child 表: deptId=*********, guid=child-001, sourceSystem=HR
INFO - 部门子表数据同步完成，共处理 3 条记录到 t_department_child 表
INFO - 部门数据同步汇总: 主表(t_org_structure)=5 条, 子表(t_department_child)=15 条
```

#### **2. 员工数据同步日志**
```
INFO - 正在处理员工: mdmId=emp-001, employeeName=张三, employeeCode=E001
INFO - 员工数据已保存到 t_user 表: id=*********, userName=张三, employeeCode=E001
INFO - 员工数据同步汇总: t_user 表共处理 20 条记录
```

#### **3. 员工扩展数据同步日志**
```
INFO - 正在处理员工扩展数据: mdmId=emp-001, employeeName=张三
INFO - 员工 emp-001 有 2 个岗位信息
INFO - 员工岗位数据已保存到 t_employee_position 表: userId=*********, positionCode=P001, orgCode=TECH001, isPrimary=1
INFO - 员工 emp-001 有 1 个职称信息
INFO - 员工职称数据已保存到 t_employee_title 表: userId=*********, titleName=高级工程师, titleType=技术类
INFO - 员工 emp-001 有 1 个系统标识信息
INFO - 员工系统标识数据已保存到 t_employee_system 表: userId=*********, systemCode=OA, loginAccount=zhangsan
INFO - 员工扩展数据同步汇总: t_employee_position=25 条, t_employee_title=15 条, t_employee_system=20 条
```

#### **4. 部门归属更新日志**
```
INFO - 正在更新用户部门归属: userId=*********, userName=张三, orgCode=TECH001
INFO - 成功更新用户 张三 (*********) 的部门归属为部门ID: *********
INFO - 员工部门归属更新汇总: t_user 表共更新 18 个用户的 organ_affiliation 字段，2 个用户未找到对应部门
```

## 📊 **完整同步流程日志示例**

### **执行完整同步时的日志输出**
```
INFO - 开始执行完整数据同步，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00
INFO - 开始同步部门数据，时间范围: 2024-01-01T00:00:00 - 2024-01-02T00:00:00
INFO - 从外部系统获取到 5 个部门

INFO - 正在处理部门: orgCode=ROOT, orgName=总公司
INFO - 部门数据已保存到 t_org_structure 表: id=1001, orgName=总公司
INFO - 正在处理部门: orgCode=TECH, orgName=技术部
INFO - 部门数据已保存到 t_org_structure 表: id=1002, orgName=技术部
INFO - 开始同步部门子表数据，部门 TECH 有 2 个子记录
INFO - 部门子表数据已保存到 t_department_child 表: deptId=1002, guid=child-001, sourceSystem=HR
INFO - 部门子表数据已保存到 t_department_child 表: deptId=1002, guid=child-002, sourceSystem=HR
INFO - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表

INFO - 部门数据同步汇总: 主表(t_org_structure)=5 条, 子表(t_department_child)=8 条
INFO - 部门数据同步完成，共处理 5 个部门

INFO - 开始同步员工数据，时间范围: 2024-01-01T00:00:00 - 2024-01-02T00:00:00
INFO - 从外部系统获取到 10 个员工

INFO - 正在处理员工: mdmId=emp-001, employeeName=张三, employeeCode=E001
INFO - 员工数据已保存到 t_user 表: id=2001, userName=张三, employeeCode=E001
INFO - 正在处理员工: mdmId=emp-002, employeeName=李四, employeeCode=E002
INFO - 员工数据已保存到 t_user 表: id=2002, userName=李四, employeeCode=E002

INFO - 员工数据同步汇总: t_user 表共处理 10 条记录
INFO - 员工数据同步完成，共处理 10 个员工

INFO - 开始同步员工扩展数据
INFO - 开始处理 10 个员工的扩展数据

INFO - 正在处理员工扩展数据: mdmId=emp-001, employeeName=张三
INFO - 员工 emp-001 有 1 个岗位信息
INFO - 员工岗位数据已保存到 t_employee_position 表: userId=2001, positionCode=P001, orgCode=TECH, isPrimary=1
INFO - 员工 emp-001 有 1 个职称信息
INFO - 员工职称数据已保存到 t_employee_title 表: userId=2001, titleName=高级工程师, titleType=技术类

INFO - 员工扩展数据同步汇总: t_employee_position=12 条, t_employee_title=8 条, t_employee_system=10 条

INFO - 开始更新员工部门归属
INFO - 找到 10 个需要更新部门归属的用户

INFO - 正在更新用户部门归属: userId=2001, userName=张三, orgCode=TECH
INFO - 成功更新用户 张三 (2001) 的部门归属为部门ID: 1002

INFO - 员工部门归属更新汇总: t_user 表共更新 10 个用户的 organ_affiliation 字段，0 个用户未找到对应部门

INFO - 完整数据同步执行成功，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00
```

## 🗂️ **数据库表同步汇总**

### **主要数据表**
1. **t_org_structure** - 部门主表
2. **t_user** - 用户主表

### **扩展数据表**
3. **t_department_child** - 部门子表
4. **t_employee_position** - 员工岗位表
5. **t_employee_title** - 员工职称表
6. **t_employee_system** - 员工系统标识表

### **关联更新**
7. **t_user.organ_affiliation** - 员工部门归属字段

## 📝 **日志级别说明**

### **INFO级别日志**
- 同步进度信息
- 数据处理统计
- 成功操作记录
- 汇总信息

### **WARN级别日志**
- 数据不完整警告
- 关联关系缺失
- 可恢复的错误

### **ERROR级别日志**
- 同步失败错误
- 数据库操作失败
- 不可恢复的错误

## 🔍 **如何查看日志**

### **1. 控制台输出**
同步过程中，所有日志都会实时输出到控制台。

### **2. 日志文件**
```bash
# 查看应用日志
tail -f logs/web_info.log

# 查看错误日志
tail -f logs/web_error.log
```

### **3. 日志过滤**
```bash
# 只查看同步相关日志
grep "同步" logs/web_info.log

# 查看特定表的操作
grep "t_user" logs/web_info.log
```

## 📊 **同步效果验证**

通过日志，您可以清楚地看到：
- ✅ 每个步骤处理了多少条数据
- ✅ 数据保存到了哪个表
- ✅ 每条记录的关键信息
- ✅ 最终的统计汇总
- ✅ 任何警告或错误信息

现在重新测试同步功能，您将看到非常详细的日志输出！
