package com.dfit.percode;

import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@EnableScheduling//开启定时任务
@EnableRabbit//开启mq
@EnableTransactionManagement//开启事务
// @EnableWebMvc // 注释掉此注解，使用Spring Boot默认的Web MVC自动配置
public class KmapApplication {

    public static void main(String[] args) {
        SpringApplication.run(KmapApplication.class, args);
    }

}
