package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TDataOperate;
import com.dfit.percode.entity.TRolesDataPermission;
import com.dfit.percode.mapper.TDataOperateMapper;
import com.dfit.percode.mapper.TDataPermissionMapper;
import com.dfit.percode.mapper.TRolesDataPermissionMapper;
import com.dfit.percode.service.ITDataOperateService;
import com.dfit.percode.util.DataOperateTypeUtil;
import com.dfit.percode.vo.ConfigureDataOperateRequestVO;
import com.dfit.percode.vo.ConfigureDataOperateListRequestVO;
import com.dfit.percode.vo.DataOperateConfigResponseVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import java.util.Map;
import java.util.HashMap;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 数据操作权限服务实现类
 * 提供数据操作权限的配置和查询功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class TDataOperateServiceImpl extends ServiceImpl<TDataOperateMapper, TDataOperate> implements ITDataOperateService {

    @Autowired
    private TDataOperateMapper dataOperateMapper;

    @Autowired
    private TDataPermissionMapper dataPermissionMapper;

    @Autowired
    private TRolesDataPermissionMapper rolesDataPermissionMapper;

    /**
     * 配置模块操作权限
     * 为指定的模块配置可执行的操作类型（新增、修改、删除等）
     * 该模块下所有数据都继承这些操作权限
     *
     * @param request 配置模块操作权限请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configureDataOperate(ConfigureDataOperateRequestVO request) {
        log.info("开始配置模块操作权限");
        log.info("模块标识: {}, 操作类型: {}",
                request.getModuleIdentifier(), request.getOperateTypes());

        // 1. 验证模块是否存在
        int moduleExists = dataOperateMapper.checkModuleExists(request.getModuleIdentifier());
        if (moduleExists == 0) {
            log.error("模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("模块不存在");
        }

        // 2. 获取当前配置的操作类型（用于级联删除检测）
        List<Integer> currentOperateTypes = dataOperateMapper.getModuleOperateTypes(request.getModuleIdentifier());
        log.info("当前模块操作权限: {}", currentOperateTypes);

        // 3. 获取新配置的操作类型
        List<Integer> newOperateTypes = request.getOperateTypes() != null ? request.getOperateTypes() : new ArrayList<>();
        log.info("新配置的操作权限: {}", newOperateTypes);

        // 4. 找出被取消的操作类型
        List<Integer> removedOperateTypes = currentOperateTypes.stream()
                .filter(type -> !newOperateTypes.contains(type))
                .collect(Collectors.toList());

        // 5. 执行级联删除
        if (!removedOperateTypes.isEmpty()) {
            log.info("检测到被取消的操作类型: {}, 开始执行级联删除", removedOperateTypes);
            cascadeDeleteModuleLevelPermissions(request.getModuleIdentifier(), removedOperateTypes);
        }

        // 6. 删除现有的模块操作权限配置
        dataOperateMapper.deleteByModule(request.getModuleIdentifier());
        log.info("已删除现有的模块操作权限配置");

        // 7. 插入新的模块操作权限配置
        if (newOperateTypes != null && !newOperateTypes.isEmpty()) {
            for (Integer operateType : newOperateTypes) {
                // 验证操作类型是否有效（1-查看，2-修改，3-下载，4-删除）
                if (operateType < 1 || operateType > 4) {
                    log.warn("无效的操作类型: {}, 跳过", operateType);
                    continue;
                }

                TDataOperate dataOperate = new TDataOperate();
                // 不设置ID，让数据库自动生成（serial类型）
                dataOperate.setModuleIdentifier(request.getModuleIdentifier());
                dataOperate.setDataType(1); // 模块级别固定为1
                dataOperate.setOperateType(operateType.longValue());
                dataOperate.setDataIdentifier(null); // 模块级别配置，数据标识为空
                dataOperate.setIsDel(false);
                dataOperate.setCreateTime(LocalDateTime.now());
                dataOperate.setModifyTime(LocalDateTime.now());

                dataOperateMapper.insert(dataOperate);
                log.info("插入模块操作权限配置: 操作类型={}", operateType);
            }
        }

        log.info("模块操作权限配置成功，模块标识: {}, 配置的操作类型数量: {}",
                request.getModuleIdentifier(),
                newOperateTypes != null ? newOperateTypes.size() : 0);
    }

    /**
     * 配置数据操作权限（数据级别）
     * 为多个数据权限分别配置可执行的操作类型
     *
     * @param request 配置数据权限操作权限请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void configureDataOperateList(ConfigureDataOperateListRequestVO request) {
        log.info("开始配置数据级别操作权限");
        log.info("模块标识: {}, 数据权限数量: {}",
                request.getModuleIdentifier(),
                request.getDataOperateConfigs() != null ? request.getDataOperateConfigs().size() : 0);

        // 1. 验证模块是否存在
        int moduleExists = dataOperateMapper.checkModuleExists(request.getModuleIdentifier());
        if (moduleExists == 0) {
            log.error("模块不存在，模块标识: {}", request.getModuleIdentifier());
            throw new RuntimeException("模块不存在");
        }

        // 2. 验证数据权限配置列表
        if (request.getDataOperateConfigs() == null || request.getDataOperateConfigs().isEmpty()) {
            log.warn("数据权限配置列表为空，模块标识: {}", request.getModuleIdentifier());
            return;
        }

        // 3. 为每个数据权限配置操作类型
        for (ConfigureDataOperateListRequestVO.DataOperateConfigItem configItem : request.getDataOperateConfigs()) {
            try {
                // 3.1 验证数据标识
                if (configItem.getDataIdentifier() == null || configItem.getDataIdentifier().trim().isEmpty()) {
                    log.warn("跳过无效的数据权限配置，数据标识为空");
                    continue;
                }

                // 3.2 获取当前数据权限在角色权限表中实际存在的操作类型（用于级联删除检测）
                // 首先通过数据标识符找到数据ID，避免查询完整实体
                Long dataId = dataPermissionMapper.getDataIdByIdentifier(configItem.getDataIdentifier());

                List<Integer> currentDataOperateTypes = new ArrayList<>();
                if (dataId != null) {
                    // 使用自定义SQL查询，避免复杂的字段映射问题
                    currentDataOperateTypes = rolesDataPermissionMapper.getExistingOperateTypesByDataId(dataId);
                }

                log.info("数据权限 {} 在角色权限表中实际存在的操作类型: {}", configItem.getDataIdentifier(), currentDataOperateTypes);

                // 3.3 获取新配置的操作类型
                List<Integer> newDataOperateTypes = configItem.getOperateTypes() != null ? configItem.getOperateTypes() : new ArrayList<>();
                log.info("数据权限 {} 新配置的操作权限: {}", configItem.getDataIdentifier(), newDataOperateTypes);

                // 3.4 找出被取消的操作类型
                List<Integer> removedDataOperateTypes = currentDataOperateTypes.stream()
                        .filter(type -> !newDataOperateTypes.contains(type))
                        .collect(Collectors.toList());

                log.info("数据权限 {} 级联删除分析: 当前存在={}, 新配置={}, 需要删除={}",
                        configItem.getDataIdentifier(), currentDataOperateTypes, newDataOperateTypes, removedDataOperateTypes);

                // 3.5 执行级联删除
                if (!removedDataOperateTypes.isEmpty()) {
                    log.info("数据权限 {} 检测到被取消的操作类型: {}, 开始执行级联删除",
                            configItem.getDataIdentifier(), removedDataOperateTypes);
                    cascadeDeleteDataLevelPermissions(configItem.getDataIdentifier(),
                                                    removedDataOperateTypes);
                }

                // 3.6 删除该数据权限的现有操作配置
                dataOperateMapper.deleteByDataIdentifier(configItem.getDataIdentifier());
                log.info("已删除数据权限 {} 的现有操作配置", configItem.getDataIdentifier());

                // 3.7 插入新的操作配置
                if (newDataOperateTypes != null && !newDataOperateTypes.isEmpty()) {
                    for (Integer operateType : newDataOperateTypes) {
                        if (operateType != null && operateType >= 1 && operateType <= 4) {
                            TDataOperate dataOperate = new TDataOperate();
                            // 不设置ID，让数据库自动生成
                            dataOperate.setModuleIdentifier(request.getModuleIdentifier());
                            dataOperate.setDataIdentifier(configItem.getDataIdentifier());
                            dataOperate.setDataType(1); // 数据级别固定为1
                            dataOperate.setOperateType(operateType.longValue());
                            dataOperate.setIsDel(false);
                            dataOperate.setCreateTime(LocalDateTime.now());
                            dataOperate.setModifyTime(LocalDateTime.now());

                            dataOperateMapper.insert(dataOperate);
                            log.info("插入数据权限操作配置: 数据标识={}, 操作类型={}",
                                    configItem.getDataIdentifier(), operateType);
                        } else {
                            log.warn("跳过无效的操作类型: {}", operateType);
                        }
                    }
                }

                log.info("数据权限 {} 操作权限配置完成，操作类型数量: {}",
                        configItem.getDataIdentifier(),
                        newDataOperateTypes != null ? newDataOperateTypes.size() : 0);

            } catch (Exception e) {
                log.error("配置数据权限 {} 的操作权限失败: {}",
                        configItem.getDataIdentifier(), e.getMessage(), e);
                throw new RuntimeException("配置数据权限操作权限失败: " + e.getMessage(), e);
            }
        }

        log.info("数据级别操作权限配置成功，模块标识: {}, 处理的数据权限数量: {}",
                request.getModuleIdentifier(), request.getDataOperateConfigs().size());
    }

    /**
     * 获取模块操作权限配置
     * 查询指定模块的操作类型配置
     *
     * @param moduleIdentifier 模块标识
     * @return 模块操作权限配置信息
     */
    public DataOperateConfigResponseVO getModuleOperateConfig(String moduleIdentifier) {
        log.info("开始获取模块操作权限配置");
        log.info("模块标识: {}", moduleIdentifier);

        // 1. 验证参数
        if (moduleIdentifier == null || moduleIdentifier.trim().isEmpty()) {
            throw new RuntimeException("模块标识不能为空");
        }

        // 2. 查询模块基本信息
        DataOperateConfigResponseVO config = dataOperateMapper.getModuleOperateConfig(moduleIdentifier);

        if (config == null) {
            log.warn("未找到模块信息，模块标识: {}", moduleIdentifier);
            // 返回空配置而不是抛异常
            config = new DataOperateConfigResponseVO();
            config.setModuleIdentifier(moduleIdentifier);
        }

        // 3. 查询模块的操作权限
        try {
            List<Integer> operateTypes = dataOperateMapper.getModuleOperateTypes(moduleIdentifier);
            config.setOperateTypes(operateTypes);
            log.info("模块 {} 的操作权限: {}", moduleIdentifier, operateTypes);
        } catch (Exception e) {
            log.warn("获取模块 {} 的操作权限失败: {}", moduleIdentifier, e.getMessage());
            config.setOperateTypes(new ArrayList<>());
        }

        log.info("模块操作权限配置获取成功");
        return config;
    }

    /**
     * 获取数据操作权限配置（保留兼容性）
     * 查询指定数据权限的操作类型配置
     *
     * @param dataIdentifier 数据标识
     * @param moduleIdentifier 模块标识
     * @return 数据操作权限配置信息
     */
    @Override
    public DataOperateConfigResponseVO getDataOperateConfig(String dataIdentifier, String moduleIdentifier) {
        log.info("开始获取数据操作权限配置（兼容模式）");
        log.info("数据标识: {}, 模块标识: {}", dataIdentifier, moduleIdentifier);

        // 直接调用模块级别的配置查询
        return getModuleOperateConfig(moduleIdentifier);
    }

    /**
     * 级联删除模块级别的角色数据权限
     * 当模块的某些操作类型被取消时，删除所有角色对应的权限记录
     *
     * @param moduleIdentifier 模块标识符
     * @param removedOperateTypes 被取消的操作类型列表
     */
    private void cascadeDeleteModuleLevelPermissions(String moduleIdentifier, List<Integer> removedOperateTypes) {
        log.info("开始级联删除模块级角色数据权限，模块: {}, 被取消的操作类型: {}",
                moduleIdentifier, removedOperateTypes);

        try {
            // 查找所有受影响的角色权限记录
            List<TRolesDataPermission> affectedPermissions = rolesDataPermissionMapper
                .findByModuleAndOperateTypes(moduleIdentifier, removedOperateTypes);

            log.info("模块级级联删除查询结果: 模块={}, 操作类型={}, 找到记录数={}",
                    moduleIdentifier, removedOperateTypes, affectedPermissions.size());

            if (affectedPermissions.isEmpty()) {
                log.info("没有找到需要级联删除的模块级角色数据权限记录");
                return;
            }

            // 批量逻辑删除
            int deletedCount = 0;
            for (TRolesDataPermission permission : affectedPermissions) {
                permission.setIsDel(true);
                permission.setModifyTime(LocalDateTime.now());
                rolesDataPermissionMapper.updateById(permission);
                deletedCount++;

                log.debug("逻辑删除角色权限记录: 角色ID={}, 模块={}, 操作类型={}",
                        permission.getRoleId(), permission.getModuleIdentifier(), permission.getOperateType());
            }

            log.info("模块级级联删除完成，共删除 {} 条角色数据权限记录", deletedCount);

        } catch (Exception e) {
            log.error("模块级级联删除角色数据权限失败: {}", e.getMessage(), e);
            throw new RuntimeException("级联删除角色数据权限失败: " + e.getMessage(), e);
        }
    }

    /**
     * 级联删除数据级别的角色数据权限
     * 当特定数据权限的某些操作类型被取消时，删除所有角色对应的权限记录
     *
     * @param dataIdentifier 数据标识符
     * @param removedOperateTypes 被取消的操作类型列表
     */
    private void cascadeDeleteDataLevelPermissions(String dataIdentifier, List<Integer> removedOperateTypes) {
        log.info("开始级联删除数据级角色权限，数据标识符: {}, 被取消的操作类型: {}",
                dataIdentifier, removedOperateTypes);

        try {
            // 步骤1：通过数据标识符找到数据ID，避免查询完整实体
            Long dataId = dataPermissionMapper.getDataIdByIdentifier(dataIdentifier);

            if (dataId == null) {
                log.warn("未找到数据标识符为 {} 的数据权限记录", dataIdentifier);
                return;
            }

            log.info("找到数据权限记录，数据ID: {}", dataId);

            // 步骤2：查找该数据ID对应的所有角色权限记录中需要删除的操作类型的ID
            List<Long> affectedPermissionIds = rolesDataPermissionMapper
                .findIdsByDataIdAndOperateTypes(dataId, removedOperateTypes);

            log.info("数据级级联删除查询结果: 数据ID={}, 操作类型={}, 找到记录数={}",
                    dataId, removedOperateTypes, affectedPermissionIds.size());

            if (affectedPermissionIds.isEmpty()) {
                log.info("没有找到需要删除的数据级角色权限记录");
                return;
            }

            // 步骤3：批量逻辑删除这些记录
            int deletedCount = rolesDataPermissionMapper.batchLogicalDeleteByIds(affectedPermissionIds);

            log.info("数据级级联删除完成，共删除 {} 条记录", deletedCount);

        } catch (Exception e) {
            log.error("级联删除角色数据权限失败: {}", e.getMessage(), e);
            throw new RuntimeException("级联删除角色数据权限失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调试接口：查看角色数据权限详情
     * 用于调试级联删除功能
     *
     * @param roleId 角色ID
     * @param moduleIdentifier 模块标识符
     * @return 角色数据权限详情列表
     */
    @Override
    public List<Map<String, Object>> debugRoleDataPermissions(Long roleId, String moduleIdentifier) {
        log.info("调试查询角色数据权限，角色ID: {}, 模块: {}", roleId, moduleIdentifier);

        List<Map<String, Object>> result = new ArrayList<>();

        try {
            // 查询角色的所有数据权限记录
            List<TRolesDataPermission> permissions = rolesDataPermissionMapper.selectList(
                new QueryWrapper<TRolesDataPermission>()
                    .eq("role_id", roleId)
                    .eq("is_del", false)
                    .orderByAsc("data_id", "operate_type")
            );

            log.info("找到角色数据权限记录数: {}", permissions.size());

            for (TRolesDataPermission permission : permissions) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", permission.getId());
                item.put("roleId", permission.getRoleId());
                item.put("moduleIdentifier", permission.getModuleIdentifier());
                item.put("dataId", permission.getDataId());
                item.put("operateType", permission.getOperateType());
                item.put("operateTypeName", DataOperateTypeUtil.getOperateTypeDescription(permission.getOperateType()));
                item.put("dataOperateId", permission.getDataOperateId());
                item.put("isDel", permission.getIsDel());
                item.put("createTime", permission.getCreateTime());
                item.put("modifyTime", permission.getModifyTime());

                result.add(item);
            }

            log.info("调试查询完成，返回记录数: {}", result.size());

        } catch (Exception e) {
            log.error("调试查询角色数据权限失败: {}", e.getMessage(), e);
            throw new RuntimeException("调试查询失败: " + e.getMessage(), e);
        }

        return result;
    }
}
