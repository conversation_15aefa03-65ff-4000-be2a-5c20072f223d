-- 数据同步表结构扩展SQL (简化版本)
-- 为支持外部系统数据同步，扩展当前系统表结构

-- =====================================================
-- 1. 扩展 t_org_structure 表（部门表）
-- =====================================================

-- 添加外部系统字段到部门表（如果字段不存在则添加）
ALTER TABLE t_org_structure ADD COLUMN dept_uuid VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN org_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN parent_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN full_name VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN is_history INTEGER DEFAULT 0;
ALTER TABLE t_org_structure ADD COLUMN description TEXT;
ALTER TABLE t_org_structure ADD COLUMN fax VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN web_address VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN org_manager VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN post_code VARCHAR(255);
ALTER TABLE t_org_structure ADD COLUMN org_type VARCHAR(50);
ALTER TABLE t_org_structure ADD COLUMN org_level INTEGER;
ALTER TABLE t_org_structure ADD COLUMN org_path VARCHAR(500);
ALTER TABLE t_org_structure ADD COLUMN full_org_code VARCHAR(500);

-- 添加同步相关字段
ALTER TABLE t_org_structure ADD COLUMN external_id INTEGER;
ALTER TABLE t_org_structure ADD COLUMN external_update_time TIMESTAMP;
ALTER TABLE t_org_structure ADD COLUMN sync_status VARCHAR(20) DEFAULT 'SYNCED';
ALTER TABLE t_org_structure ADD COLUMN last_sync_time TIMESTAMP;

-- =====================================================
-- 2. 扩展 t_user 表（用户表）
-- =====================================================

-- 添加外部系统字段到用户表
ALTER TABLE t_user ADD COLUMN mdm_id VARCHAR(255);
ALTER TABLE t_user ADD COLUMN mdm_hrdwnm VARCHAR(255);
ALTER TABLE t_user ADD COLUMN employee_code VARCHAR(255);
ALTER TABLE t_user ADD COLUMN gender VARCHAR(255);
ALTER TABLE t_user ADD COLUMN mobile VARCHAR(255);
ALTER TABLE t_user ADD COLUMN id_card VARCHAR(255);
ALTER TABLE t_user ADD COLUMN birth_date DATE;
ALTER TABLE t_user ADD COLUMN email VARCHAR(255);
ALTER TABLE t_user ADD COLUMN org_type VARCHAR(255);
ALTER TABLE t_user ADD COLUMN org_level1 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN org_level2 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN org_level3 VARCHAR(255);
ALTER TABLE t_user ADD COLUMN wechat VARCHAR(255);
ALTER TABLE t_user ADD COLUMN tel VARCHAR(255);
ALTER TABLE t_user ADD COLUMN note TEXT;
ALTER TABLE t_user ADD COLUMN employee_status VARCHAR(255);
ALTER TABLE t_user ADD COLUMN user_type VARCHAR(255);
ALTER TABLE t_user ADD COLUMN id_name VARCHAR(255);

-- 添加同步相关字段
ALTER TABLE t_user ADD COLUMN external_id BIGINT;
ALTER TABLE t_user ADD COLUMN external_org_code VARCHAR(255);
ALTER TABLE t_user ADD COLUMN external_update_time TIMESTAMP;
ALTER TABLE t_user ADD COLUMN sync_status VARCHAR(20) DEFAULT 'SYNCED';
ALTER TABLE t_user ADD COLUMN last_sync_time TIMESTAMP;

-- =====================================================
-- 3. 创建部门子表（对应外部系统的department_child）
-- =====================================================

CREATE TABLE t_department_child (
    id BIGINT PRIMARY KEY,
    dept_id BIGINT NOT NULL,
    guid VARCHAR(255),
    dept_uuid VARCHAR(255),
    source_system VARCHAR(255),
    source_data_nm VARCHAR(255),
    udef1 VARCHAR(255),
    udef2 VARCHAR(255),
    udef3 VARCHAR(255),
    udef4 VARCHAR(255),
    udef5 VARCHAR(255),
    udef6 VARCHAR(255),
    external_id INTEGER,
    sync_status VARCHAR(20) DEFAULT 'SYNCED',
    last_sync_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

-- =====================================================
-- 4. 创建员工岗位关联表（对应外部系统的employee_position）
-- =====================================================

CREATE TABLE t_employee_position (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    guid VARCHAR(255),
    employee_mdm_id VARCHAR(255),
    position_code VARCHAR(255),
    org_code VARCHAR(255),
    department_code VARCHAR(255),
    is_primary VARCHAR(255),
    status VARCHAR(255),
    is_active VARCHAR(255),
    position_detail_code VARCHAR(255),
    external_id BIGINT,
    sync_status VARCHAR(20) DEFAULT 'SYNCED',
    last_sync_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

-- =====================================================
-- 5. 创建员工职称表（对应外部系统的employee_title）
-- =====================================================

CREATE TABLE t_employee_title (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    guid VARCHAR(255),
    employee_mdm_id VARCHAR(255),
    title_code VARCHAR(255),
    title_type VARCHAR(255),
    title_level VARCHAR(255),
    title_name VARCHAR(255),
    status VARCHAR(255),
    title_category VARCHAR(255),
    external_id BIGINT,
    sync_status VARCHAR(20) DEFAULT 'SYNCED',
    last_sync_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

-- =====================================================
-- 6. 创建员工系统标识表（对应外部系统的employee_system）
-- =====================================================

CREATE TABLE t_employee_system (
    id BIGINT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    guid VARCHAR(255),
    employee_mdm_id VARCHAR(255),
    system_code VARCHAR(255),
    system_data_id VARCHAR(255),
    org_code VARCHAR(255),
    department_code VARCHAR(255),
    employee_code VARCHAR(255),
    login_account VARCHAR(50),
    external_id BIGINT,
    sync_status VARCHAR(20) DEFAULT 'SYNCED',
    last_sync_time TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modify_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_del BOOLEAN DEFAULT FALSE
);

-- =====================================================
-- 7. 创建同步日志表
-- =====================================================

CREATE TABLE t_sync_log (
    id BIGINT PRIMARY KEY,
    sync_type VARCHAR(20) NOT NULL,
    sync_action VARCHAR(20) NOT NULL,
    external_id VARCHAR(255),
    internal_id BIGINT,
    sync_status VARCHAR(20) NOT NULL,
    error_message TEXT,
    sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    external_data TEXT,
    processed_data TEXT
);

-- =====================================================
-- 8. 创建索引
-- =====================================================

-- 部门表索引
CREATE INDEX idx_org_structure_org_code ON t_org_structure(org_code);
CREATE INDEX idx_org_structure_sync_status ON t_org_structure(sync_status);

-- 用户表索引
CREATE INDEX idx_user_mdm_id ON t_user(mdm_id);
CREATE INDEX idx_user_employee_code ON t_user(employee_code);
CREATE INDEX idx_user_sync_status ON t_user(sync_status);

-- 部门子表索引
CREATE INDEX idx_department_child_dept_id ON t_department_child(dept_id);
CREATE INDEX idx_department_child_dept_uuid ON t_department_child(dept_uuid);

-- 员工岗位表索引
CREATE INDEX idx_employee_position_user_id ON t_employee_position(user_id);
CREATE INDEX idx_employee_position_mdm_id ON t_employee_position(employee_mdm_id);
CREATE INDEX idx_employee_position_org_code ON t_employee_position(org_code);

-- 员工职称表索引
CREATE INDEX idx_employee_title_user_id ON t_employee_title(user_id);
CREATE INDEX idx_employee_title_mdm_id ON t_employee_title(employee_mdm_id);

-- 员工系统标识表索引
CREATE INDEX idx_employee_system_user_id ON t_employee_system(user_id);
CREATE INDEX idx_employee_system_mdm_id ON t_employee_system(employee_mdm_id);
CREATE INDEX idx_employee_system_login_account ON t_employee_system(login_account);

-- 同步日志表索引
CREATE INDEX idx_sync_log_type ON t_sync_log(sync_type);
CREATE INDEX idx_sync_log_status ON t_sync_log(sync_status);
CREATE INDEX idx_sync_log_time ON t_sync_log(sync_time);
