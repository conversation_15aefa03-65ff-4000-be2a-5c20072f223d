package com.dfit.percode.util;

public class SnowFlakeUtils {
    // 偏移量
    private static final long EPOCH = 1288834974657L; // 自定义纪元时间
    private static final long MACHINE_ID_BITS = 10L;  // 机器ID占用位数
    private static final long SEQUENCE_BITS = 12L;    // 序列号占用位数
    private static final long MAX_MACHINE_ID = -1L ^ (-1L << MACHINE_ID_BITS); // 机器ID最大值
    private static final long SEQUENCE_MASK = -1L ^ (-1L << SEQUENCE_BITS); // 序列号最大值

    // 移位量
    private static final long MACHINE_ID_SHIFT = SEQUENCE_BITS;
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + MACHINE_ID_BITS;

    private long machineId; // 机器ID
    private long sequence = 0L; // 序列号
    private long lastTimestamp = -1L; // 上次生成ID的时间戳

    public SnowFlakeUtils(long machineId) {
        if (machineId > MAX_MACHINE_ID || machineId < 0) {
            throw new IllegalArgumentException("Machine ID can't be greater than " + MAX_MACHINE_ID + " or less than 0");
        }
        this.machineId = machineId;
    }

    // 生成ID
    public synchronized long nextId() {
        long timestamp = System.currentTimeMillis(); // 获取当前时间戳
        if (timestamp == lastTimestamp) {
            // 同一毫秒内，序列号自增
            sequence = (sequence + 1) & SEQUENCE_MASK;
            if (sequence == 0) {
                // 如果序列号用尽，等待下一毫秒
                while (timestamp <= lastTimestamp) {
                    timestamp = System.currentTimeMillis();
                }
            }
        } else {
            sequence = 0L; // 时间戳变化，重置序列号
        }
        lastTimestamp = timestamp;
        return ((timestamp - EPOCH) << TIMESTAMP_SHIFT) | (machineId << MACHINE_ID_SHIFT) | sequence;
    }

    public static void main(String[] args) {
        // 创建ID生成器，假设机器ID为1
        SnowflakeIdGenerator generator = new SnowflakeIdGenerator(1,1);

        // 生成5个ID示例
        for (int i = 0; i < 5; i++) {
            System.out.println(generator.generateId());
        }
    }
}
