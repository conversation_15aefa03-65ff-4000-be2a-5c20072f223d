2025-07-02 09:54:54.190 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 25004 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-02 09:54:54.205 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-02 09:54:54.212 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-02 09:54:59.134 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 09:54:59.142 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-02 09:54:59.214 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54 ms. Found 0 Elasticsearch repository interfaces.
2025-07-02 09:54:59.224 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 09:54:59.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-02 09:54:59.248 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-02 09:54:59.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 09:54:59.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-02 09:54:59.296 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-07-02 09:54:59.339 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 09:54:59.345 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 09:54:59.421 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-07-02 09:55:01.493 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-02 09:55:01.522 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-02 09:55:01.524 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 09:55:01.525 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 09:55:02.631 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 09:55:02.632 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8099 ms
2025-07-02 09:55:03.113 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-02 09:55:03.890 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-02 09:55:05.209 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-02 09:55:05.356 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-02 09:55:06.080 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-02 09:55:06.964 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-02 09:55:08.641 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-02 09:55:08.733 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 09:55:08.855 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-02 09:55:08.857 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-02 09:55:08.865 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-02 09:55:08.875 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-02 09:55:17.942 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 09:55:23.076 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-02 09:55:23.113 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-02 09:55:25.676 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 33.981 seconds (JVM running for 43.106)
2025-07-02 09:55:38.991 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 09:55:38.992 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 09:55:39.001 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-07-02 09:55:40.030 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: admin
2025-07-02 09:55:45.828 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: admin, 用户ID: 1938155631131365376, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 5797ms
2025-07-02 09:55:45.829 [http-nio-8285-exec-2] WARN  com.dfit.percode.controller.AuthController -  [安全审计] 超级管理员登录 - 账号: admin, 用户ID: 1938155631131365376, 时间: 2025-07-02 09:55:45, 具有系统最高权限
2025-07-02 09:56:04.572 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 09:56:04.576 [http-nio-8285-exec-5] WARN  com.dfit.percode.controller.AuthController - 混合认证失败：所有认证方式都无法获取用户ID
2025-07-02 09:56:04.577 [http-nio-8285-exec-5] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：混合认证未能获取用户ID
2025-07-02 09:56:19.601 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: admin
2025-07-02 09:56:20.008 [http-nio-8285-exec-4] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: admin, 用户ID: 1938155631131365376, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 406ms
2025-07-02 09:56:20.009 [http-nio-8285-exec-4] WARN  com.dfit.percode.controller.AuthController -  [安全审计] 超级管理员登录 - 账号: admin, 用户ID: 1938155631131365376, 时间: 2025-07-02 09:56:20, 具有系统最高权限
2025-07-02 09:56:36.305 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 09:56:36.306 [http-nio-8285-exec-3] WARN  com.dfit.percode.controller.AuthController - 混合认证失败：所有认证方式都无法获取用户ID
2025-07-02 09:56:36.308 [http-nio-8285-exec-3] WARN  com.dfit.percode.controller.AuthController - 权限查询失败：混合认证未能获取用户ID
2025-07-02 10:04:51.945 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 10:04:51.956 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-02 10:05:20.315 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 14660 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-02 10:05:20.320 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-02 10:05:20.356 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-02 10:05:25.148 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:05:25.156 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-02 10:05:25.250 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 72 ms. Found 0 Elasticsearch repository interfaces.
2025-07-02 10:05:25.265 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:05:25.267 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-02 10:05:25.298 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-02 10:05:25.320 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:05:25.323 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-02 10:05:25.365 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 JPA repository interfaces.
2025-07-02 10:05:25.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:05:25.450 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 10:05:25.499 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-07-02 10:05:27.135 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-02 10:05:27.152 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-02 10:05:27.153 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 10:05:27.153 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 10:05:27.601 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 10:05:27.603 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7073 ms
2025-07-02 10:05:27.876 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-02 10:05:28.150 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-02 10:05:29.491 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-02 10:05:29.690 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-02 10:05:30.081 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-02 10:05:30.465 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-02 10:05:31.216 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-02 10:05:31.263 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 10:05:31.317 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-02 10:05:31.318 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-02 10:05:31.322 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-02 10:05:31.323 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-02 10:05:36.076 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:05:41.120 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-02 10:05:41.156 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-02 10:05:43.477 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 25.618 seconds (JVM running for 30.874)
2025-07-02 10:06:45.076 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 10:06:45.077 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 10:06:45.083 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-07-02 10:06:45.891 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: admin
2025-07-02 10:06:49.362 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: admin, 用户ID: 1938155631131365376, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 3469ms
2025-07-02 10:06:49.364 [http-nio-8285-exec-2] WARN  com.dfit.percode.controller.AuthController -  [安全审计] 超级管理员登录 - 账号: admin, 用户ID: 1938155631131365376, 时间: 2025-07-02 10:06:49, 具有系统最高权限
2025-07-02 10:07:11.932 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-02 10:07:12.005 [http-nio-8285-exec-3] INFO  com.dfit.percode.interceptor.HybridAuthInterceptor -  超级管理员访问 - 用户ID: 1938155631131365376, URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-02 10:07:12.033 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 10:07:12.035 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1938155631131365376, 模块标识: PD, 权限类型: data
2025-07-02 10:07:12.036 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 检测到超级管理员，返回全量权限，用户ID: 1938155631131365376, 权限类型: data
2025-07-02 10:07:12.037 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员权限，用户ID: 1938155631131365376, 模块标识: PD
2025-07-02 10:07:12.038 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员模块级菜单权限，模块标识: PD
2025-07-02 10:07:12.133 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 查询到模块总数: 2
2025-07-02 10:07:12.134 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员模块级菜单权限查询完成，模块数量: 0，耗时: 95ms
2025-07-02 10:07:12.269 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员权限查询完成，用户ID: 1938155631131365376, 根菜单数量: 0, 按钮数量: 0, 耗时: 231ms
2025-07-02 10:07:12.270 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 超级管理员权限查询完成，用户ID: 1938155631131365376, 耗时: 234ms
2025-07-02 10:07:12.271 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功（混合认证），用户ID: 1938155631131365376, 认证方式: SuperAdmin, 权限类型: data, 菜单数量: 0, 按钮数量: 0, 数据权限数量: 0, 耗时: 236ms
2025-07-02 10:45:28.880 [http-nio-8285-exec-6] INFO  com.dfit.percode.controller.AuthController - 用户登录请求，账号: 123
2025-07-02 10:45:29.704 [http-nio-8285-exec-6] INFO  com.dfit.percode.controller.AuthController - 用户登录成功（混合模式），账号: 123, 用户ID: 1936640367617249280, Sa-Token会话: 已建立, 自定义JWT: 已生成, 耗时: 819ms
2025-07-02 10:45:42.645 [http-nio-8285-exec-7] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-02 10:45:42.680 [http-nio-8285-exec-7] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 10:45:42.681 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: PD, 权限类型: data
2025-07-02 10:45:42.847 [http-nio-8285-exec-7] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-07-02 10:49:12.056 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 10:49:12.080 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-02 10:49:31.227 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 26664 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-02 10:49:31.231 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-02 10:49:31.260 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-02 10:49:34.485 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:49:34.489 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-02 10:49:34.535 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Elasticsearch repository interfaces.
2025-07-02 10:49:34.544 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:49:34.545 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-02 10:49:34.561 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-02 10:49:34.581 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:49:34.582 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-02 10:49:34.607 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-07-02 10:49:34.651 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 10:49:34.654 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 10:49:34.686 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-02 10:49:36.613 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-02 10:49:36.640 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-02 10:49:36.641 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 10:49:36.642 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 10:49:37.161 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 10:49:37.161 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5752 ms
2025-07-02 10:49:37.431 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-02 10:49:37.845 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-02 10:49:39.209 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-02 10:49:39.377 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-02 10:49:39.746 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-02 10:49:40.080 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-02 10:49:40.995 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-02 10:49:41.027 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 10:49:41.059 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-02 10:49:41.061 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-02 10:49:41.064 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-02 10:49:41.065 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-02 10:49:44.814 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:49:48.850 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-02 10:49:48.873 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-02 10:49:51.482 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.542 seconds (JVM running for 27.757)
2025-07-02 10:52:18.363 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 10:52:18.365 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 10:52:18.374 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2025-07-02 10:52:18.458 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-02 10:52:19.069 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 10:52:19.070 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: PD, 权限类型: data
2025-07-02 10:52:19.371 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-07-02 10:52:19.508 [http-nio-8285-exec-2] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 用户ID: 1936640367617249280 没有任何数据权限
2025-07-02 10:52:19.508 [http-nio-8285-exec-2] WARN  c.d.percode.service.impl.UserPermissionServiceImpl - 用户ID: 1936640367617249280 没有任何权限
2025-07-02 10:52:19.509 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功（混合认证），用户ID: 1936640367617249280, 认证方式: CustomJWT, 权限类型: data, 菜单数量: 0, 按钮数量: 0, 数据权限数量: 0, 耗时: 439ms
2025-07-02 10:52:19.750 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 请求处理完成 - URI: /auth/permissions, 处理时间: 1291ms
2025-07-02 10:54:26.368 [http-nio-8285-exec-5] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-02 10:54:26.423 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: PD, 权限类型: data
2025-07-02 10:54:26.425 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1936640367617249280, 模块标识: PD, 权限类型: data
2025-07-02 10:54:26.554 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有角色数量: 1
2025-07-02 10:54:26.642 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户拥有数据权限数量: 4
2025-07-02 10:54:26.649 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 菜单权限数量: 0, 按钮权限数量: 0, 数据权限数量: 1
2025-07-02 10:54:26.651 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 用户权限查询完成，用户ID: 1936640367617249280, 根菜单数量: 0, 按钮数量: 0, 数据权限数量: 1, 耗时: 224ms
2025-07-02 10:54:26.652 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功（混合认证），用户ID: 1936640367617249280, 认证方式: CustomJWT, 权限类型: data, 菜单数量: 0, 按钮数量: 0, 数据权限数量: 1, 耗时: 227ms
2025-07-02 11:27:55.833 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 11:27:55.886 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-02 12:06:46.741 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 23152 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-02 12:06:46.743 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-02 12:06:46.756 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-02 12:06:48.208 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:06:48.210 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-02 12:06:48.236 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-07-02 12:06:48.240 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:06:48.241 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-02 12:06:48.251 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-02 12:06:48.259 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:06:48.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-02 12:06:48.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-07-02 12:06:48.297 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-02 12:06:48.300 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-02 12:06:48.327 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-07-02 12:06:49.090 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-02 12:06:49.102 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-02 12:06:49.103 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-02 12:06:49.103 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-02 12:06:49.321 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-02 12:06:49.321 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2516 ms
2025-07-02 12:06:49.431 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-02 12:06:49.577 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-02 12:06:50.428 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-02 12:06:50.487 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-02 12:06:50.630 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-02 12:06:50.784 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-02 12:06:51.095 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-02 12:06:51.108 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-02 12:06:51.125 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-02 12:06:51.125 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-02 12:06:51.126 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-02 12:06:51.126 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-02 12:06:53.092 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 12:06:54.731 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-02 12:06:54.755 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-02 12:06:55.529 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.687 seconds (JVM running for 12.571)
2025-07-02 12:06:59.588 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-02 12:06:59.588 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-02 12:06:59.590 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-02 12:16:08.742 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始查询用户列表
2025-07-02 12:16:08.743 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 执行全量查询（不分页）
2025-07-02 12:16:08.933 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 9
2025-07-02 12:16:09.578 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 9
2025-07-02 12:16:09.578 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-07-02 17:34:33.095 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始查询用户列表
2025-07-02 17:34:33.515 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 执行全量查询（不分页）
2025-07-02 17:34:39.681 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 9
2025-07-02 17:34:40.840 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 9
2025-07-02 17:34:40.841 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-07-02 17:42:01.101 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始查询用户列表
2025-07-02 17:42:02.681 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 执行全量查询（不分页）
2025-07-02 17:42:03.176 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 9
2025-07-02 17:42:03.971 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 9
2025-07-02 17:42:03.971 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-07-02 18:15:54.379 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始查询用户列表
2025-07-02 18:15:54.383 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 执行全量查询（不分页）
2025-07-02 18:15:55.511 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 9
2025-07-02 18:15:57.136 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 9
2025-07-02 18:15:57.137 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-07-02 23:33:12.034 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-07-02 23:33:17.711 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
