package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 移动部门请求VO类
 * 用于将部门移动到新的父部门下
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MoveOrgStructureRequestVO", description = "移动部门请求参数")
public class MoveOrgStructureRequestVO {
    
    @ApiModelProperty(value = "要移动的部门ID", example = "1930806593885179904", required = true)
    @JsonDeserialize(using = LongDeserializer.class)
    private Long orgId;
    
    @ApiModelProperty(value = "新的父部门ID（移动到根级别时传null或0）", example = "1930806593885179903")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long newParentId;
    
    @ApiModelProperty(value = "在新父部门下的排序位置（可选，不传则自动排到最后）", example = "1")
    private Integer newOrderInfo;
}
