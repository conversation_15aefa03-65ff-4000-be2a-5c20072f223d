# 模块级别数据操作权限配置修改任务

## 任务概述
根据业务需求变化，将 `/data-operates/configure` 接口从"数据级别配置"修改为"模块级别配置"。配置了模块权限后，该模块下所有数据都继承这些操作权限。

## 已完成步骤

### ✅ 步骤 1: 修改 ConfigureDataOperateRequestVO
- 文件: `src/main/java/com/dfit/percode/vo/ConfigureDataOperateRequestVO.java`
- 移除字段：`dataIdentifier`、`dataType`
- 保留字段：`moduleIdentifier`、`operateTypes`
- 更新注释：改为模块级别的统一权限配置

### ✅ 步骤 2: 修改 TDataOperateMapper
- 文件: `src/main/java/com/dfit/percode/mapper/TDataOperateMapper.java`
- 修改 `checkDataPermissionExists` → `checkModuleExists`：验证模块是否存在
- 修改 `deleteByDataIdentifierAndModule` → `deleteByModule`：按模块删除配置
- 修改 `getDataOperateConfig` → `getModuleOperateConfig`：按模块查询配置

### ✅ 步骤 3: 修改 DataOperateConfigResponseVO
- 文件: `src/main/java/com/dfit/percode/vo/DataOperateConfigResponseVO.java`
- 移除字段：`dataId`、`dataName`、`dataIdentifier`、`dataType`
- 保留字段：`moduleId`、`moduleIdentifier`、`moduleName`、`operateTypes`
- 更新注释：改为模块级别的操作权限配置

### ✅ 步骤 4: 修改 TDataOperateServiceImpl
- 文件: `src/main/java/com/dfit/percode/service/impl/TDataOperateServiceImpl.java`
- 修改 `configureDataOperate` 方法：改为模块级别配置逻辑
- 添加 `getModuleOperateConfig` 方法：专门查询模块配置
- 保留 `getDataOperateConfig` 方法：兼容性考虑，内部调用模块查询

### ✅ 步骤 5: 修改 DataOperateController
- 文件: `src/main/java/com/dfit/percode/controller/DataOperateController.java`
- 更新接口注释和日志：改为模块操作权限配置
- 保持接口路径不变：`/data-operates/configure`

## 核心变化对比

### 修改前（数据级别配置）

#### 请求参数
```json
{
  "dataIdentifier": "user_basic_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 3]
}
```

#### 数据库存储
```sql
INSERT INTO t_data_operate (
  module_identifier, 
  data_identifier,    -- 具体数据标识
  data_type, 
  operate_type
) VALUES (
  'user_data_module',
  'user_basic_data',  -- 针对具体数据
  1,
  1
);
```

#### 业务逻辑
- 针对具体的数据权限进行操作配置
- 需要为每个数据权限单独配置

### 修改后（模块级别配置）

#### 请求参数
```json
{
  "moduleIdentifier": "user_data_module",
  "operateTypes": [1, 2, 3]
}
```

#### 数据库存储
```sql
INSERT INTO t_data_operate (
  module_identifier, 
  data_identifier,    -- NULL（模块级别）
  data_type, 
  operate_type
) VALUES (
  'user_data_module',
  NULL,               -- 模块级别配置
  1,
  1
);
```

#### 业务逻辑
- 针对整个模块进行统一操作配置
- 该模块下所有数据都继承这些操作权限

## 数据库查询逻辑变化

### 验证逻辑
```sql
-- 修改前：验证数据权限是否存在
SELECT COUNT(*) FROM t_data_permission 
WHERE data_identifier = ? AND module_identifier = ? AND is_del = false

-- 修改后：验证模块是否存在
SELECT COUNT(*) FROM t_data_module 
WHERE module_identifier = ? AND is_del = false
```

### 删除逻辑
```sql
-- 修改前：删除指定数据权限的操作配置
DELETE FROM t_data_operate 
WHERE data_identifier = ? AND module_identifier = ?

-- 修改后：删除指定模块的操作配置
DELETE FROM t_data_operate 
WHERE module_identifier = ? AND (data_identifier IS NULL OR data_identifier = '')
```

### 查询逻辑
```sql
-- 修改前：查询数据权限配置
SELECT dp.*, array_agg(dop.operate_type) as operateTypes
FROM t_data_permission dp
LEFT JOIN t_data_operate dop ON dp.data_identifier = dop.data_identifier
WHERE dp.data_identifier = ? AND dp.module_identifier = ?

-- 修改后：查询模块配置
SELECT dm.*, array_agg(dop.operate_type) as operateTypes
FROM t_data_module dm
LEFT JOIN t_data_operate dop ON dm.module_identifier = dop.module_identifier
WHERE dm.module_identifier = ? AND (dop.data_identifier IS NULL OR dop.data_identifier = '')
```

## 接口使用方式

### 配置模块操作权限
```javascript
// POST /data-operates/configure
{
  "moduleIdentifier": "user_data_module",
  "operateTypes": [1, 2, 3]  // 1-新增，2-修改，3-删除
}
```

### 获取模块操作权限配置
```javascript
// GET /data-operates/config?moduleIdentifier=user_data_module
// 注意：dataIdentifier 参数仍然保留以保持兼容性，但会被忽略
```

### 响应格式
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "moduleId": 1001,
    "moduleIdentifier": "user_data_module",
    "moduleName": "用户数据模块",
    "operateTypes": [1, 2, 3]
  }
}
```

## 业务优势

### 1. 简化配置流程
- **修改前**：需要为每个数据权限单独配置操作类型
- **修改后**：一次配置整个模块，所有数据自动继承

### 2. 统一权限管理
- **模块级别统一**：同一模块下的所有数据具有相同的操作权限
- **减少配置复杂度**：避免数据权限之间的操作权限不一致

### 3. 维护便利性
- **批量更新**：修改模块权限即可影响所有相关数据
- **权限一致性**：确保模块内数据权限的一致性

## 兼容性考虑

### 1. 接口路径保持不变
- 配置接口：`POST /data-operates/configure`
- 查询接口：`GET /data-operates/config`

### 2. 查询接口兼容性
- 保留原有的 `getDataOperateConfig(dataIdentifier, moduleIdentifier)` 方法
- 内部调用新的 `getModuleOperateConfig(moduleIdentifier)` 方法
- 前端可以继续传递 `dataIdentifier` 参数，但会被忽略

### 3. 数据库兼容性
- 保持 `t_data_operate` 表结构不变
- 通过 `data_identifier IS NULL` 区分模块级别配置

## 测试验证

### 测试场景
1. **模块权限配置**：验证能够成功配置模块级别的操作权限
2. **权限继承**：验证模块下所有数据都继承配置的操作权限
3. **配置覆盖**：验证新配置能够覆盖旧配置
4. **查询功能**：验证能够正确查询模块级别的权限配置
5. **兼容性**：验证原有的查询接口仍然可用

### 预期结果
- 配置成功后，模块下所有数据权限都具有相同的操作权限
- 查询接口返回模块级别的权限配置
- 原有的前端调用方式仍然兼容

## 状态
🎯 **任务完成** - 数据操作权限配置已成功从数据级别修改为模块级别，实现了统一的模块权限管理，简化了配置流程并保持了向后兼容性