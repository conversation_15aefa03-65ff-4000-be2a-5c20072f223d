-- =====================================================
-- 密码MD5加密迁移脚本
-- 将现有明文密码转换为MD5加密格式
-- 执行日期：2025-07-02
-- =====================================================

-- 1. 备份现有密码数据（可选，用于回滚）
-- CREATE TABLE t_user_password_backup AS 
-- SELECT id, user_name, account, password, create_time 
-- FROM t_user WHERE is_del = false;

-- 2. 查看当前明文密码数据
SELECT 
    id,
    user_name,
    account,
    password,
    LENGTH(password) as password_length,
    CASE 
        WHEN LENGTH(password) = 32 AND password ~ '^[a-f0-9]+$' THEN 'MD5格式'
        ELSE '明文格式'
    END as password_format
FROM t_user 
WHERE is_del = false 
ORDER BY id;

-- 3. 更新明文密码为MD5加密
-- 注意：PostgreSQL使用md5()函数进行MD5加密
UPDATE t_user 
SET 
    password = md5(password),
    modify_time = CURRENT_TIMESTAMP
WHERE 
    is_del = false 
    AND LENGTH(password) != 32  -- 排除已经是MD5格式的密码
    AND password IS NOT NULL 
    AND password != '';

-- 4. 验证迁移结果
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN LENGTH(password) = 32 THEN 1 END) as md5_passwords,
    COUNT(CASE WHEN LENGTH(password) != 32 THEN 1 END) as non_md5_passwords
FROM t_user 
WHERE is_del = false;

-- 5. 查看迁移后的密码格式
SELECT 
    id,
    user_name,
    account,
    password,
    LENGTH(password) as password_length,
    CASE 
        WHEN LENGTH(password) = 32 AND password ~ '^[a-f0-9]+$' THEN 'MD5格式'
        ELSE '其他格式'
    END as password_format
FROM t_user 
WHERE is_del = false 
ORDER BY id;

-- 6. 测试用例：验证特定用户的密码加密
-- 例如：原密码'123456'的MD5值应该是'e10adc3949ba59abbe56e057f20f883e'
SELECT 
    user_name,
    account,
    password,
    md5('123456') as expected_md5_for_123456,
    CASE 
        WHEN password = md5('123456') THEN '密码可能是123456'
        ELSE '密码不是123456'
    END as password_check
FROM t_user 
WHERE is_del = false 
AND account IN ('admin', 'test', 'zhang001')  -- 测试几个常见账号
ORDER BY account;

-- =====================================================
-- 迁移完成检查清单
-- =====================================================
-- □ 所有用户密码长度都是32位
-- □ 所有密码都是小写十六进制字符
-- □ 测试登录功能正常
-- □ 新增用户密码自动MD5加密
-- □ 密码修改功能正常
-- =====================================================

-- 回滚脚本（如果需要）
-- UPDATE t_user 
-- SET password = backup.password, modify_time = CURRENT_TIMESTAMP
-- FROM t_user_password_backup backup
-- WHERE t_user.id = backup.id;
-- DROP TABLE t_user_password_backup;
