package com.dfit.percode.sync.entity;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 外部系统员工系统标识实体类
 * 对应外部系统的employee_system表结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExternalEmployeeSystem {
    
    /**
     * 全局唯一标识符
     */
    private String guid;
    
    /**
     * 关联的员工MDM ID
     */
    private String employeeMdmId;
    
    /**
     * 系统代码
     */
    private String systemCode;
    
    /**
     * 系统数据ID
     */
    private String systemDataId;
    
    /**
     * 组织代码
     */
    private String orgCode;
    
    /**
     * 部门代码
     */
    private String departmentCode;
    
    /**
     * 员工编号
     */
    private String employeeCode;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 登录账号
     */
    private String loginAccount;
}
