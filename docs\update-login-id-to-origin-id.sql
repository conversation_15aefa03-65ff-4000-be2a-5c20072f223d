-- =====================================================
-- 数据库字段修改脚本：login_id → origin_id
-- 执行时间：2025年6月25日
-- 修改说明：将t_user表中的login_id字段重命名为origin_id
-- =====================================================

-- 1. 修改字段名称
ALTER TABLE t_user RENAME COLUMN login_id TO origin_id;

-- 2. 更新字段注释
COMMENT ON COLUMN t_user.origin_id IS '原始关联id，用于外部系统对接';

-- 3. 验证修改结果
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 't_user' 
  AND column_name = 'origin_id';

-- 4. 查看表结构确认
\d t_user;

-- 5. 测试查询（确保字段可正常使用）
SELECT 
    id,
    user_name,
    origin_id,
    organ_affiliation,
    account,
    is_disable
FROM t_user 
WHERE is_del = false 
LIMIT 5;

-- =====================================================
-- 修改完成说明
-- =====================================================
-- 1. ✅ 字段名已从 login_id 修改为 origin_id
-- 2. ✅ 字段注释已更新为 '原始关联id，用于外部系统对接'
-- 3. ✅ 数据类型保持不变：varchar(255)
-- 4. ✅ 现有数据不受影响
-- 5. ✅ 应用代码已同步修改
-- =====================================================
