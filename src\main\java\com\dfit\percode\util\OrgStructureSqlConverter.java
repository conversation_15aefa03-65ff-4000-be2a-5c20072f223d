package com.dfit.percode.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OrgStructureSqlConverter {

    // ID生成器的起始值，确保足够大以避免与现有ID冲突
    private static final long START_ID = 1000000000000000000L;
    private static final AtomicLong idGenerator = new AtomicLong(START_ID);
    private static final Map<String, Long> idMapping = new HashMap<>();

    public static void main(String[] args) {
        String inputFile = "docs/org_structure_repair.sql";
        String outputFile = "docs/org_structure_repair_numeric.sql";

        try {
            convertSqlFile(inputFile, outputFile);
            System.out.println("SQL file conversion successful!");
            System.out.println("Output file: " + outputFile);
        } catch (IOException e) {
            System.err.println("Error during SQL file conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void convertSqlFile(String inputFile, String outputFile) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {

            String line;
            // 更通用的正则表达式，用于匹配 VALUES 子句中的三个字符串参数
            Pattern pattern = Pattern.compile("VALUES \\('([^']*)',\\s*'([^']*)',\\s*'([^']*)',");

            while ((line = reader.readLine()) != null) {
                if (line.trim().startsWith("INSERT INTO")) {
                    Matcher matcher = pattern.matcher(line);
                    if (matcher.find()) {
                        String oldIdStr = matcher.group(1);
                        String organName = matcher.group(2);
                        String oldPreIdStr = matcher.group(3);

                        // 为新的ID生成或获取映射
                        long newId = idMapping.computeIfAbsent(oldIdStr, k -> idGenerator.getAndIncrement());
                        
                        // 处理 pre_id
                        Long newPreId = null;
                        if (!"0".equals(oldPreIdStr) && !oldPreIdStr.isEmpty()) {
                            newPreId = idMapping.computeIfAbsent(oldPreIdStr, k -> idGenerator.getAndIncrement());
                        }

                        // 构建新的 INSERT 语句
                        String newPreIdValue = (newPreId == null) ? "NULL" : String.valueOf(newPreId);
                         if ("0".equals(oldPreIdStr)) {
                            newPreIdValue = "0";
                        }
                        
                        String newLine = line.replace("'" + oldIdStr + "'", String.valueOf(newId))
                                             .replace("'" + oldPreIdStr + "'", newPreIdValue);
                        
                        writer.write(newLine);
                    } else {
                        // 如果正则不匹配，则原样写入
                        writer.write(line);
                    }
                } else {
                    // 非 INSERT 语句直接写入
                    writer.write(line);
                }
                writer.newLine();
            }
        }
    }
}