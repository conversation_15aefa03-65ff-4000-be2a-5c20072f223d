package com.dfit.percode.util;

import java.util.concurrent.atomic.AtomicLong;

public class SnowflakeIdGenerator {
    // 起始时间戳（时间戳基准点）
    private final long epoch = 1288834974657L;  // Twitter Snowflake 起始时间：2010-11-04 11:42:54

    // 各部分的位数
    private final long workerIdBits = 5L;  // 机器ID所占的位数
    private final long datacenterIdBits = 5L;  // 数据中心ID所占的位数
    private final long sequenceBits = 12L;  // 序列号占用的位数

    // 每个部分的最大值
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);  // 31
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);  // 31
    private final long sequenceMask = -1L ^ (-1L << sequenceBits);  // 4095

    // 各部分的偏移量
    private final long workerIdShift = sequenceBits;  // 12
    private final long datacenterIdShift = sequenceBits + workerIdBits;  // 17
    private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;  // 22

    // 工作机器ID（0~31）
    private long workerId;

    // 数据中心ID（0~31）
    private long datacenterId;

    // 序列号（0~4095）
    private AtomicLong sequence = new AtomicLong(0);

    // 上次生成ID的时间戳
    private long lastTimestamp = -1L;

    // 构造方法
    public SnowflakeIdGenerator(long workerId, long datacenterId) {
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException("Worker ID can't be greater than " + maxWorkerId + " or less than 0");
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException("Datacenter ID can't be greater than " + maxDatacenterId + " or less than 0");
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
    }

    // 生成ID
    public synchronized long generateId() {
        long timestamp = System.currentTimeMillis();

        // 如果当前时间小于上次生成ID的时间戳，说明系统时钟回拨了
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("Clock moved backwards. Refusing to generate id");
        }

        // 如果当前时间和上次生成ID的时间相同，则序列号自增
        if (timestamp == lastTimestamp) {
            sequence.set((sequence.get() + 1) & sequenceMask);
            if (sequence.get() == 0) {
                // 如果序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            // 如果是新的一毫秒，重置序列号
            sequence.set(0);
        }

        lastTimestamp = timestamp;

        // 将各部分组合成ID
        long id = ((timestamp - epoch) << timestampLeftShift) // 时间戳部分
                | (datacenterId << datacenterIdShift)          // 数据中心ID部分
                | (workerId << workerIdShift)                  // 工作机器ID部分
                | sequence.get();                              // 序列号部分

        return id;
    }

    // 等待到下一毫秒
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }

    public static void main(String[] args) {
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        System.out.println(idGenerator.generateId());  // 生成ID并打印
    }
}
