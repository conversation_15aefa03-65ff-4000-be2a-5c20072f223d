package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TDataModule;
import com.dfit.percode.mapper.TDataModuleMapper;
import com.dfit.percode.service.ITDataModuleService;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.vo.AddDataModuleRequestVO;
import com.dfit.percode.vo.DataModuleListRequestVO;
import com.dfit.percode.vo.DataModuleListResponseVO;
import com.dfit.percode.vo.DeleteDataModuleRequestVO;
import com.dfit.percode.vo.response.RoleUsageVO;
import com.dfit.percode.vo.response.UsageInfoVO;
import com.dfit.percode.vo.response.UsageCheckResponseVO;
import com.dfit.percode.exception.UsageConflictException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 数据权限模块信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Slf4j
@Service
public class TDataModuleServiceImpl extends ServiceImpl<TDataModuleMapper, TDataModule> implements ITDataModuleService {

    @Autowired
    TDataModuleMapper tDataModuleMapper;

    /**
     * 获取数据模块列表（原有方法）
     * @return 数据模块列表
     */
    @Override
    public List<TDataModule> dataModule() {
        return tDataModuleMapper.dataModule();
    }

    /**
     * 分页查询数据模块列表
     * @param request 查询请求参数
     * @return 数据模块列表
     */
    @Override
    public List<DataModuleListResponseVO> getDataModuleList(DataModuleListRequestVO request) {
        log.info("开始查询数据模块列表");

        // 计算分页偏移量
        if (request.getPageNum() != null && request.getPageSize() != null) {
            int offset = (request.getPageNum() - 1) * request.getPageSize();
            request.setPageNum(offset);
        }

        List<DataModuleListResponseVO> resultList = tDataModuleMapper.getDataModuleList(request);
        log.info("数据模块列表查询完成，共{}条记录", resultList.size());

        return resultList;
    }

    /**
     * 获取数据模块总数
     * @param request 查询请求参数
     * @return 总数
     */
    @Override
    public Integer getDataModuleTotal(DataModuleListRequestVO request) {
        return tDataModuleMapper.getDataModuleTotal(request);
    }

    /**
     * 新增数据模块
     * @param request 新增请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDataModule(AddDataModuleRequestVO request) {
        log.info("开始新增数据模块");
        log.info("模块名称: {}, 模块标识: {}", request.getModuleName(), request.getModuleIdentifier());

        // 1. 检查模块标识是否已存在
        int existsCount = tDataModuleMapper.checkModuleIdentifierExists(request.getModuleIdentifier());
        if (existsCount > 0) {
            log.error("模块标识已存在: {}", request.getModuleIdentifier());
            throw new RuntimeException("模块标识已存在");
        }

        // 2. 生成ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long moduleId = idGenerator.generateId();

        // 3. 设置默认排序序号
        Integer orderInfo = request.getOrderInfo();
        if (orderInfo == null) {
            orderInfo = 1;
        }

        // 4. 插入数据模块记录
        tDataModuleMapper.insertDataModule(moduleId, request.getModuleName(),
                request.getModuleIdentifier(), orderInfo);

        log.info("数据模块新增成功，ID: {}", moduleId);
    }

    /**
     * 删除数据模块
     * @param request 删除请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataModule(DeleteDataModuleRequestVO request) {
        log.info("开始删除数据模块");

        Long moduleId = null;
        String moduleIdentifier = null;

        // 判断是通过ID删除还是通过标识符删除
        if (request.getModuleId() != null && !request.getModuleId().trim().isEmpty()) {
            // 通过ID删除
            log.info("通过模块ID删除: {}", request.getModuleId());
            try {
                moduleId = Long.parseLong(request.getModuleId());

                // 检查模块是否存在
                int existsCount = tDataModuleMapper.checkModuleExistsById(moduleId);
                if (existsCount == 0) {
                    log.error("数据模块不存在，模块ID: {}", moduleId);
                    throw new RuntimeException("数据模块不存在");
                }

                // 获取模块标识符用于检查使用情况
                moduleIdentifier = tDataModuleMapper.getModuleIdentifierById(moduleId);

            } catch (NumberFormatException e) {
                log.error("模块ID格式错误: {}", request.getModuleId());
                throw new RuntimeException("模块ID格式错误");
            }

        } else if (request.getModuleIdentifier() != null && !request.getModuleIdentifier().trim().isEmpty()) {
            // 通过标识符删除
            log.info("通过模块标识符删除: {}", request.getModuleIdentifier());
            moduleIdentifier = request.getModuleIdentifier();

            // 检查模块是否存在
            int existsCount = tDataModuleMapper.checkModuleIdentifierExists(moduleIdentifier);
            if (existsCount == 0) {
                log.error("数据模块不存在，模块标识符: {}", moduleIdentifier);
                throw new RuntimeException("数据模块不存在");
            }

            // 获取模块ID用于删除
            moduleId = tDataModuleMapper.getModuleIdByIdentifier(moduleIdentifier);

        } else {
            log.error("删除参数错误：必须提供模块ID或模块标识符");
            throw new RuntimeException("删除参数错误：必须提供模块ID或模块标识符");
        }

        // 检查模块是否已被数据权限使用
        if (moduleIdentifier != null) {
            int usedCount = tDataModuleMapper.checkModuleUsedByDataPermissions(moduleIdentifier);
            if (usedCount > 0) {
                log.error("数据模块正在被使用，无法删除，模块标识符: {}", moduleIdentifier);
                throw new RuntimeException("该模块正在被数据权限使用，无法删除");
            }
        }

        // 逻辑删除数据模块
        if (moduleId != null) {
            tDataModuleMapper.deleteDataModuleById(moduleId);
            log.info("数据模块删除成功，ID: {}, 标识符: {}", moduleId, moduleIdentifier);
        } else {
            throw new RuntimeException("删除失败：无法确定模块ID");
        }
    }

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 删除数据模块 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除请求参数
     * @return 删除结果或使用情况信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Object deleteDataModuleV2(DeleteDataModuleRequestVO request) {
        log.info("开始删除数据模块V2");

        Long moduleId = null;
        String moduleIdentifier = null;

        // 判断是通过ID删除还是通过标识符删除
        if (request.getModuleId() != null && !request.getModuleId().trim().isEmpty()) {
            // 通过ID删除
            log.info("通过模块ID删除: {}, 强制删除: {}", request.getModuleId(), request.getForceDelete());
            try {
                moduleId = Long.parseLong(request.getModuleId());

                // 检查模块是否存在
                int existsCount = tDataModuleMapper.checkModuleExistsById(moduleId);
                if (existsCount == 0) {
                    log.error("数据模块不存在，模块ID: {}", moduleId);
                    throw new RuntimeException("数据模块不存在");
                }

                // 获取模块标识符用于检查使用情况
                moduleIdentifier = tDataModuleMapper.getModuleIdentifierById(moduleId);

            } catch (NumberFormatException e) {
                log.error("模块ID格式错误: {}", request.getModuleId());
                throw new RuntimeException("模块ID格式错误");
            }
        } else if (request.getModuleIdentifier() != null && !request.getModuleIdentifier().trim().isEmpty()) {
            // 通过标识符删除
            log.info("通过模块标识符删除: {}, 强制删除: {}", request.getModuleIdentifier(), request.getForceDelete());
            moduleIdentifier = request.getModuleIdentifier();

            // 检查模块是否存在
            int existsCount = tDataModuleMapper.checkModuleIdentifierExists(moduleIdentifier);
            if (existsCount == 0) {
                log.error("数据模块不存在，模块标识符: {}", moduleIdentifier);
                throw new RuntimeException("数据模块不存在");
            }

            // 获取模块ID
            moduleId = tDataModuleMapper.getModuleIdByIdentifier(moduleIdentifier);
        } else {
            log.error("删除参数错误：模块ID和模块标识符都为空");
            throw new RuntimeException("删除参数错误：模块ID和模块标识符不能都为空");
        }

        // 2. 如果不是强制删除，检查使用情况
        if (!Boolean.TRUE.equals(request.getForceDelete())) {
            List<RoleUsageVO> usedByRoles = tDataModuleMapper.getRolesByDataModuleIdentifier(moduleIdentifier);
            if (!usedByRoles.isEmpty()) {
                log.info("数据模块正在被角色使用，返回使用情况，模块标识符: {}", moduleIdentifier);

                // 构建使用情况响应
                UsageCheckResponseVO response = new UsageCheckResponseVO();
                response.setCanDirectDelete(false);

                UsageInfoVO usageInfo = new UsageInfoVO();
                usageInfo.setModuleIdentifier(moduleIdentifier);
                usageInfo.setModuleName(tDataModuleMapper.getDataModuleNameByIdentifier(moduleIdentifier));
                usageInfo.setUsedByRoles(usedByRoles);
                usageInfo.setTotalRoleCount(usedByRoles.size());

                response.setUsageInfo(usageInfo);

                // 返回409状态码和使用情况信息
                throw new UsageConflictException("检测到使用情况，请确认是否强制删除", response);
            }
        }

        // 3. 执行删除（无使用情况或强制删除）
        if (Boolean.TRUE.equals(request.getForceDelete())) {
            // 强制删除：级联删除相关数据
            log.info("执行强制删除，模块标识符: {}", moduleIdentifier);

            // 删除模块下所有数据权限的角色权限关联
            tDataModuleMapper.deleteDataModuleRolePermissions(moduleIdentifier);

            // 删除模块下所有数据权限的操作配置
            tDataModuleMapper.deleteDataModuleOperates(moduleIdentifier);

            // 删除模块下所有数据权限
            tDataModuleMapper.deleteDataPermissionsByModuleIdentifier(moduleIdentifier);
        }

        // 4. 逻辑删除数据模块
        if (moduleId != null) {
            tDataModuleMapper.deleteDataModuleById(moduleId);
            log.info("数据模块删除成功，ID: {}, 标识符: {}", moduleId, moduleIdentifier);
        } else {
            throw new RuntimeException("删除失败：无法确定模块ID");
        }

        return null; // 成功删除返回null
    }
}
