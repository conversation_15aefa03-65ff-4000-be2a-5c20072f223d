package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 检查权限标识符响应VO类
 * 返回权限标识符的验证结果和相关信息
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "CheckIdentifierResponseVO", description = "检查权限标识符响应数据")
public class CheckIdentifierResponseVO {
    
    @ApiModelProperty(value = "标识符是否可用", example = "true")
    private Boolean isAvailable;
    
    @ApiModelProperty(value = "提示信息", example = "该权限标识符可以使用")
    private String message;
    
    @ApiModelProperty(value = "建议的标识符（当不可用时提供）", example = "user:list:view_v2")
    private String suggestion;
    
    @ApiModelProperty(value = "验证错误信息", example = "权限标识符格式不正确")
    private String validationError;
    
    @ApiModelProperty(value = "格式规则说明")
    private List<String> formatRules;
    
    /**
     * 创建可用的响应
     */
    public static CheckIdentifierResponseVO available(String message) {
        CheckIdentifierResponseVO response = new CheckIdentifierResponseVO();
        response.setIsAvailable(true);
        response.setMessage(message);
        return response;
    }
    
    /**
     * 创建不可用的响应
     */
    public static CheckIdentifierResponseVO unavailable(String message, String suggestion) {
        CheckIdentifierResponseVO response = new CheckIdentifierResponseVO();
        response.setIsAvailable(false);
        response.setMessage(message);
        response.setSuggestion(suggestion);
        return response;
    }
    
    /**
     * 创建验证错误的响应
     */
    public static CheckIdentifierResponseVO validationError(String error, List<String> rules) {
        CheckIdentifierResponseVO response = new CheckIdentifierResponseVO();
        response.setIsAvailable(false);
        response.setValidationError(error);
        response.setFormatRules(rules);
        return response;
    }
}
