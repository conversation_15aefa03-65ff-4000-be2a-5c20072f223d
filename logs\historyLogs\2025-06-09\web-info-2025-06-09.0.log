2025-06-09 10:52:34.965 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 19988 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 10:52:34.976 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 10:52:34.992 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 10:52:37.858 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:52:37.862 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 10:52:37.892 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 10:52:37.895 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:52:37.896 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 10:52:37.907 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 10:52:37.940 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:52:37.942 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 10:52:37.967 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-09 10:52:38.038 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:52:38.041 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 10:52:38.093 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-09 10:52:39.584 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 10:52:39.600 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 10:52:39.601 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 10:52:39.601 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 10:52:39.828 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 10:52:39.829 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4700 ms
2025-06-09 10:52:40.004 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 10:52:40.214 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 10:52:41.664 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 10:52:41.896 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 10:52:42.305 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 10:52:42.587 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 10:52:43.127 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 10:52:43.145 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 10:52:44.158 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSyncController': Unsatisfied dependency expressed through field 'dataSyncService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataSyncService': Unsatisfied dependency expressed through field 'snowflakeIdGenerator'; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.dfit.percode.util.SnowflakeIdGenerator' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@org.springframework.beans.factory.annotation.Autowired(required=true)}
2025-06-09 10:52:44.159 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 10:52:44.539 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 10:52:44.541 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-09 10:52:44.780 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-09 10:55:45.770 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 10:55:45.772 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 22044 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 10:55:45.773 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 10:55:47.395 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:55:47.399 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 10:55:47.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 10:55:47.431 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:55:47.432 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 10:55:47.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 10:55:47.457 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:55:47.458 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 10:55:47.472 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-09 10:55:47.489 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 10:55:47.491 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 10:55:47.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-09 10:55:48.529 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 10:55:48.539 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 10:55:48.540 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 10:55:48.541 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 10:55:48.784 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 10:55:48.785 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2937 ms
2025-06-09 10:55:48.938 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 10:55:49.268 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 10:55:50.259 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 10:55:50.327 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 10:55:50.531 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 10:55:50.721 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 10:55:51.093 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 10:55:51.106 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 10:55:52.375 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 10:55:56.149 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 10:55:56.176 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 10:55:56.879 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.01 seconds (JVM running for 14.272)
2025-06-09 10:56:07.410 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 10:56:07.410 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 10:56:07.413 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 11:00:37.711 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:00:37.833 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:00:46.831 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27572 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:00:46.833 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:00:46.834 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:00:49.077 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:00:49.080 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:00:49.106 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:00:49.110 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:00:49.111 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:00:49.124 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:00:49.131 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:00:49.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:00:49.146 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 JPA repository interfaces.
2025-06-09 11:00:49.163 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:00:49.164 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:00:49.184 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-09 11:00:50.274 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:00:50.286 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:00:50.287 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:00:50.287 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:00:50.544 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:00:50.546 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3618 ms
2025-06-09 11:00:50.758 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:00:50.996 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:00:51.928 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:00:52.025 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:00:52.277 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:00:52.552 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:00:53.094 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:00:53.117 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:00:54.096 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:00:56.607 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:00:56.634 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:00:57.272 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.451 seconds (JVM running for 13.126)
2025-06-09 11:01:01.100 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:01:01.102 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:01:01.110 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 11:09:05.329 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:09:05.343 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:09:30.036 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:09:30.045 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 10892 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:09:30.046 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:09:32.279 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:09:32.289 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:09:32.338 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:09:32.351 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:09:32.353 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:09:32.376 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:09:32.392 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:09:32.395 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:09:32.419 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-09 11:09:32.448 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:09:32.451 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:09:32.484 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-09 11:09:34.342 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:09:34.355 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:09:34.356 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:09:34.357 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:09:34.620 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:09:34.621 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4471 ms
2025-06-09 11:09:34.748 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:09:34.968 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:09:38.533 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:09:38.661 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:09:39.038 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:09:39.266 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:09:40.063 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:09:40.079 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:09:41.273 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:09:44.257 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:09:44.284 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:09:45.014 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.082 seconds (JVM running for 18.485)
2025-06-09 11:09:50.567 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:09:50.568 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:09:50.573 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 11:10:36.340 [http-nio-8285-exec-6] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-02 00:00:00
2025-06-09 11:10:36.376 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-02T00:00
2025-06-09 11:10:36.376 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-02T00:00
2025-06-09 11:10:36.381 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-02%2000:00:00
2025-06-09 11:14:57.293 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:14:57.299 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:15:11.027 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 5988 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:15:11.029 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:15:11.030 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:15:13.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:15:13.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:15:13.066 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:15:13.071 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:15:13.072 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:15:13.088 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:15:13.097 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:15:13.098 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:15:13.114 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-09 11:15:13.130 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:15:13.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:15:13.151 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-09 11:15:14.375 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:15:14.388 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:15:14.389 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:15:14.389 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:15:14.602 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:15:14.603 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3492 ms
2025-06-09 11:15:14.712 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:15:14.887 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:15:15.781 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:15:15.847 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:15:16.071 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:15:16.275 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:15:16.851 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:15:16.875 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:15:18.344 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:15:20.802 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:15:20.825 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:15:21.494 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.043 seconds (JVM running for 13.939)
2025-06-09 11:15:34.425 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:15:34.426 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:15:34.428 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-09 11:19:50.223 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:19:50.227 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:20:01.429 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 28980 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:20:01.433 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:20:01.437 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:20:03.471 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:20:03.477 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:20:03.519 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:20:03.526 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:20:03.527 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:20:03.544 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:20:03.555 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:20:03.557 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:20:03.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-09 11:20:03.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:20:03.617 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:20:03.664 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-09 11:20:05.234 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:20:05.247 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:20:05.247 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:20:05.247 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:20:05.476 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:20:05.476 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3949 ms
2025-06-09 11:20:05.582 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:20:05.735 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:20:09.620 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:20:09.703 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:20:09.908 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:20:10.077 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:20:11.267 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:20:11.290 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:20:12.554 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:20:14.878 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:20:14.893 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:20:15.584 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.169 seconds (JVM running for 17.141)
2025-06-09 11:20:20.467 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:20:20.467 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:20:20.470 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 11:20:26.296 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.ExternalDataService - 外部系统连接测试成功: API测试成功
2025-06-09 11:21:08.842 [http-nio-8285-exec-7] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-01 23:59:59
2025-06-09 11:21:08.856 [http-nio-8285-exec-7] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:21:08.857 [http-nio-8285-exec-7] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:21:08.859 [http-nio-8285-exec-7] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01T00:00:00&endDate=2024-01-01T23:59:59
2025-06-09 11:24:42.623 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:24:42.629 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:24:55.474 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:24:55.477 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 8656 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:24:55.478 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:24:56.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:24:56.954 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:24:56.985 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:24:56.990 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:24:56.991 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:24:57.002 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:24:57.014 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:24:57.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:24:57.033 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-09 11:24:57.058 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:24:57.060 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:24:57.084 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-09 11:24:58.237 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:24:58.249 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:24:58.250 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:24:58.250 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:24:58.447 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:24:58.447 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2896 ms
2025-06-09 11:24:58.562 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:24:58.856 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:25:00.546 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:25:00.612 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:25:00.828 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:25:01.077 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:25:01.660 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:25:01.678 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:25:02.576 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:25:04.607 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:25:04.632 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:25:05.495 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.908 seconds (JVM running for 13.141)
2025-06-09 11:25:21.152 [http-nio-8285-exec-4] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:25:21.153 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:25:21.157 [http-nio-8285-exec-4] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-09 11:25:43.664 [http-nio-8285-exec-6] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-01 23:59:59
2025-06-09 11:25:43.687 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:25:43.687 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:25:43.695 [http-nio-8285-exec-6] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-01%2023:59:59
2025-06-09 11:32:58.321 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:32:58.329 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:33:05.668 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 28168 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:33:05.672 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:33:05.679 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:33:07.926 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:33:07.934 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:33:07.994 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:33:08.008 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:33:08.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:33:08.037 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:33:08.062 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:33:08.064 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:33:08.100 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 JPA repository interfaces.
2025-06-09 11:33:08.137 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:33:08.140 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:33:08.174 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-09 11:33:10.524 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:33:10.549 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:33:10.550 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:33:10.551 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:33:10.916 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:33:10.916 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5136 ms
2025-06-09 11:33:11.117 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:33:11.401 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:33:12.764 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:33:12.948 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:33:13.256 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:33:13.595 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:33:14.265 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:33:14.294 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:33:16.349 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:33:19.364 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:33:19.388 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:33:20.297 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.32 seconds (JVM running for 18.126)
2025-06-09 11:33:29.806 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:33:29.807 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:33:29.812 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 11:33:41.725 [http-nio-8285-exec-7] INFO  com.dfit.percode.sync.service.ExternalDataService - 外部系统连接测试成功: API测试成功
2025-06-09 11:34:25.268 [http-nio-8285-exec-8] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-01 23:59:59
2025-06-09 11:34:25.364 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:34:25.365 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-01T23:59:59
2025-06-09 11:34:25.372 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-01%2023:59:59
2025-06-09 11:34:26.686 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 0 个部门
2025-06-09 11:34:26.687 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 0 个部门
2025-06-09 11:34:26.687 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 0 个部门
2025-06-09 11:34:26.689 [http-nio-8285-exec-8] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 11:34:54.999 [http-nio-8285-exec-9] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 11:34:55.000 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:34:55.001 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:34:55.001 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 11:34:55.735 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 11:34:55.735 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 11:39:26.946 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:39:26.954 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:39:46.741 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:39:46.744 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27640 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:39:46.746 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:39:48.360 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:39:48.365 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:39:48.394 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:39:48.398 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:39:48.398 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:39:48.407 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:39:48.424 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:39:48.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:39:48.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-09 11:39:48.467 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:39:48.468 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:39:48.490 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-06-09 11:39:49.880 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:39:49.897 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:39:49.898 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:39:49.898 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:39:50.117 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:39:50.117 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3297 ms
2025-06-09 11:39:50.303 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:39:50.577 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:39:53.007 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:39:53.102 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:39:53.445 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:39:53.640 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:39:54.031 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:39:54.045 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:39:55.105 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:39:57.905 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:39:57.927 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:39:58.545 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.77 seconds (JVM running for 14.675)
2025-06-09 11:40:12.588 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:40:12.588 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:40:12.590 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-09 11:40:12.843 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 11:40:12.893 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:40:12.893 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:40:12.910 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 11:40:14.131 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 11:40:14.132 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 11:40:16.204 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 21 个部门
2025-06-09 11:40:16.275 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 11:48:06.140 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:48:06.146 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:48:14.736 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 27792 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:48:14.740 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:48:14.743 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:48:17.973 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:48:17.979 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:48:18.026 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 33 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:48:18.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:48:18.037 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:48:18.054 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:48:18.071 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:48:18.073 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:48:18.110 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-09 11:48:18.147 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:48:18.149 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:48:18.190 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-09 11:48:19.892 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:48:19.909 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:48:19.909 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:48:19.909 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:48:20.229 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:48:20.230 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5378 ms
2025-06-09 11:48:20.381 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:48:20.850 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:48:22.574 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:48:22.703 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:48:22.980 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:48:23.242 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:48:23.894 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:48:23.917 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:48:25.520 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:48:28.754 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:48:28.781 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:48:29.451 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.883 seconds (JVM running for 18.509)
2025-06-09 11:48:34.151 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:48:34.152 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:48:34.157 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 11:48:34.573 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 11:48:34.614 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:48:34.615 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:48:34.624 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 11:48:36.022 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 11:48:36.023 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 11:48:38.588 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 21 个部门
2025-06-09 11:48:38.691 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 11:55:15.629 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:55:15.638 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:55:53.301 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:55:53.345 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 16864 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:55:53.346 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:55:56.276 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:55:56.281 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:55:56.324 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:55:56.332 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:55:56.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:55:56.347 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:55:56.365 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:55:56.367 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:55:56.398 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-09 11:55:56.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:55:56.429 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:55:56.466 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Redis repository interfaces.
2025-06-09 11:56:00.032 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:56:00.087 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:56:00.089 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:56:00.090 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:56:00.585 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:56:00.586 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7117 ms
2025-06-09 11:56:00.873 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:56:01.180 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:56:02.386 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:56:02.509 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:56:02.817 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:56:03.198 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:56:03.879 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:56:04.068 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:56:06.205 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:56:09.990 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:56:10.012 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:56:10.953 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.041 seconds (JVM running for 23.267)
2025-06-09 11:56:46.538 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:56:46.538 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:56:46.541 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-09 11:56:46.840 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 11:56:46.888 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:56:46.889 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:56:46.898 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 11:56:48.288 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 11:56:48.289 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 11:56:53.868 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 21 个部门
2025-06-09 11:56:54.027 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 11:57:12.143 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:57:12.149 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 11:57:19.077 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 30548 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 11:57:19.079 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 11:57:19.087 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 11:57:22.354 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:57:22.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:57:22.407 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 11:57:22.414 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:57:22.415 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 11:57:22.434 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 11:57:22.450 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:57:22.452 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 11:57:22.481 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-09 11:57:22.522 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 11:57:22.525 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 11:57:22.568 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-09 11:57:24.444 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 11:57:24.468 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 11:57:24.469 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 11:57:24.470 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 11:57:24.845 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 11:57:24.845 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5623 ms
2025-06-09 11:57:25.008 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 11:57:25.291 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 11:57:26.854 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 11:57:26.995 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 11:57:27.708 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 11:57:28.098 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 11:57:28.746 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 11:57:28.767 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 11:57:30.587 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 11:57:39.464 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 11:57:39.488 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 11:57:40.418 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 22.68 seconds (JVM running for 25.859)
2025-06-09 11:58:20.096 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 11:58:20.097 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 11:58:20.103 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 11:58:20.473 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 11:58:20.519 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:58:20.519 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 11:58:20.527 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 11:58:24.732 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 11:58:24.733 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 11:58:26.591 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 21 个部门
2025-06-09 11:58:26.674 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:01:28.621 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 12:01:28.632 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 12:01:41.788 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 12:01:41.841 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 12260 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 12:01:41.846 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 12:01:44.239 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 12:01:44.244 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 12:01:44.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 12:01:44.285 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 12:01:44.287 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 12:01:44.299 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 12:01:44.313 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 12:01:44.314 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 12:01:44.334 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-09 12:01:44.359 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 12:01:44.361 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 12:01:44.390 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-09 12:01:46.413 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 12:01:46.431 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 12:01:46.432 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 12:01:46.432 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 12:01:46.761 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 12:01:46.762 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4790 ms
2025-06-09 12:01:46.920 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 12:01:47.157 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 12:01:48.242 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 12:01:48.393 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 12:01:48.693 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 12:01:48.951 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 12:01:49.429 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 12:01:49.454 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 12:01:51.308 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 12:01:54.259 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 12:01:54.292 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 12:01:55.434 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.772 seconds (JVM running for 17.33)
2025-06-09 12:02:03.496 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 12:02:03.496 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 12:02:03.501 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 12:02:03.889 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-01 00:00:00 - 2024-01-10 23:59:59
2025-06-09 12:02:03.928 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 12:02:03.928 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-01T00:00 - 2024-01-10T23:59:59
2025-06-09 12:02:03.937 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-01%2000:00:00&endDate=2024-01-10%2023:59:59
2025-06-09 12:02:04.851 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 21 个部门
2025-06-09 12:02:04.853 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 21 个部门
2025-06-09 12:02:04.853 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X86100000, orgName=原料二车间
2025-06-09 12:02:05.164 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924734774218752, orgName=原料二车间
2025-06-09 12:02:05.165 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X86100001, orgName=除尘班
2025-06-09 12:02:05.257 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924736087035904, orgName=除尘班
2025-06-09 12:02:05.258 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X86100002, orgName=破碎班
2025-06-09 12:02:05.323 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924736472911872, orgName=破碎班
2025-06-09 12:02:05.324 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X86100003, orgName=原料供料班
2025-06-09 12:02:05.428 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924736753930240, orgName=原料供料班
2025-06-09 12:02:05.428 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X86100004, orgName=原料中控班
2025-06-09 12:02:05.550 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924737190137856, orgName=原料中控班
2025-06-09 12:02:05.551 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100401, orgName=点维作业区
2025-06-09 12:02:05.674 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924737706037248, orgName=点维作业区
2025-06-09 12:02:05.674 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100402, orgName=电气作业区
2025-06-09 12:02:05.873 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924738217742336, orgName=电气作业区
2025-06-09 12:02:05.874 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100100, orgName=检修一车间
2025-06-09 12:02:05.950 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924739064991744, orgName=检修一车间
2025-06-09 12:02:05.951 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100200, orgName=检修二车间
2025-06-09 12:02:06.030 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924739379564544, orgName=检修二车间
2025-06-09 12:02:06.032 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100300, orgName=检修三车间
2025-06-09 12:02:06.119 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924739723497472, orgName=检修三车间
2025-06-09 12:02:06.119 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100400, orgName=检修四车间
2025-06-09 12:02:06.211 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924740096790528, orgName=检修四车间
2025-06-09 12:02:06.212 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100101, orgName=点维一作业区
2025-06-09 12:02:06.283 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924740474277888, orgName=点维一作业区
2025-06-09 12:02:06.284 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100102, orgName=点维二作业区
2025-06-09 12:02:06.364 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924740776267776, orgName=点维二作业区
2025-06-09 12:02:06.365 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100103, orgName=电气一作业区
2025-06-09 12:02:06.452 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924741120200704, orgName=电气一作业区
2025-06-09 12:02:06.453 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100104, orgName=电气二作业区
2025-06-09 12:02:06.578 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924741485105152, orgName=电气二作业区
2025-06-09 12:02:06.584 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100201, orgName=点维一作业区
2025-06-09 12:02:06.660 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924742034558976, orgName=点维一作业区
2025-06-09 12:02:06.660 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100202, orgName=点维二作业区
2025-06-09 12:02:06.749 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924742353326080, orgName=点维二作业区
2025-06-09 12:02:06.749 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100203, orgName=电气一作业区
2025-06-09 12:02:06.850 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924742730813440, orgName=电气一作业区
2025-06-09 12:02:06.851 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100204, orgName=电气二作业区
2025-06-09 12:02:06.981 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924743154438144, orgName=电气二作业区
2025-06-09 12:02:06.981 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100301, orgName=点维作业区
2025-06-09 12:02:07.040 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924743699697664, orgName=点维作业区
2025-06-09 12:02:07.040 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100302, orgName=电气作业区
2025-06-09 12:02:07.120 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931924743947161600, orgName=电气作业区
2025-06-09 12:02:07.120 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=21 条, 子表(t_department_child)=0 条
2025-06-09 12:02:07.120 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 21 个部门
2025-06-09 12:02:07.208 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:02:35.267 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-11 00:00:00 - 2024-01-20 23:59:59
2025-06-09 12:02:35.268 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-11T00:00 - 2024-01-20T23:59:59
2025-06-09 12:02:35.269 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-11T00:00 - 2024-01-20T23:59:59
2025-06-09 12:02:35.269 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-11%2000:00:00&endDate=2024-01-20%2023:59:59
2025-06-09 12:02:35.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 0 个部门
2025-06-09 12:02:35.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 0 个部门
2025-06-09 12:02:35.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=0 条, 子表(t_department_child)=0 条
2025-06-09 12:02:35.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 0 个部门
2025-06-09 12:02:35.569 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:03:00.031 [http-nio-8285-exec-9] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-01-21 00:00:00 - 2024-01-31 23:59:59
2025-06-09 12:03:00.032 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-01-21T00:00 - 2024-01-31T23:59:59
2025-06-09 12:03:00.033 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-01-21T00:00 - 2024-01-31T23:59:59
2025-06-09 12:03:00.033 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-01-21%2000:00:00&endDate=2024-01-31%2023:59:59
2025-06-09 12:03:00.324 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 0 个部门
2025-06-09 12:03:00.324 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 0 个部门
2025-06-09 12:03:00.324 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=0 条, 子表(t_department_child)=0 条
2025-06-09 12:03:00.324 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 0 个部门
2025-06-09 12:03:00.325 [http-nio-8285-exec-9] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:03:31.748 [http-nio-8285-exec-8] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-02-01 00:00:00 - 2024-03-31 23:59:59
2025-06-09 12:03:31.749 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-02-01T00:00 - 2024-03-31T23:59:59
2025-06-09 12:03:31.750 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-02-01T00:00 - 2024-03-31T23:59:59
2025-06-09 12:03:31.750 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-02-01%2000:00:00&endDate=2024-03-31%2023:59:59
2025-06-09 12:03:32.255 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 9 个部门
2025-06-09 12:03:32.256 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 9 个部门
2025-06-09 12:03:32.256 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X06020000, orgName=资产室
2025-06-09 12:03:32.320 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925101368971264, orgName=资产室
2025-06-09 12:03:32.321 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X06080000, orgName=投资财务室
2025-06-09 12:03:32.380 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925101641601024, orgName=投资财务室
2025-06-09 12:03:32.381 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X83000101, orgName=检化验中心
2025-06-09 12:03:32.440 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925101893259264, orgName=检化验中心
2025-06-09 12:03:32.440 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X83000400, orgName=综合管理部
2025-06-09 12:03:32.508 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925102140723200, orgName=综合管理部
2025-06-09 12:03:32.509 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XA1000000, orgName=南京金智工程技术有限公司
2025-06-09 12:03:32.606 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925102430130176, orgName=南京金智工程技术有限公司
2025-06-09 12:03:32.607 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57140500, orgName=新材料投资室
2025-06-09 12:03:32.675 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925102845366272, orgName=新材料投资室
2025-06-09 12:03:32.675 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57140200, orgName=智能制造投资二室
2025-06-09 12:03:32.749 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925103130578944, orgName=智能制造投资二室
2025-06-09 12:03:32.749 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57140600, orgName=TMT投资室
2025-06-09 12:03:32.805 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925103436763136, orgName=TMT投资室
2025-06-09 12:03:32.806 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57140300, orgName=智能制造投资三室
2025-06-09 12:03:32.865 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925103675838464, orgName=智能制造投资三室
2025-06-09 12:03:32.865 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=9 条, 子表(t_department_child)=0 条
2025-06-09 12:03:32.865 [http-nio-8285-exec-8] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 9 个部门
2025-06-09 12:03:32.928 [http-nio-8285-exec-8] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:04:17.224 [http-nio-8285-exec-10] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-04-01 00:00:00 - 2024-10-31 23:59:59
2025-06-09 12:04:17.226 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-04-01T00:00 - 2024-10-31T23:59:59
2025-06-09 12:04:17.227 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-04-01T00:00 - 2024-10-31T23:59:59
2025-06-09 12:04:17.227 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-04-01%2000:00:00&endDate=2024-10-31%2023:59:59
2025-06-09 12:04:19.622 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 126 个部门
2025-06-09 12:04:19.623 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 126 个部门
2025-06-09 12:04:19.624 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090003, orgName=钳工班
2025-06-09 12:04:19.688 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925300044763136, orgName=钳工班
2025-06-09 12:04:19.689 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52060007, orgName=耐火五库班
2025-06-09 12:04:19.765 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925300317392896, orgName=耐火五库班
2025-06-09 12:04:19.766 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52060011, orgName=炼钢库班
2025-06-09 12:04:19.890 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925300640354304, orgName=炼钢库班
2025-06-09 12:04:19.891 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68010000, orgName=余材销售部
2025-06-09 12:04:19.983 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925301164642304, orgName=余材销售部
2025-06-09 12:04:19.983 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68020000, orgName=外购销售部
2025-06-09 12:04:20.059 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925301550518272, orgName=外购销售部
2025-06-09 12:04:20.059 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68030000, orgName=代理销售部
2025-06-09 12:04:20.166 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925301869285376, orgName=代理销售部
2025-06-09 12:04:20.166 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68040000, orgName=国外销售部
2025-06-09 12:04:20.258 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925302318075904, orgName=国外销售部
2025-06-09 12:04:20.259 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68050000, orgName=加工配送部
2025-06-09 12:04:20.327 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925302708146176, orgName=加工配送部
2025-06-09 12:04:20.327 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68060000, orgName=综合商务部
2025-06-09 12:04:20.415 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925302993358848, orgName=综合商务部
2025-06-09 12:04:20.415 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64000000, orgName=带钢厂
2025-06-09 12:04:20.538 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925303362457600, orgName=带钢厂
2025-06-09 12:04:20.538 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X71000000, orgName=印尼钢铁项目指挥部
2025-06-09 12:04:20.831 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925303878356992, orgName=印尼钢铁项目指挥部
2025-06-09 12:04:20.832 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72000000, orgName=江苏金凯节能环保投资控股有限公司
2025-06-09 12:04:20.951 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925305111482368, orgName=江苏金凯节能环保投资控股有限公司
2025-06-09 12:04:20.951 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68000000, orgName=江苏金贸钢宝电子商务有限公司2
2025-06-09 12:04:21.064 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925305610604544, orgName=江苏金贸钢宝电子商务有限公司2
2025-06-09 12:04:21.065 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X68110000, orgName=仓储部
2025-06-09 12:04:22.390 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925306088755200, orgName=仓储部
2025-06-09 12:04:22.390 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X71010000, orgName=综合协调部
2025-06-09 12:04:23.033 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925311646208000, orgName=综合协调部
2025-06-09 12:04:23.033 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X71020000, orgName=海外设备修造部
2025-06-09 12:04:23.489 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925314347339776, orgName=海外设备修造部
2025-06-09 12:04:23.489 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X71030000, orgName=棉兰工程项目部
2025-06-09 12:04:23.949 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925316255748096, orgName=棉兰工程项目部
2025-06-09 12:04:23.950 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72010000, orgName=南京金瀚环保科技有限公司
2025-06-09 12:04:24.140 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925318189322240, orgName=南京金瀚环保科技有限公司
2025-06-09 12:04:24.141 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72020000, orgName=能源投资部
2025-06-09 12:04:24.228 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925318990434304, orgName=能源投资部
2025-06-09 12:04:24.228 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72030000, orgName=水气投资部
2025-06-09 12:04:24.377 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925319355338752, orgName=水气投资部
2025-06-09 12:04:24.378 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72040000, orgName=固废投资部
2025-06-09 12:04:24.471 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925319984484352, orgName=固废投资部
2025-06-09 12:04:24.472 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72050000, orgName=环境投资室
2025-06-09 12:04:24.569 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925320378748928, orgName=环境投资室
2025-06-09 12:04:24.571 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72060000, orgName=新材料投资室
2025-06-09 12:04:24.682 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925320793985024, orgName=新材料投资室
2025-06-09 12:04:24.682 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72070000, orgName=智能制造投资室
2025-06-09 12:04:24.949 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925321259552768, orgName=智能制造投资室
2025-06-09 12:04:24.949 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090001, orgName=水电班
2025-06-09 12:04:25.130 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925322379431936, orgName=水电班
2025-06-09 12:04:25.130 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04210000, orgName=项目管理室
2025-06-09 12:04:25.277 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925323138600960, orgName=项目管理室
2025-06-09 12:04:25.277 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X36020000, orgName=物流运输部
2025-06-09 12:04:25.391 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925323755163648, orgName=物流运输部
2025-06-09 12:04:25.391 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X36030000, orgName=出口部
2025-06-09 12:04:25.572 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925324233314304, orgName=出口部
2025-06-09 12:04:25.572 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52040000, orgName=铁路港口室
2025-06-09 12:04:25.745 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925324992483328, orgName=铁路港口室
2025-06-09 12:04:25.745 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210001, orgName=验收班
2025-06-09 12:04:25.935 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925325722292224, orgName=验收班
2025-06-09 12:04:25.935 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63240000, orgName=生产准备车间(原)
2025-06-09 12:04:26.021 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925326515015680, orgName=生产准备车间(原)
2025-06-09 12:04:26.022 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X56010000, orgName=管理室
2025-06-09 12:04:26.097 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925326879920128, orgName=管理室
2025-06-09 12:04:26.097 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X21010000, orgName=物资招标部
2025-06-09 12:04:26.206 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925327194492928, orgName=物资招标部
2025-06-09 12:04:26.207 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X21020000, orgName=设备招标部
2025-06-09 12:04:26.297 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925327655866368, orgName=设备招标部
2025-06-09 12:04:26.297 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X21030000, orgName=工程招标部
2025-06-09 12:04:26.415 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925328033353728, orgName=工程招标部
2025-06-09 12:04:26.416 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X21050000, orgName=市场拓展部
2025-06-09 12:04:26.492 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925328532475904, orgName=市场拓展部
2025-06-09 12:04:26.492 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08040000, orgName=管理审计室
2025-06-09 12:04:26.563 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925328851243008, orgName=管理审计室
2025-06-09 12:04:26.563 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08050000, orgName=工程审计室
2025-06-09 12:04:26.645 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925329149038592, orgName=工程审计室
2025-06-09 12:04:26.645 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X02080900, orgName=三金房产
2025-06-09 12:04:26.721 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925329492971520, orgName=三金房产
2025-06-09 12:04:26.721 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050006, orgName=电炉乙班
2025-06-09 12:04:26.807 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925329811738624, orgName=电炉乙班
2025-06-09 12:04:26.807 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050007, orgName=电炉丙班
2025-06-09 12:04:26.908 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925330172448768, orgName=电炉丙班
2025-06-09 12:04:26.908 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050008, orgName=电炉丁班
2025-06-09 12:04:27.084 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925330596073472, orgName=电炉丁班
2025-06-09 12:04:27.084 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050012, orgName=精炼炉丁班
2025-06-09 12:04:27.175 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925331334270976, orgName=精炼炉丁班
2025-06-09 12:04:27.175 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060007, orgName=台上甲班
2025-06-09 12:04:27.412 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925331715952640, orgName=台上甲班
2025-06-09 12:04:27.413 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060008, orgName=台上乙班
2025-06-09 12:04:27.644 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925332714196992, orgName=台上乙班
2025-06-09 12:04:27.644 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060009, orgName=台上丙班
2025-06-09 12:04:28.038 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925333683081216, orgName=台上丙班
2025-06-09 12:04:28.038 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060011, orgName=大方坯台上甲班
2025-06-09 12:04:28.407 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925335335636992, orgName=大方坯台上甲班
2025-06-09 12:04:28.407 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060013, orgName=大方坯台上丙班
2025-06-09 12:04:28.492 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925336883335168, orgName=大方坯台上丙班
2025-06-09 12:04:28.492 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060014, orgName=大方坯台上丁班
2025-06-09 12:04:28.574 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925337239851008, orgName=大方坯台上丁班
2025-06-09 12:04:28.575 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060016, orgName=台下乙班
2025-06-09 12:04:28.649 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925337587978240, orgName=台下乙班
2025-06-09 12:04:28.650 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060017, orgName=台下丙班
2025-06-09 12:04:28.728 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925337902551040, orgName=台下丙班
2025-06-09 12:04:28.729 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070004, orgName=行车甲班
2025-06-09 12:04:28.839 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925338233901056, orgName=行车甲班
2025-06-09 12:04:28.840 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070005, orgName=行车乙班
2025-06-09 12:04:28.973 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925338699468800, orgName=行车乙班
2025-06-09 12:04:28.973 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070006, orgName=行车丙班
2025-06-09 12:04:29.076 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925339257311232, orgName=行车丙班
2025-06-09 12:04:29.076 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64060000, orgName=综合管理室
2025-06-09 12:04:29.164 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925339689324544, orgName=综合管理室
2025-06-09 12:04:29.165 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64070000, orgName=安全环保室
2025-06-09 12:04:29.254 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925340062617600, orgName=安全环保室
2025-06-09 12:04:29.254 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080000, orgName=生产计划室
2025-06-09 12:04:29.325 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925340435910656, orgName=生产计划室
2025-06-09 12:04:29.325 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080100, orgName=准备车间
2025-06-09 12:04:29.405 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925340733706240, orgName=准备车间
2025-06-09 12:04:29.405 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080101, orgName=大车班
2025-06-09 12:04:29.501 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925341069250560, orgName=大车班
2025-06-09 12:04:29.502 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080102, orgName=导卫班
2025-06-09 12:04:29.574 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925341476098048, orgName=导卫班
2025-06-09 12:04:29.574 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080200, orgName=甲作业区
2025-06-09 12:04:29.648 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925341778087936, orgName=甲作业区
2025-06-09 12:04:29.649 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080201, orgName=加热班
2025-06-09 12:04:29.759 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925342092660736, orgName=加热班
2025-06-09 12:04:29.759 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080202, orgName=轧机班
2025-06-09 12:04:30.017 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925342554034176, orgName=轧机班
2025-06-09 12:04:30.018 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080204, orgName=成品班
2025-06-09 12:04:30.112 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925343644553216, orgName=成品班
2025-06-09 12:04:30.112 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080203, orgName=行车班
2025-06-09 12:04:30.318 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925344034623488, orgName=行车班
2025-06-09 12:04:30.318 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080300, orgName=乙作业区
2025-06-09 12:04:30.403 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925344898650112, orgName=乙作业区
2025-06-09 12:04:30.404 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080301, orgName=加热班
2025-06-09 12:04:30.595 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925345259360256, orgName=加热班
2025-06-09 12:04:30.595 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080302, orgName=轧机班
2025-06-09 12:04:30.894 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925346060472320, orgName=轧机班
2025-06-09 12:04:30.894 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080304, orgName=成品班
2025-06-09 12:04:31.150 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925347314569216, orgName=成品班
2025-06-09 12:04:31.151 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080303, orgName=行车班
2025-06-09 12:04:31.289 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925348392505344, orgName=行车班
2025-06-09 12:04:31.289 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080400, orgName=丙作业区
2025-06-09 12:04:31.472 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925348971319296, orgName=丙作业区
2025-06-09 12:04:31.472 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080401, orgName=加热班
2025-06-09 12:04:31.613 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925349738876928, orgName=加热班
2025-06-09 12:04:31.613 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080402, orgName=轧机班
2025-06-09 12:04:31.731 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925350330273792, orgName=轧机班
2025-06-09 12:04:31.731 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080404, orgName=成品班
2025-06-09 12:04:31.816 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925350825201664, orgName=成品班
2025-06-09 12:04:31.816 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080403, orgName=行车班
2025-06-09 12:04:31.898 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925351185911808, orgName=行车班
2025-06-09 12:04:31.899 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080500, orgName=丁作业区
2025-06-09 12:04:32.011 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925351529844736, orgName=丁作业区
2025-06-09 12:04:32.011 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080501, orgName=加热班
2025-06-09 12:04:32.089 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925351999606784, orgName=加热班
2025-06-09 12:04:32.089 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080502, orgName=轧机班
2025-06-09 12:04:32.175 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925352326762496, orgName=轧机班
2025-06-09 12:04:32.175 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080504, orgName=成品班
2025-06-09 12:04:32.287 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925352687472640, orgName=成品班
2025-06-09 12:04:32.287 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080503, orgName=行车班
2025-06-09 12:04:32.390 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925353157234688, orgName=行车班
2025-06-09 12:04:32.390 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090000, orgName=设备管理室
2025-06-09 12:04:32.481 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925353589248000, orgName=设备管理室
2025-06-09 12:04:32.482 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090100, orgName=电修车间
2025-06-09 12:04:32.565 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925353975123968, orgName=电修车间
2025-06-09 12:04:32.565 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090102, orgName=交流电工班
2025-06-09 12:04:32.700 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925354323251200, orgName=交流电工班
2025-06-09 12:04:32.701 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090103, orgName=直流电工班
2025-06-09 12:04:32.776 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925354893676544, orgName=直流电工班
2025-06-09 12:04:32.776 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090200, orgName=机修车间
2025-06-09 12:04:32.848 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925355208249344, orgName=机修车间
2025-06-09 12:04:32.848 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090201, orgName=钳一班
2025-06-09 12:04:32.918 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925355510239232, orgName=钳一班
2025-06-09 12:04:32.918 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090202, orgName=钳二班
2025-06-09 12:04:33.251 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925355803840512, orgName=钳二班
2025-06-09 12:04:33.251 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090203, orgName=钳工值班班
2025-06-09 12:04:33.510 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925357200543744, orgName=钳工值班班
2025-06-09 12:04:33.510 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070007, orgName=行车丁班
2025-06-09 12:04:33.728 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925358286868480, orgName=行车丁班
2025-06-09 12:04:33.728 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070008, orgName=配料甲班
2025-06-09 12:04:34.160 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925359201226752, orgName=配料甲班
2025-06-09 12:04:34.160 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070009, orgName=配料乙班
2025-06-09 12:04:34.594 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925361013166080, orgName=配料乙班
2025-06-09 12:04:34.594 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070010, orgName=配料丙班
2025-06-09 12:04:34.905 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925362833494016, orgName=配料丙班
2025-06-09 12:04:34.905 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070011, orgName=配料丁班
2025-06-09 12:04:34.998 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925364137922560, orgName=配料丁班
2025-06-09 12:04:34.998 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04220000, orgName=数字化战略变革管理室
2025-06-09 12:04:35.072 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925364527992832, orgName=数字化战略变革管理室
2025-06-09 12:04:35.072 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08060000, orgName=数智审计室
2025-06-09 12:04:35.170 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925364838371328, orgName=数智审计室
2025-06-09 12:04:35.171 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04230000, orgName=数据服务管理室
2025-06-09 12:04:35.248 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925365253607424, orgName=数据服务管理室
2025-06-09 12:04:35.248 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04240000, orgName=数字技术研究所
2025-06-09 12:04:35.342 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925365576568832, orgName=数字技术研究所
2025-06-09 12:04:35.342 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63300007, orgName=废钢班
2025-06-09 12:04:35.452 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925365970833408, orgName=废钢班
2025-06-09 12:04:35.452 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X36220000, orgName=综合服务部
2025-06-09 12:04:35.549 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925366432206848, orgName=综合服务部
2025-06-09 12:04:35.553 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100702, orgName=电工二班
2025-06-09 12:04:35.646 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925366860025856, orgName=电工二班
2025-06-09 12:04:35.646 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100602, orgName=电工班
2025-06-09 12:04:35.731 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925367245901824, orgName=电工班
2025-06-09 12:04:35.731 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X29000100, orgName=信访审理室
2025-06-09 12:04:35.806 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925367602417664, orgName=信访审理室
2025-06-09 12:04:35.806 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100500, orgName=电修一车间
2025-06-09 12:04:35.897 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925367916990464, orgName=电修一车间
2025-06-09 12:04:35.897 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100600, orgName=电修二车间
2025-06-09 12:04:35.974 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925368298672128, orgName=电修二车间
2025-06-09 12:04:35.974 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100700, orgName=电修三车间
2025-06-09 12:04:36.069 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925368621633536, orgName=电修三车间
2025-06-09 12:04:36.069 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100800, orgName=检修车间
2025-06-09 12:04:36.138 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925369020092416, orgName=检修车间
2025-06-09 12:04:36.138 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100501, orgName=自动化班
2025-06-09 12:04:36.214 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925369313693696, orgName=自动化班
2025-06-09 12:04:36.215 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100502, orgName=电工班
2025-06-09 12:04:36.338 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925369632460800, orgName=电工班
2025-06-09 12:04:36.338 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100601, orgName=自动化班
2025-06-09 12:04:36.472 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925370148360192, orgName=自动化班
2025-06-09 12:04:36.472 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100701, orgName=电工一班
2025-06-09 12:04:36.798 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925370710396928, orgName=电工一班
2025-06-09 12:04:36.798 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XB8000000, orgName=团委
2025-06-09 12:04:36.889 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925372077740032, orgName=团委
2025-06-09 12:04:36.890 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X79120000, orgName=公司领导
2025-06-09 12:04:37.091 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925372463616000, orgName=公司领导
2025-06-09 12:04:37.091 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X79000300, orgName=江苏南钢鑫联鑫科技有限公司
2025-06-09 12:04:37.586 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925373306671104, orgName=江苏南钢鑫联鑫科技有限公司
2025-06-09 12:04:37.587 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X79000301, orgName=公司领导
2025-06-09 12:04:37.964 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925375387045888, orgName=公司领导
2025-06-09 12:04:37.964 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X79000302, orgName=业务部
2025-06-09 12:04:38.242 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925376968298496, orgName=业务部
2025-06-09 12:04:38.242 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X79000303, orgName=综合部
2025-06-09 12:04:38.325 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925378134315008, orgName=综合部
2025-06-09 12:04:38.325 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200000, orgName=数字应用研究院.
2025-06-09 12:04:38.448 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925378482442240, orgName=数字应用研究院.
2025-06-09 12:04:38.448 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200100, orgName=项目管理室
2025-06-09 12:04:38.537 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925378998341632, orgName=项目管理室
2025-06-09 12:04:38.537 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200200, orgName=数字技术研究所
2025-06-09 12:04:38.635 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925379371634688, orgName=数字技术研究所
2025-06-09 12:04:38.636 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200300, orgName=数据服务管理室
2025-06-09 12:04:38.705 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925379786870784, orgName=数据服务管理室
2025-06-09 12:04:38.705 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200400, orgName=数字化战略变革管理室
2025-06-09 12:04:38.771 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925380076277760, orgName=数字化战略变革管理室
2025-06-09 12:04:38.771 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210100, orgName=电炉精炼车间
2025-06-09 12:04:38.847 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925380353101824, orgName=电炉精炼车间
2025-06-09 12:04:38.847 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X21000100, orgName=技术研发部
2025-06-09 12:04:38.920 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925380671868928, orgName=技术研发部
2025-06-09 12:04:38.920 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100000, orgName=检修厂
2025-06-09 12:04:39.030 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925380978053120, orgName=检修厂
2025-06-09 12:04:39.030 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X83000700, orgName=总经办
2025-06-09 12:04:39.109 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925381439426560, orgName=总经办
2025-06-09 12:04:39.109 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X83000800, orgName=化验室
2025-06-09 12:04:39.170 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931925381770776576, orgName=化验室
2025-06-09 12:04:39.170 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=126 条, 子表(t_department_child)=0 条
2025-06-09 12:04:39.170 [http-nio-8285-exec-10] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 126 个部门
2025-06-09 12:04:39.251 [http-nio-8285-exec-10] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 12:05:43.559 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 12:05:43.564 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 14:22:25.749 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 14:22:25.759 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 6180 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 14:22:25.768 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 14:22:32.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 14:22:32.533 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 14:22:32.670 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 110 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 14:22:32.690 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 14:22:32.695 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 14:22:32.736 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 14:22:32.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 14:22:32.782 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 14:22:32.850 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 JPA repository interfaces.
2025-06-09 14:22:32.946 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 14:22:32.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 14:22:33.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Redis repository interfaces.
2025-06-09 14:22:37.874 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 14:22:37.907 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 14:22:37.909 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 14:22:37.911 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 14:22:38.749 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 14:22:38.749 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 12545 ms
2025-06-09 14:22:39.162 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 14:22:39.917 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 14:22:41.935 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 14:22:42.417 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 14:22:43.429 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 14:22:44.654 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 14:22:46.484 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 14:22:46.631 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 14:22:51.129 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 14:23:02.592 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 14:23:02.664 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 14:23:05.113 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 42.647 seconds (JVM running for 81.479)
2025-06-09 14:24:32.045 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 14:24:32.046 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 14:24:32.050 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-09 14:24:32.501 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-11-01 00:00:00 - 2024-11-10 23:59:59
2025-06-09 14:24:32.984 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-11-01T00:00 - 2024-11-10T23:59:59
2025-06-09 14:24:32.985 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-11-01T00:00 - 2024-11-10T23:59:59
2025-06-09 14:24:32.995 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-11-01%2000:00:00&endDate=2024-11-10%2023:59:59
2025-06-09 14:24:36.843 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 1 个部门
2025-06-09 14:24:36.844 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 1 个部门
2025-06-09 14:24:36.853 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57120403, orgName=万盛赋能组
2025-06-09 14:24:37.259 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960604470415360, orgName=万盛赋能组
2025-06-09 14:24:37.261 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=1 条, 子表(t_department_child)=0 条
2025-06-09 14:24:37.262 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 1 个部门
2025-06-09 14:24:37.335 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 14:24:48.660 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-11-01 00:00:00 - 2024-11-30 23:59:59
2025-06-09 14:24:48.664 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-11-01T00:00 - 2024-11-30T23:59:59
2025-06-09 14:24:48.665 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-11-01T00:00 - 2024-11-30T23:59:59
2025-06-09 14:24:48.669 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-11-01%2000:00:00&endDate=2024-11-30%2023:59:59
2025-06-09 14:24:49.584 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 26 个部门
2025-06-09 14:24:49.586 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 26 个部门
2025-06-09 14:24:49.586 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060000, orgName=电炉连铸车间
2025-06-09 14:24:49.754 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960657868099584, orgName=电炉连铸车间
2025-06-09 14:24:49.755 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060006, orgName=电炉检验班
2025-06-09 14:24:49.842 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960658581131264, orgName=电炉检验班
2025-06-09 14:24:49.844 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070000, orgName=电炉运行车间
2025-06-09 14:24:49.919 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960658954424320, orgName=电炉运行车间
2025-06-09 14:24:49.919 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090002, orgName=电工班
2025-06-09 14:24:49.997 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960659268997120, orgName=电工班
2025-06-09 14:24:49.998 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42000000, orgName=第三炼钢厂
2025-06-09 14:24:50.075 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960659596152832, orgName=第三炼钢厂
2025-06-09 14:24:50.079 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050000, orgName=电炉炼钢车间
2025-06-09 14:24:50.153 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960659940085760, orgName=电炉炼钢车间
2025-06-09 14:24:50.154 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050001, orgName=钢包班
2025-06-09 14:24:50.220 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960660254658560, orgName=钢包班
2025-06-09 14:24:50.221 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090000, orgName=电炉检修车间
2025-06-09 14:24:50.290 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960660531482624, orgName=电炉检修车间
2025-06-09 14:24:50.293 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210000, orgName=废钢车间
2025-06-09 14:24:50.373 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960660833472512, orgName=废钢车间
2025-06-09 14:24:50.375 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210002, orgName=行车班
2025-06-09 14:24:50.452 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960661185794048, orgName=行车班
2025-06-09 14:24:50.453 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210003, orgName=综合班
2025-06-09 14:24:50.524 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960661504561152, orgName=综合班
2025-06-09 14:24:50.525 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050009, orgName=精炼炉甲班
2025-06-09 14:24:50.592 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960661810745344, orgName=精炼炉甲班
2025-06-09 14:24:50.593 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050010, orgName=精炼炉乙班
2025-06-09 14:24:50.666 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960662091763712, orgName=精炼炉乙班
2025-06-09 14:24:50.667 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050011, orgName=精炼炉丙班
2025-06-09 14:24:50.759 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960662402142208, orgName=精炼炉丙班
2025-06-09 14:24:50.760 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050013, orgName=辅助班
2025-06-09 14:24:50.838 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960662792212480, orgName=辅助班
2025-06-09 14:24:50.838 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060010, orgName=台上丁班
2025-06-09 14:24:50.899 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960663123562496, orgName=台上丁班
2025-06-09 14:24:50.900 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060012, orgName=大方坯台上乙班
2025-06-09 14:24:50.976 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960663379415040, orgName=大方坯台上乙班
2025-06-09 14:24:50.978 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060015, orgName=台下甲班
2025-06-09 14:24:51.055 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960663706570752, orgName=台下甲班
2025-06-09 14:24:51.056 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060018, orgName=台下丁班
2025-06-09 14:24:51.121 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960664033726464, orgName=台下丁班
2025-06-09 14:24:51.121 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42100000, orgName=综合管理室
2025-06-09 14:24:51.204 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960664306356224, orgName=综合管理室
2025-06-09 14:24:51.204 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42110000, orgName=生产技术室
2025-06-09 14:24:51.301 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960664658677760, orgName=生产技术室
2025-06-09 14:24:51.301 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42120000, orgName=安全环保室
2025-06-09 14:24:51.380 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960665065525248, orgName=安全环保室
2025-06-09 14:24:51.382 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42130000, orgName=设备管理室
2025-06-09 14:24:51.456 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960665401069568, orgName=设备管理室
2025-06-09 14:24:51.456 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050005, orgName=电炉甲班
2025-06-09 14:24:51.521 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960665715642368, orgName=电炉甲班
2025-06-09 14:24:51.522 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57120403, orgName=万盛赋能组
2025-06-09 14:24:51.598 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960665988272128, orgName=万盛赋能组
2025-06-09 14:24:51.602 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050014, orgName=验收班
2025-06-09 14:24:51.674 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931960666332205056, orgName=验收班
2025-06-09 14:24:51.675 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=26 条, 子表(t_department_child)=0 条
2025-06-09 14:24:51.675 [http-nio-8285-exec-1] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 26 个部门
2025-06-09 14:24:51.770 [http-nio-8285-exec-1] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 15:22:46.291 [http-nio-8285-exec-5] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步部门数据，时间范围: 2024-11-01 00:00:00 - 2024-11-30 23:59:59
2025-06-09 15:22:47.105 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-11-01T00:00 - 2024-11-30T23:59:59
2025-06-09 15:22:47.106 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-11-01T00:00 - 2024-11-30T23:59:59
2025-06-09 15:22:47.109 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-11-01%2000:00:00&endDate=2024-11-30%2023:59:59
2025-06-09 15:22:51.027 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 26 个部门
2025-06-09 15:22:51.027 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 26 个部门
2025-06-09 15:22:51.027 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060000, orgName=电炉连铸车间
2025-06-09 15:22:51.336 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975260094205952, orgName=电炉连铸车间
2025-06-09 15:22:51.337 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060000 有 2 个子记录
2025-06-09 15:22:51.469 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975260094205952, guid=76ddca83-8b17-4e93-a567-9ea6f200cfdb, sourceSystem=ERP
2025-06-09 15:22:51.548 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975260094205952, guid=9429400b-3679-4c42-8e39-27e90f763734, sourceSystem=ERP
2025-06-09 15:22:51.548 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:51.549 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060006, orgName=电炉检验班
2025-06-09 15:22:51.622 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975262279438336, orgName=电炉检验班
2025-06-09 15:22:51.622 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060006 有 2 个子记录
2025-06-09 15:22:51.698 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975262279438336, guid=3b27411e-8046-4e27-8834-aa1eed9e7102, sourceSystem=ERP
2025-06-09 15:22:51.782 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975262279438336, guid=47e5761f-fb0c-4fd9-9975-37d99e1df789, sourceSystem=ERP
2025-06-09 15:22:51.782 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:51.782 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42070000, orgName=电炉运行车间
2025-06-09 15:22:51.878 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975263256711168, orgName=电炉运行车间
2025-06-09 15:22:51.880 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42070000 有 2 个子记录
2025-06-09 15:22:51.977 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975263256711168, guid=d8c733d0-6a3e-411b-ab79-551a04a47764, sourceSystem=ERP
2025-06-09 15:22:52.062 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975263256711168, guid=a588a607-f6c9-4668-86c4-2b8cb489d439, sourceSystem=ERP
2025-06-09 15:22:52.062 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:52.062 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090002, orgName=电工班
2025-06-09 15:22:52.138 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975264431116288, orgName=电工班
2025-06-09 15:22:52.138 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42090002 有 2 个子记录
2025-06-09 15:22:52.220 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975264431116288, guid=e1136fda-c009-4738-b804-5a29a181a4e5, sourceSystem=ERP
2025-06-09 15:22:52.290 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975264431116288, guid=d84d163c-cfcb-4329-b76b-2e73ffa18226, sourceSystem=ERP
2025-06-09 15:22:52.291 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:52.291 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42000000, orgName=第三炼钢厂
2025-06-09 15:22:52.371 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975265391611904, orgName=第三炼钢厂
2025-06-09 15:22:52.372 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42000000 有 2 个子记录
2025-06-09 15:22:52.455 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975265391611904, guid=d6a66ca5-62ea-4a02-9368-4d8814488cc5, sourceSystem=ERP
2025-06-09 15:22:52.537 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975265391611904, guid=b883e052-a8cb-4a15-bcfe-153b463742b1, sourceSystem=ERP
2025-06-09 15:22:52.537 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:52.537 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050000, orgName=电炉炼钢车间
2025-06-09 15:22:52.610 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975266427604992, orgName=电炉炼钢车间
2025-06-09 15:22:52.610 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050000 有 2 个子记录
2025-06-09 15:22:52.685 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975266427604992, guid=74a73a37-c27a-4440-a21e-421b4f046e59, sourceSystem=ERP
2025-06-09 15:22:52.766 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975266427604992, guid=ae7d5aa5-169c-4955-8d87-b91319b70c61, sourceSystem=ERP
2025-06-09 15:22:52.767 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:52.768 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050001, orgName=钢包班
2025-06-09 15:22:52.838 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975267392294912, orgName=钢包班
2025-06-09 15:22:52.838 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050001 有 2 个子记录
2025-06-09 15:22:52.919 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975267392294912, guid=16f28d8a-be79-4e7f-a4fa-855be6e1d253, sourceSystem=ERP
2025-06-09 15:22:52.995 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975267392294912, guid=194618d5-81ad-432c-b982-f6a47229d7a5, sourceSystem=ERP
2025-06-09 15:22:52.995 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:52.995 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42090000, orgName=电炉检修车间
2025-06-09 15:22:53.070 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975268344401920, orgName=电炉检修车间
2025-06-09 15:22:53.070 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42090000 有 2 个子记录
2025-06-09 15:22:53.144 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975268344401920, guid=9ffb43c9-2ffa-4697-abc6-705adb00b905, sourceSystem=ERP
2025-06-09 15:22:53.224 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975268344401920, guid=10d4260e-6e8b-4114-9427-48948d9f335a, sourceSystem=ERP
2025-06-09 15:22:53.224 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:53.224 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210000, orgName=废钢车间
2025-06-09 15:22:53.302 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975269304897536, orgName=废钢车间
2025-06-09 15:22:53.302 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X63210000 有 1 个子记录
2025-06-09 15:22:53.374 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975269304897536, guid=1cc7d98d-c1bd-4b1e-8483-5a1d832acf61, sourceSystem=ERP
2025-06-09 15:22:53.375 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 15:22:53.375 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210002, orgName=行车班
2025-06-09 15:22:53.460 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975269938237440, orgName=行车班
2025-06-09 15:22:53.461 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X63210002 有 1 个子记录
2025-06-09 15:22:53.544 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975269938237440, guid=3c9323b7-8897-46ea-945d-f7314dd63979, sourceSystem=ERP
2025-06-09 15:22:53.544 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 15:22:53.545 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X63210003, orgName=综合班
2025-06-09 15:22:53.624 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975270651269120, orgName=综合班
2025-06-09 15:22:53.624 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X63210003 有 1 个子记录
2025-06-09 15:22:53.705 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975270651269120, guid=83660a5a-223c-4dda-9944-fadd10efac77, sourceSystem=ERP
2025-06-09 15:22:53.705 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 15:22:53.705 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050009, orgName=精炼炉甲班
2025-06-09 15:22:53.785 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975271322357760, orgName=精炼炉甲班
2025-06-09 15:22:53.785 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050009 有 2 个子记录
2025-06-09 15:22:53.867 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975271322357760, guid=eafdbef4-8dc4-43d6-a580-c5b62e90a87a, sourceSystem=ERP
2025-06-09 15:22:53.955 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975271322357760, guid=12e14bb9-a3aa-4c26-a3c2-86ef3b1bd51b, sourceSystem=ERP
2025-06-09 15:22:53.955 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:53.961 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050010, orgName=精炼炉乙班
2025-06-09 15:22:54.037 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975272400293888, orgName=精炼炉乙班
2025-06-09 15:22:54.037 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050010 有 2 个子记录
2025-06-09 15:22:54.123 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975272400293888, guid=2c9cc1d0-57cc-46d0-97ab-f1faff63245a, sourceSystem=ERP
2025-06-09 15:22:54.199 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975272400293888, guid=5e6708e7-3d0d-4847-9a5a-9ca466f26a1e, sourceSystem=ERP
2025-06-09 15:22:54.199 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:54.199 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050011, orgName=精炼炉丙班
2025-06-09 15:22:54.271 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975273394343936, orgName=精炼炉丙班
2025-06-09 15:22:54.272 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050011 有 2 个子记录
2025-06-09 15:22:54.363 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975273394343936, guid=08986e95-2d92-4c30-8b00-f2ced07bed1b, sourceSystem=ERP
2025-06-09 15:22:54.433 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975273394343936, guid=0c70c87d-6c8d-406b-8968-3319eb88179c, sourceSystem=ERP
2025-06-09 15:22:54.433 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:54.433 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050013, orgName=辅助班
2025-06-09 15:22:54.497 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975274375811072, orgName=辅助班
2025-06-09 15:22:54.497 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050013 有 2 个子记录
2025-06-09 15:22:54.581 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975274375811072, guid=cea8e88b-e41b-4e55-b235-3e1ae8765866, sourceSystem=ERP
2025-06-09 15:22:54.656 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975274375811072, guid=7c5c0ade-d9c8-47e5-9b00-c596050eb5c0, sourceSystem=ERP
2025-06-09 15:22:54.656 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:54.656 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060010, orgName=台上丁班
2025-06-09 15:22:54.738 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975275311140864, orgName=台上丁班
2025-06-09 15:22:54.738 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060010 有 2 个子记录
2025-06-09 15:22:54.815 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975275311140864, guid=cfb17248-24cf-447f-8185-665bb6e9c6a8, sourceSystem=ERP
2025-06-09 15:22:54.910 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975275311140864, guid=d6a793bf-68f3-4609-91ac-6fd8c900839f, sourceSystem=ERP
2025-06-09 15:22:54.910 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:54.910 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060012, orgName=大方坯台上乙班
2025-06-09 15:22:54.990 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975276376494080, orgName=大方坯台上乙班
2025-06-09 15:22:54.991 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060012 有 2 个子记录
2025-06-09 15:22:55.078 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975276376494080, guid=f662d835-fcff-415c-b36c-383520979a47, sourceSystem=ERP
2025-06-09 15:22:55.161 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975276376494080, guid=75b6fabb-939f-4752-9c07-9e17dfe8bbf4, sourceSystem=ERP
2025-06-09 15:22:55.162 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:55.162 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060015, orgName=台下甲班
2025-06-09 15:22:55.233 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975277433458688, orgName=台下甲班
2025-06-09 15:22:55.233 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060015 有 2 个子记录
2025-06-09 15:22:55.318 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975277433458688, guid=f0970a1f-05bc-4e91-a254-f35e90fbe3a6, sourceSystem=ERP
2025-06-09 15:22:55.391 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975277433458688, guid=f8c4e3f5-ea9a-48f3-bca0-ddd6681f9339, sourceSystem=ERP
2025-06-09 15:22:55.392 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:55.392 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42060018, orgName=台下丁班
2025-06-09 15:22:55.469 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975278398148608, orgName=台下丁班
2025-06-09 15:22:55.469 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42060018 有 2 个子记录
2025-06-09 15:22:55.550 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975278398148608, guid=b98053e4-45e6-4a97-a945-8e60fd75d05f, sourceSystem=ERP
2025-06-09 15:22:55.630 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975278398148608, guid=ef676d4f-efa1-4b6f-a70d-b8ea303215a7, sourceSystem=ERP
2025-06-09 15:22:55.631 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:55.631 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42100000, orgName=综合管理室
2025-06-09 15:22:55.712 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975279404781568, orgName=综合管理室
2025-06-09 15:22:55.713 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42100000 有 2 个子记录
2025-06-09 15:22:55.788 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975279404781568, guid=5adaef52-0aec-4096-8fe7-56f1aa7f9aeb, sourceSystem=ERP
2025-06-09 15:22:55.867 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975279404781568, guid=fb8b21b2-8f82-4bd1-b698-8644b45d6415, sourceSystem=ERP
2025-06-09 15:22:55.867 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:55.867 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42110000, orgName=生产技术室
2025-06-09 15:22:55.938 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975280390443008, orgName=生产技术室
2025-06-09 15:22:55.939 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42110000 有 2 个子记录
2025-06-09 15:22:56.018 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975280390443008, guid=70283fca-935c-4cc6-a6d7-be236f626e61, sourceSystem=ERP
2025-06-09 15:22:56.098 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975280390443008, guid=32ff41f3-651f-463c-bc9f-7bceb59152bd, sourceSystem=ERP
2025-06-09 15:22:56.098 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:56.098 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42120000, orgName=安全环保室
2025-06-09 15:22:56.180 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975281359327232, orgName=安全环保室
2025-06-09 15:22:56.180 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42120000 有 2 个子记录
2025-06-09 15:22:56.260 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975281359327232, guid=47be2080-9fff-4080-a386-cd208be0a2b4, sourceSystem=ERP
2025-06-09 15:22:56.349 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975281359327232, guid=3f35b170-7d37-4df2-b4e8-df9b9a034098, sourceSystem=ERP
2025-06-09 15:22:56.351 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:56.351 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42130000, orgName=设备管理室
2025-06-09 15:22:56.438 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975282424680448, orgName=设备管理室
2025-06-09 15:22:56.440 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42130000 有 2 个子记录
2025-06-09 15:22:56.514 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975282424680448, guid=3de06cd4-5e67-4aca-b453-fe42c2f186dd, sourceSystem=ERP
2025-06-09 15:22:56.592 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975282424680448, guid=739f8cb5-71ec-4cc7-a6bb-a9d175f11ec6, sourceSystem=ERP
2025-06-09 15:22:56.592 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:56.593 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050005, orgName=电炉甲班
2025-06-09 15:22:56.685 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975283435507712, orgName=电炉甲班
2025-06-09 15:22:56.685 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050005 有 2 个子记录
2025-06-09 15:22:56.761 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975283435507712, guid=7f3a82a2-c51f-4d8b-9062-c9e049133b71, sourceSystem=ERP
2025-06-09 15:22:56.838 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975283435507712, guid=1f6eed68-6fdb-4533-a525-349a40d098c9, sourceSystem=ERP
2025-06-09 15:22:56.840 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 2 条记录到 t_department_child 表
2025-06-09 15:22:56.850 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X57120403, orgName=万盛赋能组
2025-06-09 15:22:56.960 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975284546998272, orgName=万盛赋能组
2025-06-09 15:22:56.960 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X57120403 有 1 个子记录
2025-06-09 15:22:57.039 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975284546998272, guid=759c522b-d7f7-4160-8039-91b25214a448, sourceSystem=ERP
2025-06-09 15:22:57.039 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 15:22:57.039 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X42050014, orgName=验收班
2025-06-09 15:22:57.116 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1931975285306167296, orgName=验收班
2025-06-09 15:22:57.117 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X42050014 有 1 个子记录
2025-06-09 15:22:57.247 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1931975285306167296, guid=fa5d2ca7-dd67-4428-bc0a-7bab0adca8ea, sourceSystem=ERP
2025-06-09 15:22:57.247 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 15:22:57.247 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=26 条, 子表(t_department_child)=47 条
2025-06-09 15:22:57.247 [http-nio-8285-exec-5] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 26 个部门
2025-06-09 15:22:57.330 [http-nio-8285-exec-5] INFO  c.dfit.percode.sync.controller.DataSyncController - 部门数据同步成功
2025-06-09 15:24:28.379 [http-nio-8285-exec-9] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步员工数据，时间范围: 2024-07-07 00:00:00 - 2024-07-07 23:59:59
2025-06-09 15:24:28.453 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 15:24:28.454 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 15:24:28.456 [http-nio-8285-exec-9] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/employees?startDate=2024-07-07%2000:00:00&endDate=2024-07-07%2023:59:59
2025-06-09 16:07:10.035 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 16:07:10.139 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 16:08:04.527 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 16:08:04.582 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 21812 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 16:08:04.584 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 16:08:08.745 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:08:08.756 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 16:08:08.840 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 16:08:08.851 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:08:08.853 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 16:08:08.876 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 16:08:08.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:08:08.899 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 16:08:08.943 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 JPA repository interfaces.
2025-06-09 16:08:08.998 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:08:09.001 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 16:08:09.056 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-09 16:08:11.485 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 16:08:11.508 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 16:08:11.509 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 16:08:11.510 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 16:08:11.891 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 16:08:11.891 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 7079 ms
2025-06-09 16:08:12.052 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 16:08:12.469 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 16:08:14.502 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 16:08:15.139 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 16:08:15.581 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 16:08:15.960 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 16:08:16.725 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 16:08:16.744 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 16:08:19.206 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 16:08:25.507 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 16:08:25.600 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 16:08:28.698 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 26.171 seconds (JVM running for 34.903)
2025-06-09 16:08:35.798 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 16:08:35.798 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 16:08:35.804 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 16:08:36.271 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始同步员工数据，时间范围: 2024-07-07 00:00:00 - 2024-07-07 23:59:59
2025-06-09 16:08:36.325 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:08:36.326 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:08:36.327 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/employees?startDate=2024-07-07%2000:00:00&endDate=2024-07-07%2023:59:59
2025-06-09 16:08:41.163 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 52 个员工
2025-06-09 16:08:41.164 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 52 个员工
2025-06-09 16:08:41.165 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=81ee8ed6-edab-4a1d-832b-812dcaf55aee, employeeName=张清辉, employeeCode=019359
2025-06-09 16:08:41.601 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986795009019904, userName=张清辉, employeeCode=019359
2025-06-09 16:08:41.602 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9db5e257-45b9-41fb-996b-c5ecf4b2d9ba, employeeName=费燕, employeeCode=001421
2025-06-09 16:08:41.690 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986796841930752, userName=费燕, employeeCode=001421
2025-06-09 16:08:41.691 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=23ccc025-1a43-47ac-aa49-f448e6cfa311, employeeName=刘继宏, employeeCode=009361
2025-06-09 16:08:41.766 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986797211029504, userName=刘继宏, employeeCode=009361
2025-06-09 16:08:41.767 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=08a7a862-c8fc-4d6c-8a92-f866ff400058, employeeName=宋思宇, employeeCode=022421
2025-06-09 16:08:41.858 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986797533990912, userName=宋思宇, employeeCode=022421
2025-06-09 16:08:41.861 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=d0ecd2fc-4e3c-4309-a562-402949b7b98e, employeeName=李震波, employeeCode=009206
2025-06-09 16:08:41.947 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986797928255488, userName=李震波, employeeCode=009206
2025-06-09 16:08:41.948 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7da59e09-6200-45eb-890b-a34dbc42f962, employeeName=王宇竹, employeeCode=024367
2025-06-09 16:08:42.026 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986798288965632, userName=王宇竹, employeeCode=024367
2025-06-09 16:08:42.027 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=43e6ef9b-dd63-496b-b54a-da50f789fb66, employeeName=黄彪凯, employeeCode=022431
2025-06-09 16:08:42.113 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986798624509952, userName=黄彪凯, employeeCode=022431
2025-06-09 16:08:42.114 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=a8718267-1337-4777-9173-e08521b3a013, employeeName=彭涛, employeeCode=008380
2025-06-09 16:08:42.212 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986798993608704, userName=彭涛, employeeCode=008380
2025-06-09 16:08:42.213 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6b9a97aa-b0fd-46a2-9d0d-5e9da34ed55b, employeeName=王桂玲, employeeCode=009396
2025-06-09 16:08:42.290 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986799400456192, userName=王桂玲, employeeCode=009396
2025-06-09 16:08:42.291 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7a146740-5491-4c2c-9cb6-1d8c0d70c4f0, employeeName=何逸飞, employeeCode=022425
2025-06-09 16:08:42.363 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986799727611904, userName=何逸飞, employeeCode=022425
2025-06-09 16:08:42.364 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=a7725f80-3761-41bc-af8e-c61ca302cafc, employeeName=陈欢欢, employeeCode=022428
2025-06-09 16:08:42.455 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986800046379008, userName=陈欢欢, employeeCode=022428
2025-06-09 16:08:42.458 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b624a436-ffeb-4738-892a-98fb72656b6f, employeeName=马伟, employeeCode=009554
2025-06-09 16:08:42.531 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986800432254976, userName=马伟, employeeCode=009554
2025-06-09 16:08:42.531 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=44804e22-b0fa-465b-9e3c-493be9f4eb39, employeeName=周翔, employeeCode=009576
2025-06-09 16:08:42.614 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986800738439168, userName=周翔, employeeCode=009576
2025-06-09 16:08:42.615 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b991304b-5913-46d1-9fbf-ad51b03c48ff, employeeName=奚树琴, employeeCode=001528
2025-06-09 16:08:42.705 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986801111732224, userName=奚树琴, employeeCode=001528
2025-06-09 16:08:42.708 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=e6309686-9b6a-48e6-8aea-5932c6eba1b2, employeeName=张仪杰, employeeCode=019414
2025-06-09 16:08:42.787 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986801476636672, userName=张仪杰, employeeCode=019414
2025-06-09 16:08:42.787 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6333981b-812c-47f7-8a2b-98572be8b75b, employeeName=欧翔, employeeCode=008659
2025-06-09 16:08:42.896 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986801807986688, userName=欧翔, employeeCode=008659
2025-06-09 16:08:42.897 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4752a186-9df3-4fd9-9b09-51d22a7c109b, employeeName=潘玉柱, employeeCode=009226
2025-06-09 16:08:42.974 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986802269360128, userName=潘玉柱, employeeCode=009226
2025-06-09 16:08:42.974 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b8a5a47e-550e-4e8f-88d6-b6a3d45034d5, employeeName=于刚, employeeCode=009546
2025-06-09 16:08:43.043 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986802592321536, userName=于刚, employeeCode=009546
2025-06-09 16:08:43.043 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=46414da6-3939-42e4-816e-69064cf18c37, employeeName=陈志成, employeeCode=026146
2025-06-09 16:08:43.130 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986802885922816, userName=陈志成, employeeCode=026146
2025-06-09 16:08:43.132 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=f5a79c09-b2fc-4d9f-9384-f1cd7ca5a7de, employeeName=郭宏昌, employeeCode=026140
2025-06-09 16:08:43.209 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986803255021568, userName=郭宏昌, employeeCode=026140
2025-06-09 16:08:43.210 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=889f76b0-fdc6-4ec9-999a-8221ef9604ee, employeeName=王建鹰, employeeCode=018949
2025-06-09 16:08:43.307 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986803582177280, userName=王建鹰, employeeCode=018949
2025-06-09 16:08:43.307 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=0d2e6215-f865-49d1-b858-2ee93d5a636d, employeeName=欧金雄, employeeCode=009209
2025-06-09 16:08:43.380 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986803997413376, userName=欧金雄, employeeCode=009209
2025-06-09 16:08:43.381 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=1b5a7ae5-3f22-4cc1-b690-688ab80ea199, employeeName=熊铁桥, employeeCode=009212
2025-06-09 16:08:43.459 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986804299403264, userName=熊铁桥, employeeCode=009212
2025-06-09 16:08:43.459 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9804004e-9337-4bbd-a03a-b75060b579aa, employeeName=王跃明, employeeCode=008695
2025-06-09 16:08:43.546 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986804626558976, userName=王跃明, employeeCode=008695
2025-06-09 16:08:43.547 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=208ea3f5-6288-4f2c-8eae-8b524321394f, employeeName=张云山, employeeCode=004599
2025-06-09 16:08:43.637 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986804995657728, userName=张云山, employeeCode=004599
2025-06-09 16:08:43.638 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=ec4d4e14-8124-4eb3-a72d-902e98d1a0e7, employeeName=严景辉, employeeCode=009352
2025-06-09 16:08:43.729 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986805377339392, userName=严景辉, employeeCode=009352
2025-06-09 16:08:43.729 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3bf283b7-5b1c-4706-a434-a7d5fd3363b3, employeeName=张舒宁, employeeCode=022427
2025-06-09 16:08:43.806 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986805759021056, userName=张舒宁, employeeCode=022427
2025-06-09 16:08:43.806 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6db78e6a-8083-4f30-94e7-9b3aa4ae0c88, employeeName=卞云丽, employeeCode=021554
2025-06-09 16:08:43.896 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986806081982464, userName=卞云丽, employeeCode=021554
2025-06-09 16:08:43.897 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=98974650-a8dd-4f0f-9b2b-09c581372edb, employeeName=黎异, employeeCode=009356
2025-06-09 16:08:43.987 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986806463664128, userName=黎异, employeeCode=009356
2025-06-09 16:08:43.987 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=17203632-d165-427c-ba0a-5634c1b6332a, employeeName=王峰, employeeCode=009061
2025-06-09 16:08:44.083 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986806845345792, userName=王峰, employeeCode=009061
2025-06-09 16:08:44.085 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3563fc99-5462-4882-85b8-eabf192fb542, employeeName=朱丽华, employeeCode=009208
2025-06-09 16:08:44.174 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986807252193280, userName=朱丽华, employeeCode=009208
2025-06-09 16:08:44.175 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=01877d97-efe7-4de1-85cf-80d3bc4e1b2b, employeeName=王立国, employeeCode=007409
2025-06-09 16:08:44.268 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986807646457856, userName=王立国, employeeCode=007409
2025-06-09 16:08:44.268 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3350548b-9093-48c6-aaf6-25375d9e2995, employeeName=周子涵, employeeCode=026250
2025-06-09 16:08:44.340 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986808023945216, userName=周子涵, employeeCode=026250
2025-06-09 16:08:44.352 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7a7d7e5c-9b2b-4a3a-ba6a-29afcfdf3e7d, employeeName=孙秋东, employeeCode=026149
2025-06-09 16:08:44.433 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986808376266752, userName=孙秋东, employeeCode=026149
2025-06-09 16:08:44.433 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=575210ca-64a6-47a7-8ad5-2ec8958816ee, employeeName=刘丹, employeeCode=027234
2025-06-09 16:08:44.521 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986808711811072, userName=刘丹, employeeCode=027234
2025-06-09 16:08:44.523 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=200046d0-e2dc-4396-ad67-49253036bd9d, employeeName=刘美, employeeCode=009207
2025-06-09 16:08:44.621 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986809093492736, userName=刘美, employeeCode=009207
2025-06-09 16:08:44.622 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9b47537d-e913-45e3-a9a5-ff0f3d1bf2de, employeeName=李磊, employeeCode=009211
2025-06-09 16:08:44.725 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986809508728832, userName=李磊, employeeCode=009211
2025-06-09 16:08:44.725 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=2cb59e3a-af44-4d35-8b97-1e9cdea24cc5, employeeName=何国华, employeeCode=007339
2025-06-09 16:08:44.810 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986809936547840, userName=何国华, employeeCode=007339
2025-06-09 16:08:44.811 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4e3a4f58-26f6-48d8-adf1-d2b99c422f2f, employeeName=盛建文, employeeCode=000417
2025-06-09 16:08:44.944 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986810297257984, userName=盛建文, employeeCode=000417
2025-06-09 16:08:44.945 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=57ea60ef-4b91-4984-b991-1acc77c4f933, employeeName=张会强, employeeCode=025775
2025-06-09 16:08:45.021 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986810859294720, userName=张会强, employeeCode=025775
2025-06-09 16:08:45.022 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9cde2131-d087-4710-b7a1-ffb49cd785bd, employeeName=刘帅, employeeCode=026142
2025-06-09 16:08:45.114 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986811182256128, userName=刘帅, employeeCode=026142
2025-06-09 16:08:45.117 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=ea68d766-fbcc-4850-9414-efe8ba70451e, employeeName=梁玉贵, employeeCode=002570
2025-06-09 16:08:45.198 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986811589103616, userName=梁玉贵, employeeCode=002570
2025-06-09 16:08:45.199 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=5f081738-ef65-46f4-8cb9-ccd416e6f92e, employeeName=赵靖, employeeCode=009015
2025-06-09 16:08:45.280 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986811928842240, userName=赵靖, employeeCode=009015
2025-06-09 16:08:45.287 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=50c3da85-115f-4133-b9bb-4609b411d55a, employeeName=王传森, employeeCode=004625
2025-06-09 16:08:45.370 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986812293746688, userName=王传森, employeeCode=004625
2025-06-09 16:08:45.370 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4741ac9a-3bfc-4e91-8c98-08daba1cf699, employeeName=蒋建华, employeeCode=002987
2025-06-09 16:08:45.449 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986812641873920, userName=蒋建华, employeeCode=002987
2025-06-09 16:08:45.451 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=743ad884-0ef1-4757-a577-d9ed71fec46e, employeeName=张涛, employeeCode=002195
2025-06-09 16:08:45.541 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986812981612544, userName=张涛, employeeCode=002195
2025-06-09 16:08:45.541 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=bbf94f47-5259-49ec-bc1b-b9e958c4b29a, employeeName=莫瑞清, employeeCode=009063
2025-06-09 16:08:45.624 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986813359099904, userName=莫瑞清, employeeCode=009063
2025-06-09 16:08:45.624 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=f01eb829-587c-48be-9abf-094158dacea0, employeeName=詹小栋, employeeCode=009065
2025-06-09 16:08:45.710 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986813707227136, userName=詹小栋, employeeCode=009065
2025-06-09 16:08:45.710 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=27925d45-7716-4506-b8c1-de480a203559, employeeName=王剑, employeeCode=009227
2025-06-09 16:08:45.791 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986814067937280, userName=王剑, employeeCode=009227
2025-06-09 16:08:45.791 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=d962725b-b71b-4687-a086-17efdabb9024, employeeName=赵士松, employeeCode=009587
2025-06-09 16:08:45.921 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986814407675904, userName=赵士松, employeeCode=009587
2025-06-09 16:08:45.921 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b1df148c-531f-42d4-ac9e-93ffc3311812, employeeName=陈赞, employeeCode=024394
2025-06-09 16:08:45.992 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986814952935424, userName=陈赞, employeeCode=024394
2025-06-09 16:08:45.993 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=97bdc980-8a11-4cb3-b123-67b0e4af0b0e, employeeName=谷盟森, employeeCode=027135
2025-06-09 16:08:46.076 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931986815254925312, userName=谷盟森, employeeCode=027135
2025-06-09 16:08:46.076 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据同步汇总: t_user 表共处理 52 条记录
2025-06-09 16:08:46.076 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据同步完成，共处理 52 个员工
2025-06-09 16:08:46.210 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 员工数据同步成功
2025-06-09 16:30:09.704 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 16:30:09.747 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 16:30:27.172 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 16:30:27.184 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 9640 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 16:30:27.190 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 16:30:30.345 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:30:30.355 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 16:30:30.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 51 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 16:30:30.433 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:30:30.434 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 16:30:30.463 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 16:30:30.478 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:30:30.479 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 16:30:30.510 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-09 16:30:30.547 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 16:30:30.550 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 16:30:30.644 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Redis repository interfaces.
2025-06-09 16:30:32.982 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 16:30:33.005 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 16:30:33.006 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 16:30:33.006 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 16:30:33.326 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 16:30:33.327 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5969 ms
2025-06-09 16:30:33.480 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 16:30:33.734 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 16:30:34.761 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 16:30:34.918 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 16:30:35.428 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 16:30:35.844 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 16:30:36.764 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 16:30:36.810 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 16:30:38.685 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 16:30:42.978 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 16:30:43.004 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 16:30:43.848 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.79 seconds (JVM running for 22.246)
2025-06-09 16:31:12.492 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 16:31:12.494 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 16:31:12.503 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-09 16:31:13.036 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始执行完整数据同步，时间范围: 2024-07-07 00:00:00 - 2024-07-07 23:59:59
2025-06-09 16:31:13.090 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始执行完整数据同步，时间范围: 2024-07-07 00:00:00 - 2024-07-07 23:59:59
2025-06-09 16:31:13.094 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:31:13.095 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:31:13.098 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-07-07%2000:00:00&endDate=2024-07-07%2023:59:59
2025-06-09 16:31:14.287 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 0 个部门
2025-06-09 16:31:14.288 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 0 个部门
2025-06-09 16:31:14.289 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=0 条, 子表(t_department_child)=0 条
2025-06-09 16:31:14.290 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 0 个部门
2025-06-09 16:31:14.290 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:31:14.291 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取员工数据，时间范围: 2024-07-07T00:00 - 2024-07-07T23:59:59
2025-06-09 16:31:14.292 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/employees?startDate=2024-07-07%2000:00:00&endDate=2024-07-07%2023:59:59
2025-06-09 16:31:18.082 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 52 个员工
2025-06-09 16:31:18.083 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 52 个员工
2025-06-09 16:31:18.084 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=81ee8ed6-edab-4a1d-832b-812dcaf55aee, employeeName=张清辉, employeeCode=019359
2025-06-09 16:31:18.216 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992486335614976, userName=张清辉, employeeCode=019359
2025-06-09 16:31:18.216 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9db5e257-45b9-41fb-996b-c5ecf4b2d9ba, employeeName=费燕, employeeCode=001421
2025-06-09 16:31:18.301 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992486889263104, userName=费燕, employeeCode=001421
2025-06-09 16:31:18.302 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=23ccc025-1a43-47ac-aa49-f448e6cfa311, employeeName=刘继宏, employeeCode=009361
2025-06-09 16:31:18.395 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992487249973248, userName=刘继宏, employeeCode=009361
2025-06-09 16:31:18.395 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=08a7a862-c8fc-4d6c-8a92-f866ff400058, employeeName=宋思宇, employeeCode=022421
2025-06-09 16:31:18.462 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992487640043520, userName=宋思宇, employeeCode=022421
2025-06-09 16:31:18.462 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=d0ecd2fc-4e3c-4309-a562-402949b7b98e, employeeName=李震波, employeeCode=009206
2025-06-09 16:31:18.533 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992487921061888, userName=李震波, employeeCode=009206
2025-06-09 16:31:18.534 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7da59e09-6200-45eb-890b-a34dbc42f962, employeeName=王宇竹, employeeCode=024367
2025-06-09 16:31:18.639 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992488223051776, userName=王宇竹, employeeCode=024367
2025-06-09 16:31:18.640 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=43e6ef9b-dd63-496b-b54a-da50f789fb66, employeeName=黄彪凯, employeeCode=022431
2025-06-09 16:31:18.716 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992488667648000, userName=黄彪凯, employeeCode=022431
2025-06-09 16:31:18.716 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=a8718267-1337-4777-9173-e08521b3a013, employeeName=彭涛, employeeCode=008380
2025-06-09 16:31:18.817 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992488986415104, userName=彭涛, employeeCode=008380
2025-06-09 16:31:18.818 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6b9a97aa-b0fd-46a2-9d0d-5e9da34ed55b, employeeName=王桂玲, employeeCode=009396
2025-06-09 16:31:18.893 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992489414234112, userName=王桂玲, employeeCode=009396
2025-06-09 16:31:18.894 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7a146740-5491-4c2c-9cb6-1d8c0d70c4f0, employeeName=何逸飞, employeeCode=022425
2025-06-09 16:31:18.966 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992489733001216, userName=何逸飞, employeeCode=022425
2025-06-09 16:31:18.967 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=a7725f80-3761-41bc-af8e-c61ca302cafc, employeeName=陈欢欢, employeeCode=022428
2025-06-09 16:31:19.046 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992490039185408, userName=陈欢欢, employeeCode=022428
2025-06-09 16:31:19.047 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b624a436-ffeb-4738-892a-98fb72656b6f, employeeName=马伟, employeeCode=009554
2025-06-09 16:31:19.138 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992490387312640, userName=马伟, employeeCode=009554
2025-06-09 16:31:19.140 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=44804e22-b0fa-465b-9e3c-493be9f4eb39, employeeName=周翔, employeeCode=009576
2025-06-09 16:31:19.233 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992490764800000, userName=周翔, employeeCode=009576
2025-06-09 16:31:19.234 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b991304b-5913-46d1-9fbf-ad51b03c48ff, employeeName=奚树琴, employeeCode=001528
2025-06-09 16:31:19.314 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992491159064576, userName=奚树琴, employeeCode=001528
2025-06-09 16:31:19.315 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=e6309686-9b6a-48e6-8aea-5932c6eba1b2, employeeName=张仪杰, employeeCode=019414
2025-06-09 16:31:19.396 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992491498803200, userName=张仪杰, employeeCode=019414
2025-06-09 16:31:19.397 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6333981b-812c-47f7-8a2b-98572be8b75b, employeeName=欧翔, employeeCode=008659
2025-06-09 16:31:19.492 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992491842736128, userName=欧翔, employeeCode=008659
2025-06-09 16:31:19.492 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4752a186-9df3-4fd9-9b09-51d22a7c109b, employeeName=潘玉柱, employeeCode=009226
2025-06-09 16:31:19.615 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992492241195008, userName=潘玉柱, employeeCode=009226
2025-06-09 16:31:19.616 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b8a5a47e-550e-4e8f-88d6-b6a3d45034d5, employeeName=于刚, employeeCode=009546
2025-06-09 16:31:19.705 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992492761288704, userName=于刚, employeeCode=009546
2025-06-09 16:31:19.706 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=46414da6-3939-42e4-816e-69064cf18c37, employeeName=陈志成, employeeCode=026146
2025-06-09 16:31:19.786 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992493138776064, userName=陈志成, employeeCode=026146
2025-06-09 16:31:19.787 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=f5a79c09-b2fc-4d9f-9384-f1cd7ca5a7de, employeeName=郭宏昌, employeeCode=026140
2025-06-09 16:31:19.864 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992493478514688, userName=郭宏昌, employeeCode=026140
2025-06-09 16:31:19.864 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=889f76b0-fdc6-4ec9-999a-8221ef9604ee, employeeName=王建鹰, employeeCode=018949
2025-06-09 16:31:19.932 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992493801476096, userName=王建鹰, employeeCode=018949
2025-06-09 16:31:19.933 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=0d2e6215-f865-49d1-b858-2ee93d5a636d, employeeName=欧金雄, employeeCode=009209
2025-06-09 16:31:20.035 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992494090883072, userName=欧金雄, employeeCode=009209
2025-06-09 16:31:20.036 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=1b5a7ae5-3f22-4cc1-b690-688ab80ea199, employeeName=熊铁桥, employeeCode=009212
2025-06-09 16:31:20.125 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992494522896384, userName=熊铁桥, employeeCode=009212
2025-06-09 16:31:20.126 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9804004e-9337-4bbd-a03a-b75060b579aa, employeeName=王跃明, employeeCode=008695
2025-06-09 16:31:20.216 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992494900383744, userName=王跃明, employeeCode=008695
2025-06-09 16:31:20.217 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=208ea3f5-6288-4f2c-8eae-8b524321394f, employeeName=张云山, employeeCode=004599
2025-06-09 16:31:20.291 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992495282065408, userName=张云山, employeeCode=004599
2025-06-09 16:31:20.293 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=ec4d4e14-8124-4eb3-a72d-902e98d1a0e7, employeeName=严景辉, employeeCode=009352
2025-06-09 16:31:20.382 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992495605026816, userName=严景辉, employeeCode=009352
2025-06-09 16:31:20.401 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3bf283b7-5b1c-4706-a434-a7d5fd3363b3, employeeName=张舒宁, employeeCode=022427
2025-06-09 16:31:20.486 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992496053817344, userName=张舒宁, employeeCode=022427
2025-06-09 16:31:20.487 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=6db78e6a-8083-4f30-94e7-9b3aa4ae0c88, employeeName=卞云丽, employeeCode=021554
2025-06-09 16:31:20.583 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992496418721792, userName=卞云丽, employeeCode=021554
2025-06-09 16:31:20.583 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=98974650-a8dd-4f0f-9b2b-09c581372edb, employeeName=黎异, employeeCode=009356
2025-06-09 16:31:20.659 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992496821374976, userName=黎异, employeeCode=009356
2025-06-09 16:31:20.659 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=17203632-d165-427c-ba0a-5634c1b6332a, employeeName=王峰, employeeCode=009061
2025-06-09 16:31:20.764 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992497140142080, userName=王峰, employeeCode=009061
2025-06-09 16:31:20.765 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3563fc99-5462-4882-85b8-eabf192fb542, employeeName=朱丽华, employeeCode=009208
2025-06-09 16:31:20.852 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992497580544000, userName=朱丽华, employeeCode=009208
2025-06-09 16:31:20.853 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=01877d97-efe7-4de1-85cf-80d3bc4e1b2b, employeeName=王立国, employeeCode=007409
2025-06-09 16:31:20.965 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992497949642752, userName=王立国, employeeCode=007409
2025-06-09 16:31:20.965 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=3350548b-9093-48c6-aaf6-25375d9e2995, employeeName=周子涵, employeeCode=026250
2025-06-09 16:31:21.058 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992498419404800, userName=周子涵, employeeCode=026250
2025-06-09 16:31:21.059 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=7a7d7e5c-9b2b-4a3a-ba6a-29afcfdf3e7d, employeeName=孙秋东, employeeCode=026149
2025-06-09 16:31:21.139 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992498813669376, userName=孙秋东, employeeCode=026149
2025-06-09 16:31:21.139 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=575210ca-64a6-47a7-8ad5-2ec8958816ee, employeeName=刘丹, employeeCode=027234
2025-06-09 16:31:21.227 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992499149213696, userName=刘丹, employeeCode=027234
2025-06-09 16:31:21.228 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=200046d0-e2dc-4396-ad67-49253036bd9d, employeeName=刘美, employeeCode=009207
2025-06-09 16:31:21.368 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992499526701056, userName=刘美, employeeCode=009207
2025-06-09 16:31:21.368 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9b47537d-e913-45e3-a9a5-ff0f3d1bf2de, employeeName=李磊, employeeCode=009211
2025-06-09 16:31:21.469 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992500109709312, userName=李磊, employeeCode=009211
2025-06-09 16:31:21.470 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=2cb59e3a-af44-4d35-8b97-1e9cdea24cc5, employeeName=何国华, employeeCode=007339
2025-06-09 16:31:21.588 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992500537528320, userName=何国华, employeeCode=007339
2025-06-09 16:31:21.588 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4e3a4f58-26f6-48d8-adf1-d2b99c422f2f, employeeName=盛建文, employeeCode=000417
2025-06-09 16:31:21.697 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992501045039104, userName=盛建文, employeeCode=000417
2025-06-09 16:31:21.703 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=57ea60ef-4b91-4984-b991-1acc77c4f933, employeeName=张会强, employeeCode=025775
2025-06-09 16:31:21.796 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992501518995456, userName=张会强, employeeCode=025775
2025-06-09 16:31:21.797 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=9cde2131-d087-4710-b7a1-ffb49cd785bd, employeeName=刘帅, employeeCode=026142
2025-06-09 16:31:21.887 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992501913260032, userName=刘帅, employeeCode=026142
2025-06-09 16:31:21.887 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=ea68d766-fbcc-4850-9414-efe8ba70451e, employeeName=梁玉贵, employeeCode=002570
2025-06-09 16:31:21.995 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992502286553088, userName=梁玉贵, employeeCode=002570
2025-06-09 16:31:21.996 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=5f081738-ef65-46f4-8cb9-ccd416e6f92e, employeeName=赵靖, employeeCode=009015
2025-06-09 16:31:22.086 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992502743732224, userName=赵靖, employeeCode=009015
2025-06-09 16:31:22.087 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=50c3da85-115f-4133-b9bb-4609b411d55a, employeeName=王传森, employeeCode=004625
2025-06-09 16:31:22.178 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992503125413888, userName=王传森, employeeCode=004625
2025-06-09 16:31:22.178 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=4741ac9a-3bfc-4e91-8c98-08daba1cf699, employeeName=蒋建华, employeeCode=002987
2025-06-09 16:31:22.259 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992503507095552, userName=蒋建华, employeeCode=002987
2025-06-09 16:31:22.259 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=743ad884-0ef1-4757-a577-d9ed71fec46e, employeeName=张涛, employeeCode=002195
2025-06-09 16:31:22.345 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992503846834176, userName=张涛, employeeCode=002195
2025-06-09 16:31:22.345 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=bbf94f47-5259-49ec-bc1b-b9e958c4b29a, employeeName=莫瑞清, employeeCode=009063
2025-06-09 16:31:22.418 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992504207544320, userName=莫瑞清, employeeCode=009063
2025-06-09 16:31:22.418 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=f01eb829-587c-48be-9abf-094158dacea0, employeeName=詹小栋, employeeCode=009065
2025-06-09 16:31:22.498 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992504513728512, userName=詹小栋, employeeCode=009065
2025-06-09 16:31:22.499 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=27925d45-7716-4506-b8c1-de480a203559, employeeName=王剑, employeeCode=009227
2025-06-09 16:31:22.581 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992504853467136, userName=王剑, employeeCode=009227
2025-06-09 16:31:22.581 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=d962725b-b71b-4687-a086-17efdabb9024, employeeName=赵士松, employeeCode=009587
2025-06-09 16:31:22.666 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992505201594368, userName=赵士松, employeeCode=009587
2025-06-09 16:31:22.666 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=b1df148c-531f-42d4-ac9e-93ffc3311812, employeeName=陈赞, employeeCode=024394
2025-06-09 16:31:22.741 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992505558110208, userName=陈赞, employeeCode=024394
2025-06-09 16:31:22.742 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工: mdmId=97bdc980-8a11-4cb3-b123-67b0e4af0b0e, employeeName=谷盟森, employeeCode=027135
2025-06-09 16:31:22.825 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据已保存到 t_user 表: id=1931992505872683008, userName=谷盟森, employeeCode=027135
2025-06-09 16:31:22.826 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据同步汇总: t_user 表共处理 52 条记录
2025-06-09 16:31:22.826 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工数据同步完成，共处理 52 个员工
2025-06-09 16:31:22.826 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步员工扩展数据
2025-06-09 16:31:22.826 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取员工数据，时间范围: 2025-06-08T16:31:22.826299200 - 2025-06-09T16:31:22.826299200
2025-06-09 16:31:22.826 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/employees?startDate=2025-06-08%2016:31:22&endDate=2025-06-09%2016:31:22
2025-06-09 16:31:24.813 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 16 个员工
2025-06-09 16:31:24.813 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始处理 16 个员工的扩展数据
2025-06-09 16:31:24.813 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=de0f0b16-967f-4f61-b865-57ed5f85da25, employeeName=王金生
2025-06-09 16:31:24.813 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 de0f0b16-967f-4f61-b865-57ed5f85da25 有 5 个岗位信息
2025-06-09 16:31:25.077 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.078 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.078 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.078 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.078 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.079 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 de0f0b16-967f-4f61-b865-57ed5f85da25 有 4 个职称信息
2025-06-09 16:31:25.080 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.080 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.080 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.081 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.081 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 de0f0b16-967f-4f61-b865-57ed5f85da25 有 2 个系统标识信息
2025-06-09 16:31:25.081 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.081 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 de0f0b16-967f-4f61-b865-57ed5f85da25 的用户
2025-06-09 16:31:25.083 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=0367729b-f733-4676-ad6d-dcc08182f64a, employeeName=薛宝东
2025-06-09 16:31:25.083 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0367729b-f733-4676-ad6d-dcc08182f64a 有 4 个岗位信息
2025-06-09 16:31:25.154 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.155 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.155 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.155 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.155 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0367729b-f733-4676-ad6d-dcc08182f64a 有 4 个职称信息
2025-06-09 16:31:25.156 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.156 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.156 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.156 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.157 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0367729b-f733-4676-ad6d-dcc08182f64a 有 1 个系统标识信息
2025-06-09 16:31:25.157 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0367729b-f733-4676-ad6d-dcc08182f64a 的用户
2025-06-09 16:31:25.157 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=8a4b0b08-1f0d-41ca-9b4f-b94237917d0c, employeeName=郑志祥
2025-06-09 16:31:25.157 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 有 9 个岗位信息
2025-06-09 16:31:25.223 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.231 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.233 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.233 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.234 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.235 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.235 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.235 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 有 7 个职称信息
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.236 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 有 3 个系统标识信息
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 8a4b0b08-1f0d-41ca-9b4f-b94237917d0c 的用户
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=37179764-26f1-4dfa-acb0-1cd258c0403b, employeeName=丁贵州
2025-06-09 16:31:25.237 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 37179764-26f1-4dfa-acb0-1cd258c0403b 有 2 个岗位信息
2025-06-09 16:31:25.330 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 37179764-26f1-4dfa-acb0-1cd258c0403b 的用户
2025-06-09 16:31:25.330 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 37179764-26f1-4dfa-acb0-1cd258c0403b 的用户
2025-06-09 16:31:25.330 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 37179764-26f1-4dfa-acb0-1cd258c0403b 有 2 个职称信息
2025-06-09 16:31:25.331 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 37179764-26f1-4dfa-acb0-1cd258c0403b 的用户
2025-06-09 16:31:25.331 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 37179764-26f1-4dfa-acb0-1cd258c0403b 的用户
2025-06-09 16:31:25.331 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 37179764-26f1-4dfa-acb0-1cd258c0403b 有 1 个系统标识信息
2025-06-09 16:31:25.331 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 37179764-26f1-4dfa-acb0-1cd258c0403b 的用户
2025-06-09 16:31:25.331 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=e27a7b4a-f7cf-4cfc-8179-ba4d503f0552, employeeName=高鑫
2025-06-09 16:31:25.332 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 有 4 个岗位信息
2025-06-09 16:31:25.395 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.396 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.396 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.396 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.396 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 有 4 个职称信息
2025-06-09 16:31:25.396 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 有 2 个系统标识信息
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 e27a7b4a-f7cf-4cfc-8179-ba4d503f0552 的用户
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=0c6dc8a9-b171-489a-9567-e24a57edc638, employeeName=张金伟
2025-06-09 16:31:25.397 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0c6dc8a9-b171-489a-9567-e24a57edc638 有 4 个岗位信息
2025-06-09 16:31:25.476 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.477 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.477 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0c6dc8a9-b171-489a-9567-e24a57edc638 有 4 个职称信息
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 0c6dc8a9-b171-489a-9567-e24a57edc638 有 1 个系统标识信息
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 0c6dc8a9-b171-489a-9567-e24a57edc638 的用户
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=86d3b9da-02be-4aff-bf65-781efd471b8b, employeeName=王永婷
2025-06-09 16:31:25.478 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 86d3b9da-02be-4aff-bf65-781efd471b8b 有 4 个岗位信息
2025-06-09 16:31:25.557 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.558 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.559 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.561 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.563 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 86d3b9da-02be-4aff-bf65-781efd471b8b 有 3 个职称信息
2025-06-09 16:31:25.563 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.566 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.567 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 86d3b9da-02be-4aff-bf65-781efd471b8b 有 1 个系统标识信息
2025-06-09 16:31:25.568 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 86d3b9da-02be-4aff-bf65-781efd471b8b 的用户
2025-06-09 16:31:25.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=a3fa9423-ac03-4109-bdc8-e62fd4674cd0, employeeName=程昱
2025-06-09 16:31:25.568 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 有 2 个岗位信息
2025-06-09 16:31:25.653 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 的用户
2025-06-09 16:31:25.654 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 的用户
2025-06-09 16:31:25.654 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 有 2 个职称信息
2025-06-09 16:31:25.654 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 的用户
2025-06-09 16:31:25.654 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 的用户
2025-06-09 16:31:25.655 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 有 1 个系统标识信息
2025-06-09 16:31:25.655 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 a3fa9423-ac03-4109-bdc8-e62fd4674cd0 的用户
2025-06-09 16:31:25.655 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=70cd09c1-e48b-4254-a0f5-389e37777536, employeeName=雷江
2025-06-09 16:31:25.655 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 70cd09c1-e48b-4254-a0f5-389e37777536 有 2 个岗位信息
2025-06-09 16:31:25.727 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 70cd09c1-e48b-4254-a0f5-389e37777536 的用户
2025-06-09 16:31:25.728 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 70cd09c1-e48b-4254-a0f5-389e37777536 的用户
2025-06-09 16:31:25.728 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 70cd09c1-e48b-4254-a0f5-389e37777536 有 2 个职称信息
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 70cd09c1-e48b-4254-a0f5-389e37777536 的用户
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 70cd09c1-e48b-4254-a0f5-389e37777536 的用户
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 70cd09c1-e48b-4254-a0f5-389e37777536 有 1 个系统标识信息
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 70cd09c1-e48b-4254-a0f5-389e37777536 的用户
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=f1b9e1c4-e45d-4670-ab4f-3afcd937baf2, employeeName=Ivannie
2025-06-09 16:31:25.729 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 有 3 个岗位信息
2025-06-09 16:31:25.801 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 有 3 个职称信息
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.802 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 有 1 个系统标识信息
2025-06-09 16:31:25.803 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 f1b9e1c4-e45d-4670-ab4f-3afcd937baf2 的用户
2025-06-09 16:31:25.803 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=7c5146b7-4351-4d03-966b-073c1694cd96, employeeName=张凯宇
2025-06-09 16:31:25.803 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7c5146b7-4351-4d03-966b-073c1694cd96 有 2 个岗位信息
2025-06-09 16:31:25.878 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7c5146b7-4351-4d03-966b-073c1694cd96 的用户
2025-06-09 16:31:25.878 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7c5146b7-4351-4d03-966b-073c1694cd96 的用户
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7c5146b7-4351-4d03-966b-073c1694cd96 有 2 个职称信息
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7c5146b7-4351-4d03-966b-073c1694cd96 的用户
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7c5146b7-4351-4d03-966b-073c1694cd96 的用户
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7c5146b7-4351-4d03-966b-073c1694cd96 有 1 个系统标识信息
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7c5146b7-4351-4d03-966b-073c1694cd96 的用户
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=9184c9b5-90cc-4e0f-a680-0318d64ba43d, employeeName=汪钲琪
2025-06-09 16:31:25.879 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 9184c9b5-90cc-4e0f-a680-0318d64ba43d 有 2 个岗位信息
2025-06-09 16:31:25.972 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 9184c9b5-90cc-4e0f-a680-0318d64ba43d 的用户
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 9184c9b5-90cc-4e0f-a680-0318d64ba43d 的用户
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 9184c9b5-90cc-4e0f-a680-0318d64ba43d 有 2 个职称信息
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 9184c9b5-90cc-4e0f-a680-0318d64ba43d 的用户
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 9184c9b5-90cc-4e0f-a680-0318d64ba43d 的用户
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 9184c9b5-90cc-4e0f-a680-0318d64ba43d 有 1 个系统标识信息
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 9184c9b5-90cc-4e0f-a680-0318d64ba43d 的用户
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=5a5bf185-10c7-4fe7-877e-8a0a132d9534, employeeName=汤帅
2025-06-09 16:31:25.973 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 5a5bf185-10c7-4fe7-877e-8a0a132d9534 有 2 个岗位信息
2025-06-09 16:31:26.063 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 5a5bf185-10c7-4fe7-877e-8a0a132d9534 的用户
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 5a5bf185-10c7-4fe7-877e-8a0a132d9534 的用户
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 5a5bf185-10c7-4fe7-877e-8a0a132d9534 有 2 个职称信息
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 5a5bf185-10c7-4fe7-877e-8a0a132d9534 的用户
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 5a5bf185-10c7-4fe7-877e-8a0a132d9534 的用户
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 5a5bf185-10c7-4fe7-877e-8a0a132d9534 有 1 个系统标识信息
2025-06-09 16:31:26.064 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 5a5bf185-10c7-4fe7-877e-8a0a132d9534 的用户
2025-06-09 16:31:26.065 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=7ee0e593-f4b6-4465-9cb2-bd0cc8217c15, employeeName=孙琼琼
2025-06-09 16:31:26.065 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 有 2 个岗位信息
2025-06-09 16:31:26.130 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.130 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.130 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 有 2 个职称信息
2025-06-09 16:31:26.131 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.131 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.131 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 有 2 个系统标识信息
2025-06-09 16:31:26.131 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.132 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7ee0e593-f4b6-4465-9cb2-bd0cc8217c15 的用户
2025-06-09 16:31:26.132 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=7835cb2a-2ea0-4d39-9920-ca8b7023cec6, employeeName=胡传东
2025-06-09 16:31:26.132 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 有 1 个岗位信息
2025-06-09 16:31:26.195 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 的用户
2025-06-09 16:31:26.195 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 有 1 个职称信息
2025-06-09 16:31:26.195 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 的用户
2025-06-09 16:31:26.195 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 有 1 个系统标识信息
2025-06-09 16:31:26.195 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 7835cb2a-2ea0-4d39-9920-ca8b7023cec6 的用户
2025-06-09 16:31:26.196 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理员工扩展数据: mdmId=c7a5ebda-a404-41a7-a182-bbf1e69672d1, employeeName=刘益良
2025-06-09 16:31:26.196 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 c7a5ebda-a404-41a7-a182-bbf1e69672d1 有 2 个岗位信息
2025-06-09 16:31:26.266 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 c7a5ebda-a404-41a7-a182-bbf1e69672d1 的用户
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 c7a5ebda-a404-41a7-a182-bbf1e69672d1 的用户
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 c7a5ebda-a404-41a7-a182-bbf1e69672d1 有 2 个职称信息
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 c7a5ebda-a404-41a7-a182-bbf1e69672d1 的用户
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 c7a5ebda-a404-41a7-a182-bbf1e69672d1 的用户
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工 c7a5ebda-a404-41a7-a182-bbf1e69672d1 有 1 个系统标识信息
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] WARN  com.dfit.percode.sync.service.DataSyncService - 未找到MDM ID为 c7a5ebda-a404-41a7-a182-bbf1e69672d1 的用户
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工扩展数据同步汇总: t_employee_position=50 条, t_employee_title=46 条, t_employee_system=21 条
2025-06-09 16:31:26.267 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 开始更新员工部门归属
2025-06-09 16:31:26.380 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 找到 0 个需要更新部门归属的用户
2025-06-09 16:31:26.380 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 员工部门归属更新汇总: t_user 表共更新 0 个用户的 organ_affiliation 字段，0 个用户未找到对应部门
2025-06-09 16:31:26.380 [http-nio-8285-exec-2] INFO  com.dfit.percode.sync.service.DataSyncService - 完整数据同步执行成功，时间范围: 2024-07-07 00:00:00 - 2024-07-07 23:59:59
2025-06-09 16:31:26.492 [http-nio-8285-exec-2] INFO  c.dfit.percode.sync.controller.DataSyncController - 完整数据同步执行成功
2025-06-09 17:27:37.233 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 17:27:37.269 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-09 17:28:25.603 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java ******** on DESKTOP-UKI346C with PID 34944 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-09 17:28:25.611 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-09 17:28:25.612 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-09 17:28:30.963 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 17:28:30.980 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-09 17:28:31.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 104 ms. Found 0 Elasticsearch repository interfaces.
2025-06-09 17:28:31.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 17:28:31.162 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-09 17:28:31.255 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 89 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-09 17:28:31.284 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 17:28:31.287 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-09 17:28:31.379 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 0 JPA repository interfaces.
2025-06-09 17:28:31.482 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-09 17:28:31.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-09 17:28:31.843 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 62 ms. Found 0 Redis repository interfaces.
2025-06-09 17:28:35.254 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-09 17:28:35.280 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-09 17:28:35.281 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-09 17:28:35.282 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-09 17:28:35.734 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-09 17:28:35.736 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 9855 ms
2025-06-09 17:28:35.959 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-09 17:28:36.276 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-09 17:28:41.301 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-09 17:28:41.474 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-09 17:28:42.304 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-09 17:28:43.797 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-09 17:28:45.434 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-09 17:28:45.466 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-09 17:28:47.999 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-09 17:28:53.992 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-09 17:28:54.059 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-09 17:28:55.495 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 33.826 seconds (JVM running for 38.41)
2025-06-09 17:29:15.150 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-09 17:29:15.151 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-09 17:29:15.163 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2025-06-09 17:29:16.285 [http-nio-8285-exec-3] INFO  c.dfit.percode.sync.controller.DataSyncController - 开始执行完整数据同步，时间范围: 2024-07-01 00:00:00 - 2024-07-31 23:59:59
2025-06-09 17:29:16.369 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始执行完整数据同步，时间范围: 2024-07-01 00:00:00 - 2024-07-31 23:59:59
2025-06-09 17:29:16.378 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2024-07-01T00:00 - 2024-07-31T23:59:59
2025-06-09 17:29:16.382 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2024-07-01T00:00 - 2024-07-31T23:59:59
2025-06-09 17:29:16.386 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2024-07-01%2000:00:00&endDate=2024-07-31%2023:59:59
2025-06-09 17:29:18.466 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.ExternalDataService - 成功获取 69 个部门
2025-06-09 17:29:18.470 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 从外部系统获取到 69 个部门
2025-06-09 17:29:18.476 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52060007, orgName=耐火五库班
2025-06-09 17:29:18.928 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007084170285056, orgName=耐火五库班
2025-06-09 17:29:18.929 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X52060007 有 1 个子记录
2025-06-09 17:29:19.070 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007084170285056, guid=1ed36392-f4f0-45c3-a24b-65564ed9d49a, sourceSystem=ERP
2025-06-09 17:29:19.070 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:19.071 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52060011, orgName=炼钢库班
2025-06-09 17:29:19.138 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007086653313024, orgName=炼钢库班
2025-06-09 17:29:19.139 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X52060011 有 1 个子记录
2025-06-09 17:29:19.277 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007086653313024, guid=5bc23335-2492-46f5-acd5-50b3259c0335, sourceSystem=ERP
2025-06-09 17:29:19.278 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:19.278 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64000000, orgName=带钢厂
2025-06-09 17:29:19.521 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007087521533952, orgName=带钢厂
2025-06-09 17:29:19.522 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64000000 有 1 个子记录
2025-06-09 17:29:19.676 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007087521533952, guid=80bdd41b-de14-4446-8023-47b1fc40dfd8, sourceSystem=ERP
2025-06-09 17:29:19.676 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:19.676 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72000000, orgName=江苏金凯节能环保投资控股有限公司
2025-06-09 17:29:19.761 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007089195061248, orgName=江苏金凯节能环保投资控股有限公司
2025-06-09 17:29:19.763 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72000000 有 1 个子记录
2025-06-09 17:29:19.827 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007089195061248, guid=51e66503-62e7-433a-a94c-56b57ebaeb81, sourceSystem=ERP
2025-06-09 17:29:19.828 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:19.829 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72010000, orgName=南京金瀚环保科技有限公司
2025-06-09 17:29:19.926 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007089836789760, orgName=南京金瀚环保科技有限公司
2025-06-09 17:29:19.927 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72010000 有 1 个子记录
2025-06-09 17:29:20.152 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007089836789760, guid=e255a477-025f-45e6-ac0e-525f7fbcf9e2, sourceSystem=ERP
2025-06-09 17:29:20.153 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.153 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72020000, orgName=能源投资部
2025-06-09 17:29:20.245 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007091191549952, orgName=能源投资部
2025-06-09 17:29:20.246 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72020000 有 1 个子记录
2025-06-09 17:29:20.336 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007091191549952, guid=fffeaf1b-f1d9-425b-a767-612388830a69, sourceSystem=ERP
2025-06-09 17:29:20.337 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.337 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72030000, orgName=水气投资部
2025-06-09 17:29:20.425 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007091963301888, orgName=水气投资部
2025-06-09 17:29:20.426 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72030000 有 1 个子记录
2025-06-09 17:29:20.491 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007091963301888, guid=dc458c01-8343-40d8-a7ba-0995c05d8263, sourceSystem=ERP
2025-06-09 17:29:20.492 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.492 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72040000, orgName=固废投资部
2025-06-09 17:29:20.557 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007092613419008, orgName=固废投资部
2025-06-09 17:29:20.558 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72040000 有 1 个子记录
2025-06-09 17:29:20.633 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007092613419008, guid=02a60c31-672b-438b-82de-98d4115ee6f6, sourceSystem=ERP
2025-06-09 17:29:20.634 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.634 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72050000, orgName=环境投资室
2025-06-09 17:29:20.711 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007093209010176, orgName=环境投资室
2025-06-09 17:29:20.712 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72050000 有 1 个子记录
2025-06-09 17:29:20.771 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007093209010176, guid=3e65c79e-f4bc-4235-a099-7d40b33062b8, sourceSystem=ERP
2025-06-09 17:29:20.772 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.772 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72060000, orgName=新材料投资室
2025-06-09 17:29:20.828 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007093787824128, orgName=新材料投资室
2025-06-09 17:29:20.830 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72060000 有 1 个子记录
2025-06-09 17:29:20.896 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007093787824128, guid=9ab78846-b2bb-4282-a813-ee478aebcf0e, sourceSystem=ERP
2025-06-09 17:29:20.897 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:20.898 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X72070000, orgName=智能制造投资室
2025-06-09 17:29:20.983 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007094316306432, orgName=智能制造投资室
2025-06-09 17:29:20.984 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X72070000 有 1 个子记录
2025-06-09 17:29:21.049 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007094316306432, guid=d3a8a37a-da84-4e35-9484-0f69f022b82b, sourceSystem=ERP
2025-06-09 17:29:21.050 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.051 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04210000, orgName=项目管理室
2025-06-09 17:29:21.123 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007094958034944, orgName=项目管理室
2025-06-09 17:29:21.124 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X04210000 有 1 个子记录
2025-06-09 17:29:21.249 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007094958034944, guid=47212cac-a3a8-477d-96bd-c2a76a661fa8, sourceSystem=ERP
2025-06-09 17:29:21.251 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.251 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X52040000, orgName=铁路港口室
2025-06-09 17:29:21.337 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007095801090048, orgName=铁路港口室
2025-06-09 17:29:21.340 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X52040000 有 1 个子记录
2025-06-09 17:29:21.404 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007095801090048, guid=e437f8c1-8753-4cea-b995-b5ba50d1e9a6, sourceSystem=ERP
2025-06-09 17:29:21.404 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.404 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X56010000, orgName=管理室
2025-06-09 17:29:21.471 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007096438624256, orgName=管理室
2025-06-09 17:29:21.472 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X56010000 有 1 个子记录
2025-06-09 17:29:21.552 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007096438624256, guid=4df60b84-7bc2-40bf-9b9b-6ccdf9592847, sourceSystem=ERP
2025-06-09 17:29:21.553 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.554 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08040000, orgName=管理审计室
2025-06-09 17:29:21.631 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007097067769856, orgName=管理审计室
2025-06-09 17:29:21.632 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X08040000 有 1 个子记录
2025-06-09 17:29:21.703 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007097067769856, guid=2ad58620-c06c-4ea6-af41-404cef413467, sourceSystem=ERP
2025-06-09 17:29:21.703 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.704 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08050000, orgName=工程审计室
2025-06-09 17:29:21.764 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007097696915456, orgName=工程审计室
2025-06-09 17:29:21.765 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X08050000 有 1 个子记录
2025-06-09 17:29:21.825 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007097696915456, guid=644d638f-1ab5-4d0c-abda-31cd35f692ab, sourceSystem=ERP
2025-06-09 17:29:21.826 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.826 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64060000, orgName=综合管理室
2025-06-09 17:29:21.909 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007098208620544, orgName=综合管理室
2025-06-09 17:29:21.910 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64060000 有 1 个子记录
2025-06-09 17:29:21.972 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007098208620544, guid=f48e60db-**************-fff276fcdfe9, sourceSystem=ERP
2025-06-09 17:29:21.973 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:21.973 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64070000, orgName=安全环保室
2025-06-09 17:29:22.045 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007098829377536, orgName=安全环保室
2025-06-09 17:29:22.045 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64070000 有 1 个子记录
2025-06-09 17:29:22.110 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007098829377536, guid=d8852691-2737-41ad-a04c-5355868bbd83, sourceSystem=ERP
2025-06-09 17:29:22.110 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:22.111 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080000, orgName=生产计划室
2025-06-09 17:29:22.186 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007099403997184, orgName=生产计划室
2025-06-09 17:29:22.187 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080000 有 1 个子记录
2025-06-09 17:29:22.306 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007099403997184, guid=a13f78fd-e2a1-407c-8aec-967ccc563217, sourceSystem=ERP
2025-06-09 17:29:22.306 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:22.307 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080100, orgName=准备车间
2025-06-09 17:29:22.376 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007100226080768, orgName=准备车间
2025-06-09 17:29:22.377 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080100 有 1 个子记录
2025-06-09 17:29:22.448 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007100226080768, guid=d94083b6-7c42-4721-9505-495bd2de89be, sourceSystem=ERP
2025-06-09 17:29:22.448 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:22.449 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080101, orgName=大车班
2025-06-09 17:29:22.555 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007100821671936, orgName=大车班
2025-06-09 17:29:22.555 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080101 有 1 个子记录
2025-06-09 17:29:22.628 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007100821671936, guid=7164f4de-1d6b-4ad1-98f5-2d73bb4198d7, sourceSystem=ERP
2025-06-09 17:29:22.629 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:22.629 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080102, orgName=导卫班
2025-06-09 17:29:22.708 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007101576646656, orgName=导卫班
2025-06-09 17:29:22.708 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080102 有 1 个子记录
2025-06-09 17:29:22.897 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007101576646656, guid=50a0a2a0-4db2-4652-a716-2b57d7905f37, sourceSystem=ERP
2025-06-09 17:29:22.902 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:22.903 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080200, orgName=甲作业区
2025-06-09 17:29:23.080 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007102738468864, orgName=甲作业区
2025-06-09 17:29:23.081 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080200 有 1 个子记录
2025-06-09 17:29:23.187 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007102738468864, guid=94610d63-930c-44a0-8dbe-4e6db665d0a5, sourceSystem=ERP
2025-06-09 17:29:23.188 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.191 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080201, orgName=加热班
2025-06-09 17:29:23.254 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007103938039808, orgName=加热班
2025-06-09 17:29:23.256 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080201 有 1 个子记录
2025-06-09 17:29:23.310 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007103938039808, guid=2970c3eb-80c9-4b62-995c-898ecea71636, sourceSystem=ERP
2025-06-09 17:29:23.311 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.311 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080202, orgName=轧机班
2025-06-09 17:29:23.378 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007104437161984, orgName=轧机班
2025-06-09 17:29:23.378 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080202 有 1 个子记录
2025-06-09 17:29:23.437 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007104437161984, guid=5c465353-e869-4254-a270-70c98768eeda, sourceSystem=ERP
2025-06-09 17:29:23.437 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.438 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080204, orgName=成品班
2025-06-09 17:29:23.505 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007104969838592, orgName=成品班
2025-06-09 17:29:23.505 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080204 有 1 个子记录
2025-06-09 17:29:23.576 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007104969838592, guid=b304ce4c-0695-4117-bd0a-8f1b4b736267, sourceSystem=ERP
2025-06-09 17:29:23.576 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.576 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080203, orgName=行车班
2025-06-09 17:29:23.636 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007105548652544, orgName=行车班
2025-06-09 17:29:23.637 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080203 有 1 个子记录
2025-06-09 17:29:23.699 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007105548652544, guid=a41fea23-9ade-4cf6-a81c-e6c0bd22902f, sourceSystem=ERP
2025-06-09 17:29:23.699 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.699 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080300, orgName=乙作业区
2025-06-09 17:29:23.761 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007106064551936, orgName=乙作业区
2025-06-09 17:29:23.762 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080300 有 1 个子记录
2025-06-09 17:29:23.838 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007106064551936, guid=ccc4768b-2229-46cc-a0ac-4c4d8ef9411e, sourceSystem=ERP
2025-06-09 17:29:23.839 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.839 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080301, orgName=加热班
2025-06-09 17:29:23.902 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007106651754496, orgName=加热班
2025-06-09 17:29:23.902 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080301 有 1 个子记录
2025-06-09 17:29:23.975 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007106651754496, guid=183f68d4-0f16-4c6f-ad65-23fdfc2eed83, sourceSystem=ERP
2025-06-09 17:29:23.976 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:23.977 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080302, orgName=轧机班
2025-06-09 17:29:24.041 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007107234762752, orgName=轧机班
2025-06-09 17:29:24.041 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080302 有 1 个子记录
2025-06-09 17:29:24.100 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007107234762752, guid=d031b170-d3fb-4ddb-81f7-f7cd26269ed9, sourceSystem=ERP
2025-06-09 17:29:24.101 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.101 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080304, orgName=成品班
2025-06-09 17:29:24.162 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007107750662144, orgName=成品班
2025-06-09 17:29:24.162 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080304 有 1 个子记录
2025-06-09 17:29:24.228 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007107750662144, guid=eeaf8185-83ea-45dc-93ac-5b836d571d57, sourceSystem=ERP
2025-06-09 17:29:24.228 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.228 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080303, orgName=行车班
2025-06-09 17:29:24.293 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007108283338752, orgName=行车班
2025-06-09 17:29:24.294 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080303 有 1 个子记录
2025-06-09 17:29:24.360 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007108283338752, guid=f3e526bb-c657-47c4-b912-c3cf7fc088c5, sourceSystem=ERP
2025-06-09 17:29:24.361 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.361 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080400, orgName=丙作业区
2025-06-09 17:29:24.424 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007108841181184, orgName=丙作业区
2025-06-09 17:29:24.425 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080400 有 1 个子记录
2025-06-09 17:29:24.484 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007108841181184, guid=28062bf6-bc0d-46a7-8695-19299568fe1a, sourceSystem=ERP
2025-06-09 17:29:24.484 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.485 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080401, orgName=加热班
2025-06-09 17:29:24.581 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007109361274880, orgName=加热班
2025-06-09 17:29:24.581 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080401 有 1 个子记录
2025-06-09 17:29:24.649 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007109361274880, guid=246be90b-82c6-4119-85db-e27e6f154576, sourceSystem=ERP
2025-06-09 17:29:24.650 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.650 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080402, orgName=轧机班
2025-06-09 17:29:24.707 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007110053335040, orgName=轧机班
2025-06-09 17:29:24.707 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080402 有 1 个子记录
2025-06-09 17:29:24.762 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007110053335040, guid=59e2b58c-3229-4d27-be83-29f9b044318b, sourceSystem=ERP
2025-06-09 17:29:24.763 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.763 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080404, orgName=成品班
2025-06-09 17:29:24.837 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007110527291392, orgName=成品班
2025-06-09 17:29:24.837 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080404 有 1 个子记录
2025-06-09 17:29:24.911 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007110527291392, guid=f4663a7b-3564-41bd-90e6-8e9138e0efbd, sourceSystem=ERP
2025-06-09 17:29:24.911 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:24.913 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080403, orgName=行车班
2025-06-09 17:29:24.977 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007111160631296, orgName=行车班
2025-06-09 17:29:24.977 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080403 有 1 个子记录
2025-06-09 17:29:25.035 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007111160631296, guid=aa4502a6-7084-400d-ab64-52a704227ea3, sourceSystem=ERP
2025-06-09 17:29:25.036 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:25.036 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080500, orgName=丁作业区
2025-06-09 17:29:25.109 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007111672336384, orgName=丁作业区
2025-06-09 17:29:25.110 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080500 有 1 个子记录
2025-06-09 17:29:25.220 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007111672336384, guid=18a043f1-4d9b-4aef-a2f2-bcff2d56487e, sourceSystem=ERP
2025-06-09 17:29:25.221 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:25.221 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080501, orgName=加热班
2025-06-09 17:29:25.285 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007112448282624, orgName=加热班
2025-06-09 17:29:25.286 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080501 有 1 个子记录
2025-06-09 17:29:25.380 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007112448282624, guid=f0b2fd1f-edd5-4985-9368-d2e30cc5eb1b, sourceSystem=ERP
2025-06-09 17:29:25.380 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:25.380 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080502, orgName=轧机班
2025-06-09 17:29:25.566 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007113115176960, orgName=轧机班
2025-06-09 17:29:25.566 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080502 有 1 个子记录
2025-06-09 17:29:25.779 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007113115176960, guid=6c26aeb6-dde3-4f25-868f-505d9a201eb7, sourceSystem=ERP
2025-06-09 17:29:25.779 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:25.779 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080504, orgName=成品班
2025-06-09 17:29:25.957 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007114788704256, orgName=成品班
2025-06-09 17:29:25.957 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080504 有 1 个子记录
2025-06-09 17:29:26.020 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007114788704256, guid=59883091-b169-4e65-b5be-0d8fa962a456, sourceSystem=ERP
2025-06-09 17:29:26.021 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.021 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64080503, orgName=行车班
2025-06-09 17:29:26.103 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007115807920128, orgName=行车班
2025-06-09 17:29:26.104 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64080503 有 1 个子记录
2025-06-09 17:29:26.181 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007115807920128, guid=a612a8c0-9f17-4886-907c-19997d8967cc, sourceSystem=ERP
2025-06-09 17:29:26.182 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.186 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090000, orgName=设备管理室
2025-06-09 17:29:26.273 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007116516757504, orgName=设备管理室
2025-06-09 17:29:26.274 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090000 有 1 个子记录
2025-06-09 17:29:26.347 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007116516757504, guid=cb26aa16-29dc-4a61-a8e5-bad9c694d79b, sourceSystem=ERP
2025-06-09 17:29:26.348 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.348 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090100, orgName=电修车间
2025-06-09 17:29:26.411 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007117175263232, orgName=电修车间
2025-06-09 17:29:26.412 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090100 有 1 个子记录
2025-06-09 17:29:26.474 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007117175263232, guid=4ccead87-c888-4f9d-a4de-0249e2a6d7c0, sourceSystem=ERP
2025-06-09 17:29:26.475 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.475 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090102, orgName=交流电工班
2025-06-09 17:29:26.531 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007117707939840, orgName=交流电工班
2025-06-09 17:29:26.531 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090102 有 1 个子记录
2025-06-09 17:29:26.589 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007117707939840, guid=167b1dfe-d7a7-4eba-b58d-ecf98d853b30, sourceSystem=ERP
2025-06-09 17:29:26.589 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.589 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090103, orgName=直流电工班
2025-06-09 17:29:26.665 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007118190284800, orgName=直流电工班
2025-06-09 17:29:26.665 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090103 有 1 个子记录
2025-06-09 17:29:26.733 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007118190284800, guid=4c687d08-ceb9-45e8-8c0e-c09220272922, sourceSystem=ERP
2025-06-09 17:29:26.734 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.734 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090200, orgName=机修车间
2025-06-09 17:29:26.810 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007118794264576, orgName=机修车间
2025-06-09 17:29:26.810 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090200 有 1 个子记录
2025-06-09 17:29:26.873 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007118794264576, guid=302e2a9a-a700-465c-b385-b572906198b1, sourceSystem=ERP
2025-06-09 17:29:26.873 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.874 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090201, orgName=钳一班
2025-06-09 17:29:26.934 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007119385661440, orgName=钳一班
2025-06-09 17:29:26.935 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090201 有 1 个子记录
2025-06-09 17:29:26.994 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007119385661440, guid=f113f8fe-24ff-466c-a0af-6548ebbfc704, sourceSystem=ERP
2025-06-09 17:29:26.995 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:26.996 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090202, orgName=钳二班
2025-06-09 17:29:27.058 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007119893172224, orgName=钳二班
2025-06-09 17:29:27.058 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090202 有 1 个子记录
2025-06-09 17:29:27.118 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007119893172224, guid=bd1c9fc2-4a57-4050-8633-da39fea508cc, sourceSystem=ERP
2025-06-09 17:29:27.118 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.119 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X64090203, orgName=钳工值班班
2025-06-09 17:29:27.187 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007120417460224, orgName=钳工值班班
2025-06-09 17:29:27.187 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X64090203 有 1 个子记录
2025-06-09 17:29:27.259 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007120417460224, guid=c8172c88-f616-4732-aab4-41ecb5ac167e, sourceSystem=ERP
2025-06-09 17:29:27.301 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.301 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04220000, orgName=数字化战略变革管理室
2025-06-09 17:29:27.439 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007121172434944, orgName=数字化战略变革管理室
2025-06-09 17:29:27.439 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X04220000 有 1 个子记录
2025-06-09 17:29:27.522 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007121172434944, guid=4fc963a2-be84-4995-abdd-eb7c24d412e5, sourceSystem=ERP
2025-06-09 17:29:27.524 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.525 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X08060000, orgName=数智审计室
2025-06-09 17:29:27.592 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007122120347648, orgName=数智审计室
2025-06-09 17:29:27.592 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X08060000 有 1 个子记录
2025-06-09 17:29:27.684 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007122120347648, guid=6ece8b13-9e8f-4aff-ac2b-09e9037800d3, sourceSystem=ERP
2025-06-09 17:29:27.686 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.686 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04230000, orgName=数据服务管理室
2025-06-09 17:29:27.752 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007122791436288, orgName=数据服务管理室
2025-06-09 17:29:27.753 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X04230000 有 1 个子记录
2025-06-09 17:29:27.823 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007122791436288, guid=49c69ea1-8600-4e44-8bf3-b3aae6cbcbe8, sourceSystem=ERP
2025-06-09 17:29:27.825 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.825 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X04240000, orgName=数字技术研究所
2025-06-09 17:29:27.890 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007123374444544, orgName=数字技术研究所
2025-06-09 17:29:27.890 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X04240000 有 1 个子记录
2025-06-09 17:29:27.948 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007123374444544, guid=423135a8-db68-44dc-84d6-ce4e7d80ae0d, sourceSystem=ERP
2025-06-09 17:29:27.949 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:27.949 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100702, orgName=电工二班
2025-06-09 17:29:28.013 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007123890343936, orgName=电工二班
2025-06-09 17:29:28.014 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100702 有 1 个子记录
2025-06-09 17:29:28.075 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007123890343936, guid=17a8447c-32f4-4b98-afe5-72003ba1ef72, sourceSystem=ERP
2025-06-09 17:29:28.075 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:28.075 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100602, orgName=电工班
2025-06-09 17:29:28.148 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007124418826240, orgName=电工班
2025-06-09 17:29:28.148 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100602 有 1 个子记录
2025-06-09 17:29:28.209 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007124418826240, guid=b62c28a1-cfb2-4b81-abc8-3154a606131f, sourceSystem=ERP
2025-06-09 17:29:28.209 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:28.209 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100500, orgName=电修一车间
2025-06-09 17:29:28.277 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007124980862976, orgName=电修一车间
2025-06-09 17:29:28.277 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100500 有 1 个子记录
2025-06-09 17:29:28.353 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007124980862976, guid=a20f0cf8-a758-4655-9536-2c6d4fca74ac, sourceSystem=ERP
2025-06-09 17:29:28.353 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:28.353 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100600, orgName=电修二车间
2025-06-09 17:29:28.447 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007125584842752, orgName=电修二车间
2025-06-09 17:29:28.447 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100600 有 1 个子记录
2025-06-09 17:29:28.506 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007125584842752, guid=2f331dd6-c600-48f5-84bd-dec5c6730dad, sourceSystem=ERP
2025-06-09 17:29:28.506 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:28.506 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100700, orgName=电修三车间
2025-06-09 17:29:28.568 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007126226571264, orgName=电修三车间
2025-06-09 17:29:28.569 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100700 有 1 个子记录
2025-06-09 17:29:28.737 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007126226571264, guid=97ed611e-b6a2-4234-a888-c65c90517615, sourceSystem=ERP
2025-06-09 17:29:28.737 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:28.737 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100800, orgName=检修车间
2025-06-09 17:29:28.910 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007127195455488, orgName=检修车间
2025-06-09 17:29:28.910 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100800 有 1 个子记录
2025-06-09 17:29:29.161 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007127195455488, guid=78235e13-9e90-4dd5-b9ff-a329b1220342, sourceSystem=ERP
2025-06-09 17:29:29.162 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:29.162 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100501, orgName=自动化班
2025-06-09 17:29:29.436 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007128978034688, orgName=自动化班
2025-06-09 17:29:29.437 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100501 有 1 个子记录
2025-06-09 17:29:29.505 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007128978034688, guid=0499f19f-7471-49c4-87ab-ccc647e30f07, sourceSystem=ERP
2025-06-09 17:29:29.506 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:29.507 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100502, orgName=电工班
2025-06-09 17:29:29.581 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007130425069568, orgName=电工班
2025-06-09 17:29:29.582 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100502 有 1 个子记录
2025-06-09 17:29:29.643 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007130425069568, guid=a5a73ecf-fb21-412f-8eb4-05bfa022757d, sourceSystem=ERP
2025-06-09 17:29:29.644 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:29.645 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100701, orgName=电工一班
2025-06-09 17:29:29.729 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007131003883520, orgName=电工一班
2025-06-09 17:29:29.729 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100701 有 1 个子记录
2025-06-09 17:29:29.791 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007131003883520, guid=19f155f7-70cc-4e0b-b544-91e2a75c3f9d, sourceSystem=ERP
2025-06-09 17:29:29.791 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:29.791 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200000, orgName=数字应用研究院.
2025-06-09 17:29:29.865 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007131616251904, orgName=数字应用研究院.
2025-06-09 17:29:29.865 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 XBB200000 有 1 个子记录
2025-06-09 17:29:29.943 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007131616251904, guid=07b28eeb-c953-4f02-99cc-26c727e875c0, sourceSystem=ERP
2025-06-09 17:29:29.943 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:29.943 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200100, orgName=项目管理室
2025-06-09 17:29:30.006 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007132253786112, orgName=项目管理室
2025-06-09 17:29:30.006 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 XBB200100 有 1 个子记录
2025-06-09 17:29:30.068 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007132253786112, guid=d4cfedd8-a39c-43b7-b569-8c8adae3ba7d, sourceSystem=ERP
2025-06-09 17:29:30.070 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:30.072 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200200, orgName=数字技术研究所
2025-06-09 17:29:30.144 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007132794851328, orgName=数字技术研究所
2025-06-09 17:29:30.146 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 XBB200200 有 1 个子记录
2025-06-09 17:29:30.206 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007132794851328, guid=c40383ce-308e-42cc-8626-c2b6284620fd, sourceSystem=ERP
2025-06-09 17:29:30.206 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:30.206 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200300, orgName=数据服务管理室
2025-06-09 17:29:30.266 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007133356888064, orgName=数据服务管理室
2025-06-09 17:29:30.267 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 XBB200300 有 1 个子记录
2025-06-09 17:29:30.330 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007133356888064, guid=6eae1714-2454-4601-8b50-c487a862fdd9, sourceSystem=ERP
2025-06-09 17:29:30.330 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:30.330 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=XBB200400, orgName=数字化战略变革管理室
2025-06-09 17:29:30.385 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007133876981760, orgName=数字化战略变革管理室
2025-06-09 17:29:30.386 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 XBB200400 有 1 个子记录
2025-06-09 17:29:30.462 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007133876981760, guid=3bcd64af-66e3-4089-9ec3-2f868713eb72, sourceSystem=ERP
2025-06-09 17:29:30.462 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:30.462 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 正在处理部门: orgCode=X47100000, orgName=检修厂
2025-06-09 17:29:30.529 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据已保存到 t_org_structure 表: id=1932007134430629888, orgName=检修厂
2025-06-09 17:29:30.530 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门子表数据，部门 X47100000 有 1 个子记录
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据已保存到 t_department_child 表: deptId=1932007134430629888, guid=65b309f3-7c6e-4fea-961d-569ccd53707e, sourceSystem=ERP
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门子表数据同步完成，共处理 1 条记录到 t_department_child 表
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步汇总: 主表(t_org_structure)=69 条, 子表(t_department_child)=69 条
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 部门数据同步完成，共处理 69 个部门
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步员工数据，时间范围: 2024-07-01T00:00 - 2024-07-31T23:59:59
2025-06-09 17:29:30.596 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取员工数据，时间范围: 2024-07-01T00:00 - 2024-07-31T23:59:59
2025-06-09 17:29:30.597 [http-nio-8285-exec-3] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/employees?startDate=2024-07-01%2000:00:00&endDate=2024-07-31%2023:59:59
