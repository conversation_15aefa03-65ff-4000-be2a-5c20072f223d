/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:42
*/


-- ----------------------------
-- Table structure for t_data_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_permission";
CREATE TABLE "public"."t_data_permission" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int4,
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_permission"."name" IS '名称';
COMMENT ON COLUMN "public"."t_data_permission"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_data_permission"."module_identifier" IS '模块标识 与菜单模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_permission"."data_type" IS '数据类型，1检签';
COMMENT ON COLUMN "public"."t_data_permission"."order_info" IS '排序序号，从1开始';
COMMENT ON COLUMN "public"."t_data_permission"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_data_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_permission"."data_identifier" IS '数据标识页面输入或表同步';
COMMENT ON TABLE "public"."t_data_permission" IS '数据权限信息列表';

-- ----------------------------
-- Records of t_data_permission
-- ----------------------------
INSERT INTO "public"."t_data_permission" VALUES (11002, '员工薪资信息', 0, 'employee_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-20 16:24:57.451389', 'employee_salary_info1');
INSERT INTO "public"."t_data_permission" VALUES (1935984357512712192, '测试数据', NULL, 'aaa', NULL, 1, 'f', 'f', '2025-06-20 16:53:34.268888', '2025-06-20 17:15:37.925125', '1_12_2');
INSERT INTO "public"."t_data_permission" VALUES (1936196765900476416, '工艺文件', NULL, 'knowledge', NULL, 1, 'f', 'f', '2025-06-21 06:57:36.385525', '2025-06-21 07:00:24.712872', 'process_file');
INSERT INTO "public"."t_data_permission" VALUES (1936199663568949248, '文献', NULL, 'knowledge', NULL, 1, 'f', 'f', '2025-06-21 07:09:07.259796', '2025-06-21 07:09:07.259796', 'article');
INSERT INTO "public"."t_data_permission" VALUES (1936703665293365248, '工艺规程', NULL, 'knowledge', NULL, 1, 'f', 'f', '2025-06-22 16:31:50.623654', '2025-06-22 16:32:02.483216', 'kn_process');
INSERT INTO "public"."t_data_permission" VALUES (11001, '员工基本信息', 0, 'employee_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11003, '员工绩效数据', 0, 'employee_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_performance_data');
INSERT INTO "public"."t_data_permission" VALUES (11004, '员工考勤记录', 0, 'employee_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'employee_attendance_record');
INSERT INTO "public"."t_data_permission" VALUES (11005, '部门基本信息', 0, 'department_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11006, '部门预算数据', 0, 'department_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_budget_data');
INSERT INTO "public"."t_data_permission" VALUES (11007, '部门人员统计', 0, 'department_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_staff_statistics');
INSERT INTO "public"."t_data_permission" VALUES (11008, '部门绩效指标', 0, 'department_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'department_performance_kpi');
INSERT INTO "public"."t_data_permission" VALUES (11009, '项目基本信息', 0, 'project_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11010, '项目进度数据', 0, 'project_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_progress_data');
INSERT INTO "public"."t_data_permission" VALUES (11011, '项目成本数据', 0, 'project_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_cost_data');
INSERT INTO "public"."t_data_permission" VALUES (11012, '项目质量报告', 0, 'project_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'project_quality_report');
INSERT INTO "public"."t_data_permission" VALUES (11013, '客户基本资料', 0, 'customer_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_basic_profile');
INSERT INTO "public"."t_data_permission" VALUES (11014, '客户联系记录', 0, 'customer_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_contact_record');
INSERT INTO "public"."t_data_permission" VALUES (11015, '客户交易历史', 0, 'customer_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_transaction_history');
INSERT INTO "public"."t_data_permission" VALUES (11016, '客户信用评级', 0, 'customer_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'customer_credit_rating');
INSERT INTO "public"."t_data_permission" VALUES (11017, '合同基本信息', 0, 'contract_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_basic_info');
INSERT INTO "public"."t_data_permission" VALUES (11018, '合同条款内容', 0, 'contract_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_terms_content');
INSERT INTO "public"."t_data_permission" VALUES (11019, '合同执行状态', 0, 'contract_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_execution_status');
INSERT INTO "public"."t_data_permission" VALUES (11020, '合同财务数据', 0, 'contract_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'contract_financial_data');
INSERT INTO "public"."t_data_permission" VALUES (11021, '业务运营报表', 0, 'report_data_module', 1, 1, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'business_operation_report');
INSERT INTO "public"."t_data_permission" VALUES (11022, '财务分析报表', 0, 'report_data_module', 1, 2, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'financial_analysis_report');
INSERT INTO "public"."t_data_permission" VALUES (11023, '人力资源报表', 0, 'report_data_module', 1, 3, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'hr_statistics_report');
INSERT INTO "public"."t_data_permission" VALUES (11024, '管理决策报表', 0, 'report_data_module', 1, 4, 'f', 'f', '2025-06-11 01:58:49.568678', '2025-06-11 01:58:49.568678', 'management_decision_report');
INSERT INTO "public"."t_data_permission" VALUES (1932722647272132608, 'vv', NULL, 'employee_data_module', NULL, 1, 'f', 'f', '2025-06-11 16:52:41.967741', '2025-06-11 16:55:10.592304', 'aaa');
INSERT INTO "public"."t_data_permission" VALUES (1936197267178524672, '标准', NULL, 'knowledge', NULL, 1, 'f', 'f', '2025-06-21 06:59:35.915775', '2025-06-21 06:59:35.915775', 'standard_file');
INSERT INTO "public"."t_data_permission" VALUES (1936615898907545600, '分厂规范', NULL, 'knowledge', NULL, 1, 'f', 'f', '2025-06-22 10:43:05.496436', '2025-06-22 10:44:11.008811', 'art');

-- ----------------------------
-- Primary Key structure for table t_data_permission
-- ----------------------------
ALTER TABLE "public"."t_data_permission" ADD CONSTRAINT "t_data_permission_pk" PRIMARY KEY ("id");
