package com.dfit.percode.exception;

import com.dfit.percode.vo.response.UsageCheckResponseVO;

/**
 * 使用冲突异常
 * 用于删除操作时检测到使用情况的场景
 * 
 * <AUTHOR>
 * @date 2024-06-30
 */
public class UsageConflictException extends RuntimeException {
    
    private final UsageCheckResponseVO usageInfo;

    public UsageConflictException(String message, UsageCheckResponseVO usageInfo) {
        super(message);
        this.usageInfo = usageInfo;
    }

    public UsageCheckResponseVO getUsageInfo() {
        return usageInfo;
    }
}
