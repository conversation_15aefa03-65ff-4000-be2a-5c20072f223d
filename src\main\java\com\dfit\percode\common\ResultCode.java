package com.dfit.percode.common;

public enum ResultCode {
    /**
     * 规范响应体中的响应码和响应信息
     */
    OK(200, "操作成功"),

    FAIL(500, "操作失败"),

    ERROR(502, "未知错误"),

    SUCCESS(200, "成功"),
    FAILED(400, "失败"),
    UNAUTHORIZED(401, "暂未登录或token已经过期"),
    FORBIDDEN(403, "没有相关权限"),

    VALIDATE_FAILED(10000, "参数检验失败"),
    DATA_TYPE_ERROR(20000, "数据格式转换错误"),
    UNKNOWN_ERROR(40000, "未知错误,请联系管理员");

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    ResultCode(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
