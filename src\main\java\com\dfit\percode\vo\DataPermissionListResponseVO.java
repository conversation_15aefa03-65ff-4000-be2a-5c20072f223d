package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据权限列表响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataPermissionListResponseVO", description = "数据权限列表响应数据")
public class DataPermissionListResponseVO {
    
    @ApiModelProperty(value = "数据权限ID", example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "数据权限名称", example = "用户基础数据")
    private String name;
    
    @ApiModelProperty(value = "父级ID", example = "0")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;
    
    @ApiModelProperty(value = "模块标识", example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "模块名称", example = "用户数据模块")
    private String moduleName;
    
    @ApiModelProperty(value = "数据类型", example = "1")
    private Integer dataType;
    
    @ApiModelProperty(value = "数据标识", example = "user_basic_data")
    private String dataIdentifier;
    
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "是否禁用", example = "false")
    private Boolean isDisable;
    
    @ApiModelProperty(value = "创建时间", example = "2023-05-15 14:30:00")
    private String createTime;

    @ApiModelProperty(value = "修改时间", example = "2023-05-15 14:30:00")
    private String modifyTime;

    @ApiModelProperty(value = "已配置的操作类型列表", example = "[1, 2, 3]")
    private List<Integer> operateTypes;
}
