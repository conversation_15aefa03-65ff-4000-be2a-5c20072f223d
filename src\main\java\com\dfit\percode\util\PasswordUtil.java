package com.dfit.percode.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.DigestUtils;

import java.security.SecureRandom;
import java.util.Random;

/**
 * 密码工具类
 * 提供MD5加密、默认密码生成、密码验证等功能
 *
 * <AUTHOR> Assistant
 * @date 2025-07-02
 */
@Slf4j
public class PasswordUtil {

    /**
     * 默认密码长度
     */
    private static final int DEFAULT_PASSWORD_LENGTH = 12;

    /**
     * 固定的复杂默认密码
     * 当前设置为：Admin@123456
     */
    private static final String FIXED_DEFAULT_PASSWORD = "Admin@123456";

    /**
     * 密码字符集：大写字母 + 小写字母 + 数字 + 特殊字符
     */
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$%^&*";
    private static final String ALL_CHARS = UPPERCASE + LOWERCASE + DIGITS + SPECIAL_CHARS;

    /**
     * 安全随机数生成器
     */
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * MD5加密密码
     *
     * @param plainPassword 明文密码
     * @return MD5加密后的密码
     */
    public static String encryptPassword(String plainPassword) {
        if (plainPassword == null || plainPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }

        try {
            String md5Password = DigestUtils.md5DigestAsHex(plainPassword.getBytes("UTF-8"));
            log.debug("密码MD5加密成功");
            return md5Password;
        } catch (Exception e) {
            log.error("密码MD5加密失败", e);
            throw new RuntimeException("密码加密失败", e);
        }
    }

    /**
     * 验证密码
     *
     * @param plainPassword 明文密码
     * @param encryptedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean verifyPassword(String plainPassword, String encryptedPassword) {
        if (plainPassword == null || encryptedPassword == null) {
            return false;
        }

        try {
            String inputEncrypted = encryptPassword(plainPassword);
            boolean matches = inputEncrypted.equals(encryptedPassword);
            log.debug("密码验证结果: {}", matches);
            return matches;
        } catch (Exception e) {
            log.error("密码验证失败", e);
            return false;
        }
    }

    /**
     * 生成固定的复杂默认密码
     * 使用固定密码：Admin@123456
     * 符合复杂度要求：包含大小写字母、数字、特殊字符
     *
     * @return 固定的默认密码
     */
    public static String generateDefaultPassword() {
        return FIXED_DEFAULT_PASSWORD;
    }

    /**
     * 生成指定长度的复杂默认密码
     *
     * @param length 密码长度（最小4位）
     * @return 生成的复杂密码
     */
    public static String generateDefaultPassword(int length) {
        if (length < 4) {
            throw new IllegalArgumentException("密码长度不能少于4位");
        }

        StringBuilder password = new StringBuilder();

        // 确保至少包含每种类型的字符
        password.append(getRandomChar(UPPERCASE));
        password.append(getRandomChar(LOWERCASE));
        password.append(getRandomChar(DIGITS));
        password.append(getRandomChar(SPECIAL_CHARS));

        // 填充剩余长度
        for (int i = 4; i < length; i++) {
            password.append(getRandomChar(ALL_CHARS));
        }

        // 打乱字符顺序
        return shuffleString(password.toString());
    }

    /**
     * 从字符集中随机选择一个字符
     *
     * @param chars 字符集
     * @return 随机字符
     */
    private static char getRandomChar(String chars) {
        return chars.charAt(SECURE_RANDOM.nextInt(chars.length()));
    }

    /**
     * 打乱字符串顺序
     *
     * @param input 输入字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String input) {
        char[] chars = input.toCharArray();
        for (int i = chars.length - 1; i > 0; i--) {
            int j = SECURE_RANDOM.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }

    /**
     * 检查密码复杂度
     *
     * @param password 密码
     * @return 是否符合复杂度要求
     */
    public static boolean isComplexPassword(String password) {
        if (password == null || password.length() < 8) {
            return false;
        }

        boolean hasUpper = false;
        boolean hasLower = false;
        boolean hasDigit = false;
        boolean hasSpecial = false;

        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            } else if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (SPECIAL_CHARS.indexOf(c) >= 0) {
                hasSpecial = true;
            }
        }

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }
}
