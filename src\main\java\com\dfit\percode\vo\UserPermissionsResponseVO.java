package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户权限响应VO类
 * 用于权限查询接口的响应数据
 * 包含菜单权限和按钮权限
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserPermissionsResponseVO", description = "用户权限响应数据")
public class UserPermissionsResponseVO {

    @ApiModelProperty(value = "用户ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userid;

    @ApiModelProperty(value = "权限数据")
    private PermissionsData permissions;

    /**
     * 权限数据内部类
     */
    @Data
    @ApiModel(value = "PermissionsData", description = "权限数据")
    public static class PermissionsData {

        @ApiModelProperty(value = "菜单权限列表（树形结构）")
        private List<MenuPermissionVO> menus;

        @ApiModelProperty(value = "按钮权限列表（平铺结构）")
        private List<ButtonPermissionVO> buttons;

        @ApiModelProperty(value = "数据权限列表（树形结构）")
        private List<DataPermissionVO> dataPermissions;
    }

    /**
     * 菜单权限VO类
     */
    @Data
    @ApiModel(value = "MenuPermissionVO", description = "菜单权限数据")
    public static class MenuPermissionVO {

        @ApiModelProperty(value = "菜单ID", example = "9001")
        private String id;

        @ApiModelProperty(value = "菜单名称", example = "系统管理")
        private String name;

        @ApiModelProperty(value = "父菜单ID", example = "0")
        private String preId;

        @ApiModelProperty(value = "菜单类型", example = "1")
        private Integer menuType;

        @ApiModelProperty(value = "路由地址", example = "/system")
        private String routeAddress;

        @ApiModelProperty(value = "组件路径", example = "Layout")
        private String componentPath;

        @ApiModelProperty(value = "权限标识", example = "system_management_view")
        private String permissionIdentifier;

        @ApiModelProperty(value = "路由参数")
        private String routeParam;

        @ApiModelProperty(value = "父级名称")
        private String preName;

        @ApiModelProperty(value = "子菜单列表")
        private List<MenuPermissionVO> children;
    }

    /**
     * 按钮权限VO类
     */
    @Data
    @ApiModel(value = "ButtonPermissionVO", description = "按钮权限数据")
    public static class ButtonPermissionVO {

        @ApiModelProperty(value = "按钮ID", example = "9004")
        private String id;

        @ApiModelProperty(value = "按钮名称", example = "配置查看")
        private String name;

        @ApiModelProperty(value = "权限标识", example = "system_config_view")
        private String permissionIdentifier;
    }

    /**
     * 数据权限VO类
     */
    @Data
    @ApiModel(value = "DataPermissionVO", description = "数据权限数据")
    public static class DataPermissionVO {

        @ApiModelProperty(value = "数据权限ID", example = "11001")
        private String id;

        @ApiModelProperty(value = "数据权限名称", example = "员工基本信息")
        private String name;

        @ApiModelProperty(value = "父级ID", example = "0")
        private String preId;

        @ApiModelProperty(value = "模块标识符", example = "employee.data.module")
        private String moduleIdentifier;

        @ApiModelProperty(value = "数据标识符", example = "employee.basic.info")
        private String dataIdentifier;

        @ApiModelProperty(value = "支持的操作类型", example = "[1, 2, 4]", notes = "1-查看，2-修改，3-下载，4-删除")
        private List<Integer> operations;

        @ApiModelProperty(value = "操作类型名称", example = "[\"查看\", \"修改\", \"删除\"]")
        private List<String> operationNames;

        @ApiModelProperty(value = "子数据权限列表")
        private List<DataPermissionVO> children;
    }
}