package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 删除用户请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持逻辑删除用户及其关联数据
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteUserRequestVO", description = "删除用户请求参数")
public class DeleteUserRequestVO {
    
    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
}
