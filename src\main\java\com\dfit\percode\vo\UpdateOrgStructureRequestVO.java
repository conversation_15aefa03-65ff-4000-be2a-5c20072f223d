package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改部门请求VO类
 * 主要用于部门重命名功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UpdateOrgStructureRequestVO", description = "修改部门请求参数")
public class UpdateOrgStructureRequestVO {

    @ApiModelProperty(value = "部门ID", example = "1002", required = true)
    // @NotNull(message = "部门ID不能为空")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long id;

    @ApiModelProperty(value = "新的组织名称", example = "技术研发部", required = true)
    // @NotBlank(message = "组织名称不能为空")
    private String organName;

    @ApiModelProperty(value = "排序序号", example = "2")
    private Integer orderInfo;

    @ApiModelProperty(value = "数据来源，1页面输入，2数据同步", example = "1")
    private Integer dataSource;
}
