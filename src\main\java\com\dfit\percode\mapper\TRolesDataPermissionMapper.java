package com.dfit.percode.mapper;

import com.dfit.percode.entity.TRolesDataPermission;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 角色数据权限关联表 Mapper接口
 * 用于管理角色与数据权限的关联关系
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface TRolesDataPermissionMapper extends BaseMapper<TRolesDataPermission> {

    /**
     * 插入角色数据权限关联
     * 使用数据库函数处理时间字段，避免类型转换问题
     *
     * @param id 关联ID
     * @param roleId 角色ID
     * @param moduleIdentifier 模块标识
     * @param dataType 数据类型
     * @param dataId 数据ID
     * @param operateType 操作类型
     * @param dataOperateId 数据id与操作类型拼接字符串
     */
    @Insert("INSERT INTO t_roles_data_permission " +
            "(id, role_id, module_identifier, data_type, data_id, operate_type, data_operate_id, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{roleId}, #{moduleIdentifier}, #{dataType}, #{dataId}, #{operateType}, #{dataOperateId}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertRoleDataPermission(@Param("id") Long id,
                                  @Param("roleId") Long roleId,
                                  @Param("moduleIdentifier") String moduleIdentifier,
                                  @Param("dataType") Integer dataType,
                                  @Param("dataId") Long dataId,
                                  @Param("operateType") Integer operateType,
                                  @Param("dataOperateId") String dataOperateId);

    /**
     * 删除角色的所有数据权限
     * 逻辑删除，设置is_del=true
     *
     * @param roleId 角色ID
     */
    @Delete("UPDATE t_roles_data_permission SET is_del = true, modify_time = CURRENT_TIMESTAMP " +
            "WHERE role_id = #{roleId}")
    void deleteRoleDataPermissions(@Param("roleId") Long roleId);

    /**
     * 物理删除角色的所有数据权限
     * 用于彻底清理数据
     *
     * @param roleId 角色ID
     */
    @Delete("DELETE FROM t_roles_data_permission WHERE role_id = #{roleId}")
    void physicalDeleteRoleDataPermissions(@Param("roleId") Long roleId);

    /**
     * 查找模块级别的角色数据权限记录（用于级联删除）
     * 根据模块标识符和操作类型查找相关的角色权限记录
     * 通过data_id关联t_data_permission表来查找模块下的数据权限
     *
     * @param moduleIdentifier 模块标识符
     * @param operateTypes 操作类型列表
     * @return 角色数据权限记录列表
     */
    @Select("<script>" +
            "SELECT rdp.* FROM t_roles_data_permission rdp " +
            "JOIN t_data_permission dp ON rdp.data_id = dp.id " +
            "WHERE dp.module_identifier = #{moduleIdentifier} " +
            "AND rdp.operate_type IN " +
            "<foreach collection='operateTypes' item='type' open='(' separator=',' close=')'>" +
            "#{type}" +
            "</foreach> " +
            "AND rdp.is_del = false " +
            "AND dp.is_del = false" +
            "</script>")
    List<TRolesDataPermission> findByModuleAndOperateTypes(
        @Param("moduleIdentifier") String moduleIdentifier,
        @Param("operateTypes") List<Integer> operateTypes);

    /**
     * 查找数据级别的角色数据权限记录ID（用于级联删除）
     * 根据数据ID和操作类型查找相关的角色权限记录ID，避免时间字段查询问题
     *
     * @param dataId 数据ID
     * @param operateTypes 操作类型列表
     * @return 角色数据权限记录ID列表
     */
    @Select("<script>" +
            "SELECT id FROM t_roles_data_permission " +
            "WHERE data_id = #{dataId} " +
            "AND operate_type IN " +
            "<foreach collection='operateTypes' item='type' open='(' separator=',' close=')'>" +
            "#{type}" +
            "</foreach> " +
            "AND is_del = false" +
            "</script>")
    List<Long> findIdsByDataIdAndOperateTypes(
        @Param("dataId") Long dataId,
        @Param("operateTypes") List<Integer> operateTypes);

    /**
     * 查询指定数据ID在角色权限表中实际存在的操作类型
     * 用于级联删除前的检查
     *
     * @param dataId 数据ID
     * @return 操作类型列表
     */
    @Select("SELECT DISTINCT operate_type " +
            "FROM t_roles_data_permission " +
            "WHERE data_id = #{dataId} " +
            "AND is_del = false " +
            "ORDER BY operate_type")
    List<Integer> getExistingOperateTypesByDataId(@Param("dataId") Long dataId);

    /**
     * 批量逻辑删除角色数据权限记录
     * 根据记录ID列表进行批量逻辑删除，避免时间字段查询问题
     *
     * @param ids 记录ID列表
     * @return 影响的记录数
     */
    @Update("<script>" +
            "UPDATE t_roles_data_permission " +
            "SET is_del = true, modify_time = CURRENT_TIMESTAMP " +
            "WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    int batchLogicalDeleteByIds(@Param("ids") List<Long> ids);
}
