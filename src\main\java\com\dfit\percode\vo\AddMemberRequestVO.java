package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 添加成员请求VO类
 * 按照前端格式要求设计
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddMemberRequestVO", description = "添加成员请求参数")
public class AddMemberRequestVO {
    
    @ApiModelProperty("用户列表")
    private List<MemberInfoVO> members;
}
