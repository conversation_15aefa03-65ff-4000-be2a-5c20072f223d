package com.dfit.orgsync.dto;

import lombok.Data;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 同步结果对象
 * 记录数据同步的详细结果和统计信息
 */
@Data
public class SyncResult {
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    // ========== 数据统计 ==========
    
    /**
     * 原始数据记录数
     */
    private int rawDataCount;
    
    /**
     * 清洗后数据记录数
     */
    private int cleanDataCount;
    
    /**
     * 生成的树节点数
     */
    private int treeNodeCount;
    
    /**
     * 准备插入的节点数
     */
    private int insertNodeCount;
    
    /**
     * 删除的记录数
     */
    private int deletedCount;
    
    /**
     * 插入的记录数
     */
    private int insertedCount;
    
    /**
     * 最终记录数
     */
    private int finalCount;
    
    // ========== 验证结果 ==========
    
    /**
     * 层级统计信息
     * key: 层级数, value: 该层级的部门数量
     */
    private List<Map<String, Object>> levelStats;
    
    /**
     * 孤立节点数量
     */
    private int orphanNodeCount;
    
    /**
     * 重复名称数量
     */
    private int duplicateNameCount;
    
    /**
     * 一级部门匹配统计
     */
    private Map<String, Object> level1Stats;
    
    /**
     * 获取执行时长
     */
    public Duration getDuration() {
        if (startTime != null && endTime != null) {
            return Duration.between(startTime, endTime);
        }
        return null;
    }
    
    /**
     * 获取格式化的执行时长
     */
    public String getFormattedDuration() {
        Duration duration = getDuration();
        if (duration == null) {
            return "未知";
        }
        
        long seconds = duration.getSeconds();
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes, secs);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, secs);
        } else {
            return String.format("%d秒", secs);
        }
    }
    
    /**
     * 检查是否有数据质量问题
     */
    public boolean hasDataQualityIssues() {
        return orphanNodeCount > 0 || duplicateNameCount > 0;
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (rawDataCount == 0) {
            return 0.0;
        }
        return (double) insertedCount / rawDataCount * 100;
    }
    
    @Override
    public String toString() {
        return String.format(
            "SyncResult{success=%s, duration=%s, rawData=%d, cleanData=%d, " +
            "inserted=%d, final=%d, orphans=%d, duplicates=%d, successRate=%.2f%%}",
            success, getFormattedDuration(), rawDataCount, cleanDataCount, 
            insertedCount, finalCount, orphanNodeCount, duplicateNameCount, getSuccessRate()
        );
    }
    
    /**
     * 生成详细报告
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 组织架构数据转换完成报告 ===\n\n");
        
        report.append("执行信息:\n");
        report.append(String.format("- 开始时间: %s\n", startTime));
        report.append(String.format("- 结束时间: %s\n", endTime));
        report.append(String.format("- 执行时长: %s\n", getFormattedDuration()));
        report.append(String.format("- 执行结果: %s\n", success ? "成功" : "失败"));
        if (!success && errorMessage != null) {
            report.append(String.format("- 错误信息: %s\n", errorMessage));
        }
        report.append("\n");
        
        report.append("数据统计:\n");
        report.append(String.format("- 原始记录数: %d\n", rawDataCount));
        report.append(String.format("- 清洗后记录数: %d\n", cleanDataCount));
        report.append(String.format("- 生成树节点数: %d\n", treeNodeCount));
        report.append(String.format("- 删除旧记录数: %d\n", deletedCount));
        report.append(String.format("- 插入新记录数: %d\n", insertedCount));
        report.append(String.format("- 最终记录数: %d\n", finalCount));
        report.append(String.format("- 成功率: %.2f%%\n", getSuccessRate()));
        report.append("\n");
        
        if (levelStats != null && !levelStats.isEmpty()) {
            report.append("层级分布:\n");
            for (Map<String, Object> stat : levelStats) {
                report.append(String.format("- 第%s级: %s个部门\n", 
                    stat.get("level"), stat.get("count")));
            }
            report.append("\n");
        }
        
        report.append("数据质量检查:\n");
        report.append(String.format("- 孤立节点: %d个\n", orphanNodeCount));
        report.append(String.format("- 重复名称: %d个\n", duplicateNameCount));
        report.append(String.format("- 数据质量: %s\n", hasDataQualityIssues() ? "存在问题" : "良好"));
        
        return report.toString();
    }
}
