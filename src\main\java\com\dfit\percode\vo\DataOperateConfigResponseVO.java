package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 模块操作权限配置响应VO类
 * 模块级别的操作权限配置，该模块下所有数据都继承这些操作权限
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DataOperateConfigResponseVO", description = "模块操作权限配置响应")
public class DataOperateConfigResponseVO {

    @ApiModelProperty(value = "模块ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long moduleId;

    @ApiModelProperty(value = "模块标识", example = "user_data_module")
    private String moduleIdentifier;

    @ApiModelProperty(value = "模块名称", example = "用户数据模块")
    private String moduleName;

    @ApiModelProperty(value = "已配置的操作类型列表", example = "[1, 2, 3]",
                     notes = "1-新增，2-修改，3-删除，4-查看")
    private List<Integer> operateTypes;

    @ApiModelProperty(value = "可选的操作类型选项")
    private List<OperateTypeOption> operateTypeOptions;

    /**
     * 操作类型选项内部类
     */
    @Data
    @ApiModel(value = "OperateTypeOption", description = "操作类型选项")
    public static class OperateTypeOption {
        @ApiModelProperty(value = "操作类型值", example = "1")
        private Integer value;

        @ApiModelProperty(value = "操作类型名称", example = "新增")
        private String label;
    }
}
