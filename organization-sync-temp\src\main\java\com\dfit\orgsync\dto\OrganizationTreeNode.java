package com.dfit.orgsync.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 组织架构树节点
 * 对应 t_org_structure 表结构
 */
@Data
public class OrganizationTreeNode {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 组织名称
     */
    private String organName;
    
    /**
     * 完整名称（最后一级使用完整路径）
     */
    private String fullName;
    
    /**
     * 父ID
     */
    private Long preId;
    
    /**
     * 层级（1=一级部门，2=二级部门...）
     */
    private Integer level;
    
    /**
     * 排序信息
     */
    private Integer orderInfo;
    
    /**
     * 是否删除
     */
    private Boolean isDel;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
    
    /**
     * 数据来源（1=页面输入，2=数据同步）
     */
    private Integer dataSource;
    
    /**
     * 子节点列表
     */
    private List<OrganizationTreeNode> children;
    
    /**
     * 父节点引用
     */
    private OrganizationTreeNode parent;
    
    /**
     * 构造函数
     */
    public OrganizationTreeNode() {
        this.children = new ArrayList<>();
        this.isDel = false;
        this.dataSource = 2; // 数据同步
        this.createTime = LocalDateTime.now();
        this.modifyTime = LocalDateTime.now();
        this.orderInfo = 1;
    }
    
    /**
     * 构造函数
     */
    public OrganizationTreeNode(String organName, String fullName, Integer level) {
        this();
        this.organName = organName;
        this.fullName = fullName;
        this.level = level;
    }
    
    /**
     * 添加子节点
     */
    public void addChild(OrganizationTreeNode child) {
        child.parent = this;
        this.children.add(child);
    }
    
    /**
     * 是否为根节点
     */
    public boolean isRoot() {
        return parent == null;
    }
    
    /**
     * 是否为叶子节点
     */
    public boolean isLeaf() {
        return children.isEmpty();
    }
    
    /**
     * 获取节点路径
     */
    public String getPath() {
        if (parent == null) {
            return organName;
        }
        return parent.getPath() + " -> " + organName;
    }
}
