package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 修改菜单请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持修改菜单的所有字段，包括父级关系
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UpdateMenuRequestVO", description = "修改菜单请求参数")
public class UpdateMenuRequestVO {

    @ApiModelProperty(value = "菜单ID", required = true, example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongDeserializer.class)
    private Long id;

    @ApiModelProperty(value = "菜单名称", required = true, example = "用户管理")
    private String name;

    @ApiModelProperty(value = "父级菜单ID，根节点传0", example = "0")
    @JsonSerialize(using = ToStringSerializer.class)
    @JsonDeserialize(using = LongDeserializer.class)
    private Long preId;

    @ApiModelProperty(value = "模块标识", required = true, example = "system_management")
    private String moduleIdentifier;

    @ApiModelProperty(value = "排序序号", required = true, example = "1")
    private Integer orderInfo;

    @ApiModelProperty(value = "是否禁用：false-启用，true-禁用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "菜单类型：1-目录，2-菜单，3-按钮", required = true, example = "2")
    private Integer menuType;

    @ApiModelProperty(value = "路由地址", example = "/user/list")
    private String routeAddress;

    @ApiModelProperty(value = "组件路径", example = "user/UserList")
    private String componentPath;

    @ApiModelProperty(value = "权限标识", example = "user:list")
    private String permissionIdentifier;

    @ApiModelProperty(value = "路由参数", example = "id=123&type=edit")
    private String routeParam;
}
