package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 删除部门响应VO类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "DeleteOrgStructureResponseVO", description = "删除部门响应数据")
public class DeleteOrgStructureResponseVO {
    
    @ApiModelProperty(value = "删除的部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deletedId;
    
    @ApiModelProperty(value = "删除的部门名称")
    private String deletedName;
    
    @ApiModelProperty(value = "删除的子部门数量")
    private Integer deletedChildCount;
    
    @ApiModelProperty(value = "删除的子部门ID列表")
    @JsonSerialize(contentUsing = ToStringSerializer.class)
    private List<Long> deletedChildIds;
    
    @ApiModelProperty(value = "删除时间")
    private String deleteTime;
}
