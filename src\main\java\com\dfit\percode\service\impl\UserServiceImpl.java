package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TUser;
import com.dfit.percode.mapper.UserMapper;
import com.dfit.percode.service.IUserService;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.util.PasswordUtil;

import com.dfit.percode.vo.AddMemberRequestVO;
import com.dfit.percode.vo.AddMemberRoleEntityVO;
import com.dfit.percode.vo.DepartmentTreeVO;
import com.dfit.percode.vo.MemberInfoVO;
import com.dfit.percode.vo.DeleteUserRequestVO;
import com.dfit.percode.vo.RoleInfoVO;
import com.dfit.percode.vo.UpdateUserRequestVO;
import com.dfit.percode.vo.UserDetailRequestVO;
import com.dfit.percode.vo.UserDetailResponseVO;
import com.dfit.percode.vo.UserListItemVO;
import com.dfit.percode.vo.UserListRequestVO;
import com.dfit.percode.vo.UserListResponseVO;
import com.dfit.percode.vo.UserListByOrgRequestVO;
import com.dfit.percode.vo.UserListByOrgResponseVO;
import com.dfit.percode.vo.UserListByOrgItemVO;
import com.dfit.percode.vo.RoleUserListRequestVO;
import com.dfit.percode.vo.RoleUserListResponseVO;
import com.dfit.percode.vo.RoleUserListItemVO;
import com.dfit.percode.vo.UnauthorizedUsersRequestVO;
import com.dfit.percode.vo.UnauthorizedUsersResponseVO;
import com.dfit.percode.vo.UnauthorizedUserItemVO;
import com.dfit.percode.vo.UpdateUserRoleRequestVO;
import com.dfit.percode.vo.DepartmentTreeOnlyVO;
import com.dfit.percode.vo.DepartmentUsersRequestVO;
import com.dfit.percode.vo.DepartmentUsersResponseVO;
import com.dfit.percode.vo.CheckUserDuplicateRequestVO;
import com.dfit.percode.vo.CheckUserDuplicateResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户相关服务实现类
 * 按照前端实际使用的格式重新实现
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class UserServiceImpl implements IUserService {

    @Autowired
    private UserMapper userMapper;

    /**
     * 获取所有用户（组织架构树形结构）
     * 用于前端选择人员功能
     *
     * 已优化：使用 RECURSIVE CTE 查询，避免 N+1 查询问题，提升性能
     *
     * @return 部门树形结构列表，包含用户信息
     */
    @Override
    public List<DepartmentTreeVO> getAllUsers() {
        log.info("开始获取所有用户（组织架构树形结构）- 使用 RECURSIVE 优化");
        long startTime = System.currentTimeMillis();

        try {
            // 使用 RECURSIVE CTE 一次性获取所有部门和用户数据
            List<Map<String, Object>> allData = userMapper.findAllDepartmentsAndUsersOptimized();
            log.info("RECURSIVE 查询返回数据条数: {}", allData.size());

            // 构建优化的部门树
            List<DepartmentTreeVO> result = buildOptimizedDepartmentTreeFromRecursive(allData);

            long endTime = System.currentTimeMillis();
            log.info("获取所有用户完成（RECURSIVE 优化），耗时: {}ms", endTime - startTime);

            return result;
        } catch (Exception e) {
            log.error("获取所有用户失败", e);
            throw new RuntimeException("获取用户列表失败: " + e.getMessage(), e);
        }

        /* 原始实现备份（N+1 查询问题）：
        try {
            List<DepartmentTreeVO> rootDepartments = userMapper.findAllRootDepartments();
            List<DepartmentTreeVO> result = buildDepartmentTree(rootDepartments);
            return result;
        } catch (Exception e) {
            log.error("获取所有用户失败", e);
            throw new RuntimeException("获取用户列表失败: " + e.getMessage(), e);
        }
        */
    }

    /* 原始递归构建方法已移除（N+1 查询问题）
     * 已替换为 buildOptimizedDepartmentTreeFromRecursive() 方法
     * 如需恢复，请参考 getAllUsers() 方法中的备份注释
     */

    /**
     * 获取所有用户（组织架构树形结构）- 优化版本
     * 使用 RECURSIVE CTE 查询，避免 N+1 查询问题，提升性能
     * 功能与 getAllUsers() 完全相同，仅实现方式不同
     *
     * @return 部门树形结构列表，包含用户信息
     */
    @Override
    public List<DepartmentTreeVO> getAllUsersOptimized() {
        log.info("开始获取所有用户（组织架构树形结构）- RECURSIVE 优化版本");
        long startTime = System.currentTimeMillis();

        try {
            // 使用 RECURSIVE CTE 一次性获取所有部门和用户数据
            List<Map<String, Object>> allData = userMapper.findAllDepartmentsAndUsersOptimized();
            log.info("RECURSIVE 查询返回数据条数: {}", allData.size());

            // 构建优化的部门树
            List<DepartmentTreeVO> result = buildOptimizedDepartmentTreeFromRecursive(allData);

            long endTime = System.currentTimeMillis();
            log.info("RECURSIVE 优化版本获取所有用户完成，耗时: {}ms", endTime - startTime);

            return result;
        } catch (Exception e) {
            log.error("RECURSIVE 优化版本获取所有用户失败", e);
            throw new RuntimeException("获取用户列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从 RECURSIVE 查询结果构建部门树
     * 利用查询结果中的层级信息，高效构建树形结构
     *
     * @param allData RECURSIVE 查询返回的所有数据
     * @return 部门树形结构列表
     */
    private List<DepartmentTreeVO> buildOptimizedDepartmentTreeFromRecursive(List<Map<String, Object>> allData) {
        // 使用 Map 存储部门信息，避免重复创建
        Map<Long, DepartmentTreeVO> departmentMap = new HashMap<>();
        Map<Long, List<DepartmentTreeVO>> userMap = new HashMap<>();

        // 处理查询结果
        for (Map<String, Object> row : allData) {
            // 安全的类型转换
            Long deptId = convertToLong(row.get("deptid"));  // 注意：PostgreSQL返回小写字段名
            String deptName = (String) row.get("deptname");
            Long parentId = convertToLong(row.get("parentid"));
            Integer level = convertToInteger(row.get("level"));
            Long userId = convertToLong(row.get("userid"));
            String userName = (String) row.get("username");

            // 构建或获取部门对象
            if (deptId != null && !departmentMap.containsKey(deptId)) {
                DepartmentTreeVO dept = new DepartmentTreeVO();
                dept.setId(deptId);
                dept.setOrganName(deptName);
                dept.setChildren(new ArrayList<>());
                departmentMap.put(deptId, dept);

                log.debug("创建部门: id={}, name={}, level={}, parentId={}",
                         deptId, deptName, level, parentId);
            }

            // 构建用户对象并添加到对应部门
            if (userId != null && userName != null && deptId != null) {
                DepartmentTreeVO user = new DepartmentTreeVO();
                user.setId(userId);
                user.setUserName(userName);

                userMap.computeIfAbsent(deptId, k -> new ArrayList<>()).add(user);
                log.debug("添加用户: id={}, name={}, 所属部门={}", userId, userName, deptId);
            }
        }

        // 将用户添加到对应部门的children中
        log.debug("部门数量: {}, 用户映射数量: {}", departmentMap.size(), userMap.size());
        for (Map.Entry<Long, List<DepartmentTreeVO>> entry : userMap.entrySet()) {
            Long deptId = entry.getKey();
            List<DepartmentTreeVO> users = entry.getValue();

            DepartmentTreeVO dept = departmentMap.get(deptId);
            if (dept != null) {
                dept.getChildren().addAll(users);
                log.debug("部门 {} 添加了 {} 个用户", dept.getOrganName(), users.size());
            }
        }

        // 构建父子关系映射
        Map<Long, List<DepartmentTreeVO>> childrenMap = new HashMap<>();
        List<DepartmentTreeVO> rootDepartments = new ArrayList<>();

        // 从原始数据中构建父子关系映射（利用已有的 parentId 信息）
        for (Map<String, Object> row : allData) {
            Long deptId = convertToLong(row.get("deptid"));  // 注意：PostgreSQL返回小写字段名
            Long parentId = convertToLong(row.get("parentid"));

            if (deptId != null) {
                DepartmentTreeVO dept = departmentMap.get(deptId);
                if (dept != null) {
                    if (parentId == null || parentId == 0) {
                        // 根部门
                        if (!rootDepartments.contains(dept)) {
                            rootDepartments.add(dept);
                            log.debug("添加根部门: id={}, name={}", dept.getId(), dept.getOrganName());
                        }
                    } else {
                        // 子部门
                        childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(dept);
                        log.debug("添加子部门: id={}, name={}, parentId={}", dept.getId(), dept.getOrganName(), parentId);
                    }
                }
            }
        }

        // 去重子部门列表
        for (Map.Entry<Long, List<DepartmentTreeVO>> entry : childrenMap.entrySet()) {
            List<DepartmentTreeVO> children = entry.getValue();
            List<DepartmentTreeVO> uniqueChildren = children.stream()
                .distinct()
                .collect(Collectors.toList());
            childrenMap.put(entry.getKey(), uniqueChildren);
        }

        // 递归构建子部门关系
        buildChildrenRelations(rootDepartments, childrenMap);

        log.debug("RECURSIVE 优化版本最终根部门数量: {}", rootDepartments.size());
        return rootDepartments;
    }

    /**
     * 安全的 Integer 类型转换方法
     *
     * @param obj 要转换的对象
     * @return Integer值，如果转换失败返回null
     */
    private Integer convertToInteger(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Integer) {
            return (Integer) obj;
        }

        if (obj instanceof Long) {
            return ((Long) obj).intValue();
        }

        if (obj instanceof String) {
            try {
                return Integer.parseInt((String) obj);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Integer: {}", obj);
                return null;
            }
        }

        if (obj instanceof Number) {
            return ((Number) obj).intValue();
        }

        log.warn("无法转换为Integer的数据类型: {} ({})", obj, obj.getClass().getSimpleName());
        return null;
    }

    /* 原始优化方法已移除（存在性能问题）
     * 已替换为 buildOptimizedDepartmentTreeFromRecursive() 方法
     * 新方法使用 RECURSIVE CTE 查询，性能更佳
     */

    /**
     * 递归构建子部门关系
     *
     * @param departments 部门列表
     * @param childrenMap 子部门映射
     */
    private void buildChildrenRelations(List<DepartmentTreeVO> departments, Map<Long, List<DepartmentTreeVO>> childrenMap) {
        for (DepartmentTreeVO dept : departments) {
            List<DepartmentTreeVO> children = childrenMap.get(dept.getId());
            if (children != null && !children.isEmpty()) {
                // 将子部门添加到children的开头（部门在前，用户在后）
                dept.getChildren().addAll(0, children);
                // 递归处理子部门
                buildChildrenRelations(children, childrenMap);
            }
        }
    }

    /**
     * 添加成员到权限管理系统
     * 按照前端设计直接接收数组格式
     * 支持新用户创建和现有用户角色分配
     *
     * 使用方式：
     * - 新增用户：MemberInfoVO.userId为null，提供userName、account、password、organAffiliation等信息
     * - 现有用户角色分配：MemberInfoVO.userId不为null，主要用于角色关联
     *
     * @param members 成员列表（直接数组格式）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addMembers(List<MemberInfoVO> members) {
        // 创建ID生成器
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);

        // 遍历成员列表，批量添加角色关联
        for (MemberInfoVO member : members) {
            // 1. 智能处理用户存在性
            if (member.getUserId() == null || member.getUserId() <= 0) {
                // 没有传ID，根据用户信息创建新用户
                log.info("用户ID为空，创建新用户: userName={}, account={}",
                        member.getUserName(), member.getAccount());
                Long newUserId = createNewUser(member);
                member.setUserId(newUserId);
                log.info("新用户创建成功，用户ID: {}", newUserId);
            } else {
                // 有ID，检查用户是否存在
                int userExists = userMapper.checkUserExists(member.getUserId());
                if (userExists == 0) {
                    log.error("用户不存在，跳过角色分配: userId={}", member.getUserId());
                    throw new RuntimeException("用户不存在: userId=" + member.getUserId());
                }
                log.info("用户存在性检查通过: userId={}", member.getUserId());
            }

            // 遍历用户的角色列表，为每个角色创建关联记录
            if (member.getRoles() != null && !member.getRoles().isEmpty()) {
                for (int i = 0; i < member.getRoles().size(); i++) {
                    RoleInfoVO role = member.getRoles().get(i);

                    // 检查用户角色关联是否已存在，避免重复分配
                    int existsCount = userMapper.checkUserRoleExists(member.getUserId(), role.getRoleId());
                    if (existsCount > 0) {
                        // 如果已存在，跳过这个角色分配
                        continue;
                    }

                    // 为每个角色生成独立的ID
                    long userRoleId = idGenerator.generateId();

                    // 创建用户角色关联表实体
                    AddMemberRoleEntityVO roleEntity = new AddMemberRoleEntityVO();
                    roleEntity.setId(userRoleId);
                    roleEntity.setUserId(member.getUserId());
                    roleEntity.setRoleId(role.getRoleId());
                    roleEntity.setOrderInfo(i + 1); // 设置角色顺序

                    // 插入到用户角色关联表
                    userMapper.insertPermUserRole(roleEntity);
                }
            }
        }
    }

    /**
     * 获取用户详细信息
     * 包含用户基本信息和已分配的角色列表
     * 支持单个用户查询和批量用户查询
     *
     * @param request 用户详情请求参数
     * @return 用户详细信息（单个查询返回UserDetailResponseVO对象，批量查询返回List<UserDetailResponseVO>数组）
     */
    @Override
    public Object getUserDetail(UserDetailRequestVO request) {
        // 判断是单个查询还是批量查询
        if (request.getUserIds() != null && !request.getUserIds().isEmpty()) {
            // 批量查询
            log.info("开始批量查询用户详情，用户ID数量: {}", request.getUserIds().size());

            List<UserDetailResponseVO> userDetails = userMapper.findUserDetailsByIds(request.getUserIds());
            log.info("批量查询到用户数量: {}", userDetails.size());

            // 为每个用户查询角色信息
            for (UserDetailResponseVO userDetail : userDetails) {
                List<RoleInfoVO> roles = userMapper.findUserRolesByUserId(userDetail.getUserId());
                userDetail.setRoles(roles);
            }

            log.info("批量查询用户详情完成");
            return userDetails; // 返回数组
        } else {
            // 单个查询（保持原有逻辑）
            log.info("开始单个查询用户详情，用户ID: {}", request.getUserId());

            UserDetailResponseVO userDetail = userMapper.findUserDetailById(request.getUserId());

            if (userDetail == null) {
                throw new RuntimeException("用户不存在或已被删除");
            }

            // 获取用户已分配的角色列表
            List<RoleInfoVO> roles = userMapper.findUserRolesByUserId(request.getUserId());
            userDetail.setRoles(roles);

            log.info("单个查询用户详情完成");
            return userDetail; // 返回单个对象
        }
    }

    /**
     * 更新用户信息
     * 包含用户基本信息和角色分配的更新
     *
     * @param request 更新用户信息请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UpdateUserRequestVO request) {
        log.info("开始更新用户信息");
        log.info("用户ID: {}", request.getUserId());
        log.info("角色数量: {}", (request.getRoles() != null ? request.getRoles().size() : 0));

        // 1. 检查账号重复（排除自己）
        if (request.getAccount() != null && !request.getAccount().trim().isEmpty()) {
            int accountExists = userMapper.checkAccountExistsForUpdate(request.getAccount(), request.getUserId());
            if (accountExists > 0) {
                throw new RuntimeException("账号已存在: " + request.getAccount());
            }
        }

        // 2. 更新用户基本信息（包括密码，如果提供）
        // 如果提供了密码，需要先进行MD5加密
        if (request.getPassword() != null && !request.getPassword().trim().isEmpty()) {
            String encryptedPassword = PasswordUtil.encryptPassword(request.getPassword());
            request.setPassword(encryptedPassword);
            log.info("用户密码已MD5加密");
        }

        userMapper.updateUserInfo(request);
        log.info("用户基本信息更新完成");

        // 3. 删除用户的所有旧角色关联
        userMapper.deleteUserRoles(request.getUserId());
        log.info("旧角色关联删除完成");

        // 4. 重新分配角色
        if (request.getRoles() != null && !request.getRoles().isEmpty()) {
            SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);

            for (int i = 0; i < request.getRoles().size(); i++) {
                RoleInfoVO role = request.getRoles().get(i);
                log.info("正在处理角色 {}: roleId={}, roleName={}", (i + 1), role.getRoleId(), role.getRoleName());

                // 为每个角色生成独立的ID
                long userRoleId = idGenerator.generateId();
                log.info("生成的关联ID: {}", userRoleId);

                // 创建用户角色关联表实体
                AddMemberRoleEntityVO roleEntity = new AddMemberRoleEntityVO();
                roleEntity.setId(userRoleId);
                roleEntity.setUserId(request.getUserId());
                roleEntity.setRoleId(role.getRoleId());
                roleEntity.setOrderInfo(i + 1); // 设置角色顺序

                try {
                    // 插入到用户角色关联表
                    userMapper.insertPermUserRole(roleEntity);
                    log.info("角色 {} 插入成功", role.getRoleName());
                } catch (Exception e) {
                    log.error("角色 {} 插入失败: {}", role.getRoleName(), e.getMessage());
                    throw e; // 重新抛出异常，触发事务回滚
                }
            }
        }
        log.info("用户信息更新完成");
    }

    /**
     * 获取角色的用户分配列表
     * 用于角色管理中的分配用户功能
     * 支持分页查询和多条件搜索
     *
     * @param request 角色用户列表查询请求参数
     * @return 角色用户列表响应，包含分页信息
     */
    @Override
    public RoleUserListResponseVO getRoleUserList(RoleUserListRequestVO request) {
        log.info("开始获取角色用户分配列表");
        log.info("角色ID: {}, 页码: {}, 页大小: {}", request.getRoleId(), request.getCurrentPage(), request.getPageSize());

        // 计算分页偏移量（MyBatis分页从0开始）
        int offset = (request.getCurrentPage() - 1) * request.getPageSize();

        // 创建一个临时变量存储偏移量，不修改原始请求对象
        int originalCurrentPage = request.getCurrentPage();

        // 临时设置偏移量用于查询
        request.setCurrentPage(offset);

        // 查询角色用户分配列表
        List<RoleUserListItemVO> userList = userMapper.findRoleUserListPage(request);

        // 恢复原始页码
        request.setCurrentPage(originalCurrentPage);
        log.info("查询到用户数量: {}", userList.size());

        // 查询总记录数
        Long total = userMapper.countRoleUserList(request);
        log.info("总记录数: {}", total);

        // 构建分页响应
        RoleUserListResponseVO response = new RoleUserListResponseVO();
        response.setRecords(userList);
        response.setTotal(total);
        response.setPageNum(originalCurrentPage); // 使用原始页码
        response.setPageSize(request.getPageSize());

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / request.getPageSize());
        response.setTotalPages(totalPages);

        // 计算是否有上一页/下一页
        response.setHasPrevious(originalCurrentPage > 1);
        response.setHasNext(originalCurrentPage < totalPages);

        log.info("角色用户分配列表查询完成");
        return response;
    }

    /**
     * 获取部门用户树形结构（层级懒加载）
     * 用于角色管理中的用户管理功能
     * 返回指定层级的部门和用户
     *
     * @param request 部门用户查询请求参数
     * @return 部门用户树形结构响应
     */
    @Override
    public UnauthorizedUsersResponseVO getUnauthorizedUsers(UnauthorizedUsersRequestVO request) {
        log.info("开始获取部门用户树形结构（层级懒加载）");
        log.info("角色ID: {}, 父部门ID: {}", request.getRoleId(), request.getParentId());
        long startTime = System.currentTimeMillis();

        try {
            // 使用层级懒加载查询获取部门和用户
            List<Map<String, Object>> allData = userMapper.findUnauthorizedUsersTreeOptimized(
                request.getRoleId(),
                request.getParentId()
            );
            log.info("层级查询返回 {} 条记录", allData.size());

            // 构建简化的树形结构
            List<DepartmentTreeVO> departmentTree = buildSimpleTreeFromQuery(allData);

            long endTime = System.currentTimeMillis();
            log.info("部门用户树形结构构建完成，耗时: {}ms，节点数量: {}",
                    endTime - startTime, departmentTree.size());

            // 构建响应
            UnauthorizedUsersResponseVO response = new UnauthorizedUsersResponseVO();
            response.setDepartmentTree(departmentTree);
            return response;

        } catch (Exception e) {
            log.error("获取部门用户树形结构失败", e);
            throw new RuntimeException("获取部门用户列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从查询结果构建简化的树形结构
     * 用于层级懒加载，直接将查询结果转换为节点列表
     *
     * @param queryResult 查询返回的原始数据
     * @return 部门和用户节点列表
     */
    private List<DepartmentTreeVO> buildSimpleTreeFromQuery(List<Map<String, Object>> queryResult) {
        List<DepartmentTreeVO> result = new ArrayList<>();

        for (Map<String, Object> row : queryResult) {
            DepartmentTreeVO node = new DepartmentTreeVO();
            node.setId(convertToLong(row.get("id")));

            // PostgreSQL返回小写字段名，优先使用小写
            String organName = (String) row.get("organname");
            if (organName == null) {
                organName = (String) row.get("organName"); // 备用大写
            }
            node.setOrganName(organName);

            String userName = (String) row.get("username");
            if (userName == null) {
                userName = (String) row.get("userName"); // 备用大写
            }
            node.setUserName(userName);

            Boolean isDisable = convertToBoolean(row.get("isdisable"));
            if (isDisable == null) {
                isDisable = convertToBoolean(row.get("isDisable")); // 备用大写
            }
            node.setIsDisable(isDisable);

            node.setChildren(new ArrayList<>()); // 前端用于后续添加子节点

            result.add(node);

            // 记录节点类型用于调试
            if (node.getOrganName() != null) {
                log.debug("添加部门节点: id={}, name={}", node.getId(), node.getOrganName());
            } else {
                log.debug("添加用户节点: id={}, name={}, isDisable={}",
                         node.getId(), node.getUserName(), node.getIsDisable());
            }
        }

        log.info("构建简化树形结构完成，节点数量: {}", result.size());
        return result;
    }

    /**
     * 从 RECURSIVE 查询结果构建未授权用户部门树
     * 利用查询结果中的层级信息，高效构建树形结构
     *
     * @param allData RECURSIVE 查询返回的所有数据
     * @return 部门树形结构列表，包含未授权用户
     */
    private List<DepartmentTreeVO> buildUnauthorizedUsersTreeFromRecursive(List<Map<String, Object>> allData) {
        // 使用 Map 存储部门信息，避免重复创建
        Map<Long, DepartmentTreeVO> departmentMap = new HashMap<>();
        Map<Long, List<DepartmentTreeVO>> childrenMap = new HashMap<>();
        List<DepartmentTreeVO> rootDepartments = new ArrayList<>();

        // 处理查询结果
        for (Map<String, Object> row : allData) {
            // 安全的类型转换
            Long deptId = convertToLong(row.get("deptid"));  // 注意：PostgreSQL返回小写字段名
            String deptName = (String) row.get("deptname");
            Long parentId = convertToLong(row.get("parentid"));
            Integer level = convertToInteger(row.get("level"));
            Long userId = convertToLong(row.get("userid"));
            String userName = (String) row.get("username");
            String account = (String) row.get("account");
            Boolean userState = convertToBoolean(row.get("userstate"));
            String createTime = convertTimestampToString(row.get("createtime"));

            // 构建或获取部门对象
            if (deptId != null && !departmentMap.containsKey(deptId)) {
                DepartmentTreeVO dept = new DepartmentTreeVO();
                dept.setId(deptId);
                dept.setOrganName(deptName);
                dept.setChildren(new ArrayList<>());
                departmentMap.put(deptId, dept);

                log.debug("创建部门: id={}, name={}, level={}, parentId={}",
                         deptId, deptName, level, parentId);
            }

            // 如果有用户数据，添加用户节点
            if (userId != null && userName != null) {
                DepartmentTreeVO userNode = new DepartmentTreeVO();
                userNode.setId(userId);
                userNode.setUserName(userName);
                // 可以添加更多用户信息到 DepartmentTreeVO 或创建专门的用户节点类

                DepartmentTreeVO dept = departmentMap.get(deptId);
                if (dept != null) {
                    dept.getChildren().add(userNode);
                    log.debug("添加用户到部门: userId={}, userName={}, deptId={}, deptName={}",
                             userId, userName, deptId, deptName);
                }
            }
        }

        // 构建父子关系映射（只处理部门，避免重复）
        Set<Long> processedDepts = new HashSet<>();
        for (Map<String, Object> row : allData) {
            Long deptId = convertToLong(row.get("deptid"));
            Long parentId = convertToLong(row.get("parentid"));

            if (deptId != null && !processedDepts.contains(deptId)) {
                processedDepts.add(deptId);
                DepartmentTreeVO dept = departmentMap.get(deptId);
                if (dept != null) {
                    if (parentId == null || parentId == 0) {
                        // 根部门
                        rootDepartments.add(dept);
                        log.debug("添加根部门: id={}, name={}", dept.getId(), dept.getOrganName());
                    } else {
                        // 子部门
                        childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(dept);
                        log.debug("添加子部门: id={}, name={}, parentId={}", dept.getId(), dept.getOrganName(), parentId);
                    }
                }
            }
        }

        // 递归构建完整的树形结构
        for (DepartmentTreeVO dept : departmentMap.values()) {
            List<DepartmentTreeVO> children = childrenMap.get(dept.getId());
            if (children != null) {
                // 将子部门添加到当前部门的 children 中（在用户节点之前）
                List<DepartmentTreeVO> allChildren = new ArrayList<>(children);
                allChildren.addAll(dept.getChildren()); // 添加用户节点
                dept.setChildren(allChildren);
            }
        }

        // 过滤掉没有未授权用户的空部门
        List<DepartmentTreeVO> filteredRoots = new ArrayList<>();
        for (DepartmentTreeVO root : rootDepartments) {
            if (hasUnauthorizedUsers(root)) {
                filteredRoots.add(root);
            }
        }

        log.info("构建未授权用户部门树完成，根部门数量: {}", filteredRoots.size());
        return filteredRoots;
    }

    /**
     * 检查部门树是否包含未授权用户
     * 递归检查部门及其子部门是否有用户
     */
    private boolean hasUnauthorizedUsers(DepartmentTreeVO dept) {
        if (dept.getChildren() != null) {
            for (DepartmentTreeVO child : dept.getChildren()) {
                // 如果是用户节点（有 userName）
                if (child.getUserName() != null) {
                    return true;
                }
                // 如果是部门节点，递归检查
                if (child.getOrganName() != null && hasUnauthorizedUsers(child)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 删除用户
     * 逻辑删除用户基本信息和所有角色关联
     *
     * @param request 删除用户请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(DeleteUserRequestVO request) {
        log.info("开始删除用户");
        log.info("用户ID: {}", request.getUserId());

        // 1. 检查用户是否存在
        int userExists = userMapper.checkUserExists(request.getUserId());
        if (userExists == 0) {
            log.error("用户不存在或已被删除");
            throw new RuntimeException("用户不存在或已被删除");
        }
        log.info("用户存在性检查通过");

        // 2. 删除用户的所有角色关联（逻辑删除）
        userMapper.deleteUserRoles(request.getUserId());
        log.info("用户角色关联删除完成");

        // 3. 删除用户基本信息（逻辑删除）
        userMapper.deleteUser(request.getUserId());
        log.info("用户基本信息删除完成");

        log.info("用户删除完成");
    }

    /**
     * 创建新用户
     * 支持创建全新的用户记录并分配角色
     * TODO: 需要创建CreateUserRequestVO类和相关接口
     *
     * @param request 创建用户请求参数
     */
    /*
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(CreateUserRequestVO request) {
        log.info("开始创建新用户");
        log.info("用户名: {}, 账号: {}", request.getUserName(), request.getAccount());

        // 1. 检查账号是否已存在
        if (request.getAccount() != null && !request.getAccount().trim().isEmpty()) {
            int accountExists = userMapper.checkAccountExists(request.getAccount());
            if (accountExists > 0) {
                log.error("账号已存在: {}", request.getAccount());
                throw new RuntimeException("账号已存在: " + request.getAccount());
            }
        }

        // 2. 生成用户ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        Long userId = idGenerator.generateId();
        log.info("生成用户ID: {}", userId);

        // 3. 创建用户实体
        TUser user = new TUser();
        user.setId(userId);
        user.setUserName(request.getUserName());
        user.setAccount(request.getAccount());
        user.setOrganAffiliation(request.getOrganAffiliation());
        user.setIsDisable(request.getIsDisable() != null ? request.getIsDisable() : false);
        user.setIsDel(false);
        user.setCreateTime(LocalDateTime.now());
        user.setModifyTime(LocalDateTime.now());

        // 设置默认密码（可以后续修改）
        user.setPassword("123456"); // 或者生成随机密码

        // 4. 插入用户基本信息
        userMapper.insert(user);
        log.info("用户基本信息创建成功");

        // 5. 分配角色
        if (request.getRoles() != null && !request.getRoles().isEmpty()) {
            for (int i = 0; i < request.getRoles().size(); i++) {
                RoleInfoVO role = request.getRoles().get(i);
                log.info("正在分配角色 {}: roleId={}, roleName={}", (i + 1), role.getRoleId(), role.getRoleName());

                // 为每个角色生成独立的ID
                long userRoleId = idGenerator.generateId();

                // 创建用户角色关联表实体
                AddMemberRoleEntityVO roleEntity = new AddMemberRoleEntityVO();
                roleEntity.setId(userRoleId);
                roleEntity.setUserId(userId);
                roleEntity.setRoleId(role.getRoleId());
                roleEntity.setOrderInfo(i + 1);

                userMapper.insertPermUserRole(roleEntity);
                log.info("角色 {} 分配成功", role.getRoleName());
            }
        }

        log.info("新用户创建完成，用户ID: {}", userId);
    }
    */

    /**
     * 根据成员信息创建新用户
     * 用于addMembers方法中的自动用户创建
     * 支持account和password参数
     *
     * @param member 成员信息
     * @return 新创建的用户ID
     */
    private Long createNewUser(MemberInfoVO member) {
        log.info("开始创建新用户: userName={}, account={}", member.getUserName(), member.getAccount());

        // 1. 参数验证
        if (member.getUserName() == null || member.getUserName().trim().isEmpty()) {
            throw new RuntimeException("用户名不能为空");
        }

        // 2. 检查账号是否已存在（如果提供了账号）
        String account = member.getAccount();
        if (account == null || account.trim().isEmpty()) {
            // 如果没有提供账号，使用用户名作为账号
            account = member.getUserName();
        } else {
            // 检查账号是否已存在
            int accountExists = userMapper.checkAccountExists(account);
            if (accountExists > 0) {
                throw new RuntimeException("账号已存在: " + account);
            }
        }

        // 3. 生成用户ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        Long userId = idGenerator.generateId();
        log.info("生成用户ID: {}", userId);

        // 4. 创建用户实体
        TUser user = new TUser();
        user.setId(userId);
        user.setUserName(member.getUserName());
        user.setAccount(account);
        user.setOrganAffiliation(member.getOrganAffiliation()); // 设置组织归属
        user.setIsDisable(member.getIsDisable() != null ? member.getIsDisable() : false);
        user.setIsDel(false);
        user.setCreateTime(LocalDateTime.now());
        user.setModifyTime(LocalDateTime.now());

        // 设置密码（支持MD5加密和默认密码生成）
        String password = member.getPassword();
        if (password == null || password.trim().isEmpty()) {
            // 前端未传密码，生成复杂默认密码
            password = PasswordUtil.generateDefaultPassword();
            log.info("前端未传密码，生成默认密码: {}", password);
        }

        // 对密码进行MD5加密
        String encryptedPassword = PasswordUtil.encryptPassword(password);
        user.setPassword(encryptedPassword);
        log.info("密码已MD5加密存储");

        // 5. 插入用户基本信息
        userMapper.insert(user);
        log.info("新用户创建成功: userId={}, userName={}, account={}", userId, member.getUserName(), account);

        return userId;
    }

    /**
     * 分页查询用户列表
     * 支持多条件搜索和分页
     *
     * @param request 分页查询请求参数
     * @return 分页查询结果
     */
    @Override
    public UserListResponseVO getUserList(UserListRequestVO request) {
        log.info("开始查询用户列表");

        // 判断是否需要分页
        boolean needPaging = (request.getCurrentPage() != null && request.getPageSize() != null);

        List<UserListItemVO> userList;
        Long total;
        int originalCurrentPage = 1;

        if (needPaging) {
            // 分页查询
            log.info("执行分页查询 - 页码: {}, 页大小: {}", request.getCurrentPage(), request.getPageSize());

            // 参数验证
            if (request.getCurrentPage() <= 0) {
                request.setCurrentPage(1);
                log.warn("页码参数无效，调整为: 1");
            }
            if (request.getPageSize() <= 0) {
                request.setPageSize(10);
                log.warn("页大小参数无效，调整为: 10");
            }

            // 限制最大页大小，防止查询过多数据
            if (request.getPageSize() > 100) {
                request.setPageSize(100);
                log.warn("页大小超过限制，调整为最大值: 100");
            }

            // 计算分页偏移量（MyBatis分页从0开始）
            int offset = (request.getCurrentPage() - 1) * request.getPageSize();
            originalCurrentPage = request.getCurrentPage();

            // 临时设置偏移量用于查询
            request.setCurrentPage(offset);

            // 查询用户基础信息列表（分页）
            userList = userMapper.findUserListPage(request);

            // 恢复原始页码
            request.setCurrentPage(originalCurrentPage);

            // 查询总记录数
            total = userMapper.countUserList(request);

        } else {
            // 查询所有数据，不分页
            log.info("执行全量查询（不分页）");

            // 查询所有用户基础信息列表
            userList = userMapper.findUserListAll(request);
            total = (long) userList.size();
            originalCurrentPage = 1;
        }

        log.info("查询到用户数量: {}", userList.size());

        // 为每个用户查询角色信息，转换为角色名称列表
        for (UserListItemVO user : userList) {
            List<RoleInfoVO> roles = userMapper.findUserRolesByUserId(user.getUserId());
            // 将角色对象列表转换为角色名称列表
            List<String> roleNames = roles.stream()
                    .map(RoleInfoVO::getRoleName)
                    .collect(java.util.stream.Collectors.toList());
            user.setRoleName(roleNames);
        }

        log.info("总记录数: {}", total);

        // 构建分页响应
        UserListResponseVO response = new UserListResponseVO();
        response.setRecords(userList);
        response.setTotal(total);
        response.setPageNum(originalCurrentPage); // 使用原始页码

        // 设置分页信息
        if (needPaging) {
            response.setPageSize(request.getPageSize());
            // 计算总页数
            int totalPages = (int) Math.ceil((double) total / request.getPageSize());
            response.setTotalPages(totalPages);
            // 计算是否有上一页/下一页
            response.setHasPrevious(originalCurrentPage > 1);
            response.setHasNext(originalCurrentPage < totalPages);
        } else {
            // 不分页时的默认值
            response.setPageSize(userList.size());
            response.setTotalPages(1);
            response.setHasPrevious(false);
            response.setHasNext(false);
        }

        log.info("用户列表查询完成");
        return response;
    }

    /**
     * 清理重复的用户角色关联记录
     * 用于修复数据不一致问题
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanDuplicateUserRoles() {
        log.info("开始清理重复的用户角色关联记录");
        userMapper.cleanDuplicateUserRoles();
        log.info("重复角色关联记录清理完成");
    }

    /**
     * 获取所有可用的角色选项
     * 用于下拉框选择
     *
     * @return 角色选项列表
     */
    @Override
    public List<RoleInfoVO> getRoleOptions() {
        log.info("开始获取角色选项列表");
        List<RoleInfoVO> roleOptions = userMapper.getRoleOptions();
        log.info("获取到角色选项数量: {}", roleOptions.size());
        return roleOptions;
    }

    /**
     * 更新用户角色分配状态
     * 用于角色管理中的授权和取消授权操作
     * 修改t_perm_user_role关联表的is_del状态
     *
     * @param request 更新用户角色分配请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserRole(UpdateUserRoleRequestVO request) {
        log.info("开始更新用户角色分配状态");
        log.info("用户ID: {}, 角色ID: {}, isDel: {}", request.getUserId(), request.getRoleId(), request.getIsDel());

        if (request.getIsDel() == false) {
            // 授权操作
            log.info("执行授权操作");

            // 检查关联记录是否存在
            int existsCount = userMapper.checkUserRoleExists(request.getUserId(), request.getRoleId());

            if (existsCount == 0) {
                // 无记录，创建新的关联记录
                log.info("关联记录不存在，创建新记录");
                SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
                long userRoleId = idGenerator.generateId();

                AddMemberRoleEntityVO roleEntity = new AddMemberRoleEntityVO();
                roleEntity.setId(userRoleId);
                roleEntity.setUserId(request.getUserId());
                roleEntity.setRoleId(request.getRoleId());
                roleEntity.setOrderInfo(1); // 默认顺序

                userMapper.insertPermUserRole(roleEntity);
                log.info("新关联记录创建成功");

            } else {
                // 记录存在，检查是否需要恢复
                log.info("关联记录已存在，检查是否需要恢复授权");
                userMapper.restoreUserRole(request.getUserId(), request.getRoleId());
                log.info("用户角色关联已恢复");
            }

        } else {
            // 取消授权操作
            log.info("执行取消授权操作");
            userMapper.revokeUserRole(request.getUserId(), request.getRoleId());
            log.info("用户角色关联已取消");
        }

        log.info("用户角色分配状态更新完成");
    }

    /**
     * 按部门分页查询用户列表
     * 支持包含子部门查询、多条件搜索和分页
     *
     * @param request 按部门查询用户列表请求参数
     * @return 按部门查询用户列表响应，包含分页信息
     */
    @Override
    public UserListByOrgResponseVO getUserListByOrg(UserListByOrgRequestVO request) {
        log.info("开始按部门分页查询用户列表");
        log.info("部门ID: {}, 包含子部门: {}, 页码: {}, 页大小: {}",
                request.getOrgId(), request.getIncludeSubOrgs(), request.getPageNum(), request.getPageSize());

        // 计算分页偏移量（MyBatis分页从0开始）
        int offset = (request.getPageNum() - 1) * request.getPageSize();

        // 创建一个临时变量存储偏移量，不修改原始请求对象
        int originalPageNum = request.getPageNum();

        // 临时设置偏移量用于查询
        request.setPageNum(offset);

        // 查询用户基础信息列表
        List<UserListByOrgItemVO> userList = userMapper.findUserListByOrgPage(request);

        // 恢复原始页码
        request.setPageNum(originalPageNum);
        log.info("查询到用户数量: {}", userList.size());

        // 为每个用户查询角色信息，转换为角色名称列表
        for (UserListByOrgItemVO user : userList) {
            List<RoleInfoVO> roles = userMapper.findUserRolesByUserId(user.getUserId());
            // 将角色对象列表转换为角色名称列表
            List<String> roleNames = roles.stream()
                    .map(RoleInfoVO::getRoleName)
                    .collect(java.util.stream.Collectors.toList());
            user.setRoleName(roleNames);

            // 构建完整的部门路径
            if (user.getOrgId() != null) {
                String orgPath = buildOrgPath(user.getOrgId());
                user.setOrgPath(orgPath);
            }
        }

        // 查询总记录数
        Long total = userMapper.countUserListByOrg(request);
        log.info("总记录数: {}", total);

        // 构建分页响应
        UserListByOrgResponseVO response = new UserListByOrgResponseVO();
        response.setRecords(userList);
        response.setTotal(total);
        response.setPageNum(originalPageNum); // 使用原始页码
        response.setPageSize(request.getPageSize());

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / request.getPageSize());
        response.setTotalPages(totalPages);

        // 计算是否有上一页/下一页
        response.setHasPrevious(originalPageNum > 1);
        response.setHasNext(originalPageNum < totalPages);

        log.info("按部门查询用户列表完成");
        return response;
    }

    /**
     * 检查用户重复
     * 实时检测用户名和账号是否重复，支持新增和编辑场景
     *
     * @param request 检查用户重复请求参数
     * @return 重复检查结果
     */
    @Override
    public CheckUserDuplicateResponseVO checkUserDuplicate(CheckUserDuplicateRequestVO request) {
        log.info("开始检查用户重复，检查类型: {}", request.getCheckType());

        CheckUserDuplicateResponseVO response = new CheckUserDuplicateResponseVO();
        boolean userNameDuplicate = false;
        boolean accountDuplicate = false;
        String userNameMessage = "";
        String accountMessage = "";

        try {
            // 根据传入的参数自动判断检查类型
            boolean hasUserName = request.getUserName() != null && !request.getUserName().trim().isEmpty();
            boolean hasAccount = request.getAccount() != null && !request.getAccount().trim().isEmpty();

            // 如果指定了检查类型，按指定类型检查
            if (request.getCheckType() != null && !request.getCheckType().trim().isEmpty()) {
                switch (request.getCheckType().toLowerCase()) {
                    case "username":
                        if (hasUserName) {
                            userNameDuplicate = checkUserNameDuplicate(request.getUserName(), request.getUserId());
                            userNameMessage = userNameDuplicate ? "检测到重名用户，建议确认是否为同一人" : "暂未发现重名用户";
                        }
                        break;

                    case "account":
                        if (hasAccount) {
                            accountDuplicate = checkAccountDuplicate(request.getAccount(), request.getUserId());
                            accountMessage = accountDuplicate ? "账号已存在" : "账号可以使用";
                        }
                        break;

                    case "both":
                        if (hasUserName) {
                            userNameDuplicate = checkUserNameDuplicate(request.getUserName(), request.getUserId());
                            userNameMessage = userNameDuplicate ? "检测到重名用户，建议确认是否为同一人" : "暂未发现重名用户";
                        }
                        if (hasAccount) {
                            accountDuplicate = checkAccountDuplicate(request.getAccount(), request.getUserId());
                            accountMessage = accountDuplicate ? "账号已存在" : "账号可以使用";
                        }
                        break;

                    default:
                        throw new IllegalArgumentException("不支持的检查类型: " + request.getCheckType());
                }
            } else {
                // 没有指定检查类型，根据传入的参数自动判断
                if (hasUserName) {
                    userNameDuplicate = checkUserNameDuplicate(request.getUserName(), request.getUserId());
                    userNameMessage = userNameDuplicate ? "检测到重名用户，建议确认是否为同一人" : "暂未发现重名用户";
                }
                if (hasAccount) {
                    accountDuplicate = checkAccountDuplicate(request.getAccount(), request.getUserId());
                    accountMessage = accountDuplicate ? "账号已存在" : "账号可以使用";
                }

                // 如果既没有用户名也没有账号，返回错误
                if (!hasUserName && !hasAccount) {
                    throw new IllegalArgumentException("请提供用户名或账号进行检查");
                }
            }

            // 设置响应结果
            response.setUserNameDuplicate(userNameDuplicate);
            response.setAccountDuplicate(accountDuplicate);
            response.setUserNameMessage(userNameMessage);
            response.setAccountMessage(accountMessage);

            // 判断整体是否可用（只有账号重复才阻止，姓名重复只是提示）
            boolean available = !accountDuplicate;
            response.setAvailable(available);

            if (available) {
                if (userNameDuplicate) {
                    response.setMessage("可以添加，但检测到重名用户，建议确认");
                } else {
                    response.setMessage("检查通过，可以使用");
                }
            } else {
                response.setMessage("账号已存在，无法添加");
            }

            log.info("用户重复检查完成，结果: {}", available ? "可用" : "存在重复");
            return response;

        } catch (Exception e) {
            log.error("检查用户重复失败", e);
            response.setAvailable(false);
            response.setMessage("检查失败: " + e.getMessage());
            return response;
        }
    }

    /**
     * 检查用户名是否重复
     *
     * @param userName 用户名
     * @param excludeUserId 要排除的用户ID（编辑时使用）
     * @return true-重复，false-不重复
     */
    private boolean checkUserNameDuplicate(String userName, Long excludeUserId) {
        if (userName == null || userName.trim().isEmpty()) {
            return false;
        }

        int count;
        if (excludeUserId != null) {
            // 编辑时排除自己
            count = userMapper.checkUserNameExistsForUpdate(userName.trim(), excludeUserId);
        } else {
            // 新增时检查
            count = userMapper.checkUserNameExists(userName.trim());
        }

        return count > 0;
    }

    /**
     * 检查账号是否重复
     *
     * @param account 账号
     * @param excludeUserId 要排除的用户ID（编辑时使用）
     * @return true-重复，false-不重复
     */
    private boolean checkAccountDuplicate(String account, Long excludeUserId) {
        if (account == null || account.trim().isEmpty()) {
            return false;
        }

        int count;
        if (excludeUserId != null) {
            // 编辑时排除自己
            count = userMapper.checkAccountExistsForUpdate(account.trim(), excludeUserId);
        } else {
            // 新增时检查
            count = userMapper.checkAccountExists(account.trim());
        }

        return count > 0;
    }

    /**
     * 构建部门的完整路径
     * 递归向上查找父部门，构建完整的路径字符串
     *
     * @param orgId 部门ID
     * @return 完整路径，如："公司/研发部/前端开发组"
     */
    private String buildOrgPath(Long orgId) {
        if (orgId == null) {
            return "";
        }

        // 这里可以调用组织架构服务来构建路径
        // 为了简化，暂时返回部门名称
        // 可以注入TOrgStructureService来获取完整路径
        return ""; // 暂时返回空，后续可以完善
    }

    /**
     * 安全的类型转换方法
     * 将Object转换为Long，处理不同的数据类型
     *
     * @param obj 要转换的对象
     * @return Long值，如果转换失败返回null
     */
    private Long convertToLong(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Long) {
            return (Long) obj;
        }

        if (obj instanceof Integer) {
            return ((Integer) obj).longValue();
        }

        if (obj instanceof String) {
            try {
                return Long.parseLong((String) obj);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串转换为Long: {}", obj);
                return null;
            }
        }

        if (obj instanceof Number) {
            return ((Number) obj).longValue();
        }

        log.warn("无法转换为Long的数据类型: {} ({})", obj, obj.getClass().getSimpleName());
        return null;
    }

    /**
     * 安全的 Boolean 类型转换方法
     *
     * @param obj 要转换的对象
     * @return Boolean值，如果转换失败返回null
     */
    private Boolean convertToBoolean(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }

        if (obj instanceof String) {
            String str = ((String) obj).toLowerCase().trim();
            if ("true".equals(str) || "1".equals(str) || "yes".equals(str)) {
                return true;
            } else if ("false".equals(str) || "0".equals(str) || "no".equals(str)) {
                return false;
            }
        }

        if (obj instanceof Number) {
            return ((Number) obj).intValue() != 0;
        }

        log.warn("无法转换为Boolean的数据类型: {} ({})", obj, obj.getClass().getSimpleName());
        return null;
    }

    /**
     * 安全的 Timestamp 到 String 转换方法
     *
     * @param obj 要转换的对象
     * @return String值，如果转换失败返回null
     */
    private String convertTimestampToString(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof String) {
            return (String) obj;
        }

        if (obj instanceof java.sql.Timestamp) {
            return obj.toString();
        }

        if (obj instanceof java.util.Date) {
            return obj.toString();
        }

        if (obj instanceof java.time.LocalDateTime) {
            return obj.toString();
        }

        log.warn("无法转换为String的时间类型: {} ({})", obj, obj.getClass().getSimpleName());
        return obj.toString(); // 尝试默认转换
    }

    // ==================== 新增：高性能部门树和用户查询接口实现 ====================

    /**
     * 获取纯部门树结构（不包含用户信息）
     * 高性能版本：只返回部门层级结构和用户数量统计
     * 用于快速展示部门树，支持按需加载用户
     *
     * @return 部门树结构列表，包含用户数量统计
     */
    @Override
    public List<DepartmentTreeOnlyVO> getDepartmentTreeOnly() {
        log.info("开始获取纯部门树结构（高性能版本）");
        long startTime = System.currentTimeMillis();

        try {
            // 使用 RECURSIVE CTE 一次性获取所有部门数据和用户数量统计
            List<Map<String, Object>> allData = userMapper.findDepartmentTreeOnly();
            log.info("RECURSIVE 查询返回部门数据条数: {}", allData.size());

            // 构建部门树结构
            List<DepartmentTreeOnlyVO> result = buildDepartmentTreeOnlyFromData(allData);

            long endTime = System.currentTimeMillis();
            log.info("纯部门树结构获取完成，耗时: {}ms", endTime - startTime);

            return result;
        } catch (Exception e) {
            log.error("获取纯部门树结构失败", e);
            throw new RuntimeException("获取部门树结构失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据部门ID获取该部门的用户列表
     * 支持只查询直属用户或包含子部门用户
     * 支持搜索、排序和分页功能
     *
     * @param request 部门用户查询请求参数
     * @return 部门用户列表响应
     */
    @Override
    public DepartmentUsersResponseVO getUsersByDepartment(DepartmentUsersRequestVO request) {
        log.info("开始获取部门用户列表");
        log.info("部门ID: {}, 包含子部门: {}, 分页: {}",
                request.getDepartmentId(), request.getIncludeChildren(), request.getEnablePaging());

        long startTime = System.currentTimeMillis();

        try {
            // 设置分页偏移量
            if (request.getEnablePaging() != null && request.getEnablePaging()) {
                int offset = (request.getCurrentPage() - 1) * request.getPageSize();
                request.setCurrentPage(offset); // 临时设置为偏移量
            }

            // 根据是否包含子部门选择不同的查询方法
            List<Map<String, Object>> userData;
            Long totalCount;

            if (request.getIncludeChildren() != null && request.getIncludeChildren()) {
                // 查询包含子部门的用户
                userData = userMapper.findUsersByDepartmentIdWithChildren(request);
                totalCount = userMapper.countUsersByDepartmentIdWithChildren(request);
            } else {
                // 只查询直属用户
                userData = userMapper.findUsersByDepartmentId(request);
                totalCount = userMapper.countUsersByDepartmentId(request);
            }

            log.info("查询到用户数据条数: {}, 总数: {}", userData.size(), totalCount);

            // 构建响应对象
            DepartmentUsersResponseVO response = buildDepartmentUsersResponse(request, userData, totalCount);

            long endTime = System.currentTimeMillis();
            log.info("部门用户列表获取完成，耗时: {}ms", endTime - startTime);

            return response;
        } catch (Exception e) {
            log.error("获取部门用户列表失败", e);
            throw new RuntimeException("获取部门用户列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从查询数据构建纯部门树结构
     *
     * @param allData 查询返回的所有数据
     * @return 部门树结构列表
     */
    private List<DepartmentTreeOnlyVO> buildDepartmentTreeOnlyFromData(List<Map<String, Object>> allData) {
        // 使用 Map 存储部门信息，避免重复创建
        Map<Long, DepartmentTreeOnlyVO> departmentMap = new HashMap<>();
        Map<Long, List<DepartmentTreeOnlyVO>> childrenMap = new HashMap<>();
        List<DepartmentTreeOnlyVO> rootDepartments = new ArrayList<>();

        // 处理查询结果，构建部门对象
        for (Map<String, Object> row : allData) {
            Long deptId = convertToLong(row.get("id"));
            String organName = (String) row.get("organname");
            Long parentId = convertToLong(row.get("parentid"));
            Integer orderInfo = convertToInteger(row.get("orderinfo"));
            Integer level = convertToInteger(row.get("level"));
            Integer userCount = convertToInteger(row.get("usercount"));
            Integer childDepartmentCount = convertToInteger(row.get("childdepartmentcount"));
            Boolean hasChildren = (Boolean) row.get("haschildren");
            Boolean isLeaf = (Boolean) row.get("isleaf");

            if (deptId != null) {
                DepartmentTreeOnlyVO dept = new DepartmentTreeOnlyVO();
                dept.setId(deptId);
                dept.setOrganName(organName);
                dept.setParentId(parentId);
                dept.setOrderInfo(orderInfo);
                dept.setLevel(level);
                dept.setFullPath(""); // 暂时设为空，后续可以在Java层构建
                dept.setUserCount(userCount != null ? userCount : 0);
                dept.setChildDepartmentCount(childDepartmentCount != null ? childDepartmentCount : 0);
                dept.setHasChildren(hasChildren != null ? hasChildren : false);
                dept.setIsLeaf(isLeaf != null ? isLeaf : true);
                dept.setChildren(new ArrayList<>());

                departmentMap.put(deptId, dept);

                // 分类根部门和子部门
                if (parentId == null || parentId == 0) {
                    rootDepartments.add(dept);
                } else {
                    childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(dept);
                }

                log.debug("创建部门: id={}, name={}, level={}, userCount={}",
                         deptId, organName, level, userCount);
            }
        }

        // 递归构建子部门关系
        buildChildrenRelationsForTreeOnly(rootDepartments, childrenMap);

        // 计算总用户数（包含子部门）
        calculateTotalUserCount(rootDepartments);

        log.debug("纯部门树构建完成，根部门数量: {}", rootDepartments.size());
        return rootDepartments;
    }

    /**
     * 递归构建子部门关系（纯部门树版本）
     *
     * @param departments 部门列表
     * @param childrenMap 子部门映射
     */
    private void buildChildrenRelationsForTreeOnly(List<DepartmentTreeOnlyVO> departments,
                                                   Map<Long, List<DepartmentTreeOnlyVO>> childrenMap) {
        for (DepartmentTreeOnlyVO dept : departments) {
            List<DepartmentTreeOnlyVO> children = childrenMap.get(dept.getId());
            if (children != null && !children.isEmpty()) {
                dept.getChildren().addAll(children);
                // 递归处理子部门
                buildChildrenRelationsForTreeOnly(children, childrenMap);
            }
        }
    }

    /**
     * 计算总用户数（包含子部门）
     *
     * @param departments 部门列表
     */
    private void calculateTotalUserCount(List<DepartmentTreeOnlyVO> departments) {
        for (DepartmentTreeOnlyVO dept : departments) {
            int totalCount = dept.getUserCount(); // 当前部门用户数

            // 递归计算子部门用户数
            if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                calculateTotalUserCount(dept.getChildren());
                for (DepartmentTreeOnlyVO child : dept.getChildren()) {
                    totalCount += child.getTotalUserCount();
                }
            }

            dept.setTotalUserCount(totalCount);
        }
    }

    /**
     * 构建部门用户响应对象
     *
     * @param request 原始请求参数
     * @param userData 用户数据
     * @param totalCount 总数量
     * @return 部门用户响应对象
     */
    private DepartmentUsersResponseVO buildDepartmentUsersResponse(DepartmentUsersRequestVO request,
                                                                   List<Map<String, Object>> userData,
                                                                   Long totalCount) {
        DepartmentUsersResponseVO response = new DepartmentUsersResponseVO();

        // 设置部门信息
        response.setDepartmentId(request.getDepartmentId());
        response.setIncludeChildren(request.getIncludeChildren());

        // 转换用户数据
        List<DepartmentUsersResponseVO.DepartmentUserItemVO> users = new ArrayList<>();
        int directUsers = 0;
        int childrenUsers = 0;

        for (Map<String, Object> row : userData) {
            DepartmentUsersResponseVO.DepartmentUserItemVO user =
                new DepartmentUsersResponseVO.DepartmentUserItemVO();

            user.setUserId(convertToLong(row.get("userid")));
            user.setUserName((String) row.get("username"));
            user.setAccount((String) row.get("account"));
            user.setIsDisabled((Boolean) row.get("isdisabled"));
            user.setDepartmentId(convertToLong(row.get("departmentid")));
            user.setDepartmentName((String) row.get("departmentname"));
            user.setIsDirect((Boolean) row.get("isdirect"));
            user.setUserType((String) row.get("usertype"));
            user.setEmployeeCode((String) row.get("employeecode"));
            user.setMobile((String) row.get("mobile"));
            user.setEmail((String) row.get("email"));

            users.add(user);

            // 统计直属和子部门用户数
            if (user.getIsDirect() != null && user.getIsDirect()) {
                directUsers++;
            } else {
                childrenUsers++;
            }
        }

        response.setUsers(users);
        response.setTotalUsers(totalCount.intValue());
        response.setDirectUsers(directUsers);
        response.setChildrenUsers(childrenUsers);

        // 设置分页信息
        if (request.getEnablePaging() != null && request.getEnablePaging()) {
            DepartmentUsersResponseVO.PaginationInfo pagination =
                new DepartmentUsersResponseVO.PaginationInfo();

            // 恢复原始页码（之前被设置为偏移量）
            int originalPage = (request.getCurrentPage() / request.getPageSize()) + 1;

            pagination.setCurrentPage(originalPage);
            pagination.setPageSize(request.getPageSize());
            pagination.setTotalRecords(totalCount);

            int totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());
            pagination.setTotalPages(totalPages);
            pagination.setHasPrevious(originalPage > 1);
            pagination.setHasNext(originalPage < totalPages);

            response.setPagination(pagination);
        }

        return response;
    }

    /**
     * 获取指定部门下的未授权用户（懒加载版本）
     * 用于懒加载场景，按需加载部门的未授权用户
     *
     * @param request 未授权用户查询请求参数（必须包含departmentId）
     * @return 指定部门下的未授权用户树形结构
     */
    @Override
    public List<DepartmentTreeVO> getUnauthorizedUsersByDepartmentLazy(UnauthorizedUsersRequestVO request) {
        log.info("开始懒加载获取部门用户，部门ID: {}, 角色ID: {}", request.getParentId(), request.getRoleId());

        if (request.getParentId() == null) {
            throw new IllegalArgumentException("部门ID不能为空");
        }

        if (request.getRoleId() == null) {
            throw new IllegalArgumentException("角色ID不能为空");
        }

        try {
            // 查询指定部门下的直属用户
            List<Map<String, Object>> rawData = userMapper.findUnauthorizedUsersByDepartment(
                request.getRoleId(),
                request.getParentId()
            );

            log.info("查询到{}条直属用户数据", rawData.size());

            // 构建用户节点列表（不需要部门树结构）
            List<DepartmentTreeVO> userNodes = buildUserNodesFromRawData(rawData);

            log.info("懒加载获取部门未授权用户完成，返回{}个用户节点", userNodes.size());
            return userNodes;

        } catch (Exception e) {
            log.error("懒加载获取部门未授权用户失败", e);
            throw new RuntimeException("获取部门未授权用户失败: " + e.getMessage(), e);
        }
    }

    /**
     * 从原始数据构建用户节点列表（懒加载版本）
     * 只返回用户节点，不包含部门结构
     */
    private List<DepartmentTreeVO> buildUserNodesFromRawData(List<Map<String, Object>> rawData) {
        List<DepartmentTreeVO> userNodes = new ArrayList<>();

        // 处理查询结果，只构建用户节点
        for (Map<String, Object> row : rawData) {
            Long userId = convertToLong(row.get("userid"));
            String userName = (String) row.get("username");

            // 只有用户数据才创建节点
            if (userId != null && userName != null) {
                DepartmentTreeVO userNode = new DepartmentTreeVO();
                userNode.setId(userId);
                userNode.setUserName(userName);
                userNode.setOrganName(null);  // 用户节点不需要部门名称
                userNode.setChildren(new ArrayList<>());  // 用户节点没有子节点

                userNodes.add(userNode);
            }
        }

        return userNodes;
    }


}
