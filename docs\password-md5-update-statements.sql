-- =====================================================
-- 密码MD5加密更新语句
-- 将现有明文密码转换为MD5加密格式
-- 执行前请备份数据库！
-- =====================================================

-- 1. 查看当前密码状态（执行前检查）
SELECT
    user_name,
    account,
    password,
    LENGTH(password) as password_length,
    CASE
        WHEN LENGTH(password) = 32 AND password ~ '^[a-f0-9]+$' THEN 'MD5格式'
        ELSE '明文格式'
    END as password_format
FROM t_user
WHERE is_del = false
ORDER BY user_name;

-- 2. 备份现有密码数据（可选，建议执行）
CREATE TABLE t_user_password_backup AS
SELECT id, user_name, account, password, create_time, modify_time
FROM t_user WHERE is_del = false;

-- 3. 常见密码的MD5值参考：
-- '123456' -> 'e10adc3949ba59abbe56e057f20f883e'
-- 'password123' -> '482c811da5d5b4bc6d497ffa98491e38'
-- 'abc' -> '900150983cd24fb0d6963f7d28e17f72'
-- 'a' -> '0cc175b9c0f1b6a831c399e269772661'
-- 'ccc' -> '9df62e693988eb4e1e1444ece0578579'
-- 'ddd' -> '77963b7a931377ad4ab5ad6a9cd718aa'
-- 'asdf' -> '912ec803b2ce49e4a541068d495ab570'
-- '456' -> '250cf8b51c773f3f8dc8b4be867a9a02'
-- '1' -> 'c4ca4238a0b923820dcc509a6f75849b'
-- 'zsw' -> 'b8c37e33defde51cf91e1e03e51657da'
-- '123' -> '202cb962ac59075b964b07152d234b70'
-- '23124' -> '1b0fd9efa5279c4203b7c70233d6de29'
-- 'Admin@123456' -> '7a57a5a743894a0e' (新的固定默认密码)

-- 4. 更新所有明文密码为MD5加密
UPDATE t_user SET password = 'e10adc3949ba59abbe56e057f20f883e', modify_time = CURRENT_TIMESTAMP WHERE password = '123456' AND is_del = false;
UPDATE t_user SET password = '482c811da5d5b4bc6d497ffa98491e38', modify_time = CURRENT_TIMESTAMP WHERE password = 'password123' AND is_del = false;
UPDATE t_user SET password = '900150983cd24fb0d6963f7d28e17f72', modify_time = CURRENT_TIMESTAMP WHERE password = 'abc' AND is_del = false;
UPDATE t_user SET password = '0cc175b9c0f1b6a831c399e269772661', modify_time = CURRENT_TIMESTAMP WHERE password = 'a' AND is_del = false;
UPDATE t_user SET password = '9df62e693988eb4e1e1444ece0578579', modify_time = CURRENT_TIMESTAMP WHERE password = 'ccc' AND is_del = false;
UPDATE t_user SET password = '77963b7a931377ad4ab5ad6a9cd718aa', modify_time = CURRENT_TIMESTAMP WHERE password = 'ddd' AND is_del = false;
UPDATE t_user SET password = '912ec803b2ce49e4a541068d495ab570', modify_time = CURRENT_TIMESTAMP WHERE password = 'asdf' AND is_del = false;
UPDATE t_user SET password = '250cf8b51c773f3f8dc8b4be867a9a02', modify_time = CURRENT_TIMESTAMP WHERE password = '456' AND is_del = false;
UPDATE t_user SET password = 'c4ca4238a0b923820dcc509a6f75849b', modify_time = CURRENT_TIMESTAMP WHERE password = '1' AND is_del = false;
UPDATE t_user SET password = 'b8c37e33defde51cf91e1e03e51657da', modify_time = CURRENT_TIMESTAMP WHERE password = 'zsw' AND is_del = false;
UPDATE t_user SET password = '202cb962ac59075b964b07152d234b70', modify_time = CURRENT_TIMESTAMP WHERE password = '123' AND is_del = false;
UPDATE t_user SET password = '1b0fd9efa5279c4203b7c70233d6de29', modify_time = CURRENT_TIMESTAMP WHERE password = '23124' AND is_del = false;

-- 验证更新结果
SELECT
    id,
    user_name,
    account,
    password,
    LENGTH(password) as password_length,
    CASE
        WHEN LENGTH(password) = 32 AND password ~ '^[a-f0-9]+$' THEN 'MD5格式'
        ELSE '其他格式'
    END as password_format
FROM t_user
WHERE is_del = false
ORDER BY id;

-- 统计更新结果
SELECT
    COUNT(*) as total_active_users,
    COUNT(CASE WHEN LENGTH(password) = 32 THEN 1 END) as md5_passwords,
    COUNT(CASE WHEN LENGTH(password) != 32 THEN 1 END) as non_md5_passwords
FROM t_user
WHERE is_del = false;
