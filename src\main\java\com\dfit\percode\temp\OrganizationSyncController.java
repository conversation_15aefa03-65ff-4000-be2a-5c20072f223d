package com.dfit.percode.temp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 组织架构同步控制器
 * 临时控制器，用于执行数据同步
 */
@RestController
@RequestMapping("/api/temp/org-sync")
@Slf4j
public class OrganizationSyncController {
    
    @Autowired
    private OrganizationSyncTool organizationSyncTool;
    
    /**
     * 执行数据同步
     */
    @PostMapping("/execute")
    public ResponseEntity<Map<String, Object>> executeSync() {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("收到组织架构数据同步请求");

            OrganizationSyncTool.SyncResult result = organizationSyncTool.executeSync();

            if (result.isSuccess()) {
                log.info("数据同步成功: {}", result);
                response.put("success", true);
                response.put("message", "数据同步成功");
                response.put("data", result);
                return ResponseEntity.ok(response);
            } else {
                log.error("数据同步失败: {}", result.getErrorMessage());
                response.put("success", false);
                response.put("message", "数据同步失败: " + result.getErrorMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("数据同步异常", e);
            response.put("success", false);
            response.put("message", "数据同步异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 使用指定文件路径执行同步
     */
    @PostMapping("/execute-with-path")
    public ResponseEntity<Map<String, Object>> executeSyncWithPath(@RequestParam String filePath) {
        Map<String, Object> response = new HashMap<>();

        try {
            log.info("收到组织架构数据同步请求，文件路径: {}", filePath);

            OrganizationSyncTool.SyncResult result = organizationSyncTool.executeSync(filePath);

            if (result.isSuccess()) {
                log.info("数据同步成功: {}", result);
                response.put("success", true);
                response.put("message", "数据同步成功");
                response.put("data", result);
                return ResponseEntity.ok(response);
            } else {
                log.error("数据同步失败: {}", result.getErrorMessage());
                response.put("success", false);
                response.put("message", "数据同步失败: " + result.getErrorMessage());
                return ResponseEntity.badRequest().body(response);
            }

        } catch (Exception e) {
            log.error("数据同步异常", e);
            response.put("success", false);
            response.put("message", "数据同步异常: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * 获取同步状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "组织架构同步工具已就绪");
        return ResponseEntity.ok(response);
    }
}
