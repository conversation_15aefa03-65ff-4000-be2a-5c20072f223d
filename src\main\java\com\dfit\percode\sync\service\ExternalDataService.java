package com.dfit.percode.sync.service;

import com.dfit.percode.sync.entity.ExternalDepartment;
import com.dfit.percode.sync.entity.ExternalEmployee;
import com.dfit.percode.sync.dto.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 外部数据访问服务
 * 负责从外部系统获取部门和员工数据
 * 支持多种数据获取方式：API调用、数据库直连、文件读取等
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Service
@Slf4j
public class ExternalDataService {

    @Autowired
    private RestTemplate restTemplate;
    
    @Value("${external.system.base-url:http://localhost:8080}")
    private String externalSystemBaseUrl;

    @Value("${external.system.api.departments:/api/data/departments}")
    private String departmentsApiPath;

    @Value("${external.system.api.employees:/api/data/employees}")
    private String employeesApiPath;

    @Value("${external.system.sync.department-threshold-hours:72}")
    private int departmentThresholdHours;

    @Value("${external.system.sync.employee-threshold-hours:24}")
    private int employeeThresholdHours;

    /**
     * 获取所有部门数据
     * 通过API接口获取指定时间范围内的部门数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 外部系统的部门列表
     */
    public List<ExternalDepartment> getDepartments(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("开始从外部系统获取部门数据，时间范围: {} - {}", startDate, endDate);

        try {
            // 手动构建URL，避免UriComponentsBuilder的自动编码
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String startDateStr = startDate.format(formatter);
            String endDateStr = endDate.format(formatter);

            // 手动构建URL，空格会被正确处理
            String url = externalSystemBaseUrl + departmentsApiPath +
                        "?startDate=" + startDateStr.replace(" ", "%20") +
                        "&endDate=" + endDateStr.replace(" ", "%20");

            log.info("调用外部API: {}", url);
            log.info("调用外部系统获取部门数据（外部系统已内置重试和分片机制）");

            ResponseEntity<ApiResponse<List<ExternalDepartment>>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<ApiResponse<List<ExternalDepartment>>>() {}
            );

            ApiResponse<List<ExternalDepartment>> apiResponse = response.getBody();
            if (apiResponse != null && apiResponse.getSuccess() && apiResponse.getData() != null) {
                log.info("成功获取 {} 个部门", apiResponse.getData().size());

                // 记录外部系统的处理信息（如果有的话）
                logProcessingInfo(apiResponse);

                return apiResponse.getData();
            } else {
                String errorMsg = apiResponse != null ? apiResponse.getMessage() : "未知错误";
                log.error("外部系统返回失败响应: {}", errorMsg);
                throw new RuntimeException("获取外部部门数据失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("获取部门数据失败", e);
            throw new RuntimeException("获取外部部门数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有部门数据（默认获取最近一天的数据）
     *
     * @return 外部系统的部门列表
     */
    public List<ExternalDepartment> getAllDepartments() {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(1);
        return getDepartments(startDate, endDate);
    }

    /**
     * 获取员工数据
     * 通过API接口获取指定时间范围内的员工数据
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 外部系统的员工列表
     */
    public List<ExternalEmployee> getEmployees(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("开始从外部系统获取员工数据，时间范围: {} - {}", startDate, endDate);

        // 手动构建URL，避免UriComponentsBuilder的自动编码
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String startDateStr = startDate.format(formatter);
        String endDateStr = endDate.format(formatter);

        // 手动构建URL，空格会被正确处理
        String url = externalSystemBaseUrl + employeesApiPath +
                    "?startDate=" + startDateStr.replace(" ", "%20") +
                    "&endDate=" + endDateStr.replace(" ", "%20");

        log.info("调用外部API: {}", url);

        try {
            log.info("调用外部系统获取员工数据（外部系统已内置重试和分片机制）");

            ResponseEntity<ApiResponse<List<ExternalEmployee>>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<ApiResponse<List<ExternalEmployee>>>() {}
            );

            ApiResponse<List<ExternalEmployee>> apiResponse = response.getBody();
            if (apiResponse != null && apiResponse.getSuccess() && apiResponse.getData() != null) {
                log.info("成功获取 {} 个员工", apiResponse.getData().size());

                // 记录外部系统的处理信息（如果有的话）
                logProcessingInfo(apiResponse);

                return apiResponse.getData();
            } else {
                String errorMsg = apiResponse != null ? apiResponse.getMessage() : "未知错误";
                log.error("外部系统返回失败响应: {}", errorMsg);
                throw new RuntimeException("获取外部员工数据失败: " + errorMsg);
            }
        } catch (Exception e) {
            log.error("获取员工数据失败", e);
            throw new RuntimeException("获取外部员工数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取所有员工数据（默认获取最近一天的数据）
     *
     * @return 外部系统的员工列表
     */
    public List<ExternalEmployee> getAllEmployees() {
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(1);
        return getEmployees(startDate, endDate);
    }

    /**
     * 获取增量部门数据
     * 根据最后更新时间获取变更的部门数据
     *
     * @param lastSyncTime 最后同步时间
     * @return 变更的部门列表
     */
    public List<ExternalDepartment> getIncrementalDepartments(LocalDateTime lastSyncTime) {
        log.info("开始获取增量部门数据，最后同步时间: {}", lastSyncTime);

        LocalDateTime endDate = LocalDateTime.now();
        return getDepartments(lastSyncTime, endDate);
    }

    /**
     * 获取增量员工数据
     * 根据最后更新时间获取变更的员工数据
     *
     * @param lastSyncTime 最后同步时间
     * @return 变更的员工列表
     */
    public List<ExternalEmployee> getIncrementalEmployees(LocalDateTime lastSyncTime) {
        log.info("开始获取增量员工数据，最后同步时间: {}", lastSyncTime);

        LocalDateTime endDate = LocalDateTime.now();
        return getEmployees(lastSyncTime, endDate);
    }

    /**
     * 测试外部系统连接
     *
     * @return 连接是否成功
     */
    public boolean testConnection() {
        try {
            String url = externalSystemBaseUrl + "/api/data/test";
            ResponseEntity<ApiResponse<String>> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    null,
                    new ParameterizedTypeReference<ApiResponse<String>>() {}
            );

            ApiResponse<String> apiResponse = response.getBody();
            if (apiResponse != null && apiResponse.getSuccess()) {
                log.info("外部系统连接测试成功: {}", apiResponse.getMessage());
                return true;
            } else {
                log.warn("外部系统连接测试失败: {}", apiResponse != null ? apiResponse.getMessage() : "响应为空");
                return false;
            }
        } catch (Exception e) {
            log.error("外部系统连接测试失败", e);
            return false;
        }
    }

    /**
     * 记录外部系统的处理信息
     * 解析并记录分片处理、重试等统计信息
     */
    private void logProcessingInfo(ApiResponse<?> apiResponse) {
        try {
            // 尝试从响应中获取处理信息
            Object data = apiResponse.getData();
            if (data instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> dataMap = (java.util.Map<String, Object>) data;
                Object processingInfo = dataMap.get("processingInfo");

                if (processingInfo instanceof java.util.Map) {
                    @SuppressWarnings("unchecked")
                    java.util.Map<String, Object> info = (java.util.Map<String, Object>) processingInfo;

                    String timeRange = (String) info.get("timeRange");
                    Boolean slicingEnabled = (Boolean) info.get("slicingEnabled");
                    Integer sliceCount = (Integer) info.get("sliceCount");
                    Integer successfulSlices = (Integer) info.get("successfulSlices");
                    Integer failedSlices = (Integer) info.get("failedSlices");
                    Integer totalRetries = (Integer) info.get("totalRetries");
                    String processingTime = (String) info.get("processingTime");
                    String sliceStrategy = (String) info.get("sliceStrategy");

                    log.info("外部系统处理统计:");
                    log.info("  时间范围: {}", timeRange);
                    log.info("  分片处理: {}", slicingEnabled != null && slicingEnabled ? "启用" : "未启用");
                    if (slicingEnabled != null && slicingEnabled) {
                        log.info("  分片策略: {}", sliceStrategy);
                        log.info("  分片统计: 总计={}, 成功={}, 失败={}", sliceCount, successfulSlices, failedSlices);
                    }
                    log.info("  重试次数: {}", totalRetries);
                    log.info("  处理耗时: {}", processingTime);
                }
            }
        } catch (Exception e) {
            log.debug("解析外部系统处理信息失败: {}", e.getMessage());
        }
    }

    /**
     * 获取同步策略建议
     * 根据外部系统的分片阈值和时间范围，给出同步策略建议
     */
    public String getSyncStrategyAdvice(LocalDateTime startDate, LocalDateTime endDate, String dataType) {
        long hours = java.time.Duration.between(startDate, endDate).toHours();

        if ("department".equalsIgnoreCase(dataType)) {
            if (hours <= departmentThresholdHours) {
                return String.format("建议策略：单次同步（%d小时 ≤ %d小时阈值）", hours, departmentThresholdHours);
            } else {
                return String.format("建议策略：外部系统将自动分片处理（%d小时 > %d小时阈值）", hours, departmentThresholdHours);
            }
        } else if ("employee".equalsIgnoreCase(dataType)) {
            if (hours <= employeeThresholdHours) {
                return String.format("建议策略：单次同步（%d小时 ≤ %d小时阈值）", hours, employeeThresholdHours);
            } else {
                return String.format("建议策略：外部系统将自动分片处理（%d小时 > %d小时阈值，12小时/片）", hours, employeeThresholdHours);
            }
        }

        return "未知数据类型";
    }

    /**
     * 检查时间范围是否会触发分片处理
     */
    public boolean willTriggerSlicing(LocalDateTime startDate, LocalDateTime endDate, String dataType) {
        long hours = java.time.Duration.between(startDate, endDate).toHours();

        if ("department".equalsIgnoreCase(dataType)) {
            return hours > departmentThresholdHours;
        } else if ("employee".equalsIgnoreCase(dataType)) {
            return hours > employeeThresholdHours;
        }

        return false;
    }

    // TODO: 如果需要数据库直连方式，可以添加以下方法
    
    /**
     * 通过数据库直连获取部门数据（备选方案）
     * 需要配置外部数据源
     */
    // public List<ExternalDepartment> getAllDepartmentsFromDatabase() {
    //     // 实现数据库直连逻辑
    // }
    
    /**
     * 通过数据库直连获取员工数据（备选方案）
     * 需要配置外部数据源
     */
    // public List<ExternalEmployee> getAllEmployeesFromDatabase() {
    //     // 实现数据库直连逻辑
    // }
}
