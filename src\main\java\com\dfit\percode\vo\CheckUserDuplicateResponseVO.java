package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 检查用户重复响应VO
 * 返回用户名和账号的重复检查结果
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "CheckUserDuplicateResponseVO", description = "检查用户重复响应结果")
public class CheckUserDuplicateResponseVO {

    @ApiModelProperty(value = "用户名是否重复", example = "false", notes = "true-检测到重名，false-未发现重名")
    private Boolean userNameDuplicate;

    @ApiModelProperty(value = "账号是否重复", example = "false", notes = "true-账号已存在，false-账号可用")
    private Boolean accountDuplicate;

    @ApiModelProperty(value = "用户名重复提示信息", example = "检测到重名用户，建议确认是否为同一人")
    private String userNameMessage;

    @ApiModelProperty(value = "账号重复提示信息", example = "账号已存在")
    private String accountMessage;

    @ApiModelProperty(value = "整体检查结果", example = "true",
                     notes = "true-可以添加，false-不能添加（仅账号重复时为false，姓名重复不影响）")
    private Boolean available;

    @ApiModelProperty(value = "整体提示信息", example = "可以添加，但检测到重名用户，建议确认")
    private String message;
}
