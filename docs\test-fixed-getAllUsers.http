### 测试修复后的 /users/getAllUsers 接口
### 验证空指针异常修复和性能优化效果

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试修复后的 getAllUsers 接口
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 2. 如果还有问题，可以测试原始方法
### 通过临时禁用优化方法来测试

### 预期结果：
### 1. 不再有 NullPointerException 异常
### 2. 接口能正常返回部门树结构
### 3. 响应时间显著提升（从50+秒降低到几秒）
### 4. 日志显示优化版本执行成功

### 成功的日志应该类似：
### "开始获取所有用户（组织架构树形结构）- 优化版本"
### "获取所有用户完成，耗时: XXXms"

### 如果优化方法还有问题，会自动回退到原始方法：
### "回退到原始查询方法"

### 预期的响应格式：
/*
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "id": 5001,
      "organName": "科技集团总公司",
      "children": [
        {
          "id": 6001,
          "userName": "张总经理"
        },
        {
          "id": 5002,
          "organName": "技术研发中心",
          "children": [
            {
              "id": 6002,
              "userName": "李技术总监"
            },
            {
              "id": 5007,
              "organName": "前端开发部",
              "children": [
                {
                  "id": 6004,
                  "userName": "赵前端经理"
                },
                {
                  "id": 6007,
                  "userName": "周前端开发"
                }
              ]
            },
            {
              "id": 5008,
              "organName": "后端开发部",
              "children": [
                {
                  "id": 6005,
                  "userName": "钱后端经理"
                },
                {
                  "id": 6008,
                  "userName": "吴后端开发"
                }
              ]
            },
            {
              "id": 5009,
              "organName": "测试质量部",
              "children": [
                {
                  "id": 6006,
                  "userName": "孙测试经理"
                },
                {
                  "id": 6009,
                  "userName": "郑测试工程师"
                }
              ]
            },
            {
              "id": 5010,
              "organName": "运维安全部",
              "children": [
                {
                  "id": 6010,
                  "userName": "王运维工程师"
                }
              ]
            }
          ]
        },
        {
          "id": 5003,
          "organName": "产品运营中心",
          "children": [
            {
              "id": 6003,
              "userName": "王产品总监"
            },
            {
              "id": 5013,
              "organName": "数据分析部",
              "children": [
                {
                  "id": 6012,
                  "userName": "陈实习生"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
*/

### 注意事项：
### 1. 确保应用已重启，代码修改生效
### 2. 观察控制台日志输出
### 3. 记录响应时间，验证性能提升
### 4. 如果还有问题，检查数据库连接和数据完整性
