package com.dfit.percode.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 使用信息VO
 * 用于删除功能中显示详细的使用情况信息
 *
 * <AUTHOR>
 * @date 2024-06-30
 */
@Data
@ApiModel(value = "使用信息VO", description = "删除使用情况信息")
public class UsageInfoVO {
    
    @ApiModelProperty("菜单ID（菜单删除时使用）")
    private String menuId;
    
    @ApiModelProperty("菜单名称（菜单删除时使用）")
    private String menuName;
    
    @ApiModelProperty("数据权限ID（数据权限删除时使用）")
    private String dataId;
    
    @ApiModelProperty("数据权限名称（数据权限删除时使用）")
    private String dataName;
    
    @ApiModelProperty("模块标识（模块删除时使用）")
    private String moduleIdentifier;
    
    @ApiModelProperty("模块名称（模块删除时使用）")
    private String moduleName;
    
    @ApiModelProperty("使用该权限的角色列表")
    private List<RoleUsageVO> usedByRoles;
    
    @ApiModelProperty("使用角色总数")
    private Integer totalRoleCount;
}
