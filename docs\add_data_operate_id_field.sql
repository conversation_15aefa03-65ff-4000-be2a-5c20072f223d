-- =====================================================
-- 数据库结构修改脚本：添加data_operate_id字段
-- 在t_roles_data_permission表中新增data_operate_id字段
-- 执行前请备份数据库！
-- =====================================================

-- 开始事务
BEGIN;

-- 添加新字段
ALTER TABLE t_roles_data_permission 
    ADD COLUMN data_operate_id varchar(255);

-- 添加字段注释
COMMENT ON COLUMN t_roles_data_permission.data_operate_id 
    IS '数据id与操作类型通过.拼接。格式：数据id.操作类型。1=查看 2=修改 3=下载 4=删除';

-- 验证字段添加成功
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 't_roles_data_permission' 
  AND column_name = 'data_operate_id';

-- 查看字段注释
SELECT 
    col_description(c.oid, a.attnum) as column_comment
FROM pg_class c
JOIN pg_attribute a ON a.attrelid = c.oid
WHERE c.relname = 't_roles_data_permission' 
  AND a.attname = 'data_operate_id';

-- 提交事务
COMMIT;

-- 如果需要回滚，使用下面这行
-- ROLLBACK;
