package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询菜单列表请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MenuListRequestVO", description = "查询菜单列表请求参数")
public class MenuListRequestVO {
    
    @ApiModelProperty(value = "模块标识，为空时查询所有模块", example = "permission_management")
    private String moduleIdentifier;

    @ApiModelProperty(value = "菜单名称，支持模糊查询", example = "用户")
    private String name;

    @ApiModelProperty(value = "是否包含禁用的菜单，默认true", example = "true")
    private Boolean isDisable = true;
}
