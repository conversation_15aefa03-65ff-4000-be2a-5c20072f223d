package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITRoleService;
import com.dfit.percode.userPerm.AddRoleParam;
import com.dfit.percode.vo.RoleListRequestVO;
import com.dfit.percode.vo.RoleListResponseVO;
import com.dfit.percode.vo.AddRoleRequestVO;
import com.dfit.percode.vo.AddRoleResponseVO;
import com.dfit.percode.vo.EditRoleRequestVO;
import com.dfit.percode.vo.DeleteRoleRequestVO;
import com.dfit.percode.vo.RoleDetailRequestVO;
import com.dfit.percode.vo.RoleDetailResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 角色信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@RestController
@Api(tags = "角色管理接口")
@RequestMapping("/roles")
public class TRoleController {
    @Autowired
    ITRoleService itRoleService;

    @RequestMapping(value = "/addNewRole",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增角色")
    public BaseResult addNewRole(@RequestBody AddRoleParam arp) {
        BaseResult baseResult = new BaseResult();
        itRoleService.addNewRole(arp);
        baseResult.setMessage("SUCCESS");
//        baseResult.setData(list);
        baseResult.setCode(200);
        return baseResult;
    }

    /**
     * 分页查询角色列表
     * 支持角色名称模糊搜索、状态筛选、删除状态筛选
     *
     * @param request 查询请求参数
     * @return 统一返回格式，包含角色列表和分页信息
     */
    @RequestMapping(value = "/getRoleList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "分页查询角色列表", notes = "支持角色名称模糊搜索、状态筛选、分页查询")
    public BaseResult getRoleList(@RequestBody RoleListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层分页查询角色列表
            RoleListResponseVO roleListResponse = itRoleService.getRoleList(request);

            // 按照前端格式设置返回结果：data直接是数组，total在根级别
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(roleListResponse.getRecords()); // data直接是角色数组
            baseResult.setTotal(roleListResponse.getTotal().intValue()); // total在根级别

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("查询角色列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 新增角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的设置
     *
     * @param request 新增角色请求参数
     * @return 统一返回格式，包含新增角色信息
     */
    @RequestMapping(value = "/addRole", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增角色", notes = "支持角色基本信息、菜单权限、数据权限的设置")
    public BaseResult addRole(@RequestBody @Valid AddRoleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增角色
            AddRoleResponseVO addRoleResponse = itRoleService.addRole(request);

            // 按照前端格式设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(addRoleResponse);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("新增角色失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 修改角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的修改
     *
     * @param request 修改角色请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/editRole", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改角色", notes = "支持角色基本信息、菜单权限、数据权限的修改")
    public BaseResult editRole(@RequestBody @Valid EditRoleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层修改角色
            itRoleService.editRole(request);

            // 按照前端格式设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("保存成功");
            baseResult.setData(null);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("修改角色失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除角色（逻辑删除）
     * 检查角色是否被用户使用，如果有关联则不能删除
     *
     * @param request 删除角色请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteRole", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除角色", notes = "逻辑删除，检查角色是否被用户使用")
    public BaseResult deleteRole(@RequestBody @Valid DeleteRoleRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除角色
            itRoleService.deleteRole(request);

            // 按照前端格式设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("删除成功");
            baseResult.setData(null);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("删除角色失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取角色详情
     * 查询角色基本信息和已关联的权限ID列表
     * 用于角色编辑时的数据回显
     *
     * @param request 角色详情查询请求参数
     * @return 统一返回格式，包含角色详情信息
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取角色详情", notes = "查询角色基本信息和已关联的权限ID列表，用于编辑时数据回显")
    public BaseResult getRoleDetail(@RequestBody @Valid RoleDetailRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取角色详情
            RoleDetailResponseVO roleDetailResponse = itRoleService.getRoleDetail(request);

            // 按照前端格式设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(roleDetailResponse);

        } catch (IllegalArgumentException e) {
            // 参数验证异常
            baseResult.setCode(400);
            baseResult.setMessage("参数错误：" + e.getMessage());
            baseResult.setData(null);
        } catch (Exception e) {
            // 其他异常
            baseResult.setCode(500);
            baseResult.setMessage("获取角色详情失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
