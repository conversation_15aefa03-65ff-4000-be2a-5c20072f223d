package com.dfit.percode.mapper;

import com.dfit.percode.sync.dto.EmployeePositionBatch;
import com.dfit.percode.sync.dto.EmployeeTitleBatch;
import com.dfit.percode.sync.dto.EmployeeSystemBatch;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 员工扩展数据Mapper接口
 * 用于处理员工岗位、职称、系统标识等扩展信息
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface EmployeeExtendedMapper {

    // =====================================================
    // 部门子表相关操作
    // =====================================================

    /**
     * 插入部门子表信息
     */
    @Insert("INSERT INTO t_department_child (" +
            "id, dept_id, guid, dept_uuid, source_system, source_data_nm, " +
            "udef1, udef2, udef3, udef4, udef5, udef6, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES (" +
            "#{id}, #{deptId}, #{guid}, #{deptUuid}, #{sourceSystem}, #{sourceDataNm}, " +
            "#{udef1}, #{udef2}, #{udef3}, #{udef4}, #{udef5}, #{udef6}, #{externalId}, " +
            "#{syncStatus}, #{lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false" +
            ")")
    void insertDepartmentChild(@Param("id") Long id,
                              @Param("deptId") Long deptId,
                              @Param("guid") String guid,
                              @Param("deptUuid") String deptUuid,
                              @Param("sourceSystem") String sourceSystem,
                              @Param("sourceDataNm") String sourceDataNm,
                              @Param("udef1") String udef1,
                              @Param("udef2") String udef2,
                              @Param("udef3") String udef3,
                              @Param("udef4") String udef4,
                              @Param("udef5") String udef5,
                              @Param("udef6") String udef6,
                              @Param("externalId") Integer externalId,
                              @Param("syncStatus") String syncStatus,
                              @Param("lastSyncTime") java.time.LocalDateTime lastSyncTime);

    // =====================================================
    // 员工岗位相关操作
    // =====================================================

    /**
     * 插入员工岗位信息
     */
    @Insert("INSERT INTO t_employee_position (" +
            "id, user_id, guid, employee_mdm_id, position_code, org_code, department_code, " +
            "is_primary, status, is_active, position_detail_code, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES (" +
            "#{id}, #{userId}, #{guid}, #{employeeMdmId}, #{positionCode}, #{orgCode}, #{departmentCode}, " +
            "#{isPrimary}, #{status}, #{isActive}, #{positionDetailCode}, #{externalId}, " +
            "#{syncStatus}, #{lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false" +
            ")")
    void insertEmployeePosition(@Param("id") Long id,
                               @Param("userId") Long userId,
                               @Param("guid") String guid,
                               @Param("employeeMdmId") String employeeMdmId,
                               @Param("positionCode") String positionCode,
                               @Param("orgCode") String orgCode,
                               @Param("departmentCode") String departmentCode,
                               @Param("isPrimary") String isPrimary,
                               @Param("status") String status,
                               @Param("isActive") String isActive,
                               @Param("positionDetailCode") String positionDetailCode,
                               @Param("externalId") Long externalId,
                               @Param("syncStatus") String syncStatus,
                               @Param("lastSyncTime") java.time.LocalDateTime lastSyncTime);

    /**
     * 根据MDM ID查询用户ID
     * 注意：由于表结构简化，mdm_id字段已移除，此方法暂时不可用
     * 可以考虑通过员工扩展表来查询
     */
    // @Select("SELECT id FROM t_user WHERE mdm_id = #{mdmId} AND is_del = false ORDER BY create_time DESC LIMIT 1")
    default Long findUserIdByMdmId(@Param("mdmId") String mdmId) {
        // 暂时返回null，避免编译错误
        return null;
    }

    /**
     * 查询用户的主岗位组织代码
     */
    @Select("SELECT org_code FROM t_employee_position " +
            "WHERE user_id = #{userId} AND is_primary = '1' AND is_del = false " +
            "ORDER BY create_time DESC LIMIT 1")
    String findPrimaryOrgCodeByUserId(@Param("userId") Long userId);

    /**
     * 根据组织代码查询部门ID
     * 注意：由于表结构简化，org_code字段已移除，此方法暂时不可用
     * 可以考虑通过部门名称进行模糊匹配
     */
    // @Select("SELECT id FROM t_org_structure WHERE org_code = #{orgCode} AND is_del = false ORDER BY create_time DESC LIMIT 1")
    default Long findDepartmentIdByOrgCode(@Param("orgCode") String orgCode) {
        // 暂时返回null，避免编译错误
        return null;
    }

    /**
     * 更新用户的部门归属
     */
    @Update("UPDATE t_user SET organ_affiliation = #{departmentId}, modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{userId} AND is_del = false")
    void updateUserDepartmentAffiliation(@Param("userId") Long userId, @Param("departmentId") Long departmentId);

    // =====================================================
    // 员工职称相关操作
    // =====================================================

    /**
     * 插入员工职称信息
     */
    @Insert("INSERT INTO t_employee_title (" +
            "id, user_id, guid, employee_mdm_id, title_code, title_type, title_level, " +
            "title_name, status, title_category, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES (" +
            "#{id}, #{userId}, #{guid}, #{employeeMdmId}, #{titleCode}, #{titleType}, #{titleLevel}, " +
            "#{titleName}, #{status}, #{titleCategory}, #{externalId}, " +
            "#{syncStatus}, #{lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false" +
            ")")
    void insertEmployeeTitle(@Param("id") Long id,
                            @Param("userId") Long userId,
                            @Param("guid") String guid,
                            @Param("employeeMdmId") String employeeMdmId,
                            @Param("titleCode") String titleCode,
                            @Param("titleType") String titleType,
                            @Param("titleLevel") String titleLevel,
                            @Param("titleName") String titleName,
                            @Param("status") String status,
                            @Param("titleCategory") String titleCategory,
                            @Param("externalId") Long externalId,
                            @Param("syncStatus") String syncStatus,
                            @Param("lastSyncTime") java.time.LocalDateTime lastSyncTime);

    // =====================================================
    // 员工系统标识相关操作
    // =====================================================

    /**
     * 插入员工系统标识信息
     */
    @Insert("INSERT INTO t_employee_system (" +
            "id, user_id, guid, employee_mdm_id, system_code, system_data_id, org_code, " +
            "department_code, employee_code, login_account, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES (" +
            "#{id}, #{userId}, #{guid}, #{employeeMdmId}, #{systemCode}, #{systemDataId}, #{orgCode}, " +
            "#{departmentCode}, #{employeeCode}, #{loginAccount}, #{externalId}, " +
            "#{syncStatus}, #{lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false" +
            ")")
    void insertEmployeeSystem(@Param("id") Long id,
                             @Param("userId") Long userId,
                             @Param("guid") String guid,
                             @Param("employeeMdmId") String employeeMdmId,
                             @Param("systemCode") String systemCode,
                             @Param("systemDataId") String systemDataId,
                             @Param("orgCode") String orgCode,
                             @Param("departmentCode") String departmentCode,
                             @Param("employeeCode") String employeeCode,
                             @Param("loginAccount") String loginAccount,
                             @Param("externalId") Long externalId,
                             @Param("syncStatus") String syncStatus,
                             @Param("lastSyncTime") java.time.LocalDateTime lastSyncTime);

    // =====================================================
    // 查询所有需要更新部门归属的用户
    // =====================================================

    /**
     * 查询所有有主岗位但部门归属为空的用户
     * 注意：由于表结构简化，移除了mdm_id字段的引用
     */
    @Select("SELECT u.id as \"userId\", u.user_name as \"userName\", ep.org_code as \"orgCode\", " +
            "ep.employee_mdm_id as \"employeeMdmId\" " +
            "FROM t_user u " +
            "INNER JOIN (" +
            "  SELECT DISTINCT ON (user_id) user_id, org_code, employee_mdm_id " +
            "  FROM t_employee_position " +
            "  WHERE is_primary = '1' AND is_del = false " +
            "  ORDER BY user_id, last_sync_time DESC, create_time DESC" +
            ") ep ON u.id = ep.user_id " +
            "WHERE u.is_del = false " +
            "AND (u.organ_affiliation IS NULL OR u.organ_affiliation = 0)")
    java.util.List<java.util.Map<String, Object>> findUsersNeedDepartmentUpdate();

    /**
     * 查询所有员工岗位数据的统计信息（用于调试）
     */
    @Select("SELECT " +
            "COUNT(*) as total_positions, " +
            "COUNT(CASE WHEN org_code IS NOT NULL AND org_code != '' THEN 1 END) as has_org_code, " +
            "COUNT(CASE WHEN org_code IS NULL OR org_code = '' THEN 1 END) as no_org_code, " +
            "COUNT(CASE WHEN is_primary = '1' THEN 1 END) as primary_positions, " +
            "COUNT(CASE WHEN user_id IS NOT NULL THEN 1 END) as has_user_id, " +
            "COUNT(CASE WHEN user_id IS NULL THEN 1 END) as no_user_id, " +
            "COUNT(DISTINCT user_id) as unique_users, " +
            "COUNT(DISTINCT employee_mdm_id) as unique_employees " +
            "FROM t_employee_position WHERE is_del = false")
    java.util.Map<String, Object> getPositionStatistics();

    /**
     * 查询用户表统计信息（用于调试）
     */
    @Select("SELECT COUNT(*) as total_users, " +
            "COUNT(CASE WHEN organ_affiliation IS NULL OR organ_affiliation = 0 THEN 1 END) as no_department " +
            "FROM t_user WHERE is_del = false")
    java.util.Map<String, Object> getUserStatistics();

    /**
     * 简单查询员工岗位数据（用于调试）
     */
    @Select("SELECT user_id, org_code, is_primary, employee_mdm_id " +
            "FROM t_employee_position " +
            "WHERE is_primary = '1' AND is_del = false " +
            "LIMIT 10")
    java.util.List<java.util.Map<String, Object>> getPositionSamples();

    /**
     * 测试JOIN查询（用于调试）
     * 每个用户只返回最新的主岗位
     */
    @Select("SELECT u.id as \"userId\", u.user_name as \"userName\", ep.org_code as \"orgCode\" " +
            "FROM t_user u " +
            "INNER JOIN (" +
            "  SELECT DISTINCT ON (user_id) user_id, org_code " +
            "  FROM t_employee_position " +
            "  WHERE is_primary = '1' AND is_del = false " +
            "  ORDER BY user_id, last_sync_time DESC, create_time DESC" +
            ") ep ON u.id = ep.user_id " +
            "WHERE u.is_del = false " +
            "LIMIT 10")
    java.util.List<java.util.Map<String, Object>> testJoinQuery();

    /**
     * 清理重复的员工岗位数据，只保留最新的记录
     * 使用临时表方式避免PostgreSQL子查询限制
     */
    @Delete("WITH duplicate_positions AS (" +
            "SELECT id, ROW_NUMBER() OVER (PARTITION BY user_id, employee_mdm_id, org_code, position_code ORDER BY last_sync_time DESC) as rn " +
            "FROM t_employee_position WHERE is_del = false" +
            ") DELETE FROM t_employee_position WHERE id IN (SELECT id FROM duplicate_positions WHERE rn > 1)")
    int cleanDuplicatePositions();

    /**
     * 清理重复的员工职称数据，只保留最新的记录
     * 使用临时表方式避免PostgreSQL子查询限制
     */
    @Delete("WITH duplicate_titles AS (" +
            "SELECT id, ROW_NUMBER() OVER (PARTITION BY user_id, employee_mdm_id, org_code ORDER BY last_sync_time DESC) as rn " +
            "FROM t_employee_title WHERE is_del = false" +
            ") DELETE FROM t_employee_title WHERE id IN (SELECT id FROM duplicate_titles WHERE rn > 1)")
    int cleanDuplicateTitles();

    /**
     * 清理重复的员工系统数据，只保留最新的记录
     * 使用临时表方式避免PostgreSQL子查询限制
     */
    @Delete("WITH duplicate_systems AS (" +
            "SELECT id, ROW_NUMBER() OVER (PARTITION BY user_id, employee_mdm_id, system_code ORDER BY last_sync_time DESC) as rn " +
            "FROM t_employee_system WHERE is_del = false" +
            ") DELETE FROM t_employee_system WHERE id IN (SELECT id FROM duplicate_systems WHERE rn > 1)")
    int cleanDuplicateSystems();

    // =====================================================
    // 孤儿记录关联方法
    // =====================================================

    /**
     * 关联员工岗位表的孤儿记录
     * @return 关联成功的记录数
     */
    @Update("UPDATE t_employee_position " +
            "SET user_id = (SELECT u.id FROM t_user u WHERE u.account = t_employee_position.employee_mdm_id AND u.is_del = false) " +
            "WHERE user_id IS NULL " +
            "AND employee_mdm_id IS NOT NULL " +
            "AND EXISTS (SELECT 1 FROM t_user u WHERE u.account = t_employee_position.employee_mdm_id AND u.is_del = false)")
    int linkOrphanPositionRecords();

    /**
     * 关联员工职称表的孤儿记录
     * @return 关联成功的记录数
     */
    @Update("UPDATE t_employee_title " +
            "SET user_id = (SELECT u.id FROM t_user u WHERE u.account = t_employee_title.employee_mdm_id AND u.is_del = false) " +
            "WHERE user_id IS NULL " +
            "AND employee_mdm_id IS NOT NULL " +
            "AND EXISTS (SELECT 1 FROM t_user u WHERE u.account = t_employee_title.employee_mdm_id AND u.is_del = false)")
    int linkOrphanTitleRecords();

    /**
     * 关联员工系统标识表的孤儿记录
     * @return 关联成功的记录数
     */
    @Update("UPDATE t_employee_system " +
            "SET user_id = (SELECT u.id FROM t_user u WHERE u.account = t_employee_system.employee_mdm_id AND u.is_del = false) " +
            "WHERE user_id IS NULL " +
            "AND employee_mdm_id IS NOT NULL " +
            "AND EXISTS (SELECT 1 FROM t_user u WHERE u.account = t_employee_system.employee_mdm_id AND u.is_del = false)")
    int linkOrphanSystemRecords();

    // =====================================================
    // 重复数据清理方法
    // =====================================================

    // 注意：由于表结构简化，以下方法暂时不可用
    // cleanDuplicateUsersByMdmId() 和 updateUserById() 方法
    // 需要扩展字段支持才能正常工作

    // =====================================================
    // 批量upsert方法（性能优化 + 去重）
    // =====================================================

    /**
     * 批量插入部门数据
     * 注意：包含data_source字段，用于标识数据来源
     */
    @Insert("<script>" +
            "INSERT INTO t_org_structure (" +
            "id, organ_name, pre_id, order_info, data_source, is_del, create_time, modify_time" +
            ") VALUES " +
            "<foreach collection='departments' item='dept' separator=','>" +
            "(#{dept.id}, #{dept.organName}, #{dept.preId}, #{dept.orderInfo}, " +
            "#{dept.dataSource}, #{dept.isDel}, #{dept.createTime}, #{dept.modifyTime})" +
            "</foreach>" +
            "</script>")
    void batchInsertDepartments(@Param("departments") java.util.List<com.dfit.percode.entity.TOrgStructure> departments);

    /**
     * 批量插入员工岗位信息
     */
    @Insert("<script>" +
            "INSERT INTO t_employee_position (" +
            "id, user_id, guid, employee_mdm_id, position_code, org_code, department_code, " +
            "is_primary, status, is_active, position_detail_code, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES " +
            "<foreach collection='positions' item='item' separator=','>" +
            "(#{item.id}, #{item.userId}, #{item.guid}, #{item.employeeMdmId}, #{item.positionCode}, " +
            "#{item.orgCode}, #{item.departmentCode}, #{item.isPrimary}, #{item.status}, " +
            "#{item.isActive}, #{item.positionDetailCode}, #{item.externalId}, " +
            "#{item.syncStatus}, #{item.lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false)" +
            "</foreach>" +
            "</script>")
    void batchInsertEmployeePositions(@Param("positions") java.util.List<EmployeePositionBatch> positions);

    /**
     * 批量插入员工职称信息
     */
    @Insert("<script>" +
            "INSERT INTO t_employee_title (" +
            "id, user_id, guid, employee_mdm_id, title_code, title_type, title_level, " +
            "title_name, status, title_category, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES " +
            "<foreach collection='titles' item='item' separator=','>" +
            "(#{item.id}, #{item.userId}, #{item.guid}, #{item.employeeMdmId}, #{item.titleCode}, " +
            "#{item.titleType}, #{item.titleLevel}, #{item.titleName}, #{item.status}, " +
            "#{item.titleCategory}, #{item.externalId}, " +
            "#{item.syncStatus}, #{item.lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false)" +
            "</foreach>" +
            "</script>")
    void batchInsertEmployeeTitles(@Param("titles") java.util.List<EmployeeTitleBatch> titles);

    /**
     * 批量插入员工系统标识信息
     */
    @Insert("<script>" +
            "INSERT INTO t_employee_system (" +
            "id, user_id, guid, employee_mdm_id, system_code, system_data_id, org_code, " +
            "department_code, employee_code, login_account, external_id, " +
            "sync_status, last_sync_time, create_time, modify_time, is_del" +
            ") VALUES " +
            "<foreach collection='systems' item='item' separator=','>" +
            "(#{item.id}, #{item.userId}, #{item.guid}, #{item.employeeMdmId}, #{item.systemCode}, " +
            "#{item.systemDataId}, #{item.orgCode}, #{item.departmentCode}, #{item.employeeCode}, " +
            "#{item.loginAccount}, #{item.externalId}, " +
            "#{item.syncStatus}, #{item.lastSyncTime}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, false)" +
            "</foreach>" +
            "</script>")
    void batchInsertEmployeeSystems(@Param("systems") java.util.List<EmployeeSystemBatch> systems);

    // =====================================================
    // 批量查询方法（性能优化）
    // =====================================================

    /**
     * 批量查询用户ID
     * 注意：由于表结构简化，mdm_id字段已移除，此方法暂时不可用
     */
    // @Select("<script>" +
    //         "SELECT mdm_id, id FROM t_user " +
    //         "WHERE mdm_id IN " +
    //         "<foreach collection='mdmIds' item='mdmId' open='(' separator=',' close=')'>" +
    //         "#{mdmId}" +
    //         "</foreach>" +
    //         " AND is_del = false" +
    //         "</script>")
    default java.util.List<java.util.Map<String, Object>> batchFindUserIdsByMdmIds(@Param("mdmIds") java.util.List<String> mdmIds) {
        // 暂时返回空列表，避免编译错误
        return new java.util.ArrayList<>();
    }
}
