# 修改角色和删除角色接口测试用例

## 📋 接口信息

### 修改角色接口
- **接口路径**: `POST /roles/editRole`
- **接口描述**: 修改角色，支持角色基本信息、菜单权限、数据权限的修改
- **Content-Type**: `application/json`

### 删除角色接口
- **接口路径**: `POST /roles/deleteRole`
- **接口描述**: 删除角色（逻辑删除），检查角色是否被用户使用
- **Content-Type**: `application/json`

## 🧪 修改角色测试用例

### 测试用例1：基础角色信息修改
**测试目的**: 验证基本的角色信息修改功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907,
    "roleName": "修改后的角色名称",
    "orderInfo": 15,
    "isDisable": false
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "保存成功",
  "data": null
}
```

**验证点**:
- ✅ 返回状态码为200
- ✅ 角色基本信息更新成功
- ✅ 数据库中角色信息已更新

---

### 测试用例2：修改角色并更新菜单权限
**测试目的**: 验证角色修改时同时更新菜单权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907,
    "roleName": "更新权限的角色",
    "orderInfo": 10,
    "isDisable": false,
    "menuName": [
      {
        "id": 6001,
        "name": "系统管理",
        "moduleIdentifier": "system"
      },
      {
        "id": 6002,
        "name": "用户管理",
        "moduleIdentifier": "user"
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "保存成功",
  "data": null
}
```

**验证点**:
- ✅ 角色信息更新成功
- ✅ 原有菜单权限被删除
- ✅ 新菜单权限创建成功

---

### 测试用例3：修改角色并更新数据权限
**测试目的**: 验证角色修改时同时更新数据权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907,
    "roleName": "数据权限角色",
    "orderInfo": 5,
    "isDisable": false,
    "dataPermission": [
      {
        "id": 9002,
        "name": "部门信息管理",
        "moduleIdentifier": "dept_data",
        "dataType": 2,
        "operateTypes": [1, 2, 3]
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "保存成功",
  "data": null
}
```

**验证点**:
- ✅ 角色信息更新成功
- ✅ 原有数据权限被删除
- ✅ 新数据权限创建成功

---

### 测试用例4：完整权限修改
**测试目的**: 验证角色修改时同时更新菜单权限和数据权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907,
    "roleName": "完整权限修改角色",
    "orderInfo": 1,
    "isDisable": false,
    "menuName": [
      {
        "id": 6004,
        "name": "用户列表",
        "moduleIdentifier": "user"
      }
    ],
    "dataPermission": [
      {
        "id": 9001,
        "name": "用户信息查看",
        "moduleIdentifier": "user_data",
        "dataType": 1,
        "operateTypes": [1, 3]
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "保存成功",
  "data": null
}
```

**验证点**:
- ✅ 角色信息更新成功
- ✅ 菜单权限和数据权限都更新成功

---

## 🧪 删除角色测试用例

### 测试用例5：正常删除角色
**测试目的**: 验证正常的角色删除功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/deleteRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**验证点**:
- ✅ 返回状态码为200
- ✅ 角色逻辑删除成功（is_del=true）
- ✅ 相关权限关联记录逻辑删除

---

## ❌ 错误场景测试

### 错误场景1：修改不存在的角色
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 999999999999999999,
    "roleName": "不存在的角色"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色不存在或已被删除，ID：999999999999999999",
  "data": null
}
```

### 错误场景2：修改角色名称重复
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1930806593885179907,
    "roleName": "超级管理员"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色名称已存在：超级管理员",
  "data": null
}
```

### 错误场景3：删除不存在的角色
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/deleteRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 999999999999999999
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色不存在或已被删除，ID：999999999999999999",
  "data": null
}
```

### 错误场景4：删除被用户使用的角色
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/deleteRole \
  -H "Content-Type: application/json" \
  -d '{
    "id": 1
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色正在被用户使用，无法删除",
  "data": null
}
```

### 错误场景5：缺少必填参数
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/editRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "测试角色"
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色ID不能为空",
  "data": null
}
```

## 🔧 数据库验证

### 验证角色修改
```sql
-- 查询修改后的角色信息
SELECT * FROM t_role WHERE id = '角色ID';

-- 查询角色的菜单权限
SELECT rmp.*, mp.name as menu_name 
FROM t_roles_menu_permission rmp
LEFT JOIN t_menu_permission mp ON rmp.menu_id = mp.id
WHERE rmp.role_id = '角色ID' AND rmp.is_del = false;

-- 查询角色的数据权限
SELECT rdp.*, dp.name as permission_name 
FROM t_roles_data_permission rdp
LEFT JOIN t_data_permission dp ON rdp.data_id = dp.id
WHERE rdp.role_id = '角色ID' AND rdp.is_del = false;
```

### 验证角色删除
```sql
-- 查询删除后的角色状态
SELECT * FROM t_role WHERE id = '角色ID';

-- 验证权限关联是否被删除
SELECT COUNT(*) FROM t_roles_menu_permission WHERE role_id = '角色ID' AND is_del = false;
SELECT COUNT(*) FROM t_roles_data_permission WHERE role_id = '角色ID' AND is_del = false;
```

## ✅ 验收标准

1. **功能完整性**: 所有测试用例都能正常执行
2. **数据一致性**: 数据库中的数据与操作结果一致
3. **事务完整性**: 失败时能正确回滚，不产生脏数据
4. **参数验证**: 能正确验证和处理各种参数错误
5. **权限更新**: 修改角色时能正确删除旧权限并创建新权限
6. **逻辑删除**: 删除角色时使用逻辑删除，不物理删除数据
