# 获取部门结构树接口测试文档

## 接口信息
- **接口路径**: `POST /t-org-structure/tree`
- **接口描述**: 获取部门结构树，用于移动部门时显示可选择的部门树形结构
- **Content-Type**: `application/json`

## 快速使用指南

### 最常用的请求格式（复制即用）

**1. 普通获取部门树（最简单）**
```json
{}                                           // 空对象，获取完整部门树
```

**2. 移动部门时获取可选树**
```json
{
    "excludeOrgId": "要移动的部门ID",          // 排除指定部门及其子部门
    "includeDeleted": false,                  // 不显示已删除部门
    "maxLevel": 0                            // 显示所有层级
}
```

**3. 限制显示层级（避免层级过深）**
```json
{
    "includeDeleted": false,                  // 不显示已删除部门
    "maxLevel": 2                            // 只显示到第2层
}
```

## 请求参数

### OrgStructureTreeRequestVO
| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| excludeOrgId | String | 否 | 要排除的部门ID（移动部门时排除自身和子部门） | "1930806593885179904" |
| includeDeleted | Boolean | 否 | 是否包含已删除的部门，默认false | false |
| maxLevel | Integer | 否 | 最大层级深度（0表示不限制），默认0 | 0 |

## 响应参数

### OrgStructureTreeResponseVO
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| id | String | 部门ID | "1930806593885179904" |
| organName | String | 部门名称 | "技术部" |
| preId | String | 父部门ID | "1930806593885179903" |
| orderInfo | Integer | 排序序号 | 1 |
| level | Integer | 部门层级（从1开始） | 2 |
| fullPath | String | 完整路径 | "总公司/技术部" |
| selectable | Boolean | 是否可选择（用于移动部门时排除自身和子部门） | true |
| children | Array | 子部门列表 | [] |
| childCount | Integer | 子部门数量 | 0 |

## 测试用例

### 测试用例1：获取完整部门树
**请求示例：**
```json
POST /t-org-structure/tree
Content-Type: application/json

{
    "includeDeleted": false,                  // 不显示已删除的部门（推荐设置）
    "maxLevel": 0                            // 显示所有层级（0=不限制，1=只显示根部门，2=显示到第二层）
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [
        {
            "id": "1930806593885179903",
            "organName": "总公司",
            "preId": null,
            "orderInfo": 1,
            "level": 1,
            "fullPath": "总公司",
            "selectable": true,
            "childCount": 2,
            "children": [
                {
                    "id": "1930806593885179904",
                    "organName": "技术部",
                    "preId": "1930806593885179903",
                    "orderInfo": 1,
                    "level": 2,
                    "fullPath": "总公司/技术部",
                    "selectable": true,
                    "childCount": 1,
                    "children": [
                        {
                            "id": "1930806593885179905",
                            "organName": "研发组",
                            "preId": "1930806593885179904",
                            "orderInfo": 1,
                            "level": 3,
                            "fullPath": "总公司/技术部/研发组",
                            "selectable": true,
                            "childCount": 0,
                            "children": []
                        }
                    ]
                },
                {
                    "id": "1930806593885179906",
                    "organName": "市场部",
                    "preId": "1930806593885179903",
                    "orderInfo": 2,
                    "level": 2,
                    "fullPath": "总公司/市场部",
                    "selectable": true,
                    "childCount": 0,
                    "children": []
                }
            ]
        }
    ]
}
```

### 测试用例2：排除指定部门（移动部门场景）
**请求示例：**
```json
POST /t-org-structure/tree
Content-Type: application/json

{
    "excludeOrgId": "1930806593885179904",    // 要移动的部门ID，会排除此部门及其所有子部门
    "includeDeleted": false,                  // 不显示已删除的部门
    "maxLevel": 0                            // 显示所有层级（0=不限制层级）
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [
        {
            "id": "1930806593885179903",
            "organName": "总公司",
            "preId": null,
            "orderInfo": 1,
            "level": 1,
            "fullPath": "总公司",
            "selectable": true,
            "childCount": 1,
            "children": [
                {
                    "id": "1930806593885179906",
                    "organName": "市场部",
                    "preId": "1930806593885179903",
                    "orderInfo": 2,
                    "level": 2,
                    "fullPath": "总公司/市场部",
                    "selectable": true,
                    "childCount": 0,
                    "children": []
                }
            ]
        }
    ]
}
```

**说明：** 技术部及其子部门（研发组）被排除，不在返回结果中

### 测试用例3：限制层级深度
**请求示例：**
```json
POST /t-org-structure/tree
Content-Type: application/json

{
    "includeDeleted": false,                  // 不显示已删除的部门
    "maxLevel": 2                            // 只显示到第2层（避免层级过深）
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [
        {
            "id": "1930806593885179903",
            "organName": "总公司",
            "preId": null,
            "orderInfo": 1,
            "level": 1,
            "fullPath": "总公司",
            "selectable": true,
            "childCount": 2,
            "children": [
                {
                    "id": "1930806593885179904",
                    "organName": "技术部",
                    "preId": "1930806593885179903",
                    "orderInfo": 1,
                    "level": 2,
                    "fullPath": "总公司/技术部",
                    "selectable": true,
                    "childCount": 0,
                    "children": []
                },
                {
                    "id": "1930806593885179906",
                    "organName": "市场部",
                    "preId": "1930806593885179903",
                    "orderInfo": 2,
                    "level": 2,
                    "fullPath": "总公司/市场部",
                    "selectable": true,
                    "childCount": 0,
                    "children": []
                }
            ]
        }
    ]
}
```

**说明：** 只显示到第2层，第3层的研发组不显示

### 测试用例4：包含已删除部门
**请求示例：**
```json
POST /t-org-structure/tree
Content-Type: application/json

{
    "includeDeleted": true,                   // 显示包括已删除的部门（用于数据恢复）
    "maxLevel": 0                            // 显示所有层级
}
```

**预期响应：** 包含所有部门（包括已删除的部门）

### 测试用例5：最简单用法（推荐）
**请求示例：**
```json
POST /t-org-structure/tree
Content-Type: application/json

{}                                           // 空对象，获取完整部门树（最常用）
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": []
}
```

## 错误场景测试

### 错误场景1：系统异常
**预期响应：**
```json
{
    "code": 500,
    "message": "获取部门结构树失败：具体错误信息",
    "data": null
}
```

## 接口特性

### 功能特性
1. **树形结构**: 返回完整的部门层级关系
2. **排除功能**: 支持排除指定部门及其子部门（用于移动部门）
3. **层级控制**: 支持限制返回的最大层级深度
4. **删除过滤**: 支持是否包含已删除的部门
5. **完整路径**: 自动生成部门的完整路径信息
6. **可选择性**: 标记部门是否可选择（排除的部门标记为不可选择）

### 技术特性
1. **递归查询**: 使用递归算法构建树形结构
2. **精度保护**: Long类型ID使用字符串序列化防止精度丢失
3. **性能优化**: 一次查询构建完整树形结构
4. **数据完整性**: 包含层级、路径、子部门数量等完整信息

## 使用场景
1. **移动部门**: 选择新的父部门时使用
2. **部门选择**: 各种需要选择部门的场景
3. **组织架构展示**: 展示完整的组织架构树
4. **权限配置**: 配置部门相关权限时使用
