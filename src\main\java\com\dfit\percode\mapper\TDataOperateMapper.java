package com.dfit.percode.mapper;

import com.dfit.percode.entity.TDataOperate;
import com.dfit.percode.vo.DataOperateConfigResponseVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * 数据操作权限Mapper接口
 * 提供数据操作权限的数据库操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface TDataOperateMapper extends BaseMapper<TDataOperate> {

    /**
     * 检查模块是否存在
     *
     * @param moduleIdentifier 模块标识
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM t_data_module " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false")
    int checkModuleExists(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 删除指定模块的所有操作配置
     *
     * @param moduleIdentifier 模块标识
     */
    @Delete("DELETE FROM t_data_operate " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND (data_identifier IS NULL OR data_identifier = '')")
    void deleteByModule(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 获取模块操作权限配置
     * 查询指定模块的操作类型配置
     *
     * @param moduleIdentifier 模块标识
     * @return 模块操作权限配置信息
     */
    @Select("SELECT " +
            "dm.id as \"moduleId\", " +
            "dm.module_name as \"moduleName\", " +
            "dm.module_identifier as \"moduleIdentifier\" " +
            "FROM t_data_module dm " +
            "WHERE dm.module_identifier = #{moduleIdentifier} " +
            "AND dm.is_del = false")
    DataOperateConfigResponseVO getModuleOperateConfig(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 获取模块的操作权限列表
     *
     * @param moduleIdentifier 模块标识
     * @return 操作权限列表
     */
    @Select("SELECT operate_type " +
            "FROM t_data_operate " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false " +
            "AND (data_identifier IS NULL OR data_identifier = '') " +
            "ORDER BY operate_type")
    List<Integer> getModuleOperateTypes(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 获取特定数据权限的操作权限列表
     *
     * @param dataIdentifier 数据权限标识符
     * @return 操作权限列表
     */
    @Select("SELECT operate_type " +
            "FROM t_data_operate " +
            "WHERE data_identifier = #{dataIdentifier} " +
            "AND is_del = false " +
            "ORDER BY operate_type")
    List<Integer> getDataOperateTypes(@Param("dataIdentifier") String dataIdentifier);

    /**
     * 删除指定数据权限的所有操作配置
     *
     * @param dataIdentifier 数据权限标识符
     */
    @Delete("DELETE FROM t_data_operate " +
            "WHERE data_identifier = #{dataIdentifier}")
    void deleteByDataIdentifier(@Param("dataIdentifier") String dataIdentifier);
}
