import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 简化版组织架构数据同步工具
 * 独立运行，不依赖Spring框架
 */
public class SimpleOrgSync {
    
    // 数据库连接信息（从您的配置文件中获取）
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    // 一级部门列表
    private static final Set<String> LEVEL_1_DEPARTMENTS = Set.of(
        "新材料科研究院（合署）", "公司办公室", "人力资源部", "企业文化部", "财务部",
        "党委办公室", "组织部", "党委工作部", "审计部", "集团领导", "风险合规部",
        "安全环保部", "纪委功公室（党风廉政办公室）", "集团战略发展部", 
        "江苏金珂水务有限公司", "工会", "印尼焦化项目部", "特钢事业部",
        "数字应用研究院（人工智能研完院）", "南京三金房地产开发有限公司",
        "南钢退休职工服务中心", "集国资产处置办公室", "江苏金贸钢宝电子商务有限公司2",
        "团委", "公司领导", "离京鑫智链科技信息有限公司2", "科技质量部",
        "数字应用研究院", "蔚蓝高科技集团", "战略运营部（产业发展研究院）",
        "物流中心", "能源动力事业部", "炼铁事业部", "保卫部", "新产业投资集团",
        "采购中心", "制造部", "板材事业部", "市场部", "江苏金凯节能环保投资控股有限公司",
        "印尼钢铁项目指挥部", "江苏南钢鑫洋供应链有限公司", "集团宿迁金鑫公司",
        "南京金智工程技术有限公司", "集团综合资产部", "证券部", "香港金腾公司",
        "南京钢铁集团国际经济贸易有限公司", "集团工会", "集团财务审计部",
        "集团宿迁金鑫靖江项目指挥部", "江苏金恒信息科技股份有限公司"
    );
    
    // 层级关键词
    private static final List<String> HIERARCHY_KEYWORDS = Arrays.asList(
        "事业部", "集团", "公司", "中心", "厂", "车间", "部", "科", "室", "院", "会", "委", "班", "组", "队", "站"
    );
    
    public static void main(String[] args) {
        System.out.println("=== 组织架构数据同步工具 ===");
        
        try {
            SimpleOrgSync sync = new SimpleOrgSync();
            SyncResult result = sync.executeSync("organization-sync-temp/department_sync_test.sql");
            
            System.out.println("\n=== 同步结果 ===");
            System.out.println("成功: " + result.success);
            System.out.println("原始数据: " + result.rawDataCount);
            System.out.println("清洗数据: " + result.cleanDataCount);
            System.out.println("树节点数: " + result.treeNodeCount);
            System.out.println("删除记录: " + result.deletedCount);
            System.out.println("插入记录: " + result.insertedCount);
            System.out.println("最终记录: " + result.finalCount);
            
            if (result.success) {
                System.out.println("✅ 数据同步成功！");
            } else {
                System.out.println("❌ 数据同步失败: " + result.errorMessage);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 同步过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public SyncResult executeSync(String filePath) {
        SyncResult result = new SyncResult();
        result.startTime = LocalDateTime.now();
        
        Connection conn = null;
        
        try {
            // 1. 连接数据库
            System.out.println("连接数据库...");
            conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            conn.setAutoCommit(false);
            
            // 2. 解析SQL文件
            System.out.println("解析SQL文件: " + filePath);
            List<DepartmentData> rawData = parseSqlFile(filePath);
            result.rawDataCount = rawData.size();
            System.out.println("解析完成，原始数据: " + rawData.size() + " 条");
            
            // 3. 数据清洗
            System.out.println("数据清洗...");
            List<DepartmentData> cleanData = cleanData(rawData);
            result.cleanDataCount = cleanData.size();
            System.out.println("清洗完成，有效数据: " + cleanData.size() + " 条");
            
            // 4. 构建层级树
            System.out.println("构建层级树...");
            Map<String, OrgNode> treeNodes = buildTree(cleanData);
            result.treeNodeCount = treeNodes.size();
            System.out.println("层级树构建完成，节点数: " + treeNodes.size());
            
            // 5. 数据库操作
            System.out.println("数据库操作...");
            insertToDatabase(conn, treeNodes, result);
            
            // 6. 提交事务
            conn.commit();
            result.success = true;
            
        } catch (Exception e) {
            System.err.println("同步失败: " + e.getMessage());
            result.success = false;
            result.errorMessage = e.getMessage();
            
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        
        result.endTime = LocalDateTime.now();
        return result;
    }
    
    private List<DepartmentData> parseSqlFile(String filePath) throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
        List<DepartmentData> result = new ArrayList<>();
        
        Pattern pattern = Pattern.compile(
            "INSERT INTO `department_sync_test` VALUES \\(" +
            "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', " +
            "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)'\\)"
        );
        
        for (String line : lines) {
            if (line.trim().startsWith("INSERT INTO `department_sync_test`")) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    DepartmentData data = new DepartmentData();
                    // 根据实际INSERT语句格式解析字段
                    // (id, orgCode, orgName, parentCode, deptUuid, fullName, isHistory, userPredef14, updateTime, syncDate, createTime)
                    data.orgCode = matcher.group(2);      // orgCode
                    data.orgName = matcher.group(3);      // orgName
                    data.parentCode = matcher.group(4);   // parentCode
                    data.fullName = matcher.group(6);     // fullName
                    data.userPredef14 = matcher.group(8); // userPredef14

                    if (!"D".equals(data.userPredef14) &&
                        data.fullName != null && !data.fullName.trim().isEmpty()) {
                        result.add(data);
                    }
                }
            }
        }
        
        return result;
    }
    
    private List<DepartmentData> cleanData(List<DepartmentData> rawData) {
        Map<String, DepartmentData> uniqueData = new LinkedHashMap<>();
        
        for (DepartmentData data : rawData) {
            String fullName = data.fullName.trim();
            if (!uniqueData.containsKey(fullName)) {
                uniqueData.put(fullName, data);
            }
        }
        
        return new ArrayList<>(uniqueData.values());
    }
    
    private Map<String, OrgNode> buildTree(List<DepartmentData> cleanData) {
        Map<String, OrgNode> allNodes = new LinkedHashMap<>();
        
        for (DepartmentData data : cleanData) {
            String fullName = data.fullName;
            List<String> hierarchy = parseHierarchy(fullName);
            
            OrgNode currentParent = null;
            
            for (int i = 0; i < hierarchy.size(); i++) {
                String levelName = hierarchy.get(i);
                
                OrgNode node = allNodes.get(levelName);
                if (node == null) {
                    node = new OrgNode();
                    node.organName = levelName;
                    node.fullName = (i == hierarchy.size() - 1) ? fullName : levelName;
                    node.level = i + 1;
                    node.children = new ArrayList<>();
                    
                    allNodes.put(levelName, node);
                    
                    if (currentParent != null) {
                        currentParent.children.add(node);
                        node.parent = currentParent;
                    }
                }
                
                currentParent = node;
            }
        }
        
        return allNodes;
    }
    
    private List<String> parseHierarchy(String fullName) {
        if (LEVEL_1_DEPARTMENTS.contains(fullName)) {
            return Collections.singletonList(fullName);
        }
        
        List<String> levels = new ArrayList<>();
        String remaining = fullName;
        
        for (String keyword : HIERARCHY_KEYWORDS) {
            int pos = remaining.indexOf(keyword);
            if (pos > 0) {
                String levelName = remaining.substring(0, pos + keyword.length());
                levels.add(levelName);
                remaining = remaining.substring(pos + keyword.length());
                
                if (remaining.isEmpty()) {
                    break;
                }
            }
        }
        
        if (!remaining.isEmpty() || levels.isEmpty()) {
            levels.add(fullName);
        }
        
        return levels;
    }
    
    private void insertToDatabase(Connection conn, Map<String, OrgNode> treeNodes, SyncResult result) throws SQLException {
        // 删除现有同步数据
        PreparedStatement deleteStmt = conn.prepareStatement("DELETE FROM t_org_structure WHERE data_source = ?");
        deleteStmt.setInt(1, 2);
        result.deletedCount = deleteStmt.executeUpdate();
        deleteStmt.close();
        System.out.println("删除现有同步数据: " + result.deletedCount + " 条");
        
        // 获取最大ID
        PreparedStatement maxIdStmt = conn.prepareStatement("SELECT COALESCE(MAX(id), 2000000000) FROM t_org_structure");
        ResultSet rs = maxIdStmt.executeQuery();
        long currentId = 2000000000L;
        if (rs.next()) {
            currentId = Math.max(rs.getLong(1), 2000000000L);
        }
        rs.close();
        maxIdStmt.close();
        
        // 分配ID并插入
        List<OrgNode> insertNodes = new ArrayList<>();
        List<OrgNode> rootNodes = new ArrayList<>();
        
        for (OrgNode node : treeNodes.values()) {
            if (node.parent == null) {
                rootNodes.add(node);
            }
        }
        
        rootNodes.sort(Comparator.comparing(node -> node.organName));
        
        for (OrgNode root : rootNodes) {
            assignIds(root, currentId, insertNodes);
            currentId += countNodes(root);
        }
        
        // 批量插入
        PreparedStatement insertStmt = conn.prepareStatement(
            "INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );
        
        LocalDateTime now = LocalDateTime.now();
        
        for (OrgNode node : insertNodes) {
            insertStmt.setLong(1, node.id);
            insertStmt.setString(2, node.organName);
            insertStmt.setLong(3, node.preId);
            insertStmt.setInt(4, 1);
            insertStmt.setBoolean(5, false);
            insertStmt.setTimestamp(6, Timestamp.valueOf(now));
            insertStmt.setTimestamp(7, Timestamp.valueOf(now));
            insertStmt.setInt(8, 2);
            
            insertStmt.addBatch();
            result.insertedCount++;
            
            if (result.insertedCount % 100 == 0) {
                insertStmt.executeBatch();
                System.out.println("批量插入进度: " + result.insertedCount + "/" + insertNodes.size());
            }
        }
        
        insertStmt.executeBatch();
        insertStmt.close();
        
        // 验证最终记录数
        PreparedStatement countStmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = ?");
        countStmt.setInt(1, 2);
        ResultSet countRs = countStmt.executeQuery();
        if (countRs.next()) {
            result.finalCount = countRs.getInt(1);
        }
        countRs.close();
        countStmt.close();
        
        System.out.println("数据库操作完成: 插入 " + result.insertedCount + " 条记录");
    }
    
    private long currentIdCounter = 0;

    private void assignIds(OrgNode node, long startId, List<OrgNode> insertNodes) {
        if (currentIdCounter == 0) {
            currentIdCounter = startId;
        }

        node.id = ++currentIdCounter;
        node.preId = node.parent != null ? node.parent.id : 0L;

        insertNodes.add(node);

        node.children.sort(Comparator.comparing(child -> child.organName));
        for (OrgNode child : node.children) {
            assignIds(child, startId, insertNodes);
        }
    }
    
    private int countNodes(OrgNode node) {
        int count = 1;
        for (OrgNode child : node.children) {
            count += countNodes(child);
        }
        return count;
    }
    
    // 内部数据类
    static class DepartmentData {
        String orgCode;
        String orgName;
        String parentCode;
        String fullName;
        String userPredef14;
    }
    
    static class OrgNode {
        long id;
        String organName;
        String fullName;
        long preId;
        int level;
        List<OrgNode> children;
        OrgNode parent;
    }
    
    static class SyncResult {
        LocalDateTime startTime;
        LocalDateTime endTime;
        boolean success;
        String errorMessage;
        int rawDataCount;
        int cleanDataCount;
        int treeNodeCount;
        int deletedCount;
        int insertedCount;
        int finalCount;
    }
}
