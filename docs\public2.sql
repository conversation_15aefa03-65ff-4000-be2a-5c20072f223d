/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 10/06/2025 22:44:48
*/


-- ----------------------------
-- Sequence structure for t_data_operate_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."t_data_operate_id_seq";
CREATE SEQUENCE "public"."t_data_operate_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Table structure for t_data_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_module";
CREATE TABLE "public"."t_data_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_data_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_data_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_data_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_data_module"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_data_module" IS '数据权限模块信息表';

-- ----------------------------
-- Table structure for t_data_operate
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_operate";
CREATE TABLE "public"."t_data_operate" (
  "id" int8 NOT NULL DEFAULT nextval('t_data_operate_id_seq'::regclass),
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int2,
  "operate_type" int8,
  "is_del" bool DEFAULT false,
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_operate"."module_identifier" IS '模块标识符，与系统模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_operate"."data_type" IS '数据类型，1为条';
COMMENT ON COLUMN "public"."t_data_operate"."operate_type" IS '操作类型，1为新增2为修改3为删除';
COMMENT ON COLUMN "public"."t_data_operate"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_operate"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."t_data_operate"."modify_time" IS '修改时间';
COMMENT ON COLUMN "public"."t_data_operate"."data_identifier" IS '数据标识,与数据权限管理列表中data_identifier一致';
COMMENT ON TABLE "public"."t_data_operate" IS '数据操作表';

-- ----------------------------
-- Table structure for t_data_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_data_permission";
CREATE TABLE "public"."t_data_permission" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int4,
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "data_identifier" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."t_data_permission"."name" IS '名称';
COMMENT ON COLUMN "public"."t_data_permission"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_data_permission"."module_identifier" IS '模块标识 与菜单模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_data_permission"."data_type" IS '数据类型，1检签';
COMMENT ON COLUMN "public"."t_data_permission"."order_info" IS '排序序号，从1开始';
COMMENT ON COLUMN "public"."t_data_permission"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_data_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON COLUMN "public"."t_data_permission"."data_identifier" IS '数据标识页面输入或表同步';
COMMENT ON TABLE "public"."t_data_permission" IS '数据权限信息列表';

-- ----------------------------
-- Table structure for t_department_child
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_department_child";
CREATE TABLE "public"."t_department_child" (
  "id" int8 NOT NULL,
  "dept_id" int8 NOT NULL,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "dept_uuid" varchar(255) COLLATE "pg_catalog"."default",
  "source_system" varchar(255) COLLATE "pg_catalog"."default",
  "source_data_nm" varchar(255) COLLATE "pg_catalog"."default",
  "udef1" varchar(255) COLLATE "pg_catalog"."default",
  "udef2" varchar(255) COLLATE "pg_catalog"."default",
  "udef3" varchar(255) COLLATE "pg_catalog"."default",
  "udef4" varchar(255) COLLATE "pg_catalog"."default",
  "udef5" varchar(255) COLLATE "pg_catalog"."default",
  "udef6" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int4,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;

-- ----------------------------
-- Table structure for t_employee_position
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_position";
CREATE TABLE "public"."t_employee_position" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "position_code" varchar(255) COLLATE "pg_catalog"."default",
  "org_code" varchar(255) COLLATE "pg_catalog"."default",
  "department_code" varchar(255) COLLATE "pg_catalog"."default",
  "is_primary" varchar(255) COLLATE "pg_catalog"."default",
  "status" varchar(255) COLLATE "pg_catalog"."default",
  "is_active" varchar(255) COLLATE "pg_catalog"."default",
  "position_detail_code" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_position"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Table structure for t_employee_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_system";
CREATE TABLE "public"."t_employee_system" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "system_code" varchar(255) COLLATE "pg_catalog"."default",
  "system_data_id" varchar(255) COLLATE "pg_catalog"."default",
  "org_code" varchar(255) COLLATE "pg_catalog"."default",
  "department_code" varchar(255) COLLATE "pg_catalog"."default",
  "employee_code" varchar(255) COLLATE "pg_catalog"."default",
  "login_account" varchar(50) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_system"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Table structure for t_employee_title
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_employee_title";
CREATE TABLE "public"."t_employee_title" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "guid" varchar(255) COLLATE "pg_catalog"."default",
  "employee_mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "title_code" varchar(255) COLLATE "pg_catalog"."default",
  "title_type" varchar(255) COLLATE "pg_catalog"."default",
  "title_level" varchar(255) COLLATE "pg_catalog"."default",
  "title_name" varchar(255) COLLATE "pg_catalog"."default",
  "status" varchar(255) COLLATE "pg_catalog"."default",
  "title_category" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6),
  "create_time" timestamp(6) DEFAULT now(),
  "modify_time" timestamp(6) DEFAULT now(),
  "is_del" bool DEFAULT false
)
;
COMMENT ON COLUMN "public"."t_employee_title"."user_id" IS '关联t_user表的用户ID（允许为空，支持孤儿记录）';

-- ----------------------------
-- Table structure for t_menu_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_menu_module";
CREATE TABLE "public"."t_menu_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_menu_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_menu_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_menu_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_menu_module"."is_del" IS '是否删除';
COMMENT ON TABLE "public"."t_menu_module" IS '菜单模块信息表，用于标识菜单管理列表中新建模块存储信息和标识';

-- ----------------------------
-- Table structure for t_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_menu_permission";
CREATE TABLE "public"."t_menu_permission" (
  "id" int8 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_disable" bool,
  "menu_type" int4,
  "route_address" varchar(255) COLLATE "pg_catalog"."default",
  "component_path" varchar(255) COLLATE "pg_catalog"."default",
  "permission_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_menu_permission"."name" IS '名称';
COMMENT ON COLUMN "public"."t_menu_permission"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_menu_permission"."module_identifier" IS '模块标识 与菜单模块表中模块标识一致';
COMMENT ON COLUMN "public"."t_menu_permission"."is_disable" IS '是否停用';
COMMENT ON COLUMN "public"."t_menu_permission"."menu_type" IS '菜单类型，1目录 2菜单 3按钮';
COMMENT ON COLUMN "public"."t_menu_permission"."route_address" IS '路由地址';
COMMENT ON COLUMN "public"."t_menu_permission"."component_path" IS '组件路径';
COMMENT ON COLUMN "public"."t_menu_permission"."permission_identifier" IS '权限标识';
COMMENT ON COLUMN "public"."t_menu_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_menu_permission" IS '菜单权限管理列表设定';

-- ----------------------------
-- Table structure for t_org_structure
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_org_structure";
CREATE TABLE "public"."t_org_structure" (
  "id" int8 NOT NULL,
  "organ_name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "dept_uuid" varchar(255) COLLATE "pg_catalog"."default",
  "org_code" varchar(255) COLLATE "pg_catalog"."default",
  "parent_code" varchar(255) COLLATE "pg_catalog"."default",
  "full_name" varchar(500) COLLATE "pg_catalog"."default",
  "is_history" int4 DEFAULT 0,
  "description" text COLLATE "pg_catalog"."default",
  "fax" varchar(255) COLLATE "pg_catalog"."default",
  "web_address" varchar(500) COLLATE "pg_catalog"."default",
  "org_manager" varchar(255) COLLATE "pg_catalog"."default",
  "post_code" varchar(255) COLLATE "pg_catalog"."default",
  "org_type" varchar(50) COLLATE "pg_catalog"."default",
  "org_level" int4,
  "org_path" varchar(500) COLLATE "pg_catalog"."default",
  "full_org_code" varchar(500) COLLATE "pg_catalog"."default",
  "external_id" int4,
  "external_update_time" timestamp(6),
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_org_structure"."organ_name" IS '组织名称';
COMMENT ON COLUMN "public"."t_org_structure"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_org_structure"."order_info" IS '顺序，从1开始';
COMMENT ON COLUMN "public"."t_org_structure"."is_del" IS '是否删除';
COMMENT ON TABLE "public"."t_org_structure" IS '组织架构表';

-- ----------------------------
-- Table structure for t_perm_user_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_perm_user_role";
CREATE TABLE "public"."t_perm_user_role" (
  "id" int8 NOT NULL,
  "user_id" int8,
  "role_id" int8,
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_perm_user_role"."user_id" IS '人员id，对应用户管理表人员id';
COMMENT ON COLUMN "public"."t_perm_user_role"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_perm_user_role"."order_info" IS '序号，一个角色对多个人员，人员所在的序号';
COMMENT ON COLUMN "public"."t_perm_user_role"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_perm_user_role" IS '人员角色对应表';

-- ----------------------------
-- Table structure for t_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_role";
CREATE TABLE "public"."t_role" (
  "id" int8 NOT NULL,
  "role_name" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_disable" bool,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_role"."id" IS '角色id';
COMMENT ON COLUMN "public"."t_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."t_role"."order_info" IS '顺序';
COMMENT ON COLUMN "public"."t_role"."is_disable" IS '是否停用，true停用，false正常';
COMMENT ON COLUMN "public"."t_role"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_role" IS '角色信息表';

-- ----------------------------
-- Table structure for t_roles_data_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_roles_data_permission";
CREATE TABLE "public"."t_roles_data_permission" (
  "id" int8 NOT NULL,
  "role_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "data_type" int4,
  "data_id" int8,
  "operate_type" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_roles_data_permission"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_roles_data_permission"."module_identifier" IS '模块标识符';
COMMENT ON COLUMN "public"."t_roles_data_permission"."data_type" IS '数据类型，1检签';
COMMENT ON COLUMN "public"."t_roles_data_permission"."data_id" IS '数据id，对应数据权限管理列表中的id';
COMMENT ON COLUMN "public"."t_roles_data_permission"."operate_type" IS '操作类型1与数据权限操作类型中operate_type一致';
COMMENT ON COLUMN "public"."t_roles_data_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_roles_data_permission" IS '角色数据权限对应表';

-- ----------------------------
-- Table structure for t_roles_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_roles_menu_permission";
CREATE TABLE "public"."t_roles_menu_permission" (
  "id" int8 NOT NULL,
  "role_id" int8,
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "menu_id" int8,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_roles_menu_permission"."role_id" IS '角色id';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."module_identifier" IS '模块标识符';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."menu_id" IS '菜单id，对应菜单管理列表表中的id';
COMMENT ON COLUMN "public"."t_roles_menu_permission"."is_del" IS '是否删除，默认为false';
COMMENT ON TABLE "public"."t_roles_menu_permission" IS '角色菜单权限对应表';

-- ----------------------------
-- Table structure for t_sync_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_sync_log";
CREATE TABLE "public"."t_sync_log" (
  "id" int8 NOT NULL,
  "sync_type" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "sync_action" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "external_id" varchar(255) COLLATE "pg_catalog"."default",
  "internal_id" int8,
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "error_message" text COLLATE "pg_catalog"."default",
  "sync_time" timestamp(6) DEFAULT now(),
  "external_data" text COLLATE "pg_catalog"."default",
  "processed_data" text COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_user";
CREATE TABLE "public"."t_user" (
  "id" int8 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "login_id" varchar(255) COLLATE "pg_catalog"."default",
  "organ_affiliation" int8,
  "account" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "is_disable" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "mdm_id" varchar(255) COLLATE "pg_catalog"."default",
  "mdm_hrdwnm" varchar(255) COLLATE "pg_catalog"."default",
  "employee_code" varchar(255) COLLATE "pg_catalog"."default",
  "gender" varchar(255) COLLATE "pg_catalog"."default",
  "mobile" varchar(255) COLLATE "pg_catalog"."default",
  "id_card" varchar(255) COLLATE "pg_catalog"."default",
  "birth_date" date,
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "org_type" varchar(255) COLLATE "pg_catalog"."default",
  "org_level1" varchar(255) COLLATE "pg_catalog"."default",
  "org_level2" varchar(255) COLLATE "pg_catalog"."default",
  "org_level3" varchar(255) COLLATE "pg_catalog"."default",
  "wechat" varchar(255) COLLATE "pg_catalog"."default",
  "tel" varchar(255) COLLATE "pg_catalog"."default",
  "note" text COLLATE "pg_catalog"."default",
  "employee_status" varchar(255) COLLATE "pg_catalog"."default",
  "user_type" varchar(255) COLLATE "pg_catalog"."default",
  "id_name" varchar(255) COLLATE "pg_catalog"."default",
  "external_id" int8,
  "external_org_code" varchar(255) COLLATE "pg_catalog"."default",
  "external_update_time" timestamp(6),
  "sync_status" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'SYNCED'::character varying,
  "last_sync_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_user"."id" IS '人员信息id';
COMMENT ON COLUMN "public"."t_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."t_user"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."t_user"."login_id" IS '登录关联id，与tokenid一致';
COMMENT ON COLUMN "public"."t_user"."organ_affiliation" IS '组织归属，对应组织架构表中的id';
COMMENT ON COLUMN "public"."t_user"."account" IS '账户';
COMMENT ON COLUMN "public"."t_user"."password" IS '密码';
COMMENT ON COLUMN "public"."t_user"."is_disable" IS '是否停用';
COMMENT ON TABLE "public"."t_user" IS '人员信息表，与组织架构绑定';

-- ----------------------------
-- Function structure for link_orphan_employee_records
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."link_orphan_employee_records"();
CREATE OR REPLACE FUNCTION "public"."link_orphan_employee_records"()
  RETURNS "pg_catalog"."int4" AS $BODY$
DECLARE
    linked_count INTEGER := 0;
    position_count INTEGER := 0;
    title_count INTEGER := 0;
    system_count INTEGER := 0;
BEGIN
    -- 关联员工岗位表的孤儿记录
    UPDATE t_employee_position
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_position.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_position.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS position_count = ROW_COUNT;

    -- 关联员工职称表的孤儿记录
    UPDATE t_employee_title
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_title.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_title.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS title_count = ROW_COUNT;

    -- 关联员工系统标识表的孤儿记录
    UPDATE t_employee_system
    SET user_id = (
        SELECT u.id
        FROM t_user u
        WHERE u.mdm_id = t_employee_system.employee_mdm_id
          AND u.is_del = false
    )
    WHERE user_id IS NULL
      AND employee_mdm_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM t_user u
        WHERE u.mdm_id = t_employee_system.employee_mdm_id
          AND u.is_del = false
    );

    GET DIAGNOSTICS system_count = ROW_COUNT;

    linked_count := position_count + title_count + system_count;

    -- 记录关联结果
    RAISE NOTICE '孤儿记录关联完成: 岗位表=% 条, 职称表=% 条, 系统表=% 条, 总计=% 条',
        position_count, title_count, system_count, linked_count;

    RETURN linked_count;
END;
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- View structure for v_orphan_employee_records
-- ----------------------------
DROP VIEW IF EXISTS "public"."v_orphan_employee_records";
CREATE VIEW "public"."v_orphan_employee_records" AS         (         SELECT 'position'::text AS record_type, 
                    t_employee_position.id, t_employee_position.employee_mdm_id, 
                    NULL::character varying AS title_name, 
                    NULL::character varying AS system_code, 
                    t_employee_position.create_time
                   FROM t_employee_position
                  WHERE t_employee_position.user_id IS NULL AND t_employee_position.employee_mdm_id IS NOT NULL
        UNION ALL 
                 SELECT 'title'::text AS record_type, t_employee_title.id, 
                    t_employee_title.employee_mdm_id, 
                    t_employee_title.title_name, 
                    NULL::character varying AS system_code, 
                    t_employee_title.create_time
                   FROM t_employee_title
                  WHERE t_employee_title.user_id IS NULL AND t_employee_title.employee_mdm_id IS NOT NULL)
UNION ALL 
         SELECT 'system'::text AS record_type, t_employee_system.id, 
            t_employee_system.employee_mdm_id, 
            NULL::character varying AS title_name, 
            t_employee_system.system_code, t_employee_system.create_time
           FROM t_employee_system
          WHERE t_employee_system.user_id IS NULL AND t_employee_system.employee_mdm_id IS NOT NULL
  ORDER BY 6 DESC;
COMMENT ON VIEW "public"."v_orphan_employee_records" IS '孤儿记录查询视图 - 显示所有未关联到用户的员工扩展数据';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."t_data_operate_id_seq"
OWNED BY "public"."t_data_operate"."id";
SELECT setval('"public"."t_data_operate_id_seq"', 8, true);

-- ----------------------------
-- Primary Key structure for table t_data_module
-- ----------------------------
ALTER TABLE "public"."t_data_module" ADD CONSTRAINT "t_data_module_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_data_operate
-- ----------------------------
ALTER TABLE "public"."t_data_operate" ADD CONSTRAINT "t_data_operate_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_data_permission
-- ----------------------------
ALTER TABLE "public"."t_data_permission" ADD CONSTRAINT "t_data_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_department_child
-- ----------------------------
CREATE INDEX "idx_department_child_dept_id" ON "public"."t_department_child" USING btree (
  "dept_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "idx_department_child_dept_uuid" ON "public"."t_department_child" USING btree (
  "dept_uuid" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_department_child
-- ----------------------------
ALTER TABLE "public"."t_department_child" ADD CONSTRAINT "t_department_child_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_position
-- ----------------------------
CREATE INDEX "idx_employee_position_mdm_id" ON "public"."t_employee_position" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_position_org_code" ON "public"."t_employee_position" USING btree (
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_position_user_id" ON "public"."t_employee_position" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_position
-- ----------------------------
ALTER TABLE "public"."t_employee_position" ADD CONSTRAINT "t_employee_position_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_system
-- ----------------------------
CREATE INDEX "idx_employee_system_login_account" ON "public"."t_employee_system" USING btree (
  "login_account" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_system_mdm_id" ON "public"."t_employee_system" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_system_user_id" ON "public"."t_employee_system" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_system
-- ----------------------------
ALTER TABLE "public"."t_employee_system" ADD CONSTRAINT "t_employee_system_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_employee_title
-- ----------------------------
CREATE INDEX "idx_employee_title_mdm_id" ON "public"."t_employee_title" USING btree (
  "employee_mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_employee_title_user_id" ON "public"."t_employee_title" USING btree (
  "user_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_employee_title
-- ----------------------------
ALTER TABLE "public"."t_employee_title" ADD CONSTRAINT "t_employee_title_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_menu_module
-- ----------------------------
ALTER TABLE "public"."t_menu_module" ADD CONSTRAINT "t_menu_module_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_menu_permission
-- ----------------------------
ALTER TABLE "public"."t_menu_permission" ADD CONSTRAINT "t_menu_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_org_structure
-- ----------------------------
CREATE INDEX "idx_org_structure_org_code" ON "public"."t_org_structure" USING btree (
  "org_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_org_structure_sync_status" ON "public"."t_org_structure" USING btree (
  "sync_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_org_structure
-- ----------------------------
ALTER TABLE "public"."t_org_structure" ADD CONSTRAINT "t_org_structure_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_perm_user_role
-- ----------------------------
ALTER TABLE "public"."t_perm_user_role" ADD CONSTRAINT "t_perm_user_role_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_role
-- ----------------------------
ALTER TABLE "public"."t_role" ADD CONSTRAINT "t_role_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_roles_data_permission
-- ----------------------------
ALTER TABLE "public"."t_roles_data_permission" ADD CONSTRAINT "t_roles_data_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table t_roles_menu_permission
-- ----------------------------
ALTER TABLE "public"."t_roles_menu_permission" ADD CONSTRAINT "t_roles_menu_permission_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_sync_log
-- ----------------------------
CREATE INDEX "idx_sync_log_status" ON "public"."t_sync_log" USING btree (
  "sync_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_sync_log_time" ON "public"."t_sync_log" USING btree (
  "sync_time" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "idx_sync_log_type" ON "public"."t_sync_log" USING btree (
  "sync_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_sync_log
-- ----------------------------
ALTER TABLE "public"."t_sync_log" ADD CONSTRAINT "t_sync_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table t_user
-- ----------------------------
CREATE INDEX "idx_user_employee_code" ON "public"."t_user" USING btree (
  "employee_code" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_user_mdm_id" ON "public"."t_user" USING btree (
  "mdm_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "idx_user_sync_status" ON "public"."t_user" USING btree (
  "sync_status" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_user
-- ----------------------------
ALTER TABLE "public"."t_user" ADD CONSTRAINT "t_user_pk" PRIMARY KEY ("id");
