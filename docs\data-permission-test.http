# 数据权限管理接口测试
# 使用VS Code REST Client插件运行
# 或者复制到Postman/Swagger中测试

### 0. 插入测试数据 - 快速创建8个测试数据权限
POST http://localhost:8080/data-permissions/insertTestData
Content-Type: application/json

{}

### 1. 分页查询数据权限列表 - 基础查询
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10
}

### 2. 分页查询数据权限列表 - 按模块筛选
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "moduleIdentifier": "user_data_module"
}

### 3. 分页查询数据权限列表 - 按名称筛选
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "name": "用户"
}

### 4. 分页查询数据权限列表 - 按状态筛选（正常）
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "isDisable": false
}

### 5. 分页查询数据权限列表 - 按状态筛选（禁用）
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "isDisable": true
}

### 6. 分页查询数据权限列表 - 组合筛选
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 5,
  "moduleIdentifier": "order_data_module",
  "name": "订单",
  "isDisable": false
}

### 7. 不分页查询 - 获取所有数据
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{}

### 8. 新增数据权限 - 基础测试
POST http://localhost:8080/data-permissions/add
Content-Type: application/json

{
  "name": "用户敏感数据",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "user_sensitive_view",
  "orderInfo": 4,
  "isDisable": false
}

### 9. 新增数据权限 - 测试必填字段验证
POST http://localhost:8080/data-permissions/add
Content-Type: application/json

{
  "name": "",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "test_empty_name"
}

### 10. 新增数据权限 - 测试重复数据标识
POST http://localhost:8080/data-permissions/add
Content-Type: application/json

{
  "name": "重复数据标识测试",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "user_data_view",
  "orderInfo": 5
}

### 11. 新增数据权限 - 测试不存在的模块
POST http://localhost:8080/data-permissions/add
Content-Type: application/json

{
  "name": "不存在模块测试",
  "preId": 0,
  "moduleIdentifier": "nonexistent_module",
  "dataType": 1,
  "dataIdentifier": "test_nonexistent_module",
  "orderInfo": 1
}

### 12. 获取数据权限详情 - 正常获取
POST http://localhost:8080/data-permissions/detail
Content-Type: application/json

{
  "dataId": "2001"
}

### 13. 获取数据权限详情 - 测试不存在的ID
POST http://localhost:8080/data-permissions/detail
Content-Type: application/json

{
  "dataId": "9999999999999999999"
}

### 14. 获取数据权限详情 - 测试空ID
POST http://localhost:8080/data-permissions/detail
Content-Type: application/json

{
  "dataId": ""
}

### 15. 获取数据权限详情 - 测试无效ID格式
POST http://localhost:8080/data-permissions/detail
Content-Type: application/json

{
  "dataId": "invalid_id"
}

### 16. 修改数据权限 - 正常修改
POST http://localhost:8080/data-permissions/update
Content-Type: application/json

{
  "id": "2001",
  "name": "用户基础数据（已修改）",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "user_data_updated",
  "orderInfo": 10,
  "isDisable": false
}

### 17. 修改数据权限 - 测试必填字段验证
POST http://localhost:8080/data-permissions/update
Content-Type: application/json

{
  "id": "2001",
  "name": "",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "user_basic_data"
}

### 18. 修改数据权限 - 测试不存在的ID
POST http://localhost:8080/data-permissions/update
Content-Type: application/json

{
  "id": "9999999999999999999",
  "name": "不存在的权限",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "nonexistent_permission",
  "orderInfo": 1
}

### 19. 修改数据权限 - 测试重复数据标识
POST http://localhost:8080/data-permissions/update
Content-Type: application/json

{
  "id": "2001",
  "name": "用户基础数据",
  "preId": 0,
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "dataIdentifier": "user_profile_data",
  "orderInfo": 1
}

### 20. 修改数据权限 - 测试不存在的模块
POST http://localhost:8080/data-permissions/update
Content-Type: application/json

{
  "id": "2001",
  "name": "用户基础数据",
  "preId": 0,
  "moduleIdentifier": "nonexistent_module",
  "dataType": 1,
  "dataIdentifier": "user_basic_data",
  "orderInfo": 1
}

### 21. 删除数据权限 - 正常删除
POST http://localhost:8080/data-permissions/delete
Content-Type: application/json

{
  "dataId": "2008"
}

### 22. 删除数据权限 - 测试不存在的ID
POST http://localhost:8080/data-permissions/delete
Content-Type: application/json

{
  "dataId": "9999999999999999999"
}

### 23. 删除数据权限 - 测试空ID
POST http://localhost:8080/data-permissions/delete
Content-Type: application/json

{
  "dataId": ""
}

### 24. 删除数据权限 - 测试无效ID格式
POST http://localhost:8080/data-permissions/delete
Content-Type: application/json

{
  "dataId": "invalid_id"
}

### 25. 删除数据权限 - 测试被使用的权限（如果有角色权限关联）
POST http://localhost:8080/data-permissions/delete
Content-Type: application/json

{
  "dataId": "2001"
}

### 26. 配置数据操作权限 - 配置全部操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_basic_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2, 3]
}

### 27. 配置数据操作权限 - 配置部分操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_profile_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": [1, 2]
}

### 28. 配置数据操作权限 - 清空操作类型
POST http://localhost:8080/data-operates/configure
Content-Type: application/json

{
  "dataIdentifier": "user_auth_data",
  "moduleIdentifier": "user_data_module",
  "dataType": 1,
  "operateTypes": []
}

### 29. 重新查询数据权限列表验证操作类型配置结果
POST http://localhost:8080/data-permissions/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "moduleIdentifier": "user_data_module"
}
