package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 人员信息表，与组织架构绑定
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Getter
@Setter
@TableName("t_user")
@ApiModel(value = "TUser对象", description = "人员信息表")
public class TUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("人员信息id")
    private Long id;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("是否删除")
    private Boolean isDel;

    @ApiModelProperty("原始关联id，用于外部系统对接")
    private String originId;

    @ApiModelProperty("组织归属，对应组织架构表中的id")
    private Long organAffiliation;

    @ApiModelProperty("账户")
    private String account;

    @ApiModelProperty("密码")
    private String password;

    @ApiModelProperty("是否停用")
    private Boolean isDisable;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime modifyTime;
}
