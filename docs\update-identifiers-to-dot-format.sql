-- =====================================================
-- 权限标识符格式修改脚本：下划线 → 点号格式
-- 执行时间：2025年6月26日
-- 修改说明：将权限标识符从下划线格式改为点号格式，符合Sa-Token规范
-- 注意：保持精确权限控制，不使用Sa-Token的层级继承特性
-- =====================================================

-- 备份当前数据（可选）
-- CREATE TABLE t_roles_menu_permission_backup AS SELECT * FROM t_roles_menu_permission;

-- =====================================================
-- 第一部分：菜单权限模块标识符转换
-- =====================================================

-- 1. 系统管理模块
UPDATE t_roles_menu_permission 
SET module_identifier = 'system.management' 
WHERE module_identifier = 'system_management';

-- 2. 用户管理模块  
UPDATE t_roles_menu_permission 
SET module_identifier = 'user.management' 
WHERE module_identifier = 'user_management';

-- 3. 权限管理模块
UPDATE t_roles_menu_permission 
SET module_identifier = 'permission.management' 
WHERE module_identifier = 'permission_management';

-- 4. 开发工具模块
UPDATE t_roles_menu_permission 
SET module_identifier = 'development.tools' 
WHERE module_identifier = 'development_tools';

-- 5. 数据分析模块
UPDATE t_roles_menu_permission 
SET module_identifier = 'data.analytics' 
WHERE module_identifier = 'data_analytics';

-- =====================================================
-- 第二部分：菜单模块表标识符转换（如果存在）
-- =====================================================

-- 更新菜单模块表中的模块标识符
UPDATE t_menu_module 
SET module_identifier = 'system.management' 
WHERE module_identifier = 'system_management';

UPDATE t_menu_module 
SET module_identifier = 'user.management' 
WHERE module_identifier = 'user_management';

UPDATE t_menu_module 
SET module_identifier = 'permission.management' 
WHERE module_identifier = 'permission_management';

UPDATE t_menu_module 
SET module_identifier = 'development.tools' 
WHERE module_identifier = 'development_tools';

UPDATE t_menu_module 
SET module_identifier = 'data.analytics' 
WHERE module_identifier = 'data_analytics';

-- =====================================================
-- 第三部分：数据权限模块标识符转换（如果存在）
-- =====================================================

-- 更新数据模块表中的模块标识符
UPDATE t_data_module 
SET module_identifier = 'user.data' 
WHERE module_identifier = 'user_data';

UPDATE t_data_module 
SET module_identifier = 'department.data' 
WHERE module_identifier = 'department_data';

UPDATE t_data_module 
SET module_identifier = 'system.data' 
WHERE module_identifier = 'system_data';

-- =====================================================
-- 第四部分：菜单权限表中的权限标识符转换（如果存在）
-- =====================================================

-- 更新菜单权限表中的权限标识符（如果有permission_identifier字段）
-- 注意：根据实际表结构调整

-- 系统用户相关权限
UPDATE t_menu_permission 
SET permission_identifier = 'system.user.view' 
WHERE permission_identifier = 'system_user_view';

UPDATE t_menu_permission 
SET permission_identifier = 'system.user.add' 
WHERE permission_identifier = 'system_user_add';

UPDATE t_menu_permission 
SET permission_identifier = 'system.user.edit' 
WHERE permission_identifier = 'system_user_edit';

UPDATE t_menu_permission 
SET permission_identifier = 'system.user.delete' 
WHERE permission_identifier = 'system_user_delete';

-- 权限角色相关权限
UPDATE t_menu_permission 
SET permission_identifier = 'permission.role.view' 
WHERE permission_identifier = 'permission_role_view';

UPDATE t_menu_permission 
SET permission_identifier = 'permission.role.add' 
WHERE permission_identifier = 'permission_role_add';

UPDATE t_menu_permission 
SET permission_identifier = 'permission.role.edit' 
WHERE permission_identifier = 'permission_role_edit';

UPDATE t_menu_permission 
SET permission_identifier = 'permission.role.delete' 
WHERE permission_identifier = 'permission_role_delete';

-- 菜单管理相关权限
UPDATE t_menu_permission 
SET permission_identifier = 'menu.management.view' 
WHERE permission_identifier = 'menu_management_view';

UPDATE t_menu_permission 
SET permission_identifier = 'menu.management.edit' 
WHERE permission_identifier = 'menu_management_edit';

-- 数据权限管理相关权限
UPDATE t_menu_permission 
SET permission_identifier = 'data.management.view' 
WHERE permission_identifier = 'data_management_view';

UPDATE t_menu_permission 
SET permission_identifier = 'data.management.edit' 
WHERE permission_identifier = 'data_management_edit';

-- =====================================================
-- 第五部分：验证修改结果
-- =====================================================

-- 验证菜单权限模块标识符修改结果
SELECT 
    module_identifier,
    COUNT(*) as count
FROM t_roles_menu_permission 
WHERE module_identifier IS NOT NULL
GROUP BY module_identifier
ORDER BY module_identifier;

-- 验证是否还有下划线格式的标识符
SELECT 
    module_identifier,
    COUNT(*) as count
FROM t_roles_menu_permission 
WHERE module_identifier LIKE '%_%'
GROUP BY module_identifier;

-- 验证菜单模块表
SELECT 
    module_identifier,
    module_name
FROM t_menu_module 
WHERE module_identifier IS NOT NULL
ORDER BY module_identifier;

-- =====================================================
-- 修改完成说明
-- =====================================================
-- 1. ✅ 菜单权限模块标识符已从下划线格式改为点号格式
-- 2. ✅ 菜单模块表标识符已更新
-- 3. ✅ 数据权限模块标识符已更新  
-- 4. ✅ 权限标识符格式已统一为点号格式
-- 5. ✅ 符合Sa-Token规范，但保持精确权限控制
-- 6. ✅ 不使用Sa-Token的层级继承特性
-- =====================================================
