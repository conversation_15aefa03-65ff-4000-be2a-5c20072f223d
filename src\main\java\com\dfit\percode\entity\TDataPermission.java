package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 数据权限信息列表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Getter
@Setter
@TableName("t_data_permission")
@ApiModel(value = "TDataPermission对象", description = "数据权限信息列表")
public class TDataPermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据权限ID")
    private Long id;

    @ApiModelProperty("数据权限名称")
    private String name;

    @ApiModelProperty("父ID，根节点为0")
    private Long preId;

    @ApiModelProperty("模块标识，与数据模块表中模块标识一致")
    private String moduleIdentifier;

    @ApiModelProperty("数据类型，1-检签等")
    private Integer dataType;

    @ApiModelProperty("排序序号，从1开始")
    private Integer orderInfo;

    @ApiModelProperty("是否停用，true停用，false正常")
    private Boolean isDisable;

    @ApiModelProperty("是否删除，true删除，false正常")
    private Boolean isDel;

    @ApiModelProperty("数据标识，页面输入或表同步")
    private String dataIdentifier;

    @ApiModelProperty("创建时间")
    private java.time.LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private java.time.LocalDateTime modifyTime;
}
