package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 角色信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_role")
@ApiModel(value = "TRole对象", description = "角色信息表")
public class TRole implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("角色名称")
    private String roleName;

    private Integer orderInfo;

    @ApiModelProperty("是否停用，true停用，false正常")
    private Boolean isDisable;

    private Boolean isDel;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;
}
