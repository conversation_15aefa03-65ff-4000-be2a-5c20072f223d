package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TRolesMenuPermission;
import com.dfit.percode.entity.TRolesDataPermission;
import com.dfit.percode.mapper.TRoleMapper;
import com.dfit.percode.mapper.TRolesMenuPermissionMapper;
import com.dfit.percode.mapper.TRolesDataPermissionMapper;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.util.DataOperateTypeUtil;
import com.dfit.percode.entity.TRole;
import com.dfit.percode.service.ITRoleService;
import com.dfit.percode.vo.RoleListRequestVO;
import com.dfit.percode.vo.RoleListResponseVO;
import com.dfit.percode.vo.RoleListItemVO;
import com.dfit.percode.vo.AddRoleRequestVO;
import com.dfit.percode.vo.AddRoleResponseVO;
import com.dfit.percode.vo.EditRoleRequestVO;
import com.dfit.percode.vo.DeleteRoleRequestVO;
import com.dfit.percode.vo.RoleDetailRequestVO;
import com.dfit.percode.vo.RoleDetailResponseVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfit.percode.userPerm.AddRoleParam;
import com.dfit.percode.userPerm.TRoleEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

import java.util.ArrayList;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;

/**
 * <p>
 * 角色信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Slf4j
@Service
public class TRoleServiceImpl extends ServiceImpl<TRoleMapper, TRole> implements ITRoleService {
    @Autowired
    TRoleMapper tRoleMapper;

    @Autowired
    TRolesMenuPermissionMapper rolesMenuPermissionMapper;

    @Autowired
    TRolesDataPermissionMapper rolesDataPermissionMapper;

    @Override
    public void addNewRole(AddRoleParam arp) {
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long id = idGenerator.generateId();
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String createTime = sdf.format(date);

        TRoleEntity tRoleEntity = new TRoleEntity();
        tRoleEntity.setId(id);
        tRoleEntity.setRoleName(arp.getRoleName());
        tRoleEntity.setIsDisable(String.valueOf(arp.getIsDisable()));
        tRoleEntity.setCreateTime(createTime);
        tRoleEntity.setModifyTime(createTime);
        tRoleEntity.setOrderInfo(1);
        tRoleMapper.insertTRole(tRoleEntity);
        //插入角色 菜单关联表
        insertRoleMenuPerm(tRoleEntity);
        //插入角色-权限 关联表
        insertRoleDataPerm(tRoleEntity);
    }

    /**
     * 分页查询角色列表
     * 支持角色名称模糊搜索、状态筛选、删除状态筛选
     *
     * @param request 查询请求参数
     * @return 角色列表响应，包含分页信息
     */
    @Override
    public RoleListResponseVO getRoleList(RoleListRequestVO request) {
        log.info("开始分页查询角色列表");
        log.info("页码: {}, 页大小: {}, 角色名称: {}, 状态: {}",
                request.getCurrentPage(), request.getPageSize(), request.getRoleName(), request.getIsDisable());

        // 计算分页偏移量（MyBatis分页从0开始）
        int offset = (request.getCurrentPage() - 1) * request.getPageSize();

        // 创建一个临时变量存储偏移量，不修改原始请求对象
        int originalCurrentPage = request.getCurrentPage();

        // 临时设置偏移量用于查询
        request.setCurrentPage(offset);

        // 查询角色列表
        List<RoleListItemVO> roleList = tRoleMapper.findRoleListPage(request);

        // 恢复原始页码
        request.setCurrentPage(originalCurrentPage);
        log.info("查询到角色数量: {}", roleList.size());

        // 设置canDelete字段（有关联用户时不可删除）
        for (RoleListItemVO role : roleList) {
            role.setCanDelete(role.getUserCount() == 0);
        }

        // 查询总记录数
        Long total = tRoleMapper.countRoleList(request);
        log.info("角色总数: {}", total);

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / request.getPageSize());

        // 构建响应对象
        RoleListResponseVO response = new RoleListResponseVO();
        response.setRecords(roleList);
        response.setTotal(total);
        response.setCurrentPage(request.getCurrentPage());
        response.setPageSize(request.getPageSize());
        response.setTotalPages(totalPages);

        log.info("角色列表查询完成，当前页: {}/{}, 总记录数: {}",
                request.getCurrentPage(), totalPages, total);

        return response;
    }

    /**
     * 新增角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的设置
     *
     * @param request 新增角色请求参数
     * @return 新增角色响应，包含角色基本信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddRoleResponseVO addRole(AddRoleRequestVO request) {
        log.info("开始新增角色");
        log.info("角色名称: {}, 是否停用: {}", request.getRoleName(), request.getIsDisable());

        // 1. 参数验证
        validateAddRoleRequest(request);

        // 2. 检查角色名称是否重复
        checkRoleNameExists(request.getRoleName());

        // 3. 生成角色ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        Long roleId = idGenerator.generateId();
        log.info("生成角色ID: {}", roleId);

        // 4. 创建角色基本信息
        TRole role = createRoleEntity(request, roleId);

        // 5. 插入角色基本信息
        int insertResult = tRoleMapper.insert(role);
        if (insertResult <= 0) {
            throw new RuntimeException("角色信息插入失败");
        }
        log.info("角色基本信息插入成功");

        // 6. 处理菜单权限
        int menuPermissionCount = 0;
        if (request.getMenuName() != null && !request.getMenuName().isEmpty()) {
            menuPermissionCount = saveMenuPermissions(roleId, request.getMenuName());
            log.info("菜单权限保存完成，数量: {}", menuPermissionCount);
        }

        // 7. 处理数据权限
        int dataPermissionCount = 0;
        if (request.getDataPermission() != null && !request.getDataPermission().isEmpty()) {
            dataPermissionCount = saveDataPermissions(roleId, request.getDataPermission());
            log.info("数据权限保存完成，数量: {}", dataPermissionCount);
        }

        // 8. 构建响应对象
        AddRoleResponseVO response = buildAddRoleResponse(role, menuPermissionCount, dataPermissionCount);

        log.info("角色新增完成，角色ID: {}, 菜单权限数: {}, 数据权限数: {}",
                roleId, menuPermissionCount, dataPermissionCount);

        return response;
    }

    /**
     * 修改角色（前端格式）
     * 支持角色基本信息、菜单权限、数据权限的修改
     *
     * @param request 修改角色请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editRole(EditRoleRequestVO request) {
        log.info("开始修改角色");
        log.info("角色ID: {}, 角色名称: {}, 是否停用: {}", request.getId(), request.getRoleName(), request.getIsDisable());

        // 1. 参数验证
        validateEditRoleRequest(request);

        // 2. 检查角色是否存在
        Long roleId = Long.parseLong(request.getId());
        TRole existingRole = checkRoleExists(roleId);

        // 3. 检查角色名称是否重复（排除自己）
        checkRoleNameExistsForEdit(request.getRoleName(), roleId);

        // 4. 更新角色基本信息
        updateRoleBasicInfo(existingRole, request);

        // 5. 删除原有的权限关联
        deleteExistingPermissions(roleId);

        // 6. 重新创建菜单权限
        int menuPermissionCount = 0;
        if (request.getMenuName() != null && !request.getMenuName().isEmpty()) {
            menuPermissionCount = saveMenuPermissionsForEdit(roleId, request.getMenuName());
            log.info("菜单权限重新保存完成，数量: {}", menuPermissionCount);
        }

        // 7. 重新创建数据权限
        int dataPermissionCount = 0;
        if (request.getDataPermission() != null && !request.getDataPermission().isEmpty()) {
            dataPermissionCount = saveDataPermissionsForEdit(roleId, request.getDataPermission());
            log.info("数据权限重新保存完成，数量: {}", dataPermissionCount);
        }

        log.info("角色修改完成，角色ID: {}, 菜单权限数: {}, 数据权限数: {}",
                request.getId(), menuPermissionCount, dataPermissionCount);
    }

    /**
     * 删除角色（逻辑删除）
     * 检查角色是否被用户使用，如果有关联则不能删除
     *
     * @param request 删除角色请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteRole(DeleteRoleRequestVO request) {
        log.info("开始删除角色，角色ID: {}", request.getId());

        // 1. 检查角色是否存在
        Long roleId = Long.parseLong(request.getId());
        TRole existingRole = checkRoleExists(roleId);

        // 2. 检查角色是否被用户使用
        checkRoleInUse(roleId);

        // 3. 逻辑删除角色
        UpdateWrapper<TRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", existingRole.getId())
                    .set("is_del", true)
                    .setSql("modify_time = CURRENT_TIMESTAMP"); // 使用数据库函数更新时间

        int updateResult = tRoleMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            throw new RuntimeException("角色删除失败");
        }

        // 4. 逻辑删除相关的权限关联记录
        deleteExistingPermissions(roleId);

        log.info("角色删除完成，角色ID: {}", request.getId());
    }

    /**
     * 验证新增角色请求参数
     */
    private void validateAddRoleRequest(AddRoleRequestVO request) {
        if (request.getRoleName() == null || request.getRoleName().trim().isEmpty()) {
            throw new IllegalArgumentException("角色名称不能为空");
        }
        if (request.getRoleName().length() > 50) {
            throw new IllegalArgumentException("角色名称长度不能超过50个字符");
        }
    }

    /**
     * 检查角色名称是否已存在
     */
    private void checkRoleNameExists(String roleName) {
        QueryWrapper<TRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", roleName)
                   .eq("is_del", false);

        long count = tRoleMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("角色名称已存在：" + roleName);
        }
    }

    /**
     * 创建角色实体对象
     */
    private TRole createRoleEntity(AddRoleRequestVO request, Long roleId) {
        TRole role = new TRole();
        role.setId(roleId);
        role.setRoleName(request.getRoleName());
        role.setOrderInfo(request.getOrderInfo() != null ? request.getOrderInfo() : 999);
        role.setIsDisable(request.getIsDisable() != null ? request.getIsDisable() : false);
        role.setIsDel(false);

        // 设置时间
        LocalDateTime currentTime = LocalDateTime.now();
        role.setCreateTime(currentTime);
        role.setModifyTime(currentTime);

        return role;
    }

    /**
     * 保存菜单权限关联
     */
    private int saveMenuPermissions(Long roleId, List<AddRoleRequestVO.MenuPermissionItem> menuPermissions) {
        // 先将树形结构扁平化为一维列表
        List<AddRoleRequestVO.MenuPermissionItem> flatMenuList = new ArrayList<>();
        flattenMenuPermissionsForAdd(menuPermissions, flatMenuList);

        log.info("扁平化后的菜单权限数量: {}", flatMenuList.size());

        int count = 0;
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        LocalDateTime currentTime = LocalDateTime.now();

        for (AddRoleRequestVO.MenuPermissionItem menuItem : flatMenuList) {
            TRolesMenuPermission roleMenuPerm = new TRolesMenuPermission();
            roleMenuPerm.setId(idGenerator.generateId());
            roleMenuPerm.setRoleId(roleId);
            roleMenuPerm.setModuleIdentifier(menuItem.getModuleIdentifier()); // 使用菜单项的模块标识符
            roleMenuPerm.setMenuId(menuItem.getId());
            roleMenuPerm.setIsDel(false);
            roleMenuPerm.setCreateTime(currentTime);
            roleMenuPerm.setModifyTime(currentTime);

            int result = rolesMenuPermissionMapper.insert(roleMenuPerm);
            if (result > 0) {
                count++;
                log.debug("保存菜单权限成功: roleId={}, menuId={}, moduleIdentifier={}",
                         roleId, menuItem.getId(), menuItem.getModuleIdentifier());
            }
        }

        return count;
    }

    /**
     * 递归扁平化菜单权限树形结构（新增角色用）
     */
    private void flattenMenuPermissionsForAdd(List<AddRoleRequestVO.MenuPermissionItem> menuItems,
                                              List<AddRoleRequestVO.MenuPermissionItem> flatList) {
        if (menuItems == null || menuItems.isEmpty()) {
            return;
        }

        for (AddRoleRequestVO.MenuPermissionItem item : menuItems) {
            // 添加当前项到扁平列表
            flatList.add(item);
            log.debug("添加菜单权限到扁平列表: id={}, name={}, moduleIdentifier={}",
                     item.getId(), item.getName(), item.getModuleIdentifier());

            // 递归处理子菜单
            if (item.getChildren() != null && !item.getChildren().isEmpty()) {
                flattenMenuPermissionsForAdd(item.getChildren(), flatList);
            }
        }
    }

    /**
     * 保存数据权限关联
     */
    private int saveDataPermissions(Long roleId, List<AddRoleRequestVO.DataPermissionItem> dataPermissions) {
        int count = 0;
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        LocalDateTime currentTime = LocalDateTime.now();

        for (AddRoleRequestVO.DataPermissionItem dataItem : dataPermissions) {
            // 跳过无效的数据权限项
            if (dataItem.getDataId() == null || dataItem.getDataId().trim().isEmpty()) {
                log.warn("跳过无效的数据权限项，dataId为空");
                continue;
            }

            // 如果有多个操作类型，为每个操作类型创建一条记录
            List<Integer> operateTypes = dataItem.getOperateTypes();
            if (operateTypes == null || operateTypes.isEmpty()) {
                log.warn("跳过无效的数据权限项，operateTypes为空，dataId: {}", dataItem.getDataId());
                continue;
            }

            try {
                Long dataId = Long.parseLong(dataItem.getDataId());

                for (Integer operateType : operateTypes) {
                    TRolesDataPermission roleDataPerm = new TRolesDataPermission();
                    roleDataPerm.setId(idGenerator.generateId());
                    roleDataPerm.setRoleId(roleId);
                    roleDataPerm.setModuleIdentifier(dataItem.getModuleIdentifier());
                    roleDataPerm.setDataType(dataItem.getDataType());
                    roleDataPerm.setDataId(dataId);
                    roleDataPerm.setOperateType(operateType);
                    roleDataPerm.setDataOperateId(
                        DataOperateTypeUtil.generateDataOperateId(dataId, operateType)
                    );
                    roleDataPerm.setIsDel(false);
                    roleDataPerm.setCreateTime(currentTime);
                    roleDataPerm.setModifyTime(currentTime);

                    int result = rolesDataPermissionMapper.insert(roleDataPerm);
                    if (result > 0) {
                        count++;
                    }
                }
            } catch (NumberFormatException e) {
                log.error("数据权限ID格式错误，跳过该项，dataId: {}", dataItem.getDataId(), e);
                continue;
            }
        }

        return count;
    }

    /**
     * 构建新增角色响应对象
     */
    private AddRoleResponseVO buildAddRoleResponse(TRole role, int menuPermissionCount, int dataPermissionCount) {
        AddRoleResponseVO response = new AddRoleResponseVO();
        response.setId(role.getId());
        response.setRoleName(role.getRoleName());
        response.setOrderInfo(role.getOrderInfo());
        response.setIsDisable(role.getIsDisable());
        response.setCreateTime(role.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        response.setMenuPermissionCount(menuPermissionCount);
        response.setDataPermissionCount(dataPermissionCount);

        return response;
    }

    /**
     * 验证修改角色请求参数
     */
    private void validateEditRoleRequest(EditRoleRequestVO request) {
        if (request.getId() == null || request.getId().trim().isEmpty()) {
            throw new IllegalArgumentException("角色ID不能为空");
        }
        try {
            Long.parseLong(request.getId());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("角色ID格式不正确");
        }
        if (request.getRoleName() == null || request.getRoleName().trim().isEmpty()) {
            throw new IllegalArgumentException("角色名称不能为空");
        }
        if (request.getRoleName().length() > 50) {
            throw new IllegalArgumentException("角色名称长度不能超过50个字符");
        }
    }

    /**
     * 检查角色是否存在
     */
    private TRole checkRoleExists(Long roleId) {
        // 使用QueryWrapper只查询基本字段，避免时间字段的类型转换问题
        QueryWrapper<TRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", roleId)
                   .eq("is_del", false)
                   .select("id", "role_name", "order_info", "is_disable", "is_del");

        TRole role = tRoleMapper.selectOne(queryWrapper);
        if (role == null) {
            throw new IllegalArgumentException("角色不存在或已被删除，ID：" + roleId);
        }
        return role;
    }

    /**
     * 检查角色名称是否已存在（修改时排除自己）
     */
    private void checkRoleNameExistsForEdit(String roleName, Long excludeRoleId) {
        QueryWrapper<TRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("role_name", roleName)
                   .eq("is_del", false)
                   .ne("id", excludeRoleId);

        long count = tRoleMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new IllegalArgumentException("角色名称已存在：" + roleName);
        }
    }

    /**
     * 检查角色是否被用户使用
     */
    private void checkRoleInUse(Long roleId) {
        // 这里需要查询t_perm_user_role表，检查是否有用户关联此角色
        // 暂时简化处理，实际应该注入TPermUserRoleMapper
        log.info("检查角色是否被用户使用，角色ID: {}", roleId);
        // TODO: 实现具体的检查逻辑
    }

    /**
     * 更新角色基本信息
     */
    private void updateRoleBasicInfo(TRole existingRole, EditRoleRequestVO request) {
        // 使用UpdateWrapper来更新，可以设置SQL函数
        UpdateWrapper<TRole> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", existingRole.getId())
                    .set("role_name", request.getRoleName())
                    .set("order_info", request.getOrderInfo() != null ? request.getOrderInfo() : existingRole.getOrderInfo())
                    .set("is_disable", request.getIsDisable() != null ? request.getIsDisable() : existingRole.getIsDisable())
                    .setSql("modify_time = CURRENT_TIMESTAMP"); // 使用数据库函数更新时间

        int updateResult = tRoleMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            throw new RuntimeException("角色基本信息更新失败");
        }
        log.info("角色基本信息更新成功，角色ID: {}", existingRole.getId());
    }

    /**
     * 删除现有的权限关联
     */
    private void deleteExistingPermissions(Long roleId) {
        // 删除菜单权限关联
        QueryWrapper<TRolesMenuPermission> menuWrapper = new QueryWrapper<>();
        menuWrapper.eq("role_id", roleId)
                   .eq("is_del", false)
                   .select("id", "role_id", "module_identifier", "menu_id", "is_del"); // 只查询基本字段
        List<TRolesMenuPermission> menuPermissions = rolesMenuPermissionMapper.selectList(menuWrapper);
        log.info("查询到需要删除的菜单权限数量: {}", menuPermissions.size());

        for (TRolesMenuPermission permission : menuPermissions) {
            // 使用UpdateWrapper更新，同时更新时间字段
            UpdateWrapper<TRolesMenuPermission> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", permission.getId())
                        .set("is_del", true)
                        .setSql("modify_time = CURRENT_TIMESTAMP");

            int result = rolesMenuPermissionMapper.update(null, updateWrapper);
            log.info("删除菜单权限关联，ID: {}, 结果: {}", permission.getId(), result);
        }

        // 删除数据权限关联
        QueryWrapper<TRolesDataPermission> dataWrapper = new QueryWrapper<>();
        dataWrapper.eq("role_id", roleId)
                   .eq("is_del", false)
                   .select("id", "role_id", "module_identifier", "data_type", "data_id", "operate_type", "is_del"); // 只查询基本字段
        List<TRolesDataPermission> dataPermissions = rolesDataPermissionMapper.selectList(dataWrapper);
        log.info("查询到需要删除的数据权限数量: {}", dataPermissions.size());

        for (TRolesDataPermission permission : dataPermissions) {
            // 使用UpdateWrapper更新，同时更新时间字段
            UpdateWrapper<TRolesDataPermission> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", permission.getId())
                        .set("is_del", true)
                        .setSql("modify_time = CURRENT_TIMESTAMP");

            int result = rolesDataPermissionMapper.update(null, updateWrapper);
            log.info("删除数据权限关联，ID: {}, 结果: {}", permission.getId(), result);
        }

        log.info("删除现有权限关联完成，角色ID: {}", roleId);
    }

    /**
     * 保存菜单权限关联（修改时使用）
     */
    private int saveMenuPermissionsForEdit(Long roleId, List<EditRoleRequestVO.MenuPermissionItem> menuPermissions) {
        // 先将树形结构扁平化为一维列表
        List<EditRoleRequestVO.MenuPermissionItem> flatMenuList = new ArrayList<>();
        flattenMenuPermissionsForEdit(menuPermissions, flatMenuList);

        log.info("编辑角色时扁平化后的菜单权限数量: {}", flatMenuList.size());

        int count = 0;
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        LocalDateTime currentTime = LocalDateTime.now();

        for (EditRoleRequestVO.MenuPermissionItem menuItem : flatMenuList) {
            TRolesMenuPermission roleMenuPerm = new TRolesMenuPermission();
            roleMenuPerm.setId(idGenerator.generateId());
            roleMenuPerm.setRoleId(roleId);
            roleMenuPerm.setModuleIdentifier(menuItem.getModuleIdentifier()); // 使用菜单项的模块标识符
            roleMenuPerm.setMenuId(menuItem.getId());
            roleMenuPerm.setIsDel(false);
            roleMenuPerm.setCreateTime(currentTime);
            roleMenuPerm.setModifyTime(currentTime);

            int result = rolesMenuPermissionMapper.insert(roleMenuPerm);
            if (result > 0) {
                count++;
                log.debug("编辑角色保存菜单权限成功: roleId={}, menuId={}, moduleIdentifier={}",
                         roleId, menuItem.getId(), menuItem.getModuleIdentifier());
            }
        }

        return count;
    }

    /**
     * 递归扁平化菜单权限树形结构（编辑角色用）
     */
    private void flattenMenuPermissionsForEdit(List<EditRoleRequestVO.MenuPermissionItem> menuItems,
                                               List<EditRoleRequestVO.MenuPermissionItem> flatList) {
        if (menuItems == null || menuItems.isEmpty()) {
            return;
        }

        for (EditRoleRequestVO.MenuPermissionItem item : menuItems) {
            // 添加当前项到扁平列表
            flatList.add(item);
            log.debug("编辑角色添加菜单权限到扁平列表: id={}, name={}, moduleIdentifier={}",
                     item.getId(), item.getName(), item.getModuleIdentifier());

            // 递归处理子菜单
            if (item.getChildren() != null && !item.getChildren().isEmpty()) {
                flattenMenuPermissionsForEdit(item.getChildren(), flatList);
            }
        }
    }

    /**
     * 保存数据权限关联（修改时使用）
     */
    private int saveDataPermissionsForEdit(Long roleId, List<EditRoleRequestVO.DataPermissionItem> dataPermissions) {
        int count = 0;
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        LocalDateTime currentTime = LocalDateTime.now();

        for (EditRoleRequestVO.DataPermissionItem dataItem : dataPermissions) {
            // 跳过无效的数据权限项
            if (dataItem.getDataId() == null || dataItem.getDataId().trim().isEmpty()) {
                log.warn("跳过无效的数据权限项，dataId为空");
                continue;
            }

            // 如果有多个操作类型，为每个操作类型创建一条记录
            List<Integer> operateTypes = dataItem.getOperateTypes();
            if (operateTypes == null || operateTypes.isEmpty()) {
                log.warn("跳过无效的数据权限项，operateTypes为空，dataId: {}", dataItem.getDataId());
                continue;
            }

            try {
                Long dataId = Long.parseLong(dataItem.getDataId());

                for (Integer operateType : operateTypes) {
                    TRolesDataPermission roleDataPerm = new TRolesDataPermission();
                    roleDataPerm.setId(idGenerator.generateId());
                    roleDataPerm.setRoleId(roleId);
                    roleDataPerm.setModuleIdentifier(dataItem.getModuleIdentifier());
                    roleDataPerm.setDataType(dataItem.getDataType());
                    roleDataPerm.setDataId(dataId);
                    roleDataPerm.setOperateType(operateType);
                    roleDataPerm.setDataOperateId(
                        DataOperateTypeUtil.generateDataOperateId(dataId, operateType)
                    );
                    roleDataPerm.setIsDel(false);
                    roleDataPerm.setCreateTime(currentTime);
                    roleDataPerm.setModifyTime(currentTime);

                    int result = rolesDataPermissionMapper.insert(roleDataPerm);
                    if (result > 0) {
                        count++;
                    }
                }
            } catch (NumberFormatException e) {
                log.error("数据权限ID格式错误，跳过该项，dataId: {}", dataItem.getDataId(), e);
                continue;
            }
        }

        return count;
    }

    private void insertRoleDataPerm(TRoleEntity tRoleEntity) {
    }

    private void insertRoleMenuPerm(TRoleEntity tRoleEntity) {
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long id = idGenerator.generateId();
        //创建时间
        LocalDateTime currentTime = LocalDateTime.now();

        TRolesMenuPermission tmp = new TRolesMenuPermission();
        tmp.setId(id);
        tmp.setRoleId(tRoleEntity.getId());
        tmp.setModuleIdentifier("default_module"); // 设置默认模块标识符
        tmp.setIsDel(false);
        tmp.setMenuId(1L);
        tmp.setCreateTime(currentTime);
        tmp.setModifyTime(currentTime);
        tRoleMapper.insertRoleMenuPerm(tRoleEntity);
    }

    /**
     * 获取角色详情
     * 查询角色基本信息和已关联的权限ID列表
     * 用于角色编辑时的数据回显
     *
     * @param request 角色详情查询请求参数
     * @return 角色详情响应，包含基本信息和权限关联
     */
    @Override
    public RoleDetailResponseVO getRoleDetail(RoleDetailRequestVO request) {
        log.info("开始获取角色详情，角色ID: {}", request.getRoleId());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 查询角色基本信息
            RoleDetailResponseVO.RoleInfo roleInfo = tRoleMapper.getRoleBasicInfo(request.getRoleId());
            if (roleInfo == null) {
                log.warn("角色不存在，角色ID: {}", request.getRoleId());
                throw new RuntimeException("角色不存在");
            }

            // 2. 查询已关联的菜单权限ID列表
            List<Long> menuPermissions = tRoleMapper.getRoleMenuPermissions(request.getRoleId());
            log.info("角色 {} 关联的菜单权限数量: {}", request.getRoleId(), menuPermissions.size());

            // 3. 查询已关联的数据权限ID列表
            List<Long> dataPermissionIds = tRoleMapper.getRoleDataPermissionIds(request.getRoleId());
            log.info("角色 {} 关联的数据权限数量: {}", request.getRoleId(), dataPermissionIds.size());

            // 4. 为每个数据权限查询操作类型
            List<RoleDetailResponseVO.DataPermissionItem> dataPermissions = new ArrayList<>();
            for (Long dataId : dataPermissionIds) {
                try {
                    List<Integer> operateTypes = tRoleMapper.getRoleDataPermissionOperateTypes(request.getRoleId(), dataId);

                    RoleDetailResponseVO.DataPermissionItem item = new RoleDetailResponseVO.DataPermissionItem();
                    item.setDataId(dataId);
                    item.setOperateTypes(operateTypes);
                    dataPermissions.add(item);

                    log.debug("数据权限 {} 的操作类型: {}", dataId, operateTypes);
                } catch (Exception e) {
                    log.warn("获取数据权限 {} 的操作类型失败: {}", dataId, e.getMessage());

                    RoleDetailResponseVO.DataPermissionItem item = new RoleDetailResponseVO.DataPermissionItem();
                    item.setDataId(dataId);
                    item.setOperateTypes(new ArrayList<>());
                    dataPermissions.add(item);
                }
            }

            // 5. 构建响应对象
            RoleDetailResponseVO response = new RoleDetailResponseVO();
            response.setRoleInfo(roleInfo);
            response.setMenuPermissions(menuPermissions);
            response.setDataPermissions(dataPermissions);

            long endTime = System.currentTimeMillis();
            log.info("角色详情获取完成，角色ID: {}，耗时: {}ms",
                    request.getRoleId(), endTime - startTime);

            return response;

        } catch (Exception e) {
            log.error("获取角色详情失败，角色ID: {}", request.getRoleId(), e);
            throw new RuntimeException("获取角色详情失败: " + e.getMessage(), e);
        }
    }
}
