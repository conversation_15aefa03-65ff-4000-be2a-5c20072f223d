# 角色列表查询接口测试文档

## 接口信息
- **接口路径**: `POST /roles/getRoleList`
- **接口描述**: 分页查询角色列表，支持角色名称模糊搜索、状态筛选、分页查询
- **Content-Type**: `application/json`

## 快速使用指南

### 最常用的请求格式（复制即用）

**1. 基础分页查询（最常用）**
```json
{
    "currentPage": 1,                        // 当前页码（从1开始）
    "pageSize": 10                          // 每页大小
}
```

**2. 按角色名称搜索**
```json
{
    "currentPage": 1,                        // 当前页码
    "pageSize": 10,                         // 每页大小
    "roleName": "管理员"                     // 角色名称模糊搜索
}
```

**3. 按状态筛选**
```json
{
    "currentPage": 1,                        // 当前页码
    "pageSize": 10,                         // 每页大小
    "isDisable": false                      // false-启用，true-停用，null-全部
}
```

**4. 组合条件查询**
```json
{
    "currentPage": 1,                        // 当前页码
    "pageSize": 10,                         // 每页大小
    "roleName": "管理",                      // 角色名称模糊搜索
    "isDisable": false,                     // 只查询启用的角色
    "includeDeleted": false                 // 不包含已删除的角色
}
```

## 请求参数

### RoleListRequestVO
| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| currentPage | Integer | 是 | 当前页码（从1开始） | 1 |
| pageSize | Integer | 是 | 每页大小 | 10 |
| roleName | String | 否 | 角色名称（模糊搜索） | "管理员" |
| isDisable | Boolean | 否 | 角色状态：false-启用，true-停用，null-全部 | false |
| includeDeleted | Boolean | 否 | 是否包含已删除的角色，默认false | false |

## 响应参数

### 响应格式（按前端要求）
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [角色列表数组],
    "total": 总记录数
}
```

### RoleListItemVO（data数组中的每个元素）
| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| id | String | 角色ID | "1930806593885179904" |
| roleName | String | 角色名称 | "系统管理员" |
| orderInfo | Integer | 排序序号 | 1 |
| isDisable | Boolean | 是否停用：false-启用，true-停用 | false |
| statusText | String | 角色状态显示文本 | "启用" |
| createTime | String | 创建时间 | "2025-01-20 15:30:45" |
| modifyTime | String | 修改时间 | "2025-01-20 15:30:45" |
| userCount | Integer | 关联用户数量 | 5 |
| canDelete | Boolean | 是否可删除（有关联用户时不可删除） | false |

## 测试用例

### 测试用例1：基础分页查询
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 10                          // 每页10条
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [
        {
            "id": "1930806593885179904",
            "roleName": "系统管理员",
            "orderInfo": 1,
            "isDisable": false,
            "statusText": "启用",
            "createTime": "2025-01-20 15:30:45",
            "modifyTime": "2025-01-20 15:30:45",
            "userCount": 5,
            "canDelete": false
        },
        {
            "id": "1930806593885179905",
            "roleName": "普通用户",
            "orderInfo": 2,
            "isDisable": false,
            "statusText": "启用",
            "createTime": "2025-01-20 16:00:00",
            "modifyTime": "2025-01-20 16:00:00",
            "userCount": 0,
            "canDelete": true
        }
    ],
    "total": 15
}
```

### 测试用例2：角色名称模糊搜索
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 10,                         // 每页10条
    "roleName": "管理"                       // 搜索包含"管理"的角色
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [
        {
            "id": "1930806593885179904",
            "roleName": "系统管理员",
            "orderInfo": 1,
            "isDisable": false,
            "statusText": "启用",
            "createTime": "2025-01-20 15:30:45",
            "modifyTime": "2025-01-20 15:30:45",
            "userCount": 5,
            "canDelete": false
        },
        {
            "id": "1930806593885179906",
            "roleName": "部门管理员",
            "orderInfo": 3,
            "isDisable": false,
            "statusText": "启用",
            "createTime": "2025-01-20 16:30:00",
            "modifyTime": "2025-01-20 16:30:00",
            "userCount": 2,
            "canDelete": false
        }
    ],
    "total": 2
}
```

### 测试用例3：按状态筛选（只查询启用的角色）
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 10,                         // 每页10条
    "isDisable": false                      // 只查询启用的角色
}
```

**预期响应：** 只返回isDisable=false的角色

### 测试用例4：按状态筛选（只查询停用的角色）
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 10,                         // 每页10条
    "isDisable": true                       // 只查询停用的角色
}
```

**预期响应：** 只返回isDisable=true的角色，statusText为"停用"

### 测试用例5：组合条件查询
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 5,                          // 每页5条
    "roleName": "管理",                      // 角色名称包含"管理"
    "isDisable": false,                     // 只查询启用的角色
    "includeDeleted": false                 // 不包含已删除的角色
}
```

**预期响应：** 返回同时满足所有条件的角色

### 测试用例6：空结果场景
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "currentPage": 1,                        // 第1页
    "pageSize": 10,                         // 每页10条
    "roleName": "不存在的角色名称"             // 搜索不存在的角色
}
```

**预期响应：**
```json
{
    "code": 200,
    "message": "SUCCESS",
    "data": [],
    "total": 0
}
```

## 错误场景测试

### 错误场景1：缺少必填参数
**请求示例：**
```json
POST /roles/getRoleList
Content-Type: application/json

{
    "roleName": "管理员"                     // 缺少currentPage和pageSize
}
```

**预期响应：**
```json
{
    "code": 400,
    "message": "参数验证失败：currentPage和pageSize为必填参数",
    "data": null
}
```

### 错误场景2：系统异常
**预期响应：**
```json
{
    "code": 500,
    "message": "查询角色列表失败：具体错误信息",
    "data": null
}
```

## 接口特性

### 功能特性
1. **分页查询**: 支持标准的分页查询，返回总记录数
2. **模糊搜索**: 支持角色名称的模糊搜索（不区分大小写）
3. **状态筛选**: 支持按启用/停用状态筛选
4. **删除过滤**: 支持是否包含已删除角色的选择
5. **关联统计**: 自动统计每个角色关联的用户数量
6. **删除判断**: 自动判断角色是否可删除（有关联用户时不可删除）

### 技术特性
1. **高性能查询**: 使用LEFT JOIN优化查询性能
2. **精度保护**: Long类型ID使用字符串序列化
3. **时间格式化**: 统一的时间格式输出
4. **状态映射**: 自动将布尔值转换为中文显示文本
5. **异常处理**: 完善的异常捕获和错误信息返回

## 使用场景
1. **角色管理页面**: 展示角色列表，支持分页浏览
2. **角色搜索**: 快速查找特定角色
3. **状态管理**: 查看和管理角色的启用/停用状态
4. **用户分配**: 查看角色的用户关联情况
5. **权限审计**: 审计角色的使用情况和关联关系

## 性能优化
1. **索引建议**: 在role_name、is_disable、is_del字段上建立索引
2. **查询优化**: 使用LEFT JOIN避免N+1查询问题
3. **分页优化**: 使用LIMIT/OFFSET进行数据库层面分页
4. **缓存建议**: 可考虑对角色列表进行适当缓存
