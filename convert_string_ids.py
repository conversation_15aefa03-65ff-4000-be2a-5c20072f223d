#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于将 org_structure_repair.sql 中的字符串 ID 转换为数字 ID
"""

import re
import sys
from typing import Dict, Set, List, Tuple

def extract_string_ids_from_sql(sql_content: str) -> Set[str]:
    """从 SQL 内容中提取所有字符串类型的 ID"""
    string_ids = set()
    
    # 匹配 INSERT 语句中的值
    insert_pattern = r"INSERT INTO.*?VALUES\s*\((.*?)\);"
    matches = re.findall(insert_pattern, sql_content, re.DOTALL | re.IGNORECASE)
    
    for match in matches:
        # 分割值并清理
        values = [v.strip().strip("'\"") for v in match.split(',')]
        
        # 检查第一个值（id）和第三个值（pre_id）
        if len(values) >= 1:
            id_value = values[0].strip().strip("'\"")
            if not is_numeric(id_value):
                string_ids.add(id_value)
        
        if len(values) >= 3:
            pre_id_value = values[2].strip().strip("'\"")
            if not is_numeric(pre_id_value) and pre_id_value not in ['0', 'NULL']:
                string_ids.add(pre_id_value)
    
    return string_ids

def is_numeric(value: str) -> bool:
    """检查字符串是否为数字"""
    if not value or value in ['NULL', '0']:
        return True
    try:
        int(value)
        return True
    except ValueError:
        return False

def create_id_mapping(string_ids: Set[str], start_id: int = 2000000000) -> Dict[str, int]:
    """为字符串 ID 创建数字映射"""
    mapping = {}
    current_id = start_id
    
    # 按字母顺序排序，确保一致性
    sorted_ids = sorted(string_ids)
    
    for string_id in sorted_ids:
        mapping[string_id] = current_id
        current_id += 1
    
    return mapping

def replace_ids_in_sql(sql_content: str, id_mapping: Dict[str, int]) -> str:
    """替换 SQL 中的字符串 ID 为数字 ID"""
    result = sql_content

    # 直接替换所有出现的字符串 ID
    for string_id, numeric_id in id_mapping.items():
        # 替换被单引号包围的字符串 ID
        result = result.replace(f"'{string_id}'", str(numeric_id))
        # 替换被双引号包围的字符串 ID
        result = result.replace(f'"{string_id}"', str(numeric_id))

    return result

def main():
    input_file = "docs/org_structure_repair.sql"
    output_file = "docs/org_structure_repair_converted.sql"
    mapping_file = "docs/id_mapping.txt"
    
    try:
        # 读取原始 SQL 文件
        with open(input_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        print("正在分析 SQL 文件...")
        
        # 提取字符串 ID
        string_ids = extract_string_ids_from_sql(sql_content)
        print(f"发现 {len(string_ids)} 个字符串 ID")
        
        # 创建 ID 映射
        id_mapping = create_id_mapping(string_ids)
        
        # 保存映射关系
        with open(mapping_file, 'w', encoding='utf-8') as f:
            f.write("字符串 ID -> 数字 ID 映射关系:\n")
            f.write("=" * 50 + "\n")
            for string_id, numeric_id in sorted(id_mapping.items()):
                f.write(f"{string_id} -> {numeric_id}\n")
        
        print(f"ID 映射关系已保存到: {mapping_file}")
        
        # 替换 SQL 中的 ID
        print("正在转换 SQL 文件...")
        converted_sql = replace_ids_in_sql(sql_content, id_mapping)
        
        # 保存转换后的 SQL 文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted_sql)
        
        print(f"转换后的 SQL 文件已保存到: {output_file}")
        print("转换完成！")
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
