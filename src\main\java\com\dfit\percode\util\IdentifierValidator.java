package com.dfit.percode.util;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 权限标识符验证工具类
 * 提供权限标识符的格式验证和规则说明
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public class IdentifierValidator {

    // 菜单权限标识符格式：支持1-3段式结构（Sa-Token点号格式）
    // 示例：user, user.list, user.list.view, system.management, permission.role.edit
    private static final Pattern MENU_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9][a-zA-Z0-9.]*(?:\\.[a-zA-Z0-9][a-zA-Z0-9.]*){0,2}$"
    );

    // 数据权限标识符格式：支持1-3段式结构（Sa-Token点号格式）
    // 示例：user, user.data, user.data.view, department.data.edit
    private static final Pattern DATA_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9][a-zA-Z0-9.]*(?:\\.[a-zA-Z0-9][a-zA-Z0-9.]*){0,2}$"
    );

    /**
     * 验证并清理权限标识符
     *
     * @param identifier 原始标识符
     * @return 清理后的标识符
     */
    public static String cleanIdentifier(String identifier) {
        if (identifier == null) {
            return "";
        }
        return identifier.trim();
    }

    /**
     * 检查标识符是否为空
     *
     * @param identifier 标识符
     * @return 是否为空
     */
    public static boolean isEmpty(String identifier) {
        return identifier == null || identifier.trim().isEmpty();
    }

    /**
     * 验证菜单权限标识符格式
     *
     * @param identifier 权限标识符
     * @return 是否符合格式
     */
    public static boolean isValidMenuIdentifier(String identifier) {
        if (isEmpty(identifier)) {
            return false;
        }
        return MENU_PATTERN.matcher(identifier.trim()).matches();
    }

    /**
     * 验证数据权限标识符格式
     *
     * @param identifier 权限标识符
     * @return 是否符合格式
     */
    public static boolean isValidDataIdentifier(String identifier) {
        if (isEmpty(identifier)) {
            return false;
        }
        return DATA_PATTERN.matcher(identifier.trim()).matches();
    }

    /**
     * 根据类型验证标识符格式
     *
     * @param identifier 权限标识符
     * @param type 权限类型（menu 或 data）
     * @return 是否符合格式
     */
    public static boolean isValidFormat(String identifier, String type) {
        if ("menu".equals(type)) {
            return isValidMenuIdentifier(identifier);
        } else if ("data".equals(type)) {
            return isValidDataIdentifier(identifier);
        }
        return false;
    }

    /**
     * 获取菜单权限标识符格式规则
     *
     * @return 格式规则列表
     */
    public static List<String> getMenuFormatRules() {
        return Arrays.asList(
            "格式：支持1-3段式结构（模块、模块.对象、模块.对象.操作）",
            "示例：user, user.list, user.list.view, system.management, permission.role.edit",
            "规则：只能包含字母、数字、点号，符合Sa-Token规范的层级结构",
            "长度：不超过100个字符"
        );
    }

    /**
     * 获取数据权限标识符格式规则
     *
     * @return 格式规则列表
     */
    public static List<String> getDataFormatRules() {
        return Arrays.asList(
            "格式：支持1-3段式结构（模块、模块.对象、模块.对象.操作）",
            "示例：user, user.data, user.data.view, department.data.edit",
            "规则：只能包含字母、数字、点号，符合Sa-Token规范的层级结构",
            "长度：不超过100个字符"
        );
    }

    /**
     * 根据类型获取格式规则
     *
     * @param type 权限类型（menu 或 data）
     * @return 格式规则列表
     */
    public static List<String> getFormatRules(String type) {
        if ("menu".equals(type)) {
            return getMenuFormatRules();
        } else if ("data".equals(type)) {
            return getDataFormatRules();
        }
        return Arrays.asList("未知的权限类型");
    }

    /**
     * 生成建议的标识符
     *
     * @param originalIdentifier 原始标识符
     * @param type 权限类型
     * @return 建议的标识符
     */
    public static String generateSuggestion(String originalIdentifier, String type) {
        String clean = cleanIdentifier(originalIdentifier);
        if (isEmpty(clean)) {
            if ("menu".equals(type)) {
                return "module_object_operation";
            } else {
                return "module_data_operation";
            }
        }

        // 智能建议生成逻辑
        if ("menu".equals(type)) {
            // 菜单权限必须符合 module.object.operation 格式
            String sanitized = clean.replaceAll("[^a-zA-Z0-9.]", ".");

            // 如果已经包含点号，尝试修复格式
            if (clean.contains(".")) {
                String[] parts = clean.split("\\.");
                if (parts.length >= 3) {
                    // 已经是三段式，智能修复每一段
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    String operation = fixSegment(parts[2]);
                    return module + "." + object + "." + operation;
                } else if (parts.length == 2) {
                    // 两段式，添加默认操作
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    return module + "." + object + ".view";
                }
            }

            // 如果包含下划线（旧格式），转换为点号格式
            if (clean.contains("_")) {
                String[] parts = clean.split("_");
                if (parts.length >= 3) {
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    String operation = fixSegment(parts[2]);
                    return module + "." + object + "." + operation;
                } else if (parts.length == 2) {
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    return module + "." + object + ".view";
                }
            }

            // 如果包含冒号（旧格式），转换为新格式
            if (clean.contains(":")) {
                String[] parts = clean.split(":");
                if (parts.length >= 2) {
                    String module = parts[0].replaceAll("[^a-zA-Z0-9.]", "");
                    String action = parts[1].replaceAll("[^a-zA-Z0-9.]", "");
                    return module + "." + action + ".view";
                }
            }

            // 如果是单个词，智能分割为三段
            if (sanitized.length() > 15) {
                // 长标识符：尝试三等分
                int firstPoint = sanitized.length() / 3;
                int secondPoint = sanitized.length() * 2 / 3;
                String module = sanitized.substring(0, firstPoint);
                String object = sanitized.substring(firstPoint, secondPoint);
                String operation = sanitized.substring(secondPoint);
                return module + "." + object + "." + operation;
            } else if (sanitized.length() > 6) {
                // 中等长度：前半部分作为模块，后半部分分为对象和操作
                int midPoint = sanitized.length() / 2;
                String module = sanitized.substring(0, midPoint);
                String remaining = sanitized.substring(midPoint);
                String object = remaining.length() > 3 ? remaining.substring(0, remaining.length()/2) : remaining;
                String operation = remaining.length() > 3 ? remaining.substring(remaining.length()/2) : "view";
                return module + "." + object + "." + operation;
            } else {
                // 短标识符：添加默认对象和操作
                return sanitized + ".item.view";
            }
        } else {
            // 数据权限格式：module.object.operation（与菜单权限统一）
            String sanitized = clean.replaceAll("[^a-zA-Z0-9.]", ".");

            // 如果已经包含点号，尝试修复格式
            if (clean.contains(".")) {
                String[] parts = clean.split("\\.");
                if (parts.length >= 3) {
                    // 已经是三段式，智能修复每一段
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    String operation = fixSegment(parts[2]);
                    return module + "." + object + "." + operation;
                } else if (parts.length == 2) {
                    // 两段式，添加默认操作
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    return module + "." + object + ".view";
                }
            }

            // 如果包含下划线（旧格式），转换为点号格式
            if (clean.contains("_")) {
                String[] parts = clean.split("_");
                if (parts.length >= 3) {
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    String operation = fixSegment(parts[2]);
                    return module + "." + object + "." + operation;
                } else if (parts.length == 2) {
                    String module = fixSegment(parts[0]);
                    String object = fixSegment(parts[1]);
                    return module + "." + object + ".view";
                }
            }

            // 如果是单个词，智能分割为三段
            if (sanitized.length() > 15) {
                // 长标识符：尝试三等分
                int firstPoint = sanitized.length() / 3;
                int secondPoint = sanitized.length() * 2 / 3;
                String module = sanitized.substring(0, firstPoint);
                String object = sanitized.substring(firstPoint, secondPoint);
                String operation = sanitized.substring(secondPoint);
                return module + "." + object + "." + operation;
            } else if (sanitized.length() > 6) {
                // 中等长度：前半部分作为模块，后半部分分为对象和操作
                int midPoint = sanitized.length() / 2;
                String module = sanitized.substring(0, midPoint);
                String remaining = sanitized.substring(midPoint);
                String object = remaining.length() > 3 ? remaining.substring(0, remaining.length()/2) : remaining;
                String operation = remaining.length() > 3 ? remaining.substring(remaining.length()/2) : "view";
                return module + "." + object + "." + operation;
            } else {
                // 短标识符：添加默认对象和操作
                return sanitized + ".data.view";
            }
        }
    }

    /**
     * 修复单个段落，确保符合格式要求
     * 只清理特殊字符，保持原有的数字和字母
     *
     * @param segment 原始段落
     * @return 修复后的段落
     */
    private static String fixSegment(String segment) {
        if (segment == null || segment.trim().isEmpty()) {
            return "item";
        }

        // 清理特殊字符，只保留字母、数字（点号在外层处理）
        String cleaned = segment.replaceAll("[^a-zA-Z0-9]", "");

        // 如果为空，返回默认值
        if (cleaned.isEmpty()) {
            return "item";
        }

        // 直接返回清理后的结果，不再强制要求字母开头
        return cleaned;
    }
}
