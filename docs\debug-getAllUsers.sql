-- =====================================================
-- 调试 /users/getAllUsers 接口返回空数据问题
-- =====================================================

-- 1. 检查用户表数据
SELECT
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_del = false THEN 1 END) as active_users,
    COUNT(CASE WHEN is_del = true THEN 1 END) as deleted_users
FROM t_user;

-- 查看用户表前10条记录
SELECT
    id,
    user_name,
    organ_affiliation,
    is_del,
    create_time
FROM t_user
WHERE is_del = false
ORDER BY id
LIMIT 10;

-- =====================================================
-- 2. 检查组织架构表数据
-- =====================================================

SELECT
    COUNT(*) as total_orgs,
    COUNT(CASE WHEN is_del = false THEN 1 END) as active_orgs,
    COUNT(CASE WHEN is_del = true THEN 1 END) as deleted_orgs
FROM t_org_structure;

-- 查看组织架构表前10条记录
SELECT
    id,
    organ_name,
    pre_id,
    is_del,
    create_time
FROM t_org_structure
WHERE is_del = false
ORDER BY id
LIMIT 10;

-- =====================================================
-- 3. 检查根部门（最关键）
-- =====================================================

-- 查找根部门（pre_id = 0 或 NULL）
SELECT
    id,
    organ_name,
    pre_id,
    order_info
FROM t_org_structure
WHERE (pre_id = 0 OR pre_id IS NULL)
  AND is_del = false
ORDER BY order_info;

-- 如果上面查询为空，查看所有部门的pre_id分布
SELECT
    pre_id,
    COUNT(*) as count
FROM t_org_structure
WHERE is_del = false
GROUP BY pre_id
ORDER BY pre_id;

-- =====================================================
-- 4. 检查用户和部门的关联关系
-- =====================================================

-- 查看用户和部门的关联情况
SELECT
    u.id as user_id,
    u.user_name,
    u.organ_affiliation,
    o.organ_name,
    o.pre_id
FROM t_user u
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
WHERE u.is_del = false
ORDER BY u.organ_affiliation, u.id
LIMIT 20;

-- 统计每个部门的用户数量
SELECT
    o.id as org_id,
    o.organ_name,
    o.pre_id,
    COUNT(u.id) as user_count
FROM t_org_structure o
LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false
WHERE o.is_del = false
GROUP BY o.id, o.organ_name, o.pre_id
ORDER BY o.id;

-- =====================================================
-- 5. 测试优化后的查询SQL
-- =====================================================

-- 这是新的优化查询，应该返回所有部门和用户数据
SELECT
    o.id AS orgId,
    o.organ_name AS orgName,
    o.pre_id AS parentId,
    o.order_info AS orgOrder,
    u.id AS userId,
    u.user_name AS userName
FROM t_org_structure o
LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false
WHERE o.is_del = false
ORDER BY o.order_info, u.user_name
LIMIT 50;

-- =====================================================
-- 6. 如果数据为空，插入测试数据
-- =====================================================

-- 检查是否需要插入测试数据
SELECT
    (SELECT COUNT(*) FROM t_org_structure WHERE is_del = false) as org_count,
    (SELECT COUNT(*) FROM t_user WHERE is_del = false) as user_count;

-- 如果上面的查询返回都是0，执行下面的插入语句：

/*
-- 插入测试组织架构数据
INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, org_code, org_level, org_path) VALUES
(5001, '科技集团总公司', 0, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'GROUP_HQ', 1, '/GROUP_HQ'),
(5002, '技术研发中心', 5001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'TECH_CENTER', 2, '/GROUP_HQ/TECH_CENTER'),
(5003, '市场营销部', 5001, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'MARKETING', 2, '/GROUP_HQ/MARKETING'),
(5004, '人力资源部', 5001, 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'HR', 2, '/GROUP_HQ/HR'),
(5005, '财务部', 5001, 4, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'FINANCE', 2, '/GROUP_HQ/FINANCE');

-- 插入测试用户数据
INSERT INTO t_user (id, user_name, origin_id, organ_affiliation, account, password, is_disable, is_del, create_time, modify_time, employee_code, gender, mobile, email) VALUES
(6001, '张总经理', 'zhang_ceo', 5001, 'zhang.ceo', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP001', '男', '***********', '<EMAIL>'),
(6002, '李技术总监', 'li_cto', 5002, 'li.cto', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP002', '男', '***********', '<EMAIL>'),
(6003, '王研发经理', 'wang_dev', 5002, 'wang.dev', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP003', '女', '***********', '<EMAIL>'),
(6004, '赵市场经理', 'zhao_mkt', 5003, 'zhao.mkt', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP004', '男', '***********', '<EMAIL>'),
(6005, '刘HR经理', 'liu_hr', 5004, 'liu.hr', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP005', '女', '***********', '<EMAIL>'),
(6006, '陈财务经理', 'chen_fin', 5005, 'chen.fin', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'EMP006', '男', '13800138006', '<EMAIL>');
*/

-- =====================================================
-- 7. 验证数据插入结果
-- =====================================================

-- 再次检查数据
SELECT
    o.id AS orgId,
    o.organ_name AS orgName,
    o.pre_id AS parentId,
    COUNT(u.id) as user_count
FROM t_org_structure o
LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false
WHERE o.is_del = false
GROUP BY o.id, o.organ_name, o.pre_id
ORDER BY o.id;

-- 检查根部门
SELECT
    id,
    organ_name,
    pre_id
FROM t_org_structure
WHERE (pre_id = 0 OR pre_id IS NULL)
  AND is_del = false;

-- =====================================================
-- 使用说明
-- =====================================================

/*
执行步骤：
1. 先执行前5个查询，检查数据情况
2. 如果数据为空，执行插入测试数据的SQL
3. 重新测试 /users/getAllUsers 接口
4. 查看应用日志，确认是否有异常

常见问题：
1. 如果根部门查询为空，说明pre_id字段值不对
2. 如果用户关联查询为空，说明organ_affiliation字段值不对
3. 如果优化查询返回数据但接口还是空，说明代码逻辑有问题

调试建议：
1. 查看应用日志中的SQL执行情况
2. 在Service方法中添加更多日志输出
3. 检查数据类型转换是否正确（Long vs Integer）
*/
