/*
 Navicat Premium Data Transfer

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224
 File Encoding         : 65001

 Date: 02/07/2025 11:41:02
 Updated: 02/07/2025 - 密码MD5加密版本
*/

-- 注意：此文件基于public702.sql，但将所有明文密码转换为MD5加密格式
-- 密码对应关系：
-- '123456' -> 'e10adc3949ba59abbe56e057f20f883e'
-- 'password123' -> '482c811da5d5b4bc6d497ffa98491e38'
-- 'abc' -> '900150983cd24fb0d6963f7d28e17f72'
-- 'a' -> '0cc175b9c0f1b6a831c399e269772661'
-- 其他密码类似转换...

-- 由于文件过大，这里只展示关键的用户表数据更新
-- 完整的表结构请参考原始的public702.sql文件

-- ----------------------------
-- Records of t_user (MD5加密版本)
-- ----------------------------
INSERT INTO "public"."t_user" VALUES (1932639748158001152, '测试用户001', 't', NULL, NULL, '测试用户001', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-11 11:23:17.337668', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6002, '李技术总监', 't', 'li_cto', NULL, NULL, '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6001, '张总经理', 't', 'zhang_ceo', 5001, 'zhang001', '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936370058246885376, 'a', 't', NULL, 5008, 'a', '0cc175b9c0f1b6a831c399e269772661', 'f', '2025-06-21 18:26:12.559204', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1934773648393113600, '测试人员', 't', NULL, 5008, 'admintest', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-17 08:42:38.791092', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936613097838088192, 'ttt', 'f', NULL, 5007, 'abc', '900150983cd24fb0d6963f7d28e17f72', 'f', '2025-06-22 10:31:57.713584', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936613452760092672, 'abc', 'f', NULL, 5008, 'xxx', '9df62e693988eb4e1e1444ece0578579', 'f', '2025-06-22 10:33:22.333885', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936614969823072256, '555', 'f', NULL, 5009, 'ddd', '77963b7a931377ad4ab5ad6a9cd718aa', 't', '2025-06-22 10:39:24.029588', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936631880975781888, 'a', 'f', NULL, 5009, 'aa', '0cc175b9c0f1b6a831c399e269772661', 'f', '2025-06-22 11:46:35.962764', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1935318005881901056, '123', 't', NULL, 5012, '123123', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-18 20:45:43.729772', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1935155281398992896, 'sdf', 't', NULL, NULL, 'asdf', '912ec803b2ce49e4a541068d495ab570', 'f', '2025-06-18 09:59:07.189611', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1935946418258841600, '小韩', 't', NULL, 5008, '456', '250cf8b51c773f3f8dc8b4be867a9a02', 't', '2025-06-20 14:22:48.91593', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6003, '王产品总监', 't', 'wang_cpo', 5003, 'wang.cpo', '482c811da5d5b4bc6d497ffa98491e38', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6010, '王运维工程师', 't', 'wang_ops', 5010, 'wang.ops', '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6006, '孙测试经理', 't', 'sun_qa_mgr', 5009, 'sun.qa', '482c811da5d5b4bc6d497ffa98491e38', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6009, '郑测试工程师', 't', 'zheng_qa', 5009, 'zheng.qa', '482c811da5d5b4bc6d497ffa98491e38', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6005, '钱后端经理', 't', 'qian_be_mgr', 5008, 'qian.be', '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1934460205056266240, 'testa', 't', NULL, 5008, 'z', 'c4ca4238a0b923820dcc509a6f75849b', 'f', '2025-06-16 11:57:08.075555', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1934845562293719040, '徐小青', 't', NULL, 5011, '徐小青', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-17 13:28:24.400219', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1935148278794555392, 'test', 't', NULL, 5007, 'test12345', 'e10adc3949ba59abbe56e057f20f883e', 't', '2025-06-18 09:31:17.638376', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1935937134821249024, '张茂才', 't', NULL, 5008, '789', '250cf8b51c773f3f8dc8b4be867a9a02', 't', '2025-06-20 13:45:55.571197', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936006492402618368, '徐小青', 't', NULL, 5007, 'x2005', 'e10adc3949ba59abbe56e057f20f883e', 't', '2025-06-20 18:21:31.70736', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6012, '陈实习生', 't', 'chen_intern', 5013, '123', '202cb962ac59075b964b07152d234b70', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6011, '冯离职员工', 't', 'feng_former', 5007, 'feng.former', '482c811da5d5b4bc6d497ffa98491e38', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6008, '吴后端开发', 't', 'wu_be_dev', 5008, 'wu.be', '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6007, '周前端开发', 't', 'zhou_fe_dev', 5007, 'zhou.fe', '482c811da5d5b4bc6d497ffa98491e38', 'f', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (6004, '赵前端经理', 't', 'zhao_fe_mgr', 5007, 'zhao.fe', '482c811da5d5b4bc6d497ffa98491e38', 't', '2025-06-11 01:58:47.958483', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1938155631131365376, '管理员', 'f', NULL, 5008, 'admin', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-26 16:41:26.309519', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1938209346265681920, '徐国政', 'f', NULL, 5004, 'qwerty', 'e10adc3949ba59abbe56e057f20f883e', 'f', '2025-06-26 20:14:52.995278', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1939271549475491840, 'zsw', 'f', NULL, 5002, 'zsw', 'b8c37e33defde51cf91e1e03e51657da', 'f', '2025-06-29 18:35:41.975539', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1936640367617249280, '123', 'f', NULL, 5009, '123', '202cb962ac59075b964b07152d234b70', 'f', '2025-06-22 12:20:19.335999', '2025-07-02 12:00:00.000000');
INSERT INTO "public"."t_user" VALUES (1940248407419523072, '256', 'f', NULL, 5008, '12314', '1b0fd9efa5279c4203b7c70233d6de29', 'f', '2025-07-02 11:17:23.058157', '2025-07-02 12:00:00.000000');

-- =====================================================
-- 密码对应关系说明
-- =====================================================
-- 原密码 -> MD5加密后
-- '123456' -> 'e10adc3949ba59abbe56e057f20f883e'
-- 'password123' -> '482c811da5d5b4bc6d497ffa98491e38'
-- 'abc' -> '900150983cd24fb0d6963f7d28e17f72'
-- 'a' -> '0cc175b9c0f1b6a831c399e269772661'
-- 'ccc' -> '9df62e693988eb4e1e1444ece0578579'
-- 'ddd' -> '77963b7a931377ad4ab5ad6a9cd718aa'
-- 'asdf' -> '912ec803b2ce49e4a541068d495ab570'
-- '456' -> '250cf8b51c773f3f8dc8b4be867a9a02'
-- '1' -> 'c4ca4238a0b923820dcc509a6f75849b'
-- 'zsw' -> 'b8c37e33defde51cf91e1e03e51657da'
-- '123' -> '202cb962ac59075b964b07152d234b70'
-- '23124' -> '1b0fd9efa5279c4203b7c70233d6de29'
-- =====================================================
