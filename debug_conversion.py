#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试转换脚本
"""

# 测试简单的替换
test_line = "INSERT INTO \"public\".\"t_org_structure\" (id, organ_name, pre_id, is_del, data_source, create_time, modify_time) VALUES ('X17000000', '工会', 'XC2000000', 'f', 2, NOW(), NOW());"

print("原始行:")
print(test_line)
print()

# 测试替换
mapping = {
    'X17000000': 2000001000,
    'XC2000000': 2000001001
}

result = test_line
for string_id, numeric_id in mapping.items():
    old_pattern = f"'{string_id}'"
    new_value = str(numeric_id)
    print(f"替换 {old_pattern} -> {new_value}")
    result = result.replace(old_pattern, new_value)

print()
print("替换后:")
print(result)
