package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 模块信息VO
 * 用于菜单树构建时的模块信息
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "ModuleInfoVO对象", description = "模块信息")
public class ModuleInfoVO {

    @ApiModelProperty("模块标识符")
    private String moduleIdentifier;

    @ApiModelProperty("模块名称")
    private String moduleName;

    @ApiModelProperty("排序序号")
    private Integer orderInfo;
}
