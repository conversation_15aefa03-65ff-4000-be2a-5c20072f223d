package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 更新用户角色分配请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 用于角色授权和取消授权操作
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UpdateUserRoleRequestVO", description = "更新用户角色分配请求参数")
public class UpdateUserRoleRequestVO {
    
    @ApiModelProperty(value = "用户ID", required = true, example = "3001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;
    
    @ApiModelProperty(value = "角色ID", required = true, example = "2001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;
    
    @ApiModelProperty(value = "是否删除关联：false-授权，true-取消授权", required = true, example = "false")
    private Boolean isDel;
}
