-- =====================================================
-- 权限管理系统性能优化索引
-- 解决 /users/getAllUsers 接口性能问题
-- =====================================================

-- 检查当前索引情况
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('t_user', 't_org_structure')
ORDER BY tablename, indexname;

-- =====================================================
-- 1. 用户表相关索引
-- =====================================================

-- 为用户表的部门归属字段添加索引（最重要）
-- 这个索引可以大幅提升按部门查询用户的性能
CREATE INDEX IF NOT EXISTS idx_user_organ_affiliation 
ON t_user(organ_affiliation) 
WHERE is_del = false;

-- 为用户表的删除标记和部门归属添加复合索引
-- 支持同时按删除状态和部门查询
CREATE INDEX IF NOT EXISTS idx_user_is_del_organ 
ON t_user(is_del, organ_affiliation);

-- 为用户表的用户名添加索引（支持模糊查询）
CREATE INDEX IF NOT EXISTS idx_user_name 
ON t_user(user_name) 
WHERE is_del = false;

-- 为用户表的账号添加唯一索引（支持登录查询）
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_account_unique 
ON t_user(account) 
WHERE is_del = false;

-- 为用户表的员工编号添加索引
CREATE INDEX IF NOT EXISTS idx_user_employee_code 
ON t_user(employee_code) 
WHERE is_del = false;

-- =====================================================
-- 2. 组织架构表相关索引
-- =====================================================

-- 为组织架构表的父级ID添加索引（最重要）
-- 这个索引可以大幅提升构建部门树的性能
CREATE INDEX IF NOT EXISTS idx_org_structure_pre_id 
ON t_org_structure(pre_id) 
WHERE is_del = false;

-- 为组织架构表的删除标记和父级ID添加复合索引
CREATE INDEX IF NOT EXISTS idx_org_structure_is_del_pre 
ON t_org_structure(is_del, pre_id);

-- 为组织架构表的排序字段添加索引
CREATE INDEX IF NOT EXISTS idx_org_structure_order 
ON t_org_structure(order_info) 
WHERE is_del = false;

-- 为组织架构表的组织编码添加索引
CREATE INDEX IF NOT EXISTS idx_org_structure_code 
ON t_org_structure(org_code) 
WHERE is_del = false;

-- =====================================================
-- 3. 角色权限相关索引
-- =====================================================

-- 为用户角色关联表添加索引
CREATE INDEX IF NOT EXISTS idx_perm_user_role_user_id 
ON t_perm_user_role(user_id) 
WHERE is_del = false;

CREATE INDEX IF NOT EXISTS idx_perm_user_role_role_id 
ON t_perm_user_role(role_id) 
WHERE is_del = false;

-- 为用户角色关联表添加复合索引
CREATE INDEX IF NOT EXISTS idx_perm_user_role_user_role 
ON t_perm_user_role(user_id, role_id) 
WHERE is_del = false;

-- 为角色表添加索引
CREATE INDEX IF NOT EXISTS idx_role_name 
ON t_role(role_name) 
WHERE is_del = false;

-- =====================================================
-- 4. 菜单权限相关索引
-- =====================================================

-- 为角色菜单权限关联表添加索引
CREATE INDEX IF NOT EXISTS idx_roles_menu_permission_role_id 
ON t_roles_menu_permission(role_id) 
WHERE is_del = false;

CREATE INDEX IF NOT EXISTS idx_roles_menu_permission_menu_id 
ON t_roles_menu_permission(menu_id) 
WHERE is_del = false;

-- 为菜单权限表添加索引
CREATE INDEX IF NOT EXISTS idx_menu_permission_module 
ON t_menu_permission(module_identifier) 
WHERE is_del = false;

CREATE INDEX IF NOT EXISTS idx_menu_permission_pre_id 
ON t_menu_permission(pre_id) 
WHERE is_del = false;

-- =====================================================
-- 5. 数据权限相关索引
-- =====================================================

-- 为角色数据权限关联表添加索引
CREATE INDEX IF NOT EXISTS idx_roles_data_permission_role_id 
ON t_roles_data_permission(role_id) 
WHERE is_del = false;

CREATE INDEX IF NOT EXISTS idx_roles_data_permission_data_id 
ON t_roles_data_permission(data_id) 
WHERE is_del = false;

-- 为数据权限表添加索引
CREATE INDEX IF NOT EXISTS idx_data_permission_module 
ON t_data_permission(module_identifier) 
WHERE is_del = false;

-- 为数据操作权限表添加索引
CREATE INDEX IF NOT EXISTS idx_data_operate_data_module 
ON t_data_operate(data_identifier, module_identifier) 
WHERE is_del = false;

-- =====================================================
-- 6. 数据同步相关索引（如果有同步表）
-- =====================================================

-- 为员工扩展表添加索引（如果存在）
-- CREATE INDEX IF NOT EXISTS idx_employee_position_user_id 
-- ON t_employee_position(user_id);

-- CREATE INDEX IF NOT EXISTS idx_employee_title_user_id 
-- ON t_employee_title(user_id);

-- CREATE INDEX IF NOT EXISTS idx_employee_system_user_id 
-- ON t_employee_system(user_id);

-- =====================================================
-- 7. 验证索引创建结果
-- =====================================================

-- 查看新创建的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename IN ('t_user', 't_org_structure', 't_perm_user_role', 't_role')
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- 查看表的大小和索引大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexes_size
FROM pg_tables 
WHERE tablename IN ('t_user', 't_org_structure', 't_perm_user_role', 't_role')
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =====================================================
-- 使用说明
-- =====================================================

/*
执行顺序：
1. 先执行索引创建语句
2. 重启应用或等待几分钟让索引生效
3. 测试 /users/getAllUsers 接口性能
4. 如果还是慢，可以考虑使用优化后的查询方法

预期效果：
- /users/getAllUsers 接口从50+秒降低到几秒内
- 其他用户查询接口性能也会显著提升
- 角色权限查询性能提升

注意事项：
1. 索引创建可能需要一些时间，特别是数据量大的表
2. 索引会占用额外的存储空间
3. 索引会略微影响插入/更新性能，但查询性能提升更明显
4. 建议在业务低峰期执行索引创建
*/
