package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 配置数据权限操作权限请求VO类（数据级别）
 * 支持为多个数据权限配置不同的操作权限
 *
 * <AUTHOR>
 * @since 2025-06-21
 */
@Data
@ApiModel(value = "ConfigureDataOperateListRequestVO", description = "配置数据权限操作权限请求参数（数据级别）")
public class ConfigureDataOperateListRequestVO {

    @ApiModelProperty(value = "模块标识", required = true, example = "knowledge")
    private String moduleIdentifier;

    @ApiModelProperty(value = "数据权限操作配置列表", required = true)
    private List<DataOperateConfigItem> dataOperateConfigs;

    /**
     * 数据权限操作配置项
     */
    @Data
    @ApiModel(value = "DataOperateConfigItem", description = "数据权限操作配置项")
    public static class DataOperateConfigItem {
        
        @ApiModelProperty(value = "数据权限标识", required = true, example = "article")
        private String dataIdentifier;
        
        @ApiModelProperty(value = "操作类型列表", required = true, example = "[1, 2, 3]",
                         notes = "1-新增，2-修改，3-删除，4-查看")
        private List<Integer> operateTypes;
    }
}
