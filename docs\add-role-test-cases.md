# 新增角色接口测试用例

## 📋 接口信息
- **接口路径**: `POST /roles/addRole`
- **接口描述**: 新增角色，支持角色基本信息、菜单权限、数据权限的设置
- **Content-Type**: `application/json`

## 🧪 测试用例

### 测试用例1：基础角色新增
**测试目的**: 验证基本的角色新增功能

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "测试角色001",
    "orderInfo": 10,
    "isDisable": false
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179907",
    "roleName": "测试角色001",
    "orderInfo": 10,
    "isDisable": false,
    "createTime": "2025-01-20 16:30:45",
    "menuPermissionCount": 0,
    "dataPermissionCount": 0
  }
}
```

**验证点**:
- ✅ 返回状态码为200
- ✅ 返回新创建角色的完整信息
- ✅ 角色ID为雪花算法生成的长整型
- ✅ 创建时间格式正确
- ✅ 权限数量统计正确

---

### 测试用例2：带菜单权限的角色新增
**测试目的**: 验证角色新增时同时分配菜单权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "菜单管理员",
    "orderInfo": 5,
    "isDisable": false,
    "menuName": [
      {
        "id": 6004,
        "name": "用户列表",
        "moduleIdentifier": "user"
      },
      {
        "id": 6008,
        "name": "角色管理",
        "moduleIdentifier": "permission"
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179908",
    "roleName": "菜单管理员",
    "orderInfo": 5,
    "isDisable": false,
    "createTime": "2025-01-20 16:35:00",
    "menuPermissionCount": 2,
    "dataPermissionCount": 0
  }
}
```

**验证点**:
- ✅ 角色创建成功
- ✅ 菜单权限关联创建成功
- ✅ menuPermissionCount正确统计为2

---

### 测试用例3：带数据权限的角色新增
**测试目的**: 验证角色新增时同时分配数据权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "数据管理员",
    "orderInfo": 8,
    "isDisable": false,
    "dataPermission": [
      {
        "id": 9001,
        "name": "用户信息查看",
        "moduleIdentifier": "user_data",
        "dataType": 1,
        "operateTypes": [1, 3]
      },
      {
        "id": 9004,
        "name": "部门信息查看",
        "moduleIdentifier": "dept_data",
        "dataType": 2,
        "operateTypes": [1]
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179909",
    "roleName": "数据管理员",
    "orderInfo": 8,
    "isDisable": false,
    "createTime": "2025-01-20 16:40:00",
    "menuPermissionCount": 0,
    "dataPermissionCount": 3
  }
}
```

**验证点**:
- ✅ 角色创建成功
- ✅ 数据权限关联创建成功
- ✅ dataPermissionCount正确统计为3（2个操作类型+1个操作类型）

---

### 测试用例4：完整权限角色新增
**测试目的**: 验证角色新增时同时分配菜单权限和数据权限

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "完整权限角色",
    "orderInfo": 3,
    "isDisable": false,
    "menuName": [
      {
        "id": 6004,
        "name": "用户列表",
        "moduleIdentifier": "user"
      },
      {
        "id": 6008,
        "name": "角色管理",
        "moduleIdentifier": "permission"
      }
    ],
    "dataPermission": [
      {
        "id": 9001,
        "name": "用户信息查看",
        "moduleIdentifier": "user_data",
        "dataType": 1,
        "operateTypes": [1, 2, 3]
      }
    ]
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179910",
    "roleName": "完整权限角色",
    "orderInfo": 3,
    "isDisable": false,
    "createTime": "2025-01-20 16:45:00",
    "menuPermissionCount": 2,
    "dataPermissionCount": 3
  }
}
```

**验证点**:
- ✅ 角色创建成功
- ✅ 菜单权限和数据权限都创建成功
- ✅ 权限数量统计正确

---

### 测试用例5：停用状态角色新增
**测试目的**: 验证创建停用状态的角色

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "停用角色",
    "orderInfo": 999,
    "isDisable": true
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179911",
    "roleName": "停用角色",
    "orderInfo": 999,
    "isDisable": true,
    "createTime": "2025-01-20 16:50:00",
    "menuPermissionCount": 0,
    "dataPermissionCount": 0
  }
}
```

**验证点**:
- ✅ 停用状态角色创建成功
- ✅ isDisable字段正确设置为true

---

### 测试用例6：最小参数角色新增
**测试目的**: 验证只提供必填参数时的角色新增

**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "最小参数角色"
  }'
```

**预期响应**:
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": {
    "id": "1930806593885179912",
    "roleName": "最小参数角色",
    "orderInfo": 999,
    "isDisable": false,
    "createTime": "2025-01-20 16:55:00",
    "menuPermissionCount": 0,
    "dataPermissionCount": 0
  }
}
```

**验证点**:
- ✅ 只提供角色名称也能成功创建
- ✅ 默认值设置正确（orderInfo=999, isDisable=false）

---

## ❌ 错误场景测试

### 错误场景1：角色名称为空
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "",
    "orderInfo": 10
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色名称不能为空",
  "data": null
}
```

### 错误场景2：角色名称过长
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "这是一个非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常非常长的角色名称",
    "orderInfo": 10
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色名称长度不能超过50个字符",
  "data": null
}
```

### 错误场景3：角色名称重复
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "超级管理员",
    "orderInfo": 10
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色名称已存在：超级管理员",
  "data": null
}
```

### 错误场景4：缺少必填参数
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "orderInfo": 10,
    "isDisable": false
  }'
```

**预期响应**:
```json
{
  "code": 400,
  "message": "参数错误：角色名称不能为空",
  "data": null
}
```

### 错误场景5：无效的JSON格式
**请求示例**:
```bash
curl -X POST http://localhost:8080/roles/addRole \
  -H "Content-Type: application/json" \
  -d '{
    "roleName": "测试角色",
    "orderInfo": 10,
  }'
```

**预期响应**:
```json
{
  "code": 500,
  "message": "新增角色失败：JSON格式错误",
  "data": null
}
```

## 🔧 数据库验证

### 验证角色基本信息
```sql
-- 查询新增的角色
SELECT * FROM t_role WHERE role_name = '测试角色001';
```

### 验证菜单权限关联
```sql
-- 查询角色的菜单权限
SELECT rmp.*, mp.name as menu_name 
FROM t_roles_menu_permission rmp
LEFT JOIN t_menu_permission mp ON rmp.menu_id = mp.id
WHERE rmp.role_id = '新增角色的ID' AND rmp.is_del = 0;
```

### 验证数据权限关联
```sql
-- 查询角色的数据权限
SELECT rdp.*, dp.name as permission_name 
FROM t_roles_data_permission rdp
LEFT JOIN t_data_permission dp ON rdp.data_id = dp.id
WHERE rdp.role_id = '新增角色的ID' AND rdp.is_del = false;
```

## 📊 性能测试

### 并发新增测试
```bash
# 使用ab工具进行并发测试
ab -n 50 -c 5 -p add_role_request.json -T application/json http://localhost:8080/roles/addRole
```

### 大批量权限测试
创建包含大量菜单权限和数据权限的角色，测试性能表现。

## ✅ 验收标准

1. **功能完整性**: 所有测试用例都能正常执行
2. **数据一致性**: 数据库中的数据与返回结果一致
3. **事务完整性**: 失败时能正确回滚，不产生脏数据
4. **参数验证**: 能正确验证和处理各种参数错误
5. **性能要求**: 响应时间 < 1000ms
6. **并发安全**: 支持并发创建不同角色
