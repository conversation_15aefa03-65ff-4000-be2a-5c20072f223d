package com.dfit.percode.mapper;


import com.dfit.percode.entity.TUser;
import com.dfit.percode.vo.AddMemberRoleEntityVO;
import com.dfit.percode.vo.DeleteUserRequestVO;
import com.dfit.percode.vo.DepartmentTreeVO;
import com.dfit.percode.vo.RoleInfoVO;
import com.dfit.percode.vo.UpdateUserRequestVO;
import com.dfit.percode.vo.UserDetailResponseVO;
import com.dfit.percode.vo.UserListItemVO;
import com.dfit.percode.vo.UserListRequestVO;
import com.dfit.percode.vo.UserListByOrgRequestVO;
import com.dfit.percode.vo.UserListByOrgItemVO;
import com.dfit.percode.vo.DepartmentUsersRequestVO;
import com.dfit.percode.vo.RoleUserListRequestVO;
import com.dfit.percode.vo.RoleUserListItemVO;
import com.dfit.percode.vo.UnauthorizedUsersRequestVO;
import com.dfit.percode.vo.UnauthorizedUserItemVO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * 用户相关Mapper接口
 * 按照前端实际使用的格式重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface UserMapper {

    /**
     * 查询所有根部门（父ID为NULL的根节点）
     *
     * @return 根部门列表
     */
    @Select("SELECT id AS id, organ_name AS organName " +
            "FROM t_org_structure " +
            "WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false " +
            "ORDER BY order_info")
    List<DepartmentTreeVO> findAllRootDepartments();

    /**
     * 根据父部门ID查询子部门
     *
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    @Select("SELECT id AS id, organ_name AS organName " +
            "FROM t_org_structure " +
            "WHERE pre_id = #{parentId} AND is_del = false " +
            "ORDER BY order_info")
    List<DepartmentTreeVO> findChildDepartments(Long parentId);

    /**
     * 根据部门ID查询部门下的用户
     * 使用新的t_user表，organ_affiliation已改为bigint类型
     *
     * @param departmentId 部门ID
     * @return 用户列表（使用DepartmentTreeVO格式）
     */
    @Select("SELECT id AS id, user_name AS userName " +
            "FROM t_user " +
            "WHERE organ_affiliation = #{departmentId} AND is_del = false " +
            "ORDER BY user_name")
    List<DepartmentTreeVO> findUsersByDepartmentId(Long departmentId);

    /**
     * 一次性获取所有部门和用户数据（优化版本）
     * 避免N+1查询问题，提高性能
     *
     * @return 部门和用户的完整数据
     */
    @Select("SELECT " +
            "o.id AS orgId, " +
            "o.organ_name AS orgName, " +
            "o.pre_id AS parentId, " +
            "o.order_info AS orgOrder, " +
            "u.id AS userId, " +
            "u.user_name AS userName " +
            "FROM t_org_structure o " +
            "LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false " +
            "WHERE o.is_del = false " +
            "ORDER BY o.order_info, u.user_name")
    List<Map<String, Object>> findAllDepartmentsAndUsers();

    /**
     * 使用 RECURSIVE CTE 优化查询所有部门和用户数据
     * 一次性获取完整的部门树结构和用户信息，避免 N+1 查询问题
     *
     * @return 部门和用户的完整数据，包含层级信息
     */
    @Select("WITH RECURSIVE dept_tree AS ( " +
            "SELECT id, organ_name, pre_id, order_info, 0 as level, " +
            "CAST(order_info AS VARCHAR) as sort_path " +
            "FROM t_org_structure " +
            "WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false " +
            "UNION ALL " +
            "SELECT o.id, o.organ_name, o.pre_id, o.order_info, dt.level + 1, " +
            "dt.sort_path || ',' || CAST(o.order_info AS VARCHAR) " +
            "FROM t_org_structure o " +
            "JOIN dept_tree dt ON o.pre_id = dt.id " +
            "WHERE o.is_del = false " +
            ") " +
            "SELECT d.id as deptId, d.organ_name as deptName, d.pre_id as parentId, " +
            "d.level, d.sort_path, u.id as userId, u.user_name as userName " +
            "FROM dept_tree d " +
            "LEFT JOIN t_user u ON d.id = u.organ_affiliation AND u.is_del = false " +
            "ORDER BY d.level, d.sort_path, u.user_name")
    List<Map<String, Object>> findAllDepartmentsAndUsersOptimized();

    /**
     * 插入用户基本信息
     * 根据新的表结构，只包含核心字段
     *
     * @param user 用户信息
     */
    @Insert("INSERT INTO t_user (" +
            "id, user_name, is_del, origin_id, organ_affiliation, account, password, is_disable, " +
            "create_time, modify_time" +
            ") VALUES (" +
            "#{id}, #{userName}, #{isDel}, #{originId}, #{organAffiliation}, #{account}, #{password}, #{isDisable}, " +
            "#{createTime}, #{modifyTime}" +
            ")")
    void insert(TUser user);

    /**
     * 插入用户角色关联
     * 基于新的表结构，只需要建立用户和角色的关联关系
     * 使用数据库函数处理时间，避免类型转换问题
     *
     * @param memberRole 成员角色关联信息
     */
    @Insert("INSERT INTO t_perm_user_role (id, user_id, role_id, order_info, is_del, create_time, modify_time) " +
            "VALUES (#{id}, #{userId}, #{roleId}, #{orderInfo}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertPermUserRole(AddMemberRoleEntityVO memberRole);

    /**
     * 检查用户是否已存在角色关联
     * 避免重复分配相同角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 关联记录数量
     */
    @Select("SELECT COUNT(*) FROM t_perm_user_role " +
            "WHERE user_id = #{userId} AND role_id = #{roleId} AND is_del = false")
    int checkUserRoleExists(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 根据用户ID获取用户基本信息
     * 包含组织归属信息和密码（管理员权限）
     *
     * @param userId 用户ID
     * @return 用户详情信息
     */
    @Select("SELECT " +
            "u.id AS userId, " +
            "u.user_name AS userName, " +
            "u.account AS account, " +
            "u.password AS password, " +
            "u.organ_affiliation AS organAffiliation, " +
            "o.organ_name AS organName, " +
            "u.is_disable AS isDisable " +
            "FROM t_user u " +
            "LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id " +
            "WHERE u.id = #{userId} AND u.is_del = false")
    UserDetailResponseVO findUserDetailById(@Param("userId") Long userId);

    /**
     * 根据用户ID数组批量查询用户详情
     * 按照传入的userIds顺序返回结果
     * 包含用户基本信息和组织归属信息
     *
     * @param userIds 用户ID列表
     * @return 用户详情信息列表，按传入顺序排列
     */
    @Select("<script>" +
            "SELECT " +
            "u.id AS userId, " +
            "u.user_name AS userName, " +
            "u.account AS account, " +
            "u.password AS password, " +
            "u.organ_affiliation AS organAffiliation, " +
            "o.organ_name AS organName, " +
            "u.is_disable AS isDisable " +
            "FROM t_user u " +
            "LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id " +
            "WHERE u.id IN " +
            "<foreach collection='userIds' item='userId' open='(' separator=',' close=')'>" +
            "#{userId}" +
            "</foreach>" +
            " AND u.is_del = false " +
            "ORDER BY " +
            "CASE u.id " +
            "<foreach collection='userIds' item='userId' index='index'>" +
            "WHEN #{userId} THEN #{index} " +
            "</foreach>" +
            "END" +
            "</script>")
    List<UserDetailResponseVO> findUserDetailsByIds(@Param("userIds") List<Long> userIds);

    /**
     * 根据用户ID获取用户已分配的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT DISTINCT " +
            "r.id AS roleId, " +
            "r.role_name AS roleName " +
            "FROM t_perm_user_role pur " +
            "LEFT JOIN t_role r ON pur.role_id = r.id " +
            "WHERE pur.user_id = #{userId} AND pur.is_del = false AND r.is_del = false " +
            "ORDER BY r.id")
    List<RoleInfoVO> findUserRolesByUserId(@Param("userId") Long userId);

    /**
     * 更新用户基本信息
     * 支持密码的条件更新（如果提供密码则更新，否则不更新）
     *
     * @param request 更新用户信息请求
     */
    @Update("<script>" +
            "UPDATE t_user SET " +
            "user_name = #{userName}, " +
            "account = #{account}, " +
            "organ_affiliation = #{organAffiliation}, " +
            "is_disable = #{isDisable}, " +
            "<if test='password != null and password != \"\"'>" +
            "password = #{password}, " +
            "</if>" +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{userId} AND is_del = false" +
            "</script>")
    void updateUserInfo(UpdateUserRequestVO request);

    /**
     * 删除用户的所有角色关联（逻辑删除）
     *
     * @param userId 用户ID
     */
    @Update("UPDATE t_perm_user_role SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE user_id = #{userId} AND is_del = false")
    void deleteUserRoles(@Param("userId") Long userId);

    /**
     * 删除用户基本信息（逻辑删除）
     *
     * @param userId 用户ID
     */
    @Update("UPDATE t_user SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{userId} AND is_del = false")
    void deleteUser(@Param("userId") Long userId);

    /**
     * 检查用户是否存在且未被删除
     *
     * @param userId 用户ID
     * @return 存在的用户数量
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE id = #{userId} AND is_del = false")
    int checkUserExists(@Param("userId") Long userId);

    /**
     * 检查账号是否已存在
     *
     * @param account 账号
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE account = #{account} AND is_del = false")
    int checkAccountExists(@Param("account") String account);

    /**
     * 检查用户名是否已存在
     *
     * @param userName 用户名
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE user_name = #{userName} AND is_del = false")
    int checkUserNameExists(@Param("userName") String userName);

    /**
     * 检查用户名是否已存在（排除指定用户ID）
     * 用于编辑用户时的重复检查
     *
     * @param userName 用户名
     * @param excludeUserId 要排除的用户ID
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE user_name = #{userName} AND id != #{excludeUserId} AND is_del = false")
    int checkUserNameExistsForUpdate(@Param("userName") String userName, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查账号是否已存在（排除指定用户ID）
     * 用于编辑用户时的重复检查
     *
     * @param account 账号
     * @param excludeUserId 要排除的用户ID
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE account = #{account} AND id != #{excludeUserId} AND is_del = false")
    int checkAccountExistsForUpdate(@Param("account") String account, @Param("excludeUserId") Long excludeUserId);

    /**
     * 分页查询用户列表（基础用户信息）
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 用户基础信息列表
     */
    List<UserListItemVO> findUserListPage(UserListRequestVO request);

    /**
     * 查询所有用户列表（不分页）
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 所有符合条件的用户列表
     */
    List<UserListItemVO> findUserListAll(UserListRequestVO request);

    /**
     * 查询用户列表总数
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 总记录数
     */
    Long countUserList(UserListRequestVO request);

    /**
     * 清理重复的用户角色关联记录
     * 保留每个用户-角色组合的最新记录，删除重复的旧记录
     */
    @Update("UPDATE t_perm_user_role SET is_del = true, modify_time = CURRENT_TIMESTAMP " +
            "WHERE id NOT IN (" +
            "  SELECT DISTINCT ON (user_id, role_id) id " +
            "  FROM t_perm_user_role " +
            "  WHERE is_del = false " +
            "  ORDER BY user_id, role_id, create_time DESC" +
            ") AND is_del = false")
    void cleanDuplicateUserRoles();

    /**
     * 获取所有可用的角色选项
     * 用于下拉框选择，只返回启用且未删除的角色
     *
     * @return 角色选项列表
     */
    @Select("SELECT " +
            "id AS roleId, " +
            "role_name AS roleName " +
            "FROM t_role " +
            "WHERE is_del = false AND is_disable = false " +
            "ORDER BY role_name")
    List<RoleInfoVO> getRoleOptions();

    /**
     * 分页查询角色的用户分配列表
     * 使用XML实现复杂查询逻辑，包含角色分配状态判断
     *
     * @param request 查询参数
     * @return 角色用户分配列表
     */
    List<RoleUserListItemVO> findRoleUserListPage(RoleUserListRequestVO request);

    /**
     * 查询角色用户分配列表总数
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 总记录数
     */
    Long countRoleUserList(RoleUserListRequestVO request);

    /**
     * 恢复用户角色关联（设置is_del=false）
     * 用于重新授权操作
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    @Update("UPDATE t_perm_user_role SET " +
            "is_del = false, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE user_id = #{userId} AND role_id = #{roleId}")
    void restoreUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 取消用户角色关联（设置is_del=true）
     * 用于取消授权操作
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    @Update("UPDATE t_perm_user_role SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE user_id = #{userId} AND role_id = #{roleId}")
    void revokeUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 分页查询指定角色的未授权用户列表
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 未授权用户列表
     */
    List<UnauthorizedUserItemVO> findUnauthorizedUsersPage(UnauthorizedUsersRequestVO request);

    /**
     * 查询指定角色的未授权用户总数
     * 使用XML实现复杂查询逻辑
     *
     * @param request 查询参数
     * @return 总记录数
     */
    Long countUnauthorizedUsers(UnauthorizedUsersRequestVO request);

    /**
     * 层级懒加载查询部门和用户
     * 根据parentId查询直属部门和用户，支持懒加载
     *
     * @param roleId 角色ID
     * @param parentId 父部门ID，null表示查询顶级部门
     * @return 部门和用户的原始数据
     */
    List<Map<String, Object>> findUnauthorizedUsersTreeOptimized(@Param("roleId") Long roleId, @Param("parentId") Long parentId);

    /**
     * 使用 RECURSIVE CTE 查询部门树和未授权用户（分页优化版本）
     * 适用于大数据量场景，支持用户分页和搜索过滤
     *
     * @param roleId 角色ID
     * @param userName 用户名搜索条件（可选）
     * @param userState 用户状态过滤（可选）
     * @param offset 分页偏移量
     * @param pageSize 分页大小
     * @return 部门树和未授权用户的原始数据（分页）
     */
    List<Map<String, Object>> findUnauthorizedUsersTreeOptimizedWithPaging(
            @Param("roleId") Long roleId,
            @Param("userName") String userName,
            @Param("userState") Boolean userState,
            @Param("offset") Integer offset,
            @Param("pageSize") Integer pageSize
    );

    /**
     * 查询指定部门下的直属未授权用户
     * 用于懒加载场景，只返回当前部门的直属用户，不包含子部门
     *
     * @param roleId 角色ID
     * @param departmentId 部门ID
     * @return 指定部门下的直属未授权用户
     */
    List<Map<String, Object>> findUnauthorizedUsersByDepartment(
            @Param("roleId") Long roleId,
            @Param("departmentId") Long departmentId
    );

    /**
     * 按部门分页查询用户列表
     * 使用XML实现复杂查询逻辑，支持包含子部门查询
     *
     * @param request 查询参数
     * @return 用户列表
     */
    List<UserListByOrgItemVO> findUserListByOrgPage(UserListByOrgRequestVO request);

    /**
     * 按部门查询用户列表总数
     * 使用XML实现复杂查询逻辑，支持包含子部门查询
     *
     * @param request 查询参数
     * @return 总记录数
     */
    Long countUserListByOrg(UserListByOrgRequestVO request);

    // ==================== 新增：高性能部门树和用户查询接口 ====================

    /**
     * 获取纯部门树结构（不包含用户信息）
     * 使用 RECURSIVE CTE 一次性获取完整的部门层级结构
     * 包含用户数量统计，用于高性能的部门树展示
     *
     * @return 部门树结构列表，包含用户数量统计
     */
    @Select("WITH RECURSIVE dept_tree AS ( " +
            "SELECT id, organ_name, pre_id, order_info, 0 as level " +
            "FROM t_org_structure " +
            "WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false " +
            "UNION ALL " +
            "SELECT o.id, o.organ_name, o.pre_id, o.order_info, dt.level + 1 " +
            "FROM t_org_structure o " +
            "JOIN dept_tree dt ON o.pre_id = dt.id " +
            "WHERE o.is_del = false " +
            "), " +
            "user_counts AS ( " +
            "SELECT organ_affiliation as dept_id, COUNT(*) as user_count " +
            "FROM t_user " +
            "WHERE is_del = false " +
            "GROUP BY organ_affiliation " +
            "), " +
            "child_counts AS ( " +
            "SELECT dt.id, COUNT(child.id) as child_count " +
            "FROM dept_tree dt " +
            "LEFT JOIN t_org_structure child ON child.pre_id = dt.id AND child.is_del = false " +
            "GROUP BY dt.id " +
            ") " +
            "SELECT d.id, d.organ_name AS organName, d.pre_id AS parentId, " +
            "d.order_info AS orderInfo, d.level, " +
            "COALESCE(uc.user_count, 0) AS userCount, " +
            "COALESCE(cc.child_count, 0) AS childDepartmentCount, " +
            "CASE WHEN cc.child_count > 0 THEN true ELSE false END AS hasChildren, " +
            "CASE WHEN cc.child_count = 0 THEN true ELSE false END AS isLeaf " +
            "FROM dept_tree d " +
            "LEFT JOIN user_counts uc ON d.id = uc.dept_id " +
            "LEFT JOIN child_counts cc ON d.id = cc.id " +
            "ORDER BY d.level, d.order_info")
    List<Map<String, Object>> findDepartmentTreeOnly();

    /**
     * 根据部门ID查询该部门的直属用户
     * 不包含子部门用户，用于按需加载用户列表
     *
     * @param request 查询参数
     * @return 用户列表
     */
    @Select("<script>" +
            "SELECT " +
            "u.id AS userId, " +
            "u.user_name AS userName, " +
            "u.account AS account, " +
            "u.is_disable AS isDisabled, " +
            "u.organ_affiliation AS departmentId, " +
            "o.organ_name AS departmentName, " +
            "true AS isDirect, " +
            "'' AS userType, " +
            "'' AS employeeCode, " +
            "'' AS mobile, " +
            "'' AS email " +
            "FROM t_user u " +
            "LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id " +
            "WHERE u.organ_affiliation = #{departmentId} " +
            "AND u.is_del = false " +
            "<if test='includeDisabled == null or !includeDisabled'>" +
            "  AND u.is_disable = false " +
            "</if>" +
            "<if test='userName != null and userName != \"\"'>" +
            "  AND u.user_name LIKE CONCAT('%', #{userName}, '%') " +
            "</if>" +
            "<if test='account != null and account != \"\"'>" +
            "  AND u.account LIKE CONCAT('%', #{account}, '%') " +
            "</if>" +
            "ORDER BY " +
            "<choose>" +
            "  <when test='sortField == \"account\"'>" +
            "    u.account " +
            "  </when>" +
            "  <when test='sortField == \"employeeCode\"'>" +
            "    u.employee_code " +
            "  </when>" +
            "  <otherwise>" +
            "    u.user_name " +
            "  </otherwise>" +
            "</choose>" +
            "<choose>" +
            "  <when test='sortDirection == \"DESC\"'>" +
            "    DESC " +
            "  </when>" +
            "  <otherwise>" +
            "    ASC " +
            "  </otherwise>" +
            "</choose>" +
            "<if test='enablePaging != null and enablePaging'>" +
            "  LIMIT #{pageSize} OFFSET #{offset} " +
            "</if>" +
            "</script>")
    List<Map<String, Object>> findUsersByDepartmentId(DepartmentUsersRequestVO request);

    /**
     * 根据部门ID查询该部门及所有子部门的用户
     * 使用 RECURSIVE CTE 查询所有子部门，然后获取用户列表
     *
     * @param request 查询参数
     * @return 用户列表，包含子部门用户
     */
    @Select("<script>" +
            "WITH RECURSIVE dept_tree AS ( " +
            "  SELECT id FROM t_org_structure " +
            "  WHERE id = #{departmentId} AND is_del = false " +
            "  UNION ALL " +
            "  SELECT o.id FROM t_org_structure o " +
            "  JOIN dept_tree dt ON o.pre_id = dt.id " +
            "  WHERE o.is_del = false " +
            ") " +
            "SELECT " +
            "u.id AS userId, " +
            "u.user_name AS userName, " +
            "u.account AS account, " +
            "u.is_disable AS isDisabled, " +
            "u.organ_affiliation AS departmentId, " +
            "o.organ_name AS departmentName, " +
            "CASE WHEN u.organ_affiliation = #{departmentId} THEN true ELSE false END AS isDirect, " +
            "'' AS userType, " +
            "'' AS employeeCode, " +
            "'' AS mobile, " +
            "'' AS email " +
            "FROM t_user u " +
            "LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id " +
            "WHERE u.organ_affiliation IN (SELECT id FROM dept_tree) " +
            "AND u.is_del = false " +
            "<if test='includeDisabled == null or !includeDisabled'>" +
            "  AND u.is_disable = false " +
            "</if>" +
            "<if test='userName != null and userName != \"\"'>" +
            "  AND u.user_name LIKE CONCAT('%', #{userName}, '%') " +
            "</if>" +
            "<if test='account != null and account != \"\"'>" +
            "  AND u.account LIKE CONCAT('%', #{account}, '%') " +
            "</if>" +
            "ORDER BY " +
            "u.organ_affiliation, " +
            "<choose>" +
            "  <when test='sortField == \"account\"'>" +
            "    u.account " +
            "  </when>" +
            "  <when test='sortField == \"employeeCode\"'>" +
            "    u.user_name " +
            "  </when>" +
            "  <otherwise>" +
            "    u.user_name " +
            "  </otherwise>" +
            "</choose>" +
            "<choose>" +
            "  <when test='sortDirection == \"DESC\"'>" +
            "    DESC " +
            "  </when>" +
            "  <otherwise>" +
            "    ASC " +
            "  </otherwise>" +
            "</choose>" +
            "<if test='enablePaging != null and enablePaging'>" +
            "  LIMIT #{pageSize} OFFSET #{offset} " +
            "</if>" +
            "</script>")
    List<Map<String, Object>> findUsersByDepartmentIdWithChildren(DepartmentUsersRequestVO request);

    /**
     * 统计部门用户数量（不包含子部门）
     *
     * @param request 查询参数
     * @return 用户数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) " +
            "FROM t_user u " +
            "WHERE u.organ_affiliation = #{departmentId} " +
            "AND u.is_del = false " +
            "<if test='includeDisabled == null or !includeDisabled'>" +
            "  AND u.is_disable = false " +
            "</if>" +
            "<if test='userName != null and userName != \"\"'>" +
            "  AND u.user_name LIKE CONCAT('%', #{userName}, '%') " +
            "</if>" +
            "<if test='account != null and account != \"\"'>" +
            "  AND u.account LIKE CONCAT('%', #{account}, '%') " +
            "</if>" +
            "</script>")
    Long countUsersByDepartmentId(DepartmentUsersRequestVO request);

    /**
     * 统计部门及子部门用户数量
     *
     * @param request 查询参数
     * @return 用户数量
     */
    @Select("<script>" +
            "WITH RECURSIVE dept_tree AS ( " +
            "  SELECT id FROM t_org_structure " +
            "  WHERE id = #{departmentId} AND is_del = false " +
            "  UNION ALL " +
            "  SELECT o.id FROM t_org_structure o " +
            "  JOIN dept_tree dt ON o.pre_id = dt.id " +
            "  WHERE o.is_del = false " +
            ") " +
            "SELECT COUNT(*) " +
            "FROM t_user u " +
            "WHERE u.organ_affiliation IN (SELECT id FROM dept_tree) " +
            "AND u.is_del = false " +
            "<if test='includeDisabled == null or !includeDisabled'>" +
            "  AND u.is_disable = false " +
            "</if>" +
            "<if test='userName != null and userName != \"\"'>" +
            "  AND u.user_name LIKE CONCAT('%', #{userName}, '%') " +
            "</if>" +
            "<if test='account != null and account != \"\"'>" +
            "  AND u.account LIKE CONCAT('%', #{account}, '%') " +
            "</if>" +
            "</script>")
    Long countUsersByDepartmentIdWithChildren(DepartmentUsersRequestVO request);

    // ==================== 认证相关查询方法 ====================

    /**
     * 根据账号查询用户信息（包含密码）
     * 用于用户登录验证，返回用户基本信息和加密密码
     *
     * @param account 登录账号
     * @return 用户基本信息，如果用户不存在返回null
     */
    @Select("SELECT id AS userId, user_name AS userName, account, password, is_disable AS isDisable " +
            "FROM t_user " +
            "WHERE account = #{account} AND is_del = false")
    UserDetailResponseVO findUserByAccount(@Param("account") String account);

    /**
     * 根据账号和密码查询用户信息
     * 用于用户登录验证，返回用户基本信息
     * @deprecated 使用findUserByAccount替代，支持MD5密码验证
     *
     * @param account 登录账号
     * @param password 登录密码（明文）
     * @return 用户基本信息，如果验证失败返回null
     */
    @Deprecated
    @Select("SELECT id AS userId, user_name AS userName, account, is_disable AS isDisable " +
            "FROM t_user " +
            "WHERE account = #{account} AND password = #{password} AND is_del = false")
    UserDetailResponseVO findUserByAccountAndPassword(@Param("account") String account, @Param("password") String password);

    /**
     * 根据用户ID查询用户有权限的菜单列表
     * 通过用户角色关联查询菜单权限，支持模块筛选
     *
     * @param userId 用户ID
     * @param moduleIdentifier 模块标识符，可选
     * @return 用户有权限的菜单列表
     */
    @Select("<script>" +
            "SELECT rmp.menu_id " +
            "FROM t_user u " +
            "INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false " +
            "INNER JOIN t_role r ON pur.role_id = r.id AND r.is_del = false AND r.is_disable = false " +
            "INNER JOIN t_roles_menu_permission rmp ON r.id = rmp.role_id AND rmp.is_del = false " +
            "WHERE u.id = #{userId} AND u.is_del = false AND u.is_disable = false " +
            "</script>")
    List<Long> findUserMenuIds(@Param("userId") Long userId);

    @Select("SELECT " +
            "id::text as id, " +
            "name, " +
            "CASE WHEN pre_id IS NULL OR pre_id = 0 THEN '0' ELSE pre_id::text END as \"preId\", " +
            "COALESCE(order_info, 0) as \"orderInfo\", " +
            "menu_type as \"menuType\", " +
            "route_address as \"routeAddress\", " +
            "component_path as \"componentPath\", " +
            "permission_identifier as \"permissionIdentifier\", " +
            "route_param as \"routeParam\" " +
            "FROM t_menu_permission " +
            "WHERE id = #{menuId} AND (is_del = false OR is_del IS NULL)")
    Map<String, Object> findMenuById(@Param("menuId") Long menuId);

    @Select("SELECT name FROM t_menu_permission WHERE id = #{parentId} AND (is_del = false OR is_del IS NULL)")
    String findMenuNameById(@Param("parentId") Long parentId);

    @Select("<script>" +
            "SELECT DISTINCT " +
            "m.id::text as id, " +
            "m.name, " +
            "CASE WHEN m.pre_id IS NULL OR m.pre_id = 0 THEN '0' ELSE m.pre_id::text END as \"preId\", " +
            "COALESCE(m.order_info, 0) as \"orderInfo\", " +
            "m.menu_type as \"menuType\", " +
            "m.route_address as \"routeAddress\", " +
            "m.component_path as \"componentPath\", " +
            "m.permission_identifier as \"permissionIdentifier\", " +
            "m.route_param as \"routeParam\", " +
            "p.name as \"preName\" " +
            "FROM t_user u " +
            "INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false " +
            "INNER JOIN t_role r ON pur.role_id = r.id AND r.is_del = false AND r.is_disable = false " +
            "INNER JOIN t_roles_menu_permission rmp ON r.id = rmp.role_id AND rmp.is_del = false " +
            "INNER JOIN t_menu_permission m ON rmp.menu_id = m.id AND (m.is_del = false OR m.is_del IS NULL) " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND (p.is_del = false OR p.is_del IS NULL) " +
            "WHERE u.id = #{userId} AND u.is_del = false AND u.is_disable = false " +
            "AND (m.is_disable = false OR m.is_disable IS NULL) " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND m.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "UNION " +
            "SELECT DISTINCT " +
            "parent.id::text as id, " +
            "parent.name, " +
            "CASE WHEN parent.pre_id IS NULL OR parent.pre_id = 0 THEN '0' ELSE parent.pre_id::text END as \"preId\", " +
            "COALESCE(parent.order_info, 0) as \"orderInfo\", " +
            "parent.menu_type as \"menuType\", " +
            "parent.route_address as \"routeAddress\", " +
            "parent.component_path as \"componentPath\", " +
            "parent.permission_identifier as \"permissionIdentifier\", " +
            "parent.route_param as \"routeParam\", " +
            "pp.name as \"preName\" " +
            "FROM t_user u " +
            "INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.is_del = false " +
            "INNER JOIN t_role r ON pur.role_id = r.id AND r.is_del = false AND r.is_disable = false " +
            "INNER JOIN t_roles_menu_permission rmp ON r.id = rmp.role_id AND rmp.is_del = false " +
            "INNER JOIN t_menu_permission m ON rmp.menu_id = m.id AND (m.is_del = false OR m.is_del IS NULL) " +
            "INNER JOIN t_menu_permission parent ON m.pre_id = parent.id AND (parent.is_del = false OR parent.is_del IS NULL) " +
            "LEFT JOIN t_menu_permission pp ON parent.pre_id = pp.id AND (pp.is_del = false OR pp.is_del IS NULL) " +
            "WHERE u.id = #{userId} AND u.is_del = false AND u.is_disable = false " +
            "AND (m.is_disable = false OR m.is_disable IS NULL) " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND parent.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "ORDER BY \"orderInfo\" ASC, id ASC" +
            "</script>")
    List<Map<String, Object>> findUserMenuPermissions(@Param("userId") Long userId, @Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 查询所有可用的菜单权限（超级管理员专用）
     * 直接查询数据库中所有未删除且未禁用的菜单，不依赖角色权限关联
     *
     * @param moduleIdentifier 模块标识符，可选
     * @return 所有可用的菜单列表
     */
    @Select("<script>" +
            "SELECT DISTINCT " +
            "m.id::text as id, " +
            "m.name, " +
            "CASE WHEN m.pre_id IS NULL OR m.pre_id = 0 THEN '0' ELSE m.pre_id::text END as \"preId\", " +
            "COALESCE(m.order_info, 0) as \"orderInfo\", " +
            "m.menu_type as \"menuType\", " +
            "m.route_address as \"routeAddress\", " +
            "m.component_path as \"componentPath\", " +
            "m.permission_identifier as \"permissionIdentifier\", " +
            "m.route_param as \"routeParam\", " +
            "p.name as \"preName\" " +
            "FROM t_menu_permission m " +
            "LEFT JOIN t_menu_permission p ON m.pre_id = p.id AND (p.is_del = false OR p.is_del IS NULL) " +
            "WHERE (m.is_del = false OR m.is_del IS NULL) " +
            "AND (m.is_disable = false OR m.is_disable IS NULL) " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND m.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "ORDER BY \"orderInfo\" ASC, id ASC" +
            "</script>")
    List<Map<String, Object>> findAllMenuPermissions(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 查询用户数据权限
     * 通过用户角色关联查询用户拥有的数据权限和操作类型
     *
     * @param userId 用户ID
     * @param moduleIdentifier 模块标识符，可选
     * @return 用户数据权限列表
     */
    @Select("<script>" +
            "SELECT DISTINCT " +
            "dp.id::text as id, " +
            "dp.name, " +
            "CASE WHEN dp.pre_id IS NULL OR dp.pre_id = 0 THEN '0' ELSE dp.pre_id::text END as \"preId\", " +
            "dp.module_identifier as \"moduleIdentifier\", " +
            "dp.data_identifier as \"dataIdentifier\", " +
            "COALESCE(dp.order_info, 0) as \"orderInfo\", " +
            "rdp.operate_type as \"operateType\" " +
            "FROM t_data_permission dp " +
            "INNER JOIN t_roles_data_permission rdp ON dp.id = rdp.data_id " +
            "INNER JOIN t_perm_user_role pur ON rdp.role_id = pur.role_id " +
            "WHERE pur.user_id = #{userId} " +
            "AND dp.is_del = false " +
            "AND dp.is_disable = false " +
            "AND rdp.is_del = false " +
            "AND pur.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND dp.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "ORDER BY \"moduleIdentifier\", \"orderInfo\" ASC, id" +
            "</script>")
    List<Map<String, Object>> findUserDataPermissions(@Param("userId") Long userId,
                                                      @Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 查询所有可用的数据权限（超级管理员使用）
     * 不进行角色权限过滤，返回系统中所有可用的数据权限
     *
     * @param moduleIdentifier 模块标识符，可选
     * @return 所有可用的数据权限列表
     */
    @Select("<script>" +
            "SELECT DISTINCT " +
            "dp.id::text as id, " +
            "dp.name, " +
            "CASE WHEN dp.pre_id IS NULL OR dp.pre_id = 0 THEN '0' ELSE dp.pre_id::text END as \"preId\", " +
            "dp.module_identifier as \"moduleIdentifier\", " +
            "dp.data_identifier as \"dataIdentifier\", " +
            "COALESCE(dp.order_info, 0) as \"orderInfo\", " +
            "do.operate_type as \"operateType\" " +
            "FROM t_data_permission dp " +
            "LEFT JOIN t_data_operate do ON (" +
            "  (dp.data_identifier = do.data_identifier AND do.data_identifier IS NOT NULL) " +
            "  OR (dp.module_identifier = do.module_identifier AND do.data_identifier IS NULL)" +
            ") " +
            "WHERE dp.is_del = false " +
            "AND dp.is_disable = false " +
            "AND (do.is_del = false OR do.is_del IS NULL) " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND dp.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "ORDER BY \"moduleIdentifier\", \"orderInfo\" ASC, id" +
            "</script>")
    List<Map<String, Object>> findAllDataPermissions(@Param("moduleIdentifier") String moduleIdentifier);
}
