2025-06-23 02:00:00.120 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行每日增量数据同步任务 ===
2025-06-23 02:00:02.065 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - 同步时间范围: 2025-06-22 00:00:00 - 2025-06-22 23:59:59
2025-06-23 02:00:02.871 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始执行增量数据同步，时间范围: 2025-06-22 00:00:00 - 2025-06-22 23:59:59
2025-06-23 02:00:03.315 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 采用增量同步策略，只处理时间范围内的变更数据
2025-06-23 02:00:03.318 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始同步部门数据，时间范围: 2025-06-22T00:00 - 2025-06-22T23:59:59
2025-06-23 02:00:03.595 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 同步策略: 建议策略：单次同步（23小时 ≤ 72小时阈值）
2025-06-23 02:00:03.606 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 开始从外部系统获取部门数据，时间范围: 2025-06-22T00:00 - 2025-06-22T23:59:59
2025-06-23 02:00:05.165 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部API: http://localhost:8080/api/data/departments?startDate=2025-06-22%2000:00:00&endDate=2025-06-22%2023:59:59
2025-06-23 02:00:05.292 [scheduling-1] INFO  com.dfit.percode.sync.service.ExternalDataService - 调用外部系统获取部门数据（外部系统已内置重试和分片机制）
2025-06-23 10:09:28.819 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-23 10:09:29.207 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-23 17:14:32.789 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 33660 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-23 17:14:32.791 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-23 17:14:32.802 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-23 17:14:36.633 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:14:36.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-23 17:14:36.679 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-23 17:14:36.686 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:14:36.687 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-23 17:14:36.706 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-23 17:14:36.721 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:14:36.722 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:14:36.761 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-23 17:14:36.795 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:14:36.798 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23 17:14:36.828 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-23 17:14:38.401 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-23 17:14:38.436 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-23 17:14:38.437 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-23 17:14:38.437 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-23 17:14:39.071 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-23 17:14:39.072 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6116 ms
2025-06-23 17:14:39.285 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-23 17:14:39.570 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-23 17:14:40.708 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-23 17:14:40.863 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-23 17:14:41.370 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-23 17:14:41.685 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-23 17:14:42.388 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-23 17:14:42.412 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-23 17:14:45.530 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-23 17:14:49.306 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-23 17:14:49.324 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-23 17:14:50.933 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.488 seconds (JVM running for 24.791)
2025-06-23 17:14:57.179 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-23 17:14:57.180 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-23 17:14:57.182 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-23 17:15:55.902 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.MenuModuleController - 开始查询菜单列表（优化版本）
2025-06-23 17:15:55.903 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.MenuModuleController - 请求参数 - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-23 17:15:55.912 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表（优化版本）
2025-06-23 17:15:55.912 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-23 17:15:56.189 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 查询到菜单总数: 34
2025-06-23 17:15:56.190 [http-nio-8285-exec-5] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功（优化版本），根节点数量: 13，耗时: 277ms，预计性能提升: 90%+
2025-06-23 17:15:56.191 [http-nio-8285-exec-5] INFO  com.dfit.percode.controller.MenuModuleController - 菜单列表查询成功（优化版本），根节点数量: 13，接口耗时: 286ms
2025-06-23 17:17:49.263 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.MenuModuleController - 开始查询菜单列表（优化版本）
2025-06-23 17:17:49.264 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.MenuModuleController - 请求参数 - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-23 17:17:49.264 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始查询菜单列表（优化版本）
2025-06-23 17:17:49.264 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 模块标识: null, 菜单名称: null, 包含禁用菜单: true
2025-06-23 17:17:50.002 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 查询到菜单总数: 34
2025-06-23 17:17:50.003 [http-nio-8285-exec-9] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单列表查询成功（优化版本），根节点数量: 13，耗时: 739ms，预计性能提升: 90%+
2025-06-23 17:17:50.003 [http-nio-8285-exec-9] INFO  com.dfit.percode.controller.MenuModuleController - 菜单列表查询成功（优化版本），根节点数量: 13，接口耗时: 739ms
2025-06-23 17:22:58.160 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构（优化版本）
2025-06-23 17:22:58.169 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构（优化版本）
2025-06-23 17:22:58.286 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-23 17:22:59.023 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成（优化版本），耗时: 853ms，模块数量: 7，预计性能提升: 90%+
2025-06-23 17:22:59.024 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功（优化版本），模块数量: 7，接口耗时: 864ms
2025-06-23 17:31:35.501 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-23 17:31:35.520 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-23 17:32:30.096 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 17272 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-23 17:32:30.100 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-23 17:32:30.116 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-23 17:32:32.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:32:32.826 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-23 17:32:32.894 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 57 ms. Found 0 Elasticsearch repository interfaces.
2025-06-23 17:32:32.904 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:32:32.909 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-23 17:32:32.966 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-23 17:32:32.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:32:32.987 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-23 17:32:33.030 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 JPA repository interfaces.
2025-06-23 17:32:33.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-23 17:32:33.134 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-23 17:32:33.190 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-23 17:32:36.422 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-23 17:32:36.440 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-23 17:32:36.443 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-23 17:32:36.444 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-23 17:32:37.130 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-23 17:32:37.131 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6913 ms
2025-06-23 17:32:37.371 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-23 17:32:38.950 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-23 17:32:39.900 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-23 17:32:40.028 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-23 17:32:40.279 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-23 17:32:40.547 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-23 17:32:41.067 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-23 17:32:41.094 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-23 17:32:45.673 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-23 17:32:50.057 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-23 17:32:50.080 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-23 17:32:52.423 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 23.754 seconds (JVM running for 32.221)
2025-06-23 17:33:00.725 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-23 17:33:00.731 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
