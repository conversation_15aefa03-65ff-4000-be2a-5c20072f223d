package com.dfit.percode.service;

import com.dfit.percode.vo.AddMemberRequestVO;
import com.dfit.percode.vo.DeleteUserRequestVO;
import com.dfit.percode.vo.DepartmentTreeVO;
import com.dfit.percode.vo.MemberInfoVO;
import com.dfit.percode.vo.RoleInfoVO;
import com.dfit.percode.vo.UpdateUserRequestVO;
import com.dfit.percode.vo.UserDetailRequestVO;
import com.dfit.percode.vo.UserDetailResponseVO;
import com.dfit.percode.vo.UserListRequestVO;
import com.dfit.percode.vo.UserListResponseVO;
import com.dfit.percode.vo.UserListByOrgRequestVO;
import com.dfit.percode.vo.UserListByOrgResponseVO;
import com.dfit.percode.vo.RoleUserListRequestVO;
import com.dfit.percode.vo.RoleUserListResponseVO;
import com.dfit.percode.vo.UnauthorizedUsersRequestVO;
import com.dfit.percode.vo.UnauthorizedUsersResponseVO;
import com.dfit.percode.vo.UpdateUserRoleRequestVO;
import com.dfit.percode.vo.DepartmentTreeOnlyVO;
import com.dfit.percode.vo.DepartmentUsersRequestVO;
import com.dfit.percode.vo.DepartmentUsersResponseVO;
import com.dfit.percode.vo.CheckUserDuplicateRequestVO;
import com.dfit.percode.vo.CheckUserDuplicateResponseVO;

import java.util.List;

/**
 * 用户相关服务接口
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IUserService {

    /**
     * 获取所有用户（组织架构树形结构）
     * 用于前端选择人员功能
     *
     * @return 部门树形结构列表，包含用户信息
     */
    List<DepartmentTreeVO> getAllUsers();

    /**
     * 获取所有用户（组织架构树形结构）- 优化版本
     * 使用 RECURSIVE CTE 查询，避免 N+1 查询问题，提升性能
     * 功能与 getAllUsers() 完全相同，仅实现方式不同
     *
     * @return 部门树形结构列表，包含用户信息
     */
    List<DepartmentTreeVO> getAllUsersOptimized();

    /**
     * 添加成员到权限管理系统
     * 按照前端设计直接接收数组格式
     *
     * @param members 成员列表（直接数组格式）
     */
    void addMembers(List<MemberInfoVO> members);

    /**
     * 获取用户详细信息
     * 包含用户基本信息和已分配的角色列表
     * 支持单个用户查询和批量用户查询
     *
     * @param request 用户详情请求参数
     * @return 用户详细信息（单个查询返回UserDetailResponseVO对象，批量查询返回List<UserDetailResponseVO>数组）
     */
    Object getUserDetail(UserDetailRequestVO request);

    /**
     * 更新用户信息
     * 包含用户基本信息和角色分配的更新
     *
     * @param request 更新用户信息请求参数
     */
    void updateUser(UpdateUserRequestVO request);

    /**
     * 删除用户
     * 逻辑删除用户基本信息和所有角色关联
     *
     * @param request 删除用户请求参数
     */
    void deleteUser(DeleteUserRequestVO request);

    /**
     * 分页查询用户列表
     * 支持多条件搜索和分页
     *
     * @param request 分页查询请求参数
     * @return 分页查询结果
     */
    UserListResponseVO getUserList(UserListRequestVO request);

    /**
     * 清理重复的用户角色关联记录
     * 用于修复数据不一致问题
     */
    void cleanDuplicateUserRoles();

    /**
     * 获取所有可用的角色选项
     * 用于下拉框选择
     *
     * @return 角色选项列表
     */
    List<RoleInfoVO> getRoleOptions();

    /**
     * 获取角色的用户分配列表
     * 用于角色管理中的分配用户功能
     * 支持分页查询和多条件搜索
     *
     * @param request 角色用户列表查询请求参数
     * @return 角色用户列表响应，包含分页信息
     */
    RoleUserListResponseVO getRoleUserList(RoleUserListRequestVO request);

    /**
     * 获取指定角色的未授权用户列表
     * 用于角色管理中的新增用户功能
     * 支持分页查询和多条件搜索
     *
     * @param request 未授权用户查询请求参数
     * @return 未授权用户列表响应，包含分页信息
     */
    UnauthorizedUsersResponseVO getUnauthorizedUsers(UnauthorizedUsersRequestVO request);

    /**
     * 更新用户角色分配状态
     * 用于角色管理中的授权和取消授权操作
     * 修改t_perm_user_role关联表的is_del状态
     *
     * @param request 更新用户角色分配请求参数
     */
    void updateUserRole(UpdateUserRoleRequestVO request);

    /**
     * 按部门分页查询用户列表
     * 支持包含子部门查询、多条件搜索和分页
     *
     * @param request 按部门查询用户列表请求参数
     * @return 按部门查询用户列表响应，包含分页信息
     */
    UserListByOrgResponseVO getUserListByOrg(UserListByOrgRequestVO request);

    /**
     * 检查用户重复
     * 实时检测用户名和账号是否重复，支持新增和编辑场景
     *
     * @param request 检查用户重复请求参数
     * @return 重复检查结果
     */
    CheckUserDuplicateResponseVO checkUserDuplicate(CheckUserDuplicateRequestVO request);

    // ==================== 新增：高性能部门树和用户查询接口 ====================

    /**
     * 获取纯部门树结构（不包含用户信息）
     * 高性能版本：只返回部门层级结构和用户数量统计
     * 用于快速展示部门树，支持按需加载用户
     *
     * @return 部门树结构列表，包含用户数量统计
     */
    List<DepartmentTreeOnlyVO> getDepartmentTreeOnly();

    /**
     * 根据部门ID获取该部门的用户列表
     * 支持只查询直属用户或包含子部门用户
     * 支持搜索、排序和分页功能
     *
     * @param request 部门用户查询请求参数
     * @return 部门用户列表响应
     */
    DepartmentUsersResponseVO getUsersByDepartment(DepartmentUsersRequestVO request);

    /**
     * 获取指定部门下的未授权用户（懒加载版本）
     * 用于懒加载场景，按需加载部门的未授权用户
     * 支持包含子部门查询和搜索过滤
     *
     * @param request 未授权用户查询请求参数（必须包含departmentId）
     * @return 指定部门下的未授权用户树形结构
     */
    List<DepartmentTreeVO> getUnauthorizedUsersByDepartmentLazy(UnauthorizedUsersRequestVO request);
}
