package com.dfit.percode.userPerm;

import lombok.Data;

/**
 * 角色权限实体类
 * 用于角色权限查询结果
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class RolePermEntity {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色状态
     */
    private String roleState;
    
    /**
     * 功能权限
     */
    private String functionPerm;
    
    /**
     * 数据权限
     */
    private String dataPrem;
}
