package com.dfit.percode.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * 超级管理员配置类
 * 用于配置和管理超级管理员账号信息
 * 支持多种识别方式：用户ID、登录账号、用户名
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "super-admin")
public class SuperAdminConfig {

    /**
     * 是否启用超级管理员功能
     * 默认为false，需要在配置文件中显式启用
     */
    private Boolean enabled = false;

    /**
     * 超级管理员用户ID列表
     * 支持配置多个用户ID
     */
    private List<Long> userIds = new ArrayList<>();

    /**
     * 超级管理员登录账号列表
     * 支持配置多个登录账号
     */
    private List<String> loginAccounts = new ArrayList<>();

    /**
     * 超级管理员用户名列表
     * 支持配置多个用户名
     */
    private List<String> usernames = new ArrayList<>();

    /**
     * 配置初始化后的日志记录
     */
    @PostConstruct
    public void init() {
        if (enabled) {
            log.info("超级管理员功能已启用");
            log.info("配置的超级管理员用户ID数量: {}", userIds.size());
            log.info("配置的超级管理员登录账号数量: {}", loginAccounts.size());
            log.info("配置的超级管理员用户名数量: {}", usernames.size());
            
            // 安全考虑：不在日志中显示具体的账号信息
            if (userIds.isEmpty() && loginAccounts.isEmpty() && usernames.isEmpty()) {
                log.warn("超级管理员功能已启用，但未配置任何超级管理员账号！");
            }
        } else {
            log.info("超级管理员功能已禁用");
        }
    }

    /**
     * 判断指定用户ID是否为超级管理员
     * 
     * @param userId 用户ID
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdmin(Long userId) {
        if (!enabled || userId == null) {
            return false;
        }
        
        boolean result = userIds.contains(userId);
        if (result) {
            log.debug("用户ID {} 被识别为超级管理员", userId);
        }
        return result;
    }

    /**
     * 判断指定登录账号是否为超级管理员
     * 
     * @param loginAccount 登录账号
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdmin(String loginAccount) {
        if (!enabled || loginAccount == null || loginAccount.trim().isEmpty()) {
            return false;
        }
        
        boolean result = loginAccounts.contains(loginAccount.trim());
        if (result) {
            log.debug("登录账号 {} 被识别为超级管理员", loginAccount);
        }
        return result;
    }

    /**
     * 判断指定用户名是否为超级管理员
     * 
     * @param username 用户名
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdminByUsername(String username) {
        if (!enabled || username == null || username.trim().isEmpty()) {
            return false;
        }
        
        boolean result = usernames.contains(username.trim());
        if (result) {
            log.debug("用户名 {} 被识别为超级管理员", username);
        }
        return result;
    }

    /**
     * 综合判断是否为超级管理员
     * 支持同时检查用户ID、登录账号和用户名
     * 
     * @param userId 用户ID，可为null
     * @param loginAccount 登录账号，可为null
     * @param username 用户名，可为null
     * @return true表示是超级管理员，false表示不是
     */
    public boolean isSuperAdmin(Long userId, String loginAccount, String username) {
        if (!enabled) {
            return false;
        }
        
        // 任意一种方式匹配即认为是超级管理员
        return isSuperAdmin(userId) || 
               isSuperAdmin(loginAccount) || 
               isSuperAdminByUsername(username);
    }

    /**
     * 获取超级管理员配置摘要信息（脱敏处理）
     * 用于日志记录和调试，不包含敏感信息
     * 
     * @return 配置摘要信息
     */
    public String getConfigSummary() {
        if (!enabled) {
            return "超级管理员功能已禁用";
        }
        
        return String.format("超级管理员功能已启用 - 用户ID数量: %d, 登录账号数量: %d, 用户名数量: %d",
                userIds.size(), loginAccounts.size(), usernames.size());
    }

    /**
     * 检查配置是否有效
     * 
     * @return true表示配置有效，false表示配置无效
     */
    public boolean isConfigValid() {
        if (!enabled) {
            return true; // 禁用状态下认为配置有效
        }
        
        // 启用状态下至少需要配置一种识别方式
        return !userIds.isEmpty() || !loginAccounts.isEmpty() || !usernames.isEmpty();
    }
}
