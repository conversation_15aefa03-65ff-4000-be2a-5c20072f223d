package com.dfit.percode.mapper;

import com.dfit.percode.entity.TDataModule;
import com.dfit.percode.vo.DataModuleListRequestVO;
import com.dfit.percode.vo.DataModuleListResponseVO;
import com.dfit.percode.vo.response.RoleUsageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * 数据权限模块信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface TDataModuleMapper extends BaseMapper<TDataModule> {

    /**
     * 获取数据模块列表（原有方法）
     * 查询所有字段，包含删除状态信息
     * @return 数据模块列表
     */
    @Select("SELECT id as id, " +
            "module_name as moduleName, " +
            "module_identifier as moduleIdentifier, " +
            "order_info as orderInfo, " +
            "is_del as isDel, " +
            "create_time as createTime, " +
            "modify_time as modifyTime " +
            "FROM t_data_module WHERE is_del = false ORDER BY order_info ")
    List<TDataModule> dataModule();

    /**
     * 分页查询数据模块列表
     * @param request 查询请求参数
     * @return 数据模块列表
     */
    List<DataModuleListResponseVO> getDataModuleList(DataModuleListRequestVO request);

    /**
     * 获取数据模块总数
     * @param request 查询请求参数
     * @return 总数
     */
    Integer getDataModuleTotal(DataModuleListRequestVO request);

    /**
     * 新增数据模块
     * @param moduleId 模块ID
     * @param moduleName 模块名称
     * @param moduleIdentifier 模块标识
     * @param orderInfo 排序序号
     */
    @Insert("INSERT INTO t_data_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) " +
            "VALUES (#{moduleId}, #{moduleName}, #{moduleIdentifier}, #{orderInfo}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)")
    void insertDataModule(@Param("moduleId") Long moduleId,
                         @Param("moduleName") String moduleName,
                         @Param("moduleIdentifier") String moduleIdentifier,
                         @Param("orderInfo") Integer orderInfo);

    /**
     * 检查模块标识是否存在
     * @param moduleIdentifier 模块标识
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_data_module WHERE module_identifier = #{moduleIdentifier} AND is_del = false")
    int checkModuleIdentifierExists(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 检查模块是否被数据权限使用
     * @param moduleIdentifier 模块标识
     * @return 使用数量
     */
    @Select("SELECT COUNT(*) FROM t_data_permission WHERE module_identifier = #{moduleIdentifier} AND is_del = false")
    int checkModuleUsedByDataPermissions(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 逻辑删除数据模块
     * @param moduleId 模块ID
     */
    @Update("UPDATE t_data_module SET is_del = true, modify_time = CURRENT_TIMESTAMP WHERE id = #{moduleId}")
    void deleteDataModuleById(@Param("moduleId") Long moduleId);

    /**
     * 根据ID检查模块是否存在
     * @param moduleId 模块ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_data_module WHERE id = #{moduleId} AND is_del = false")
    int checkModuleExistsById(@Param("moduleId") Long moduleId);

    /**
     * 根据ID获取模块标识符
     * @param moduleId 模块ID
     * @return 模块标识符
     */
    @Select("SELECT module_identifier FROM t_data_module WHERE id = #{moduleId} AND is_del = false")
    String getModuleIdentifierById(@Param("moduleId") Long moduleId);

    /**
     * 根据模块标识符获取模块ID
     * @param moduleIdentifier 模块标识符
     * @return 模块ID
     */
    @Select("SELECT id FROM t_data_module WHERE module_identifier = #{moduleIdentifier} AND is_del = false")
    Long getModuleIdByIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 查询使用指定数据模块下数据权限的角色列表
     * 用于V2版本删除功能中显示模块使用情况
     *
     * @param moduleIdentifier 模块标识
     * @return 角色列表
     */
    @Select("SELECT DISTINCT r.id, r.role_name " +
            "FROM t_role r " +
            "JOIN t_roles_data_permission rdp ON r.id = rdp.role_id " +
            "JOIN t_data_permission dp ON rdp.data_id = dp.id " +
            "WHERE dp.module_identifier = #{moduleIdentifier} " +
            "AND r.is_del = false " +
            "AND rdp.is_del = false " +
            "AND dp.is_del = false")
    List<RoleUsageVO> getRolesByDataModuleIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 根据模块标识获取数据模块名称
     * 用于V2版本删除功能中显示模块信息
     *
     * @param moduleIdentifier 模块标识
     * @return 模块名称
     */
    @Select("SELECT module_name FROM t_data_module " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false")
    String getDataModuleNameByIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 级联删除数据模块下所有数据权限的角色权限关联
     * 用于V2版本强制删除功能
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_roles_data_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE data_id IN (" +
            "  SELECT id FROM t_data_permission " +
            "  WHERE module_identifier = #{moduleIdentifier} AND is_del = false" +
            ")")
    void deleteDataModuleRolePermissions(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 级联删除数据模块下所有数据权限的操作配置
     * 用于V2版本强制删除功能
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_data_operate SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE data_identifier IN (" +
            "  SELECT data_identifier FROM t_data_permission " +
            "  WHERE module_identifier = #{moduleIdentifier} AND is_del = false" +
            ")")
    void deleteDataModuleOperates(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 级联删除数据模块下所有数据权限
     * 用于V2版本强制删除功能
     *
     * @param moduleIdentifier 模块标识
     */
    @Update("UPDATE t_data_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE module_identifier = #{moduleIdentifier}")
    void deleteDataPermissionsByModuleIdentifier(@Param("moduleIdentifier") String moduleIdentifier);

}
