-- 权限管理系统测试数据（修正版）
-- 生成时间：2025-01-20
-- 说明：根据开发文档中的正确表结构生成测试数据

-- ================================
-- 1. 组织架构数据（t_org_structure）
-- ================================

-- 根级别部门
INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time) VALUES
(1001, '总公司', NULL, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 一级部门
INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time) VALUES
(1002, '技术部', 1001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1003, '市场部', 1001, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1004, '人事部', 1001, 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1005, '财务部', 1001, 4, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 二级部门（技术部下属）
INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time) VALUES
(1006, '研发组', 1002, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1007, '测试组', 1002, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1008, '运维组', 1002, 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 二级部门（市场部下属）
INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time) VALUES
(1009, '销售组', 1003, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(1010, '推广组', 1003, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 2. 用户数据（t_user）
-- ================================

-- 管理员用户
INSERT INTO t_user (id, user_name, login_id, organ_affiliation, account, password, is_disable, is_del, create_time, modify_time) VALUES
(2001, '系统管理员', 'admin', 1001, 'admin', 'admin123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2002, '张三', 'zhangsan', 1006, 'zhangsan', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2003, '李四', 'lisi', 1006, 'lisi', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2004, '王五', 'wangwu', 1007, 'wangwu', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2005, '赵六', 'zhaoliu', 1008, 'zhaoliu', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2006, '钱七', 'qianqi', 1009, 'qianqi', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2007, '孙八', 'sunba', 1010, 'sunba', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2008, '周九', 'zhoujiu', 1004, 'zhoujiu', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2009, '吴十', 'wushi', 1005, 'wushi', 'password123', false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(2010, '郑十一', 'zhengshiyi', 1003, 'zhengshiyi', 'password123', true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 3. 角色数据（t_role）
-- ================================

INSERT INTO t_role (id, role_name, order_info, is_disable, is_del, create_time, modify_time) VALUES
(3001, '超级管理员', 1, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3002, '系统管理员', 2, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3003, '部门管理员', 3, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3004, '普通用户', 4, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3005, '只读用户', 5, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(3006, '测试角色', 6, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 4. 用户角色关联（t_perm_user_role）
-- ================================

INSERT INTO t_perm_user_role (id, user_id, role_id, order_info, is_del, create_time, modify_time) VALUES
-- 系统管理员拥有超级管理员角色
(4001, 2001, 3001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 张三拥有系统管理员角色
(4002, 2002, 3002, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 李四拥有部门管理员角色
(4003, 2003, 3003, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 王五拥有普通用户角色
(4004, 2004, 3004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 赵六拥有普通用户角色
(4005, 2005, 3004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 钱七拥有部门管理员角色
(4006, 2006, 3003, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 孙八拥有普通用户角色
(4007, 2007, 3004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 周九拥有只读用户角色
(4008, 2008, 3005, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
-- 吴十拥有只读用户角色
(4009, 2009, 3005, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 5. 菜单模块数据（t_menu_module）
-- ================================

INSERT INTO t_menu_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) VALUES
(5001, '系统管理', 'system', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(5002, '用户管理', 'user', 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(5003, '权限管理', 'permission', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(5004, '数据管理', 'data', 4, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 6. 菜单权限数据（t_menu_permission）
-- ================================

-- 系统管理模块菜单
INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES
(6001, '系统设置', 0, 'system', 1, false, 2, '/system/setting', '/system/setting', 'system:setting', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6002, '系统监控', 0, 'system', 2, false, 2, '/system/monitor', '/system/monitor', 'system:monitor', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6003, '系统日志', 0, 'system', 3, false, 2, '/system/log', '/system/log', 'system:log', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 用户管理模块菜单
INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES
(6004, '用户列表', 0, 'user', 1, false, 2, '/user/list', '/user/list', 'user:list', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6005, '用户新增', 6004, 'user', 1, false, 3, NULL, NULL, 'user:add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6006, '用户编辑', 6004, 'user', 2, false, 3, NULL, NULL, 'user:edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6007, '用户删除', 6004, 'user', 3, false, 3, NULL, NULL, 'user:delete', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 权限管理模块菜单
INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES
(6008, '角色管理', 0, 'permission', 1, false, 2, '/permission/role', '/permission/role', 'permission:role', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6009, '菜单管理', 0, 'permission', 2, false, 2, '/permission/menu', '/permission/menu', 'permission:menu', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6010, '部门管理', 0, 'permission', 3, false, 2, '/permission/dept', '/permission/dept', 'permission:dept', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 数据管理模块菜单
INSERT INTO t_menu_permission (id, name, pre_id, module_identifier, order_info, is_disable, menu_type, route_address, component_path, permission_identifier, is_del, create_time, modify_time) VALUES
(6011, '数据权限', 0, 'data', 1, false, 2, '/data/permission', '/data/permission', 'data:permission', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6012, '数据导入', 0, 'data', 2, false, 2, '/data/import', '/data/import', 'data:import', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(6013, '数据导出', 0, 'data', 3, false, 2, '/data/export', '/data/export', 'data:export', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 7. 数据模块数据（t_data_module）
-- ================================

INSERT INTO t_data_module (id, module_name, module_identifier, order_info, is_del, create_time, modify_time) VALUES
(7001, '用户数据', 'user_data', 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(7002, '部门数据', 'dept_data', 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(7003, '财务数据', 'finance_data', 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 8. 数据操作类型（t_data_operate）
-- ================================

INSERT INTO t_data_operate (module_identifier, data_type, operate_type, data_identifier, is_del, create_time, modify_time) VALUES
('user_data', 1, 1, 'user_view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('user_data', 1, 2, 'user_add', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('user_data', 1, 3, 'user_edit', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('dept_data', 2, 1, 'dept_view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('dept_data', 2, 2, 'dept_manage', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('finance_data', 3, 1, 'finance_view', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('finance_data', 3, 5, 'finance_export', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 9. 数据权限（t_data_permission）
-- ================================

INSERT INTO t_data_permission (id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, data_identifier, create_time, modify_time) VALUES
(9001, '用户信息查看', NULL, 'user_data', 1, 1, false, false, 'user_view', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9002, '用户信息新增', NULL, 'user_data', 1, 2, false, false, 'user_add', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9003, '用户信息编辑', NULL, 'user_data', 1, 3, false, false, 'user_edit', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9004, '部门信息查看', NULL, 'dept_data', 2, 1, false, false, 'dept_view', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9005, '部门信息管理', NULL, 'dept_data', 2, 2, false, false, 'dept_manage', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9006, '财务数据查看', NULL, 'finance_data', 3, 1, false, false, 'finance_view', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(9007, '财务数据导出', NULL, 'finance_data', 3, 2, false, false, 'finance_export', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 10. 角色菜单权限关联（t_roles_menu_permission）
-- ================================

-- 超级管理员拥有所有菜单权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) VALUES
(10001, 3001, 'system', 6001, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10002, 3001, 'system', 6002, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10003, 3001, 'system', 6003, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10004, 3001, 'user', 6004, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10005, 3001, 'user', 6005, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10006, 3001, 'user', 6006, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10007, 3001, 'user', 6007, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10008, 3001, 'permission', 6008, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10009, 3001, 'permission', 6009, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10010, 3001, 'permission', 6010, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10011, 3001, 'data', 6011, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10012, 3001, 'data', 6012, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10013, 3001, 'data', 6013, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 系统管理员拥有用户管理和权限管理权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) VALUES
(10014, 3002, 'user', 6004, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10015, 3002, 'user', 6005, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10016, 3002, 'user', 6006, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10017, 3002, 'permission', 6008, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10018, 3002, 'permission', 6010, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 部门管理员拥有用户查看和部门管理权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) VALUES
(10019, 3003, 'user', 6004, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(10020, 3003, 'permission', 6010, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 普通用户只有基本查看权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) VALUES
(10021, 3004, 'user', 6004, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 只读用户只有查看权限
INSERT INTO t_roles_menu_permission (id, role_id, module_identifier, menu_id, is_del, create_time, modify_time) VALUES
(10022, 3005, 'user', 6004, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 11. 角色数据权限关联（t_roles_data_permission）
-- ================================

-- 超级管理员拥有所有数据权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time) VALUES
(11001, 3001, 'user_data', 1, 9001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11002, 3001, 'user_data', 1, 9002, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11003, 3001, 'user_data', 1, 9003, 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11004, 3001, 'dept_data', 2, 9004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11005, 3001, 'dept_data', 2, 9005, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11006, 3001, 'finance_data', 3, 9006, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11007, 3001, 'finance_data', 3, 9007, 5, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 系统管理员拥有用户和部门数据权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time) VALUES
(11008, 3002, 'user_data', 1, 9001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11009, 3002, 'user_data', 1, 9002, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11010, 3002, 'user_data', 1, 9003, 3, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11011, 3002, 'dept_data', 2, 9004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 部门管理员拥有部门数据权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time) VALUES
(11012, 3003, 'dept_data', 2, 9004, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11013, 3003, 'dept_data', 2, 9005, 2, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(11014, 3003, 'user_data', 1, 9001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 普通用户只有基本查看权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time) VALUES
(11015, 3004, 'user_data', 1, 9001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- 只读用户只有查看权限
INSERT INTO t_roles_data_permission (id, role_id, module_identifier, data_type, data_id, operate_type, is_del, create_time, modify_time) VALUES
(11016, 3005, 'user_data', 1, 9001, 1, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- ================================
-- 数据说明和使用指南
-- ================================

/*
测试数据概览：

1. 组织架构（10个部门）：
   - 总公司（1001）
     ├── 技术部（1002）
     │   ├── 研发组（1006）
     │   ├── 测试组（1007）
     │   └── 运维组（1008）
     ├── 市场部（1003）
     │   ├── 销售组（1009）
     │   └── 推广组（1010）
     ├── 人事部（1004）
     └── 财务部（1005）

2. 用户账户（10个用户）：
   - admin/admin123（系统管理员，超级管理员角色）
   - zhangsan/password123（张三，系统管理员角色）
   - lisi/password123（李四，部门管理员角色）
   - wangwu/password123（王五，普通用户角色）
   - zhaoliu/password123（赵六，普通用户角色）
   - qianqi/password123（钱七，部门管理员角色）
   - sunba/password123（孙八，普通用户角色）
   - zhoujiu/password123（周九，只读用户角色）
   - wushi/password123（吴十，只读用户角色）
   - zhengshiyi/password123（郑十一，已停用用户）

3. 角色权限（6个角色）：
   - 超级管理员（3001）：拥有所有权限
   - 系统管理员（3002）：拥有用户管理和权限管理权限
   - 部门管理员（3003）：拥有部门管理权限
   - 普通用户（3004）：拥有基本查看权限
   - 只读用户（3005）：只有查看权限
   - 测试角色（3006）：已停用角色

4. 菜单权限（13个菜单）：
   - 系统管理：系统设置、系统监控、系统日志
   - 用户管理：用户列表、用户新增、用户编辑、用户删除
   - 权限管理：角色管理、菜单管理、部门管理
   - 数据管理：数据权限、数据导入、数据导出

5. 数据权限（7个数据权限）：
   - 用户数据：查看、新增、编辑
   - 部门数据：查看、管理
   - 财务数据：查看、导出

使用建议：
1. 使用admin账户登录测试超级管理员功能
2. 使用zhangsan账户测试系统管理员功能
3. 使用lisi账户测试部门管理员功能
4. 使用wangwu账户测试普通用户功能
5. 可以通过修改用户的角色关联来测试不同权限组合

注意事项：
1. 所有密码都是明文存储，生产环境请使用加密
2. ID使用简单数字，生产环境建议使用雪花算法
3. 时间戳使用CURRENT_TIMESTAMP，会根据执行时间自动设置
4. 可以根据实际需要调整数据内容和数量
*/

-- 执行完成提示
SELECT '测试数据插入完成！' AS message,
       '总共插入了约110条测试数据，涵盖组织架构、用户、角色、菜单、权限等完整数据' AS description,
       '数据已按照开发文档中的正确表结构生成，可以安全使用' AS note;
