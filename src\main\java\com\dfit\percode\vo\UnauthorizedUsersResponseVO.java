package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 未授权用户查询响应VO类
 * 返回部门树形结构，每个部门包含未授权用户
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UnauthorizedUsersResponseVO", description = "未授权用户查询响应")
public class UnauthorizedUsersResponseVO {

    @ApiModelProperty("部门树形结构，包含未授权用户")
    private List<DepartmentTreeVO> departmentTree;
}
