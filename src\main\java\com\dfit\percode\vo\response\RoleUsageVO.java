package com.dfit.percode.vo.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 角色使用信息VO
 * 用于删除功能中显示使用该权限的角色信息
 *
 * <AUTHOR>
 * @date 2024-06-30
 */
@Data
@ApiModel(value = "角色使用信息VO", description = "角色使用信息")
public class RoleUsageVO {
    
    @ApiModelProperty("角色ID")
    private String roleId;
    
    @ApiModelProperty("角色名称")
    private String roleName;
}
