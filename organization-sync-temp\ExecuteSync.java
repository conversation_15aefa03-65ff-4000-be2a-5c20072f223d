package com.dfit.orgsync;

import com.dfit.orgsync.dto.SyncResult;
import com.dfit.orgsync.service.OrganizationSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 组织架构数据同步执行器
 * 可以作为独立应用运行，也可以集成到现有项目中
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.dfit.orgsync", "com.dfit.percode"})
@Slf4j
public class ExecuteSync implements CommandLineRunner {
    
    @Autowired
    private OrganizationSyncService organizationSyncService;
    
    public static void main(String[] args) {
        System.out.println("=== 组织架构数据转换工具 ===");
        System.out.println("开始启动应用...");
        
        SpringApplication.run(ExecuteSync.class, args);
    }
    
    @Override
    public void run(String... args) throws Exception {
        System.out.println("\n=== 开始执行组织架构数据同步 ===");
        
        try {
            // 数据文件路径
            String filePath = "organization-sync-temp/department_sync_test.sql";
            
            System.out.println("数据文件路径: " + filePath);
            System.out.println("开始同步...\n");
            
            // 执行同步
            SyncResult result = organizationSyncService.syncOrganizationData(filePath);
            
            // 输出结果
            System.out.println("\n=== 同步完成 ===");
            System.out.println(result.generateDetailedReport());
            
            if (result.isSuccess()) {
                System.out.println("✅ 数据同步成功！");
                
                if (result.hasDataQualityIssues()) {
                    System.out.println("⚠️  发现数据质量问题，请检查日志");
                } else {
                    System.out.println("✅ 数据质量检查通过");
                }
            } else {
                System.out.println("❌ 数据同步失败: " + result.getErrorMessage());
                System.exit(1);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 执行过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
        
        System.out.println("\n=== 程序执行完成 ===");
        System.out.println("提示：如果同步成功，可以删除 organization-sync-temp 目录");
    }
}

/**
 * 简单的工具类，用于在现有项目中直接调用
 */
class SyncTool {
    
    /**
     * 在现有项目中调用此方法执行同步
     * 
     * @param organizationSyncService 注入的服务
     * @return 同步结果
     */
    public static SyncResult executeSync(OrganizationSyncService organizationSyncService) {
        String filePath = "organization-sync-temp/department_sync_test.sql";
        
        System.out.println("=== 开始组织架构数据同步 ===");
        System.out.println("文件路径: " + filePath);
        
        try {
            SyncResult result = organizationSyncService.syncOrganizationData(filePath);
            
            System.out.println("=== 同步结果 ===");
            System.out.println(result.generateDetailedReport());
            
            return result;
            
        } catch (Exception e) {
            System.err.println("同步失败: " + e.getMessage());
            throw new RuntimeException("数据同步失败", e);
        }
    }
    
    /**
     * 快速测试方法
     */
    public static void quickTest(OrganizationSyncService organizationSyncService) {
        try {
            SyncResult result = executeSync(organizationSyncService);
            
            if (result.isSuccess()) {
                System.out.println("✅ 快速测试通过");
            } else {
                System.out.println("❌ 快速测试失败");
            }
            
        } catch (Exception e) {
            System.out.println("❌ 快速测试异常: " + e.getMessage());
        }
    }
}
