package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 组织架构表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Getter
@Setter
@TableName("t_org_structure")
@ApiModel(value = "TOrgStructure对象", description = "组织架构表")
public class TOrgStructure implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty("组织名称")
    private String organName;

    @ApiModelProperty("父ID，根节点为空")
    private Long preId;

    private Integer orderInfo;

    private Boolean isDel;

    @ApiModelProperty("创建时间")
    @TableField(select = false)
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @TableField(select = false)
    private LocalDateTime modifyTime;

    @ApiModelProperty("数据来源，1页面输入，2数据同步")
    private Integer dataSource;
}
