package com.dfit.percode.sync.config;

import com.dfit.percode.util.SnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 数据同步相关配置
 * 配置RestTemplate等同步所需的组件
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Configuration
public class SyncConfig {

    @Value("${external.system.timeout.connect:5000}")
    private int connectTimeout;
    
    @Value("${external.system.timeout.read:30000}")
    private int readTimeout;

    /**
     * 配置RestTemplate用于外部系统API调用
     * 设置连接超时和读取超时
     *
     * @return RestTemplate实例
     */
    @Bean
    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);

        return new RestTemplate(factory);
    }

    /**
     * 配置SnowflakeIdGenerator用于生成唯一ID
     * 设置工作机器ID和数据中心ID
     *
     * @return SnowflakeIdGenerator实例
     */
    @Bean
    public SnowflakeIdGenerator snowflakeIdGenerator() {
        // 使用固定的工作机器ID和数据中心ID
        // 在生产环境中，这些值应该从配置文件中读取或根据机器信息动态生成
        return new SnowflakeIdGenerator(1, 1);
    }
}
