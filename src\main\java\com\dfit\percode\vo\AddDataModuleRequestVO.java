package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增数据模块请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddDataModuleRequestVO", description = "新增数据模块请求参数")
public class AddDataModuleRequestVO {
    
    @ApiModelProperty(value = "模块名称", required = true, example = "用户数据模块")
    private String moduleName;
    
    @ApiModelProperty(value = "模块标识", required = true, example = "user_data_module")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "排序序号", required = false, example = "1")
    private Integer orderInfo;
}
