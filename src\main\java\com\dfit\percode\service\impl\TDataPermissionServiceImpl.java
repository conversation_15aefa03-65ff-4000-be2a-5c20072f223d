package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TDataPermission;
import com.dfit.percode.mapper.TDataPermissionMapper;
import com.dfit.percode.service.ITDataPermissionService;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.vo.*;
import com.dfit.percode.vo.response.RoleUsageVO;
import com.dfit.percode.vo.response.UsageInfoVO;
import com.dfit.percode.vo.response.UsageCheckResponseVO;
import com.dfit.percode.exception.UsageConflictException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * 数据权限服务实现类
 * 提供数据权限的CRUD操作功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class TDataPermissionServiceImpl extends ServiceImpl<TDataPermissionMapper, TDataPermission> implements ITDataPermissionService {

    @Autowired
    private TDataPermissionMapper dataPermissionMapper;

    /**
     * 分页查询数据权限列表
     * @param request 查询请求参数
     * @return 数据权限列表
     */
    @Override
    public List<DataPermissionListResponseVO> getDataPermissionList(DataPermissionListRequestVO request) {
        log.info("开始查询数据权限列表");

        // 计算分页偏移量
        if (request.getPageNum() != null && request.getPageSize() != null) {
            int offset = (request.getPageNum() - 1) * request.getPageSize();
            request.setPageNum(offset);
        }

        List<DataPermissionListResponseVO> resultList = dataPermissionMapper.getDataPermissionList(request);
        log.info("数据权限列表查询完成，共{}条记录", resultList.size());

        // 为每个数据权限设置操作权限
        for (DataPermissionListResponseVO dataPermission : resultList) {
            try {
                // 优先查询该数据权限自己的操作权限
                List<Integer> operateTypes = dataPermissionMapper.getDataOperateTypes(dataPermission.getDataIdentifier());

                // 如果数据权限没有自己的操作权限，则继承模块的操作权限
                if (operateTypes == null || operateTypes.isEmpty()) {
                    operateTypes = dataPermissionMapper.getModuleOperateTypes(dataPermission.getModuleIdentifier());
                    log.debug("数据权限 {} 继承模块操作权限: {}", dataPermission.getName(), operateTypes);
                } else {
                    log.debug("数据权限 {} 使用自定义操作权限: {}", dataPermission.getName(), operateTypes);
                }

                dataPermission.setOperateTypes(operateTypes);
            } catch (Exception e) {
                log.warn("获取数据权限 {} 的操作权限失败: {}", dataPermission.getName(), e.getMessage());
                dataPermission.setOperateTypes(new ArrayList<>());
            }
        }

        return resultList;
    }

    /**
     * 获取数据权限总数
     * @param request 查询请求参数
     * @return 总数
     */
    @Override
    public Integer getDataPermissionTotal(DataPermissionListRequestVO request) {
        return dataPermissionMapper.getDataPermissionTotal(request);
    }

    /**
     * 新增数据权限
     * @param request 新增请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDataPermission(AddDataPermissionRequestVO request) {
        log.info("开始新增数据权限");
        log.info("权限名称: {}, 数据标识: {}", request.getName(), request.getDataIdentifier());

        // 1. 验证必填字段
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            throw new RuntimeException("数据权限名称不能为空");
        }
        if (request.getDataIdentifier() == null || request.getDataIdentifier().trim().isEmpty()) {
            throw new RuntimeException("数据标识不能为空");
        }

        // 2. 检查数据标识是否已存在
        int existsCount = dataPermissionMapper.checkDataIdentifierExists(
                request.getDataIdentifier(), request.getModuleIdentifier());
        if (existsCount > 0) {
            log.error("数据标识已存在: {}", request.getDataIdentifier());
            throw new RuntimeException("数据标识已存在");
        }

        // 3. 检查模块是否存在
        int moduleExists = dataPermissionMapper.checkModuleExists(request.getModuleIdentifier());
        if (moduleExists == 0) {
            log.error("数据模块不存在: {}", request.getModuleIdentifier());
            throw new RuntimeException("数据模块不存在");
        }

        // 4. 生成ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long permissionId = idGenerator.generateId();

        // 5. 设置默认值
        Integer orderInfo = request.getOrderInfo();
        if (orderInfo == null) {
            orderInfo = 1;
        }
        Boolean isDisable = request.getIsDisable();
        if (isDisable == null) {
            isDisable = false;
        }

        // 6. 插入数据权限记录
        dataPermissionMapper.insertDataPermission(
                permissionId,
                request.getName(),
                request.getPreId(),
                request.getModuleIdentifier(),
                request.getDataType(),
                orderInfo,
                isDisable,
                request.getDataIdentifier()
        );

        log.info("数据权限新增成功，ID: {}", permissionId);
    }

    /**
     * 获取数据权限详情
     * @param request 详情请求参数
     * @return 数据权限详情
     */
    @Override
    public DataPermissionDetailResponseVO getDataPermissionDetail(DataPermissionDetailRequestVO request) {
        log.info("开始获取数据权限详情，ID: {}", request.getDataId());

        Long dataId = Long.parseLong(request.getDataId());
        DataPermissionDetailResponseVO detail = dataPermissionMapper.getDataPermissionDetail(dataId);

        if (detail == null) {
            log.error("数据权限不存在，ID: {}", dataId);
            throw new RuntimeException("数据权限不存在");
        }

        log.info("数据权限详情获取成功");
        return detail;
    }

    /**
     * 修改数据权限
     * @param request 修改请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDataPermission(UpdateDataPermissionRequestVO request) {
        log.info("开始修改数据权限，ID: {}", request.getId());

        // 1. 验证必填字段
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            throw new RuntimeException("数据权限名称不能为空");
        }

        Long dataId = Long.parseLong(request.getId());

        // 2. 检查数据权限是否存在
        int existsCount = dataPermissionMapper.checkDataPermissionExistsById(dataId);
        if (existsCount == 0) {
            log.error("数据权限不存在，ID: {}", dataId);
            throw new RuntimeException("数据权限不存在");
        }

        // 3. 如果修改了数据标识，检查新标识是否重复
        if (request.getDataIdentifier() != null && !request.getDataIdentifier().trim().isEmpty()) {
            int duplicateCount = dataPermissionMapper.checkDataIdentifierExistsExcludeId(
                    request.getDataIdentifier(), request.getModuleIdentifier(), dataId);
            if (duplicateCount > 0) {
                log.error("数据标识已存在: {}", request.getDataIdentifier());
                throw new RuntimeException("数据标识已存在");
            }
        }

        // 4. 更新数据权限
        dataPermissionMapper.updateDataPermission(
                dataId,
                request.getName(),
                request.getOrderInfo(),
                request.getIsDisable(),
                request.getDataIdentifier()
        );

        log.info("数据权限修改成功，ID: {}", dataId);
    }

    /**
     * 删除数据权限
     * @param request 删除请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDataPermission(DeleteDataPermissionRequestVO request) {
        log.info("开始删除数据权限，ID: {}", request.getDataId());

        Long dataId = Long.parseLong(request.getDataId());

        // 1. 检查数据权限是否存在
        int existsCount = dataPermissionMapper.checkDataPermissionExistsById(dataId);
        if (existsCount == 0) {
            log.error("数据权限不存在，ID: {}", dataId);
            throw new RuntimeException("数据权限不存在");
        }

        // 2. 检查是否被角色使用
        int usedCount = dataPermissionMapper.checkDataPermissionInUse(dataId);
        if (usedCount > 0) {
            log.error("数据权限正在被使用，无法删除，ID: {}", dataId);
            throw new RuntimeException("该数据权限正在被角色使用，无法删除");
        }

        // 3. 逻辑删除数据权限
        dataPermissionMapper.deleteDataPermissionById(dataId);

        log.info("数据权限删除成功，ID: {}", dataId);
    }

    /**
     * 插入测试数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertTestData() {
        log.info("开始插入数据权限测试数据");

        try {
            // 调用Mapper插入测试数据
            dataPermissionMapper.insertTestData();
            log.info("数据权限测试数据插入成功");

        } catch (Exception e) {
            log.error("插入数据权限测试数据失败", e);
            throw new RuntimeException("插入数据权限测试数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取数据权限树形结构
     * 按模块分组展示数据权限，类似菜单权限的树形结构
     *
     * @return 数据权限树形结构列表
     */
    @Override
    public List<DataPermissionTreeResponseVO> getDataPermissionTree() {
        log.info("开始获取数据权限树形结构");
        long startTime = System.currentTimeMillis();

        try {
            // 1. 获取所有模块信息
            List<DataPermissionTreeResponseVO> modules = dataPermissionMapper.getDataPermissionModules();
            log.info("查询到模块数量: {}", modules.size());

            // 2. 为每个模块设置操作权限和数据权限
            for (DataPermissionTreeResponseVO module : modules) {
                // 2.1 查询模块的操作权限（保留作为默认值）
                try {
                    List<Integer> moduleOperateTypes = dataPermissionMapper.getModuleOperateTypes(module.getModuleIdentifier());
                    module.setOperateTypes(moduleOperateTypes);
                    log.debug("模块 {} 的默认操作权限: {}", module.getModuleName(), moduleOperateTypes);
                } catch (Exception e) {
                    log.warn("获取模块 {} 的操作权限失败: {}", module.getModuleName(), e.getMessage());
                    module.setOperateTypes(new ArrayList<>());
                }

                // 2.2 查询模块下的数据权限
                List<DataPermissionTreeResponseVO.DataPermissionTreeItemVO> dataPermissions =
                    dataPermissionMapper.getDataPermissionsByModule(module.getModuleIdentifier());

                // 2.3 为每个数据权限设置其独立的操作权限
                for (DataPermissionTreeResponseVO.DataPermissionTreeItemVO dataItem : dataPermissions) {
                    try {
                        // 优先查询数据权限自己的操作权限
                        List<Integer> dataOperateTypes = dataPermissionMapper.getDataOperateTypes(dataItem.getDataIdentifier());

                        // 如果数据权限没有自己的操作权限，则继承模块的操作权限
                        if (dataOperateTypes == null || dataOperateTypes.isEmpty()) {
                            dataOperateTypes = module.getOperateTypes();
                            log.debug("数据权限 {} 继承模块操作权限: {}", dataItem.getDataName(), dataOperateTypes);
                        } else {
                            log.debug("数据权限 {} 使用自定义操作权限: {}", dataItem.getDataName(), dataOperateTypes);
                        }

                        dataItem.setOperateTypes(dataOperateTypes);
                    } catch (Exception e) {
                        log.warn("获取数据权限 {} 的操作权限失败: {}", dataItem.getDataName(), e.getMessage());
                        dataItem.setOperateTypes(new ArrayList<>());
                    }
                }

                module.setChildren(dataPermissions);
                log.debug("模块 {} 下的数据权限数量: {}",
                         module.getModuleName(), dataPermissions.size());
            }

            long endTime = System.currentTimeMillis();
            log.info("数据权限树形结构获取完成，耗时: {}ms，模块数量: {}",
                    endTime - startTime, modules.size());

            return modules;

        } catch (Exception e) {
            log.error("获取数据权限树形结构失败", e);
            throw new RuntimeException("获取数据权限树形结构失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取数据权限树形结构（优化版本）
     * 使用批量查询替代N+1查询，大幅提升性能
     * 参考TOrgStructureServiceImpl的优化实现模式
     *
     * @return 数据权限树形结构列表
     */
    @Override
    public List<DataPermissionTreeResponseVO> getDataPermissionTreeOptimized() {
        log.info("开始获取数据权限树形结构（优化版本）");
        long startTime = System.currentTimeMillis();

        try {
            // 第1步：查询所有模块
            log.debug("第1步：查询所有模块");
            List<DataPermissionTreeResponseVO> modules = dataPermissionMapper.getDataPermissionModules();
            log.info("查询到模块数量: {}", modules.size());

            if (modules.isEmpty()) {
                log.info("没有找到任何模块，返回空列表");
                return modules;
            }

            // 第2步：批量查询所有模块的操作权限
            log.debug("第2步：批量查询所有模块的操作权限");
            List<TDataPermissionMapper.ModuleOperateTypeVO> moduleOperateList =
                dataPermissionMapper.getAllModuleOperateTypes();

            // 构建模块操作权限映射
            Map<String, List<Integer>> moduleOperateMap = new HashMap<>();
            for (TDataPermissionMapper.ModuleOperateTypeVO item : moduleOperateList) {
                moduleOperateMap.computeIfAbsent(item.getModuleIdentifier(), k -> new ArrayList<>())
                    .add(item.getOperateType());
            }
            log.debug("模块操作权限映射构建完成，模块数量: {}", moduleOperateMap.size());

            // 第3步：批量查询所有数据权限
            log.debug("第3步：批量查询所有数据权限");
            Map<String, List<DataPermissionTreeResponseVO.DataPermissionTreeItemVO>> moduleDataMap = new HashMap<>();
            for (DataPermissionTreeResponseVO module : modules) {
                List<DataPermissionTreeResponseVO.DataPermissionTreeItemVO> dataPermissions =
                    dataPermissionMapper.getDataPermissionsByModule(module.getModuleIdentifier());
                moduleDataMap.put(module.getModuleIdentifier(), dataPermissions);
            }
            log.debug("数据权限查询完成");

            // 第4步：批量查询所有数据权限的操作权限
            log.debug("第4步：批量查询所有数据权限的操作权限");
            List<TDataPermissionMapper.DataOperateTypeVO> dataOperateList =
                dataPermissionMapper.getAllDataOperateTypes();

            // 构建数据权限操作权限映射
            Map<String, List<Integer>> dataOperateMap = new HashMap<>();
            for (TDataPermissionMapper.DataOperateTypeVO item : dataOperateList) {
                dataOperateMap.computeIfAbsent(item.getDataIdentifier(), k -> new ArrayList<>())
                    .add(item.getOperateType());
            }
            log.debug("数据权限操作权限映射构建完成，数据权限数量: {}", dataOperateMap.size());

            // 第5步：在内存中组装树形结构
            log.debug("第5步：在内存中组装树形结构");
            for (DataPermissionTreeResponseVO module : modules) {
                // 设置模块的操作权限
                List<Integer> moduleOperateTypes = moduleOperateMap.get(module.getModuleIdentifier());
                if (moduleOperateTypes == null) {
                    moduleOperateTypes = new ArrayList<>();
                }
                module.setOperateTypes(moduleOperateTypes);
                log.debug("模块 {} 的操作权限: {}", module.getModuleName(), moduleOperateTypes);

                // 获取模块下的数据权限
                List<DataPermissionTreeResponseVO.DataPermissionTreeItemVO> dataPermissions =
                    moduleDataMap.get(module.getModuleIdentifier());
                if (dataPermissions == null) {
                    dataPermissions = new ArrayList<>();
                }

                // 为每个数据权限设置操作权限
                for (DataPermissionTreeResponseVO.DataPermissionTreeItemVO dataItem : dataPermissions) {
                    // 优先使用数据权限自己的操作权限
                    List<Integer> dataOperateTypes = dataOperateMap.get(dataItem.getDataIdentifier());

                    // 如果数据权限没有自己的操作权限，则继承模块的操作权限
                    if (dataOperateTypes == null || dataOperateTypes.isEmpty()) {
                        dataOperateTypes = moduleOperateTypes;
                        log.debug("数据权限 {} 继承模块操作权限: {}", dataItem.getDataName(), dataOperateTypes);
                    } else {
                        log.debug("数据权限 {} 使用自定义操作权限: {}", dataItem.getDataName(), dataOperateTypes);
                    }

                    dataItem.setOperateTypes(dataOperateTypes);
                }

                module.setChildren(dataPermissions);
                log.debug("模块 {} 下的数据权限数量: {}", module.getModuleName(), dataPermissions.size());
            }

            long endTime = System.currentTimeMillis();
            log.info("数据权限树形结构获取完成（优化版本），耗时: {}ms，模块数量: {}，预计性能提升: 90%+",
                    endTime - startTime, modules.size());

            return modules;

        } catch (Exception e) {
            log.error("获取数据权限树形结构失败（优化版本）", e);
            throw new RuntimeException("获取数据权限树形结构失败（优化版本）: " + e.getMessage(), e);
        }
    }

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 删除数据权限 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除请求参数
     * @return 删除结果或使用情况信息
     */
    @Transactional(rollbackFor = Exception.class)
    public Object deleteDataPermissionV2(DeleteDataPermissionRequestVO request) {
        log.info("开始删除数据权限V2，ID: {}, 强制删除: {}", request.getDataId(), request.getForceDelete());

        Long dataId = Long.parseLong(request.getDataId());

        // 1. 检查数据权限是否存在
        int existsCount = dataPermissionMapper.checkDataPermissionExistsById(dataId);
        if (existsCount == 0) {
            log.error("数据权限不存在，ID: {}", dataId);
            throw new RuntimeException("数据权限不存在");
        }

        // 2. 如果不是强制删除，检查使用情况
        if (!Boolean.TRUE.equals(request.getForceDelete())) {
            List<RoleUsageVO> usedByRoles = dataPermissionMapper.getRolesByDataId(dataId);
            if (!usedByRoles.isEmpty()) {
                log.info("数据权限正在被角色使用，返回使用情况，ID: {}", dataId);

                // 构建使用情况响应
                UsageCheckResponseVO response = new UsageCheckResponseVO();
                response.setCanDirectDelete(false);

                UsageInfoVO usageInfo = new UsageInfoVO();
                usageInfo.setDataId(request.getDataId());
                usageInfo.setDataName(dataPermissionMapper.getDataPermissionNameById(dataId));
                usageInfo.setUsedByRoles(usedByRoles);
                usageInfo.setTotalRoleCount(usedByRoles.size());

                response.setUsageInfo(usageInfo);

                // 返回409状态码和使用情况信息
                throw new UsageConflictException("检测到使用情况，请确认是否强制删除", response);
            }
        }

        // 3. 执行删除（无使用情况或强制删除）
        if (Boolean.TRUE.equals(request.getForceDelete())) {
            // 强制删除：级联删除相关数据
            log.info("执行强制删除，数据权限ID: {}", dataId);

            // 删除角色权限关联
            dataPermissionMapper.deleteDataRolePermissions(dataId);

            // 删除数据操作配置
            dataPermissionMapper.deleteDataOperatesByDataId(dataId);
        }

        // 4. 逻辑删除数据权限
        dataPermissionMapper.deleteDataPermissionById(dataId);

        log.info("数据权限删除成功，ID: {}", dataId);
        return null; // 成功删除返回null
    }
}
