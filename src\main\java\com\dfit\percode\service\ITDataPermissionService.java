package com.dfit.percode.service;

import com.dfit.percode.entity.TDataPermission;
import com.dfit.percode.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 数据权限服务接口
 * 提供数据权限的CRUD操作功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface ITDataPermissionService extends IService<TDataPermission> {

    /**
     * 分页查询数据权限列表
     * @param request 查询请求参数
     * @return 数据权限列表
     */
    List<DataPermissionListResponseVO> getDataPermissionList(DataPermissionListRequestVO request);

    /**
     * 获取数据权限总数
     * @param request 查询请求参数
     * @return 总数
     */
    Integer getDataPermissionTotal(DataPermissionListRequestVO request);

    /**
     * 新增数据权限
     * @param request 新增请求参数
     */
    void addDataPermission(AddDataPermissionRequestVO request);

    /**
     * 获取数据权限详情
     * @param request 详情请求参数
     * @return 数据权限详情
     */
    DataPermissionDetailResponseVO getDataPermissionDetail(DataPermissionDetailRequestVO request);

    /**
     * 修改数据权限
     * @param request 修改请求参数
     */
    void updateDataPermission(UpdateDataPermissionRequestVO request);

    /**
     * 删除数据权限
     * @param request 删除请求参数
     */
    void deleteDataPermission(DeleteDataPermissionRequestVO request);

    /**
     * 插入测试数据
     */
    void insertTestData();

    /**
     * 获取数据权限树形结构
     * 按模块分组展示数据权限，类似菜单权限的树形结构
     *
     * @return 数据权限树形结构列表
     */
    List<DataPermissionTreeResponseVO> getDataPermissionTree();

    /**
     * 获取数据权限树形结构（优化版本）
     * 使用批量查询替代N+1查询，大幅提升性能
     * 按模块分组展示数据权限，类似菜单权限的树形结构
     *
     * @return 数据权限树形结构列表
     */
    List<DataPermissionTreeResponseVO> getDataPermissionTreeOptimized();
}
