package com.dfit.orgsync.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 部门同步数据传输对象
 * 对应 department_sync_test 表的数据结构
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentSyncDto {
    
    /**
     * 组织编码
     */
    private String orgCode;
    
    /**
     * 组织名称
     */
    private String orgName;
    
    /**
     * 父级编码
     */
    private String parentCode;
    
    /**
     * 组织全称（完整层级路径）
     */
    private String fullName;
    
    /**
     * 部门UUID
     */
    private String deptUuid;
    
    /**
     * 是否历史数据
     */
    private String isHistory;
    
    /**
     * 用户预定义字段14（D表示删除）
     */
    private String userPredef14;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 同步日期
     */
    private LocalDate syncDate;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 检查是否为删除记录
     */
    public boolean isDeleted() {
        return "D".equals(userPredef14);
    }
    
    /**
     * 检查是否有效记录
     */
    public boolean isValid() {
        return fullName != null && !fullName.trim().isEmpty() && !isDeleted();
    }
}
