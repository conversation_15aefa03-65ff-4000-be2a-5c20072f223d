package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 更新用户信息请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持更新用户基本信息和角色分配
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UpdateUserRequestVO", description = "更新用户信息请求参数")
public class UpdateUserRequestVO {

    @ApiModelProperty(value = "用户ID", required = true, example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户姓名", required = true, example = "张三")
    private String userName;

    @ApiModelProperty(value = "登录账号", example = "zhang001")
    private String account;

    @ApiModelProperty(value = "组织归属ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organAffiliation;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "密码（可选，不填则不修改密码）", example = "newPassword123")
    private String password;

    @ApiModelProperty(value = "分配的角色列表", required = true)
    private List<RoleInfoVO> roles;
}
