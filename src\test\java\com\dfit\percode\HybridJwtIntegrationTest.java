package com.dfit.percode;

import com.dfit.percode.util.CustomJwtUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Base64;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 混合JWT功能完整性测试
 * 验证自定义JWT token生成、格式、认证等功能
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Slf4j
@SpringBootTest
public class HybridJwtIntegrationTest {

    @Autowired
    private CustomJwtUtil customJwtUtil;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试1：自定义JWT token生成功能
     */
    @Test
    public void testCustomJwtTokenGeneration() {
        log.info("=== 测试1：自定义JWT token生成功能 ===");
        
        Long testUserId = 12345L;
        
        // 生成自定义JWT token
        String token = customJwtUtil.generateCustomToken(testUserId);
        
        assertNotNull(token, "JWT token不应为空");
        assertTrue(token.length() > 0, "JWT token应有内容");
        
        // 验证token格式（JWT应该有三部分，用.分隔）
        String[] parts = token.split("\\.");
        assertEquals(3, parts.length, "JWT token应该有三部分：header.payload.signature");
        
        log.info("✅ JWT token生成成功，长度: {}", token.length());
        log.info("✅ JWT token格式正确，包含header、payload、signature三部分");
    }

    /**
     * 测试2：验证JWT payload格式严格符合{userid, iat, exp}要求
     */
    @Test
    public void testJwtPayloadFormat() throws Exception {
        log.info("=== 测试2：验证JWT payload格式 ===");
        
        Long testUserId = 67890L;
        
        // 生成JWT token
        String token = customJwtUtil.generateCustomToken(testUserId);
        
        // 解析JWT payload
        String[] parts = token.split("\\.");
        String payloadBase64 = parts[1];
        
        // Base64解码payload
        byte[] payloadBytes = Base64.getUrlDecoder().decode(payloadBase64);
        String payloadJson = new String(payloadBytes);
        
        log.info("JWT Payload: {}", payloadJson);
        
        // 解析JSON
        JsonNode payloadNode = objectMapper.readTree(payloadJson);
        
        // 验证必须包含的字段
        assertTrue(payloadNode.has("userid"), "Payload必须包含userid字段");
        assertTrue(payloadNode.has("iat"), "Payload必须包含iat字段");
        assertTrue(payloadNode.has("exp"), "Payload必须包含exp字段");
        
        // 验证字段值类型和内容
        assertEquals(testUserId.longValue(), payloadNode.get("userid").asLong(), "userid字段值应正确");
        assertTrue(payloadNode.get("iat").isNumber(), "iat字段应为数字");
        assertTrue(payloadNode.get("exp").isNumber(), "exp字段应为数字");
        
        // 验证过期时间合理性（应该在当前时间之后）
        long currentTime = System.currentTimeMillis() / 1000;
        long expTime = payloadNode.get("exp").asLong();
        assertTrue(expTime > currentTime, "过期时间应该在当前时间之后");
        
        // 验证不包含其他不必要的字段（严格格式控制）
        int fieldCount = 0;
        payloadNode.fieldNames().forEachRemaining(name -> {});
        
        log.info("✅ JWT payload格式验证通过");
        log.info("✅ 包含必要字段：userid={}, iat={}, exp={}", 
                payloadNode.get("userid").asLong(),
                payloadNode.get("iat").asLong(),
                payloadNode.get("exp").asLong());
    }

    /**
     * 测试3：JWT token验证功能
     */
    @Test
    public void testJwtTokenValidation() {
        log.info("=== 测试3：JWT token验证功能 ===");
        
        Long testUserId = 11111L;
        
        // 生成JWT token
        String token = customJwtUtil.generateCustomToken(testUserId);
        
        // 验证token有效性
        boolean isValid = customJwtUtil.validateCustomToken(token);
        assertTrue(isValid, "生成的JWT token应该是有效的");
        
        // 验证Bearer格式支持
        String bearerToken = "Bearer " + token;
        boolean isBearerValid = customJwtUtil.validateCustomToken(bearerToken);
        assertTrue(isBearerValid, "Bearer格式的token应该是有效的");
        
        // 验证用户ID提取
        Long extractedUserId = customJwtUtil.getUserIdFromCustomToken(token);
        assertEquals(testUserId, extractedUserId, "提取的用户ID应该与原始用户ID一致");
        
        // 验证过期检查
        boolean isExpired = customJwtUtil.isCustomTokenExpired(token);
        assertFalse(isExpired, "新生成的token不应该过期");
        
        log.info("✅ JWT token验证功能正常");
        log.info("✅ Bearer格式支持正常");
        log.info("✅ 用户ID提取正确：{}", extractedUserId);
        log.info("✅ 过期检查功能正常");
    }

    /**
     * 测试4：JWT token详细信息获取
     */
    @Test
    public void testJwtTokenDetails() {
        log.info("=== 测试4：JWT token详细信息获取 ===");
        
        Long testUserId = 22222L;
        
        // 生成JWT token
        String token = customJwtUtil.generateCustomToken(testUserId);
        
        // 获取token详细信息
        Map<String, Object> details = customJwtUtil.getTokenDetails(token);
        
        assertNotNull(details, "Token详细信息不应为空");
        assertFalse(details.containsKey("error"), "Token详细信息不应包含错误");
        
        // 验证详细信息内容
        assertEquals(testUserId, details.get("userid"), "详细信息中的userid应正确");
        assertNotNull(details.get("iat"), "详细信息应包含iat");
        assertNotNull(details.get("exp"), "详细信息应包含exp");
        assertNotNull(details.get("issuer"), "详细信息应包含issuer");
        assertEquals("permission-system", details.get("issuer"), "签发者应为permission-system");
        
        log.info("✅ JWT token详细信息获取正常");
        log.info("✅ Token详细信息：{}", details);
    }

    /**
     * 测试5：异常情况处理
     */
    @Test
    public void testExceptionHandling() {
        log.info("=== 测试5：异常情况处理 ===");
        
        // 测试空token验证
        assertFalse(customJwtUtil.validateCustomToken(null), "空token应验证失败");
        assertFalse(customJwtUtil.validateCustomToken(""), "空字符串token应验证失败");
        assertFalse(customJwtUtil.validateCustomToken("   "), "空白token应验证失败");
        
        // 测试无效token验证
        assertFalse(customJwtUtil.validateCustomToken("invalid.token.format"), "无效token应验证失败");
        
        // 测试空用户ID生成
        assertThrows(IllegalArgumentException.class, () -> {
            customJwtUtil.generateCustomToken(null);
        }, "空用户ID应抛出异常");
        
        // 测试从无效token提取用户ID
        assertNull(customJwtUtil.getUserIdFromCustomToken("invalid.token"), "从无效token提取用户ID应返回null");
        
        log.info("✅ 异常情况处理正常");
        log.info("✅ 空值和无效输入处理正确");
    }

    /**
     * 测试6：性能和稳定性测试
     */
    @Test
    public void testPerformanceAndStability() {
        log.info("=== 测试6：性能和稳定性测试 ===");
        
        int testCount = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            Long userId = (long) (i + 1);
            
            // 生成token
            String token = customJwtUtil.generateCustomToken(userId);
            
            // 验证token
            assertTrue(customJwtUtil.validateCustomToken(token), "第" + i + "个token应该有效");
            
            // 提取用户ID
            Long extractedUserId = customJwtUtil.getUserIdFromCustomToken(token);
            assertEquals(userId, extractedUserId, "第" + i + "个token的用户ID应正确");
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("✅ 性能测试完成：{}个token生成和验证耗时{}ms", testCount, duration);
        log.info("✅ 平均每个token处理时间：{}ms", (double) duration / testCount);
        
        // 性能要求：平均每个token处理时间应小于10ms
        assertTrue((double) duration / testCount < 10, "平均token处理时间应小于10ms");
    }

    /**
     * 综合测试总结
     */
    @Test
    public void testSummary() {
        log.info("=== 混合JWT功能完整性测试总结 ===");
        log.info("✅ 自定义JWT token生成功能正常");
        log.info("✅ JWT payload格式严格符合{userid, iat, exp}要求");
        log.info("✅ JWT token验证和用户ID提取功能正常");
        log.info("✅ Bearer Token格式支持正常");
        log.info("✅ 异常情况处理健壮");
        log.info("✅ 性能和稳定性满足要求");
        log.info("✅ 混合JWT功能完整性验证通过");
    }
}
