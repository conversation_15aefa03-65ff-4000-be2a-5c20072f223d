-- =====================================================
-- 权限标识符格式转换脚本：下划线/冒号格式 → 点号格式
-- 基于 public626.sql 数据分析生成
-- 执行前请备份数据库！
-- =====================================================

-- 开始事务
BEGIN;

-- =====================================================
-- 第一部分：菜单权限标识符转换 (t_menu_permission.permission_identifier)
-- =====================================================

-- 1. 冒号格式转点号格式
UPDATE t_menu_permission SET permission_identifier = 'system.config.edit' WHERE permission_identifier = 'system:config:edit';
UPDATE t_menu_permission SET permission_identifier = 'user.dept' WHERE permission_identifier = 'user:dept';
UPDATE t_menu_permission SET permission_identifier = 'permission.data' WHERE permission_identifier = 'permission:data';
UPDATE t_menu_permission SET permission_identifier = 'dev.generator' WHERE permission_identifier = 'dev:generator';
UPDATE t_menu_permission SET permission_identifier = 'dev.swagger' WHERE permission_identifier = 'dev:swagger';
UPDATE t_menu_permission SET permission_identifier = 'analytics.user' WHERE permission_identifier = 'analytics:user';
UPDATE t_menu_permission SET permission_identifier = 'analytics.business' WHERE permission_identifier = 'analytics:business';

-- 2. 下划线格式转点号格式
UPDATE t_menu_permission SET permission_identifier = 'system.log.view' WHERE permission_identifier = 'system_log_view';
UPDATE t_menu_permission SET permission_identifier = 'system.config.view' WHERE permission_identifier = 'system_config_view';
UPDATE t_menu_permission SET permission_identifier = 'system.config' WHERE permission_identifier = 'system_config';
UPDATE t_menu_permission SET permission_identifier = 'permission.management.view' WHERE permission_identifier = 'permission_management_view';
UPDATE t_menu_permission SET permission_identifier = 'permission.role.view' WHERE permission_identifier = 'permission_role_view';
UPDATE t_menu_permission SET permission_identifier = 'permission.menu.view' WHERE permission_identifier = 'permission_menu_view';
UPDATE t_menu_permission SET permission_identifier = 'user.list.view' WHERE permission_identifier = 'user_list_view';
UPDATE t_menu_permission SET permission_identifier = 'user.item.add' WHERE permission_identifier = 'user_item_add';
UPDATE t_menu_permission SET permission_identifier = 'user.item.edit' WHERE permission_identifier = 'user_item_edit';
UPDATE t_menu_permission SET permission_identifier = 'user.item.delete' WHERE permission_identifier = 'user_item_delete';
UPDATE t_menu_permission SET permission_identifier = 'analytics.user.test' WHERE permission_identifier = 'analytics_user_test';
UPDATE t_menu_permission SET permission_identifier = 'system.config.test' WHERE permission_identifier = 'system_config_test';
UPDATE t_menu_permission SET permission_identifier = 'system.management.view' WHERE permission_identifier = 'system_management_view_123';

-- 3. 特殊格式转换
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.home' WHERE permission_identifier = 'RB_KB_HOME';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.knowledge' WHERE permission_identifier = 'RB_KB_KN';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.design' WHERE permission_identifier = 'RB_KB_DE';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.technical' WHERE permission_identifier = 'RB_KB_TE';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.personal' WHERE permission_identifier = 'RB_KB_PE';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.create' WHERE permission_identifier = 'RB_KB_CREATE';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.rename' WHERE permission_identifier = 'RB_KB_RENAME';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.move' WHERE permission_identifier = 'RB_KB_MOVE';
UPDATE t_menu_permission SET permission_identifier = 'rb.kb.delete' WHERE permission_identifier = 'RB_KB_DELETE';
UPDATE t_menu_permission SET permission_identifier = 'test.demo.test2' WHERE permission_identifier = 'test2_demo_test2';
UPDATE t_menu_permission SET permission_identifier = 'test.demo.btn1' WHERE permission_identifier = 'test1_demo_btn1';
UPDATE t_menu_permission SET permission_identifier = 'test.demo.test1' WHERE permission_identifier = 'test1_demo_test1';
UPDATE t_menu_permission SET permission_identifier = 'knowledge.article.view' WHERE permission_identifier = 'kn_article_view';
UPDATE t_menu_permission SET permission_identifier = 'knowledge.article.btn' WHERE permission_identifier = 'kn_article_btn';
UPDATE t_menu_permission SET permission_identifier = 'knowledge.article' WHERE permission_identifier = 'kn_article';

-- 4. 简单格式保持或规范化
UPDATE t_menu_permission SET permission_identifier = 'user.management' WHERE permission_identifier = 'user';
UPDATE t_menu_permission SET permission_identifier = 'dev.tools' WHERE permission_identifier = 'dev';
UPDATE t_menu_permission SET permission_identifier = 'analytics.dashboard' WHERE permission_identifier = 'analytics';
UPDATE t_menu_permission SET permission_identifier = 'test.basic' WHERE permission_identifier = 'test123';

-- =====================================================
-- 第二部分：数据权限标识符转换 (t_data_permission.data_identifier)
-- =====================================================

-- 1. 下划线格式转点号格式
UPDATE t_data_permission SET data_identifier = 'employee.salary.info' WHERE data_identifier = 'employee_salary_info1';
UPDATE t_data_permission SET data_identifier = 'employee.basic.info' WHERE data_identifier = 'employee_basic_info';
UPDATE t_data_permission SET data_identifier = 'employee.performance.data' WHERE data_identifier = 'employee_performance_data';
UPDATE t_data_permission SET data_identifier = 'employee.attendance.record' WHERE data_identifier = 'employee_attendance_record';
UPDATE t_data_permission SET data_identifier = 'department.basic.info' WHERE data_identifier = 'department_basic_info';
UPDATE t_data_permission SET data_identifier = 'department.budget.data' WHERE data_identifier = 'department_budget_data';
UPDATE t_data_permission SET data_identifier = 'department.staff.statistics' WHERE data_identifier = 'department_staff_statistics';
UPDATE t_data_permission SET data_identifier = 'department.performance.kpi' WHERE data_identifier = 'department_performance_kpi';
UPDATE t_data_permission SET data_identifier = 'project.basic.info' WHERE data_identifier = 'project_basic_info';
UPDATE t_data_permission SET data_identifier = 'project.progress.data' WHERE data_identifier = 'project_progress_data';
UPDATE t_data_permission SET data_identifier = 'project.cost.data' WHERE data_identifier = 'project_cost_data';
UPDATE t_data_permission SET data_identifier = 'project.quality.report' WHERE data_identifier = 'project_quality_report';
UPDATE t_data_permission SET data_identifier = 'customer.basic.profile' WHERE data_identifier = 'customer_basic_profile';
UPDATE t_data_permission SET data_identifier = 'customer.contact.record' WHERE data_identifier = 'customer_contact_record';
UPDATE t_data_permission SET data_identifier = 'customer.transaction.history' WHERE data_identifier = 'customer_transaction_history';
UPDATE t_data_permission SET data_identifier = 'customer.credit.rating' WHERE data_identifier = 'customer_credit_rating';
UPDATE t_data_permission SET data_identifier = 'contract.basic.info' WHERE data_identifier = 'contract_basic_info';
UPDATE t_data_permission SET data_identifier = 'contract.terms.content' WHERE data_identifier = 'contract_terms_content';
UPDATE t_data_permission SET data_identifier = 'contract.execution.status' WHERE data_identifier = 'contract_execution_status';
UPDATE t_data_permission SET data_identifier = 'contract.financial.data' WHERE data_identifier = 'contract_financial_data';
UPDATE t_data_permission SET data_identifier = 'business.operation.report' WHERE data_identifier = 'business_operation_report';
UPDATE t_data_permission SET data_identifier = 'financial.analysis.report' WHERE data_identifier = 'financial_analysis_report';
UPDATE t_data_permission SET data_identifier = 'hr.statistics.report' WHERE data_identifier = 'hr_statistics_report';
UPDATE t_data_permission SET data_identifier = 'management.decision.report' WHERE data_identifier = 'management_decision_report';
UPDATE t_data_permission SET data_identifier = 'process.file.data' WHERE data_identifier = 'process_file';
UPDATE t_data_permission SET data_identifier = 'standard.file.data' WHERE data_identifier = 'standard_file';
UPDATE t_data_permission SET data_identifier = 'knowledge.process.data' WHERE data_identifier = 'kn_process';

-- 2. 简单格式规范化
UPDATE t_data_permission SET data_identifier = 'article.data' WHERE data_identifier = 'article';
UPDATE t_data_permission SET data_identifier = 'art.data' WHERE data_identifier = 'art';
UPDATE t_data_permission SET data_identifier = 'test.data' WHERE data_identifier = 'aaa';
UPDATE t_data_permission SET data_identifier = 'test.numeric.data' WHERE data_identifier = '1_12_2';

-- =====================================================
-- 第三部分：模块标识符转换 (module_identifier字段)
-- =====================================================

-- 更新菜单模块表
UPDATE t_menu_module SET module_identifier = 'system.management' WHERE module_identifier = 'system_management';
UPDATE t_menu_module SET module_identifier = 'user.management' WHERE module_identifier = 'user_management';
UPDATE t_menu_module SET module_identifier = 'permission.management' WHERE module_identifier = 'permission_management';
UPDATE t_menu_module SET module_identifier = 'development.tools' WHERE module_identifier = 'development_tools';
UPDATE t_menu_module SET module_identifier = 'data.analytics' WHERE module_identifier = 'data_analytics';

-- 更新数据模块表
UPDATE t_data_module SET module_identifier = 'employee.data.module' WHERE module_identifier = 'employee_data_module';
UPDATE t_data_module SET module_identifier = 'department.data.module' WHERE module_identifier = 'department_data_module';
UPDATE t_data_module SET module_identifier = 'project.data.module' WHERE module_identifier = 'project_data_module';
UPDATE t_data_module SET module_identifier = 'customer.data.module' WHERE module_identifier = 'customer_data_module';
UPDATE t_data_module SET module_identifier = 'contract.data.module' WHERE module_identifier = 'contract_data_module';
UPDATE t_data_module SET module_identifier = 'report.data.module' WHERE module_identifier = 'report_data_module';
UPDATE t_data_module SET module_identifier = 'rb.kb' WHERE module_identifier = 'rb_kb';

-- 更新菜单权限表中的模块标识符
UPDATE t_menu_permission SET module_identifier = 'system.management' WHERE module_identifier = 'system_management';
UPDATE t_menu_permission SET module_identifier = 'user.management' WHERE module_identifier = 'user_management';
UPDATE t_menu_permission SET module_identifier = 'permission.management' WHERE module_identifier = 'permission_management';
UPDATE t_menu_permission SET module_identifier = 'development.tools' WHERE module_identifier = 'development_tools';
UPDATE t_menu_permission SET module_identifier = 'data.analytics' WHERE module_identifier = 'data_analytics';
UPDATE t_menu_permission SET module_identifier = 'rb.kb' WHERE module_identifier = 'RB_KB';

-- 更新数据权限表中的模块标识符
UPDATE t_data_permission SET module_identifier = 'employee.data.module' WHERE module_identifier = 'employee_data_module';
UPDATE t_data_permission SET module_identifier = 'rb.kb' WHERE module_identifier = 'rb_kb';

-- 更新角色权限关联表中的模块标识符
UPDATE t_roles_menu_permission SET module_identifier = 'system.management' WHERE module_identifier = 'system_management';
UPDATE t_roles_menu_permission SET module_identifier = 'user.management' WHERE module_identifier = 'user_management';
UPDATE t_roles_menu_permission SET module_identifier = 'permission.management' WHERE module_identifier = 'permission_management';
UPDATE t_roles_menu_permission SET module_identifier = 'development.tools' WHERE module_identifier = 'development_tools';
UPDATE t_roles_menu_permission SET module_identifier = 'data.analytics' WHERE module_identifier = 'data_analytics';
UPDATE t_roles_menu_permission SET module_identifier = 'rb.kb' WHERE module_identifier = 'RB_KB';

UPDATE t_roles_data_permission SET module_identifier = 'employee.data.module' WHERE module_identifier = 'employee_data_module';
UPDATE t_roles_data_permission SET module_identifier = 'department.data.module' WHERE module_identifier = 'department_data_module';
UPDATE t_roles_data_permission SET module_identifier = 'project.data.module' WHERE module_identifier = 'project_data_module';
UPDATE t_roles_data_permission SET module_identifier = 'customer.data.module' WHERE module_identifier = 'customer_data_module';
UPDATE t_roles_data_permission SET module_identifier = 'contract.data.module' WHERE module_identifier = 'contract_data_module';
UPDATE t_roles_data_permission SET module_identifier = 'report.data.module' WHERE module_identifier = 'report_data_module';

UPDATE t_data_operate SET module_identifier = 'employee.data.module' WHERE module_identifier = 'employee_data_module';
UPDATE t_data_operate SET module_identifier = 'department.data.module' WHERE module_identifier = 'department_data_module';
UPDATE t_data_operate SET module_identifier = 'project.data.module' WHERE module_identifier = 'project_data_module';
UPDATE t_data_operate SET module_identifier = 'customer.data.module' WHERE module_identifier = 'customer_data_module';
UPDATE t_data_operate SET module_identifier = 'contract.data.module' WHERE module_identifier = 'contract_data_module';
UPDATE t_data_operate SET module_identifier = 'report.data.module' WHERE module_identifier = 'report_data_module';

-- =====================================================
-- 第四部分：验证转换结果
-- =====================================================

-- 验证菜单权限标识符转换结果
SELECT 
    '菜单权限标识符格式统计' as category,
    CASE 
        WHEN permission_identifier LIKE '%.%.%' THEN '三段点号格式'
        WHEN permission_identifier LIKE '%.%' THEN '二段点号格式'
        WHEN permission_identifier LIKE '%_%' THEN '下划线格式(未转换)'
        WHEN permission_identifier LIKE '%:%' THEN '冒号格式(未转换)'
        ELSE '其他格式'
    END as format_type,
    COUNT(*) as count
FROM t_menu_permission 
WHERE is_del = false AND permission_identifier IS NOT NULL
GROUP BY format_type
ORDER BY count DESC;

-- 验证数据权限标识符转换结果
SELECT 
    '数据权限标识符格式统计' as category,
    CASE 
        WHEN data_identifier LIKE '%.%.%' THEN '三段点号格式'
        WHEN data_identifier LIKE '%.%' THEN '二段点号格式'
        WHEN data_identifier LIKE '%_%' THEN '下划线格式(未转换)'
        ELSE '其他格式'
    END as format_type,
    COUNT(*) as count
FROM t_data_permission 
WHERE is_del = false AND data_identifier IS NOT NULL
GROUP BY format_type
ORDER BY count DESC;

-- 验证模块标识符转换结果
SELECT 
    '模块标识符格式统计' as category,
    CASE 
        WHEN module_identifier LIKE '%.%' THEN '点号格式'
        WHEN module_identifier LIKE '%_%' THEN '下划线格式(未转换)'
        ELSE '其他格式'
    END as format_type,
    COUNT(*) as count
FROM t_menu_module 
WHERE is_del = false AND module_identifier IS NOT NULL
GROUP BY format_type
ORDER BY count DESC;

-- 检查是否还有未转换的标识符
SELECT 'permission_identifier' as field, permission_identifier as value, COUNT(*) as count
FROM t_menu_permission 
WHERE is_del = false 
  AND permission_identifier IS NOT NULL
  AND (permission_identifier LIKE '%_%' OR permission_identifier LIKE '%:%')
GROUP BY permission_identifier
UNION ALL
SELECT 'data_identifier' as field, data_identifier as value, COUNT(*) as count
FROM t_data_permission 
WHERE is_del = false 
  AND data_identifier IS NOT NULL
  AND data_identifier LIKE '%_%'
GROUP BY data_identifier
ORDER BY field, value;

-- =====================================================
-- 提交事务
-- =====================================================

-- 如果验证结果正确，取消注释下面这行来提交事务
-- COMMIT;

-- 如果需要回滚，使用下面这行
-- ROLLBACK;
