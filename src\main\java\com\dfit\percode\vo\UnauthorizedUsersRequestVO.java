package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部门用户查询请求VO类
 * 用于层级懒加载查询指定部门下的直属部门和用户
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UnauthorizedUsersRequestVO", description = "部门用户查询请求参数")
public class UnauthorizedUsersRequestVO {

    @ApiModelProperty(value = "角色ID", required = true, example = "123456789")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;

    @ApiModelProperty(value = "父部门ID，null表示查询顶级部门", example = "5002")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;
}
