package com.dfit.orgsync.mapper;

import com.dfit.orgsync.dto.OrganizationTreeNode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 组织架构同步数据访问接口
 */
@Mapper
public interface OrganizationSyncMapper {
    
    /**
     * 批量插入组织架构数据
     * 
     * @param list 组织节点列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<OrganizationTreeNode> list);
    
    /**
     * 根据数据来源删除记录
     * 
     * @param dataSource 数据来源（2=数据同步）
     * @return 删除的记录数
     */
    int deleteByDataSource(@Param("dataSource") Integer dataSource);
    
    /**
     * 根据数据来源统计记录数
     * 
     * @param dataSource 数据来源
     * @return 记录数量
     */
    int countByDataSource(@Param("dataSource") Integer dataSource);
    
    /**
     * 获取当前最大ID
     * 
     * @return 最大ID值
     */
    Long getMaxId();
    
    /**
     * 验证层级结构，统计各层级的部门数量
     * 
     * @param dataSource 数据来源
     * @return 层级统计信息列表
     */
    List<Map<String, Object>> validateHierarchy(@Param("dataSource") Integer dataSource);
    
    /**
     * 查找孤立节点（父节点不存在的节点）
     * 
     * @param dataSource 数据来源
     * @return 孤立节点列表
     */
    List<OrganizationTreeNode> findOrphanNodes(@Param("dataSource") Integer dataSource);
    
    /**
     * 查找重复名称的部门
     * 
     * @param dataSource 数据来源
     * @return 重复名称统计信息
     */
    List<Map<String, Object>> findDuplicateNames(@Param("dataSource") Integer dataSource);
    
    /**
     * 查询一级部门列表（pre_id = 0）
     * 
     * @param dataSource 数据来源
     * @return 一级部门列表
     */
    List<OrganizationTreeNode> findLevel1Departments(@Param("dataSource") Integer dataSource);
    
    /**
     * 根据ID查询组织节点
     * 
     * @param id 节点ID
     * @return 组织节点
     */
    OrganizationTreeNode findById(@Param("id") Long id);
    
    /**
     * 根据名称查询组织节点
     * 
     * @param organName 组织名称
     * @param dataSource 数据来源
     * @return 组织节点列表
     */
    List<OrganizationTreeNode> findByName(@Param("organName") String organName, 
                                         @Param("dataSource") Integer dataSource);
    
    /**
     * 查询指定节点的所有子节点
     * 
     * @param preId 父节点ID
     * @param dataSource 数据来源
     * @return 子节点列表
     */
    List<OrganizationTreeNode> findChildren(@Param("preId") Long preId, 
                                           @Param("dataSource") Integer dataSource);
    
    /**
     * 获取树的最大深度
     * 
     * @param dataSource 数据来源
     * @return 最大深度
     */
    Integer getMaxDepth(@Param("dataSource") Integer dataSource);
    
    /**
     * 检查数据完整性
     * 
     * @param dataSource 数据来源
     * @return 完整性检查结果
     */
    Map<String, Object> checkDataIntegrity(@Param("dataSource") Integer dataSource);
}
