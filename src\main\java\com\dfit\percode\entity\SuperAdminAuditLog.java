package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 超级管理员审计日志实体类
 * 用于记录超级管理员的关键操作和访问行为
 * 确保超级管理员的行为可追溯和审计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@TableName("t_super_admin_audit_log")
@ApiModel(value = "SuperAdminAuditLog对象", description = "超级管理员审计日志")
public class SuperAdminAuditLog {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("登录账号")
    private String loginAccount;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("操作类型")
    private String operationType;

    @ApiModelProperty("访问路径")
    private String accessPath;

    @ApiModelProperty("HTTP方法")
    private String httpMethod;

    @ApiModelProperty("操作描述")
    private String operationDescription;

    @ApiModelProperty("客户端IP地址")
    private String clientIp;

    @ApiModelProperty("User-Agent信息")
    private String userAgent;

    @ApiModelProperty("请求参数")
    private String requestParams;

    @ApiModelProperty("响应状态码")
    private Integer responseStatus;

    @ApiModelProperty("操作结果")
    private String operationResult;

    @ApiModelProperty("操作时间")
    private LocalDateTime operationTime;

    @ApiModelProperty("会话ID")
    private String sessionId;

    @ApiModelProperty("认证方式")
    private String authMethod;

    @ApiModelProperty("风险等级")
    private String riskLevel;

    @ApiModelProperty("备注信息")
    private String remarks;

    /**
     * 创建审计日志记录
     *
     * @param userId 用户ID
     * @param operationType 操作类型
     * @param accessPath 访问路径
     * @param clientIp 客户端IP
     * @return 审计日志对象
     */
    public static SuperAdminAuditLog createAuditLog(Long userId, String operationType, String accessPath, String clientIp) {
        SuperAdminAuditLog auditLog = new SuperAdminAuditLog();
        auditLog.setUserId(userId);
        auditLog.setOperationType(operationType);
        auditLog.setAccessPath(accessPath);
        auditLog.setClientIp(clientIp);
        auditLog.setOperationTime(LocalDateTime.now());
        auditLog.setRiskLevel("HIGH"); // 超级管理员操作默认为高风险
        return auditLog;
    }

    /**
     * 操作类型常量
     */
    public static class OperationType {
        public static final String LOGIN = "LOGIN";
        public static final String LOGOUT = "LOGOUT";
        public static final String PERMISSION_QUERY = "PERMISSION_QUERY";
        public static final String CONFIG_ACCESS = "CONFIG_ACCESS";
        public static final String SYSTEM_ACCESS = "SYSTEM_ACCESS";
        public static final String USER_MANAGEMENT = "USER_MANAGEMENT";
        public static final String ROLE_MANAGEMENT = "ROLE_MANAGEMENT";
        public static final String DATA_ACCESS = "DATA_ACCESS";
        public static final String TEST_ACCESS = "TEST_ACCESS";
        public static final String OTHER = "OTHER";
    }

    /**
     * 风险等级常量
     */
    public static class RiskLevel {
        public static final String LOW = "LOW";
        public static final String MEDIUM = "MEDIUM";
        public static final String HIGH = "HIGH";
        public static final String CRITICAL = "CRITICAL";
    }
}
