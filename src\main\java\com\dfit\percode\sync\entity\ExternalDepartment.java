package com.dfit.percode.sync.entity;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 外部系统部门实体类
 * 对应外部系统的department表结构
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class ExternalDepartment {
    
    /**
     * 主键ID
     */
    private Integer id;
    
    /**
     * 部门唯一标识符
     */
    private String deptUuid;
    
    /**
     * 组织代码
     */
    private String orgCode;
    
    /**
     * 组织名称
     */
    private String orgName;
    
    /**
     * 父级部门ID
     */
    private String parentId;
    
    /**
     * 父级部门代码
     */
    private String parentCode;
    
    /**
     * 部门全称
     */
    private String fullName;
    
    /**
     * 是否历史部门
     */
    private Integer isHistory;
    
    /**
     * 部门描述
     */
    private String description;
    
    /**
     * 传真
     */
    private String fax;
    
    /**
     * 网站地址
     */
    private String webAddress;
    
    /**
     * 部门经理
     */
    private String orgManager;
    
    /**
     * 邮政编码
     */
    private String postCode;
    
    /**
     * 自定义字段13
     */
    private String userPredef13;
    
    /**
     * 自定义字段14
     */
    private String userPredef14;
    
    /**
     * 自定义字段18
     */
    private String userPredef18;
    
    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 组织类型，如公司、部门、科室等
     */
    private String orgType;
    
    /**
     * 组织层级，如1=集团、2=公司、3=部门、4=科室
     */
    private Integer orgLevel;
    
    /**
     * 组织路径，存储从根节点到当前节点的完整路径
     */
    private String orgPath;
    
    /**
     * 完整组织代码，包含所有上级组织代码
     */
    private String fullOrgCode;

    /**
     * 部门子表数据（对应department_child表）
     */
    private List<DepartmentChild> children;
}
