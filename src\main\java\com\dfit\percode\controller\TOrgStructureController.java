package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITOrgStructureService;
import com.dfit.percode.vo.SearchOrgStructureRequestVO;
import com.dfit.percode.vo.SearchOrgStructureResponseVO;
import com.dfit.percode.vo.AddOrgStructureRequestVO;
import com.dfit.percode.vo.AddOrgStructureResponseVO;
import com.dfit.percode.vo.UpdateOrgStructureRequestVO;
import com.dfit.percode.vo.UpdateOrgStructureResponseVO;
import com.dfit.percode.vo.DeleteOrgStructureRequestVO;
import com.dfit.percode.vo.DeleteOrgStructureResponseVO;
import com.dfit.percode.vo.OrgStructureTreeRequestVO;
import com.dfit.percode.vo.OrgStructureTreeResponseVO;
import com.dfit.percode.vo.MoveOrgStructureRequestVO;
import com.dfit.percode.vo.MoveOrgStructureResponseVO;
import com.dfit.percode.userPerm.RoleEntity;
import com.dfit.percode.userPerm.UserTree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

// import javax.validation.Valid;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 组织架构表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@RestController
@Api(tags = "组织架构管理接口")
@RequestMapping("/org-structure")
public class TOrgStructureController {
    @Autowired
    ITOrgStructureService itOrgStructureService;

    @RequestMapping(value = "/choseUser",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "选择人员")
    public BaseResult choseUser() {
        BaseResult baseResult = new BaseResult();
        List<UserTree> directoryTree = itOrgStructureService.choseUser();
        baseResult.setMessage("SUCCESS");
        baseResult.setCode(200);
        baseResult.setData(directoryTree);
        return baseResult;
    }


    @RequestMapping(value = "/roleSelected",method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "用户角色下拉框")
    public BaseResult roleSelected() {
        BaseResult baseResult = new BaseResult();
        List<RoleEntity> roleList = itOrgStructureService.roleSelected();
        baseResult.setMessage("SUCCESS");
        baseResult.setCode(200);
        baseResult.setData(roleList);
        return baseResult;
    }

    /**
     * 搜索部门
     * 支持按组织名称模糊搜索、父部门ID精确匹配、是否包含已删除部门
     *
     * @param request 搜索请求参数
     * @return 统一返回格式，包含搜索结果列表
     */
    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "搜索部门", notes = "支持按组织名称模糊搜索、父部门ID精确匹配、是否包含已删除部门")
    public BaseResult searchOrgStructure(@RequestBody SearchOrgStructureRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层搜索部门
            List<SearchOrgStructureResponseVO> searchResults = itOrgStructureService.searchOrgStructure(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(searchResults);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("搜索部门失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 新增部门
     * 创建新的组织架构节点，支持父子层级关系
     *
     * @param request 新增部门请求参数
     * @return 统一返回格式，包含新增部门的详细信息
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增部门", notes = "创建新的组织架构节点，支持父子层级关系")
    public BaseResult addOrgStructure(@Valid @RequestBody AddOrgStructureRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增部门
            AddOrgStructureResponseVO addResult = itOrgStructureService.addOrgStructure(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(addResult);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("新增部门失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 修改部门
     * 主要用于部门重命名和排序调整
     *
     * @param request 修改部门请求参数
     * @return 统一返回格式，包含修改后的部门详细信息
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改部门", notes = "主要用于部门重命名和排序调整")
    public BaseResult updateOrgStructure(@RequestBody UpdateOrgStructureRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层修改部门
            UpdateOrgStructureResponseVO updateResult = itOrgStructureService.updateOrgStructure(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(updateResult);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("修改部门失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除部门
     * 支持级联删除子部门，逻辑删除方式
     *
     * @param request 删除部门请求参数
     * @return 统一返回格式，包含删除操作的详细信息
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除部门", notes = "支持级联删除子部门，逻辑删除方式")
    public BaseResult deleteOrgStructure(@RequestBody DeleteOrgStructureRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除部门
            DeleteOrgStructureResponseVO deleteResult = itOrgStructureService.deleteOrgStructure(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(deleteResult);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("删除部门失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取部门结构树
     * 用于移动部门时显示可选择的部门树形结构
     *
     * @param request 获取部门树请求参数
     * @return 统一返回格式，包含部门树形结构数据
     */
    @RequestMapping(value = "/tree", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取部门结构树", notes = "用于移动部门时显示可选择的部门树形结构")
    public BaseResult getOrgStructureTree(@RequestBody OrgStructureTreeRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取部门结构树
            List<OrgStructureTreeResponseVO> treeData = itOrgStructureService.getOrgStructureTree(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(treeData);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("获取部门结构树失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 移动部门
     * 将指定部门移动到新的父部门下，并重新排序
     *
     * @param request 移动部门请求参数
     * @return 统一返回格式，包含移动操作的详细结果信息
     */
    @RequestMapping(value = "/move", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "移动部门", notes = "将部门移动到新的父部门下，支持排序调整，防止循环引用")
    public BaseResult moveOrgStructure(@RequestBody MoveOrgStructureRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层移动部门
            MoveOrgStructureResponseVO moveResult = itOrgStructureService.moveOrgStructure(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(moveResult);

        } catch (Exception e) {
            // 异常处理
            baseResult.setCode(500);
            baseResult.setMessage("移动部门失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

}
