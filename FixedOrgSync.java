import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 修正版组织架构数据同步工具
 * 修复了层级拆解逻辑，严格按照56个一级部门列表进行匹配
 */
public class FixedOrgSync {
    
    // 数据库连接信息
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    // 严格的一级部门列表（您提供的56个）
    private static final Set<String> LEVEL_1_DEPARTMENTS = Set.of(
        "新材料科研究院（合署）", "公司办公室", "人力资源部", "企业文化部", "财务部",
        "党委办公室", "组织部", "党委工作部", "审计部", "集团领导", "风险合规部",
        "安全环保部", "纪委功公室（党风廉政办公室）", "集团战略发展部", 
        "江苏金珂水务有限公司", "工会", "印尼焦化项目部", "特钢事业部",
        "数字应用研究院（人工智能研完院）", "南京三金房地产开发有限公司",
        "南钢退休职工服务中心", "集国资产处置办公室", "江苏金贸钢宝电子商务有限公司2",
        "团委", "公司领导", "离京鑫智链科技信息有限公司2", "科技质量部",
        "数字应用研究院", "蔚蓝高科技集团", "战略运营部（产业发展研究院）",
        "物流中心", "能源动力事业部", "炼铁事业部", "保卫部", "新产业投资集团",
        "采购中心", "制造部", "板材事业部", "市场部", "江苏金凯节能环保投资控股有限公司",
        "印尼钢铁项目指挥部", "江苏南钢鑫洋供应链有限公司", "集团宿迁金鑫公司",
        "南京金智工程技术有限公司", "集团综合资产部", "证券部", "香港金腾公司",
        "南京钢铁集团国际经济贸易有限公司", "集团工会", "集团财务审计部",
        "集团宿迁金鑫靖江项目指挥部", "江苏金恒信息科技股份有限公司"
    );
    
    // 层级关键词（用于拆解子层级）
    private static final List<String> HIERARCHY_KEYWORDS = Arrays.asList(
        "厂", "车间", "科", "室", "班", "组", "队", "站", "中心", "部门"
    );
    
    public static void main(String[] args) {
        System.out.println("=== 修正版组织架构数据同步工具 ===");
        
        try {
            FixedOrgSync sync = new FixedOrgSync();
            SyncResult result = sync.executeSync("organization-sync-temp/department_sync_test.sql");
            
            System.out.println("\n=== 同步结果 ===");
            System.out.println("成功: " + result.success);
            System.out.println("原始数据: " + result.rawDataCount);
            System.out.println("清洗数据: " + result.cleanDataCount);
            System.out.println("树节点数: " + result.treeNodeCount);
            System.out.println("删除记录: " + result.deletedCount);
            System.out.println("插入记录: " + result.insertedCount);
            System.out.println("最终记录: " + result.finalCount);
            
            if (result.success) {
                System.out.println("✅ 数据同步成功！");
                
                // 验证一级部门数量
                if (result.level1Count == LEVEL_1_DEPARTMENTS.size()) {
                    System.out.println("✅ 一级部门数量正确: " + result.level1Count);
                } else {
                    System.out.println("⚠️  一级部门数量异常: 期望" + LEVEL_1_DEPARTMENTS.size() + "，实际" + result.level1Count);
                }
            } else {
                System.out.println("❌ 数据同步失败: " + result.errorMessage);
            }
            
        } catch (Exception e) {
            System.err.println("❌ 同步过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public SyncResult executeSync(String filePath) {
        SyncResult result = new SyncResult();
        result.startTime = LocalDateTime.now();
        
        Connection conn = null;
        
        try {
            // 1. 连接数据库
            System.out.println("连接数据库...");
            conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            conn.setAutoCommit(false);
            
            // 2. 解析SQL文件
            System.out.println("解析SQL文件: " + filePath);
            List<DepartmentData> rawData = parseSqlFile(filePath);
            result.rawDataCount = rawData.size();
            System.out.println("解析完成，原始数据: " + rawData.size() + " 条");
            
            // 3. 数据清洗
            System.out.println("数据清洗...");
            List<DepartmentData> cleanData = cleanData(rawData);
            result.cleanDataCount = cleanData.size();
            System.out.println("清洗完成，有效数据: " + cleanData.size() + " 条");
            
            // 4. 构建层级树（修正版）
            System.out.println("构建层级树（修正版）...");
            Map<String, OrgNode> treeNodes = buildCorrectTree(cleanData);
            result.treeNodeCount = treeNodes.size();
            System.out.println("层级树构建完成，节点数: " + treeNodes.size());
            
            // 5. 数据库操作
            System.out.println("数据库操作...");
            insertToDatabase(conn, treeNodes, result);
            
            // 6. 提交事务
            conn.commit();
            result.success = true;
            
        } catch (Exception e) {
            System.err.println("同步失败: " + e.getMessage());
            result.success = false;
            result.errorMessage = e.getMessage();
            
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    ex.printStackTrace();
                }
            }
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
        
        result.endTime = LocalDateTime.now();
        return result;
    }
    
    private List<DepartmentData> parseSqlFile(String filePath) throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
        List<DepartmentData> result = new ArrayList<>();
        
        Pattern pattern = Pattern.compile(
            "INSERT INTO `department_sync_test` VALUES \\(" +
            "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', " +
            "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)'\\)"
        );
        
        for (String line : lines) {
            if (line.trim().startsWith("INSERT INTO `department_sync_test`")) {
                Matcher matcher = pattern.matcher(line);
                if (matcher.find()) {
                    DepartmentData data = new DepartmentData();
                    data.orgCode = matcher.group(2);
                    data.orgName = matcher.group(3);
                    data.parentCode = matcher.group(4);
                    data.fullName = matcher.group(6);
                    data.userPredef14 = matcher.group(8);
                    
                    if (!"D".equals(data.userPredef14) && 
                        data.fullName != null && !data.fullName.trim().isEmpty()) {
                        result.add(data);
                    }
                }
            }
        }
        
        return result;
    }
    
    private List<DepartmentData> cleanData(List<DepartmentData> rawData) {
        Map<String, DepartmentData> uniqueData = new LinkedHashMap<>();
        
        for (DepartmentData data : rawData) {
            String fullName = data.fullName.trim();
            if (!uniqueData.containsKey(fullName)) {
                uniqueData.put(fullName, data);
            }
        }
        
        return new ArrayList<>(uniqueData.values());
    }
    
    /**
     * 修正版层级树构建算法
     * 核心逻辑：严格按照一级部门列表进行匹配和归类
     */
    private Map<String, OrgNode> buildCorrectTree(List<DepartmentData> cleanData) {
        Map<String, OrgNode> allNodes = new LinkedHashMap<>();
        
        // 第一步：创建所有一级部门节点（即使数据中没有）
        for (String level1Name : LEVEL_1_DEPARTMENTS) {
            OrgNode level1Node = new OrgNode();
            level1Node.organName = level1Name;
            level1Node.fullName = level1Name;
            level1Node.level = 1;
            level1Node.children = new ArrayList<>();
            allNodes.put(level1Name, level1Node);
        }
        
        System.out.println("创建了 " + LEVEL_1_DEPARTMENTS.size() + " 个一级部门节点");
        
        // 第二步：处理所有数据，将每条记录归类到正确的一级部门下
        int processedCount = 0;
        int matchedCount = 0;
        
        for (DepartmentData data : cleanData) {
            String fullName = data.fullName;
            processedCount++;
            
            // 检查是否为一级部门
            if (LEVEL_1_DEPARTMENTS.contains(fullName)) {
                // 这是一级部门，已经创建过了，跳过
                matchedCount++;
                continue;
            }
            
            // 查找这个部门属于哪个一级部门
            String belongsToLevel1 = findBelongsToLevel1(fullName);
            
            if (belongsToLevel1 != null) {
                matchedCount++;
                
                // 获取一级部门节点
                OrgNode level1Node = allNodes.get(belongsToLevel1);
                
                // 构建从一级部门到当前部门的层级路径
                List<String> hierarchy = buildHierarchyPath(belongsToLevel1, fullName);
                
                // 创建层级节点
                OrgNode currentParent = level1Node;
                
                for (int i = 1; i < hierarchy.size(); i++) { // 从1开始，跳过一级部门
                    String levelName = hierarchy.get(i);
                    
                    // 查找是否已存在该节点
                    OrgNode existingChild = null;
                    for (OrgNode child : currentParent.children) {
                        if (child.organName.equals(levelName)) {
                            existingChild = child;
                            break;
                        }
                    }
                    
                    if (existingChild == null) {
                        // 创建新节点
                        OrgNode newNode = new OrgNode();
                        newNode.organName = levelName;
                        newNode.fullName = (i == hierarchy.size() - 1) ? fullName : levelName;
                        newNode.level = i + 1;
                        newNode.children = new ArrayList<>();
                        newNode.parent = currentParent;
                        
                        currentParent.children.add(newNode);
                        allNodes.put(levelName, newNode);
                        
                        currentParent = newNode;
                    } else {
                        currentParent = existingChild;
                    }
                }
            } else {
                System.out.println("⚠️  无法归类的部门: " + fullName);
            }
            
            if (processedCount % 200 == 0) {
                System.out.println("处理进度: " + processedCount + "/" + cleanData.size() + 
                    "，匹配: " + matchedCount);
            }
        }
        
        System.out.println("数据处理完成: 总计" + processedCount + "条，匹配" + matchedCount + "条");
        
        return allNodes;
    }

    /**
     * 查找部门属于哪个一级部门
     * 使用字符串匹配算法
     */
    private String findBelongsToLevel1(String fullName) {
        // 方法1：直接包含匹配
        for (String level1 : LEVEL_1_DEPARTMENTS) {
            if (fullName.contains(level1)) {
                return level1;
            }
        }

        // 方法2：关键词匹配
        // 炼铁相关
        if (fullName.contains("炼铁") || fullName.contains("铁厂")) {
            return "炼铁事业部";
        }

        // 板材相关
        if (fullName.contains("板厂") || fullName.contains("宽厚板") || fullName.contains("中厚板") || fullName.contains("中板")) {
            return "板材事业部";
        }

        // 特钢相关
        if (fullName.contains("特钢") || fullName.contains("炼钢")) {
            return "特钢事业部";
        }

        // 能源动力相关
        if (fullName.contains("能源") || fullName.contains("动力") || fullName.contains("电力") || fullName.contains("供电")) {
            return "能源动力事业部";
        }

        // 物流相关
        if (fullName.contains("物流") || fullName.contains("运输") || fullName.contains("储运")) {
            return "物流中心";
        }

        // 制造相关
        if (fullName.contains("制造") || fullName.contains("机修") || fullName.contains("设备")) {
            return "制造部";
        }

        // 采购相关
        if (fullName.contains("采购") || fullName.contains("供应")) {
            return "采购中心";
        }

        // 科技质量相关
        if (fullName.contains("科技") || fullName.contains("质量") || fullName.contains("检测") || fullName.contains("实验")) {
            return "科技质量部";
        }

        // 安全环保相关
        if (fullName.contains("安全") || fullName.contains("环保")) {
            return "安全环保部";
        }

        // 人力资源相关
        if (fullName.contains("人力") || fullName.contains("人事")) {
            return "人力资源部";
        }

        // 财务相关
        if (fullName.contains("财务") || fullName.contains("会计")) {
            return "财务部";
        }

        // 市场相关
        if (fullName.contains("市场") || fullName.contains("销售") || fullName.contains("营销")) {
            return "市场部";
        }

        // 审计相关
        if (fullName.contains("审计")) {
            return "审计部";
        }

        // 保卫相关
        if (fullName.contains("保卫") || fullName.contains("安保")) {
            return "保卫部";
        }

        // 工会相关
        if (fullName.contains("工会")) {
            return "工会";
        }

        // 党委相关
        if (fullName.contains("党委")) {
            return "党委办公室";
        }

        // 组织部相关
        if (fullName.contains("组织部")) {
            return "组织部";
        }

        // 纪委相关
        if (fullName.contains("纪委")) {
            return "纪委功公室（党风廉政办公室）";
        }

        // 企业文化相关
        if (fullName.contains("企业文化") || fullName.contains("文化")) {
            return "企业文化部";
        }

        // 办公室相关
        if (fullName.contains("办公室") && !fullName.contains("党委") && !fullName.contains("纪委")) {
            return "公司办公室";
        }

        return null; // 无法匹配
    }

    /**
     * 构建从一级部门到目标部门的层级路径
     */
    private List<String> buildHierarchyPath(String level1Name, String fullName) {
        List<String> path = new ArrayList<>();
        path.add(level1Name);

        // 如果就是一级部门，直接返回
        if (level1Name.equals(fullName)) {
            return path;
        }

        // 移除一级部门名称，获取剩余部分
        String remaining = fullName;
        if (fullName.startsWith(level1Name)) {
            remaining = fullName.substring(level1Name.length());
        }

        // 使用关键词拆解剩余部分
        for (String keyword : HIERARCHY_KEYWORDS) {
            int pos = remaining.indexOf(keyword);
            if (pos > 0) {
                String levelName = remaining.substring(0, pos + keyword.length());
                if (!levelName.trim().isEmpty()) {
                    path.add(level1Name + levelName);
                    remaining = remaining.substring(pos + keyword.length());

                    if (remaining.isEmpty()) {
                        break;
                    }
                }
            }
        }

        // 如果还有剩余，最后一级使用完整名称
        if (!remaining.isEmpty() || path.size() == 1) {
            path.add(fullName);
        }

        return path;
    }

    /**
     * 插入数据库
     */
    private void insertToDatabase(Connection conn, Map<String, OrgNode> treeNodes, SyncResult result) throws SQLException {
        // 删除现有同步数据
        PreparedStatement deleteStmt = conn.prepareStatement("DELETE FROM t_org_structure WHERE data_source = ?");
        deleteStmt.setInt(1, 2);
        result.deletedCount = deleteStmt.executeUpdate();
        deleteStmt.close();
        System.out.println("删除现有同步数据: " + result.deletedCount + " 条");

        // 获取最大ID
        PreparedStatement maxIdStmt = conn.prepareStatement("SELECT COALESCE(MAX(id), 2000000000) FROM t_org_structure");
        ResultSet rs = maxIdStmt.executeQuery();
        long currentId = 2000000000L;
        if (rs.next()) {
            currentId = Math.max(rs.getLong(1), 2000000000L);
        }
        rs.close();
        maxIdStmt.close();

        // 分配ID并插入
        List<OrgNode> insertNodes = new ArrayList<>();

        // 只处理一级部门（父节点为null的节点）
        List<OrgNode> rootNodes = new ArrayList<>();
        for (OrgNode node : treeNodes.values()) {
            if (node.parent == null && LEVEL_1_DEPARTMENTS.contains(node.organName)) {
                rootNodes.add(node);
            }
        }

        rootNodes.sort(Comparator.comparing(node -> node.organName));
        System.out.println("找到 " + rootNodes.size() + " 个一级部门");

        // 递归分配ID
        for (OrgNode root : rootNodes) {
            assignIds(root, currentId, insertNodes);
            currentId += countNodes(root);
        }

        // 批量插入
        PreparedStatement insertStmt = conn.prepareStatement(
            "INSERT INTO t_org_structure (id, organ_name, pre_id, order_info, is_del, create_time, modify_time, data_source) " +
            "VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
        );

        LocalDateTime now = LocalDateTime.now();
        int level1Count = 0;

        for (OrgNode node : insertNodes) {
            insertStmt.setLong(1, node.id);
            insertStmt.setString(2, node.organName);
            insertStmt.setLong(3, node.preId);
            insertStmt.setInt(4, 1);
            insertStmt.setBoolean(5, false);
            insertStmt.setTimestamp(6, Timestamp.valueOf(now));
            insertStmt.setTimestamp(7, Timestamp.valueOf(now));
            insertStmt.setInt(8, 2);

            if (node.preId == 0) {
                level1Count++;
            }

            insertStmt.addBatch();
            result.insertedCount++;

            if (result.insertedCount % 100 == 0) {
                insertStmt.executeBatch();
                System.out.println("批量插入进度: " + result.insertedCount + "/" + insertNodes.size());
            }
        }

        insertStmt.executeBatch();
        insertStmt.close();

        result.level1Count = level1Count;

        // 验证最终记录数
        PreparedStatement countStmt = conn.prepareStatement("SELECT COUNT(*) FROM t_org_structure WHERE data_source = ?");
        countStmt.setInt(1, 2);
        ResultSet countRs = countStmt.executeQuery();
        if (countRs.next()) {
            result.finalCount = countRs.getInt(1);
        }
        countRs.close();
        countStmt.close();

        System.out.println("数据库操作完成: 插入 " + result.insertedCount + " 条记录，其中一级部门 " + level1Count + " 个");
    }

    private long currentIdCounter = 0;

    private void assignIds(OrgNode node, long startId, List<OrgNode> insertNodes) {
        if (currentIdCounter == 0) {
            currentIdCounter = startId;
        }

        node.id = ++currentIdCounter;
        node.preId = node.parent != null ? node.parent.id : 0L;

        insertNodes.add(node);

        node.children.sort(Comparator.comparing(child -> child.organName));
        for (OrgNode child : node.children) {
            assignIds(child, startId, insertNodes);
        }
    }

    private int countNodes(OrgNode node) {
        int count = 1;
        for (OrgNode child : node.children) {
            count += countNodes(child);
        }
        return count;
    }

    // 内部数据类
    static class DepartmentData {
        String orgCode;
        String orgName;
        String parentCode;
        String fullName;
        String userPredef14;
    }

    static class OrgNode {
        long id;
        String organName;
        String fullName;
        long preId;
        int level;
        List<OrgNode> children;
        OrgNode parent;
    }

    static class SyncResult {
        LocalDateTime startTime;
        LocalDateTime endTime;
        boolean success;
        String errorMessage;
        int rawDataCount;
        int cleanDataCount;
        int treeNodeCount;
        int deletedCount;
        int insertedCount;
        int finalCount;
        int level1Count;
    }
}
