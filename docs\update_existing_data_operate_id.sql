-- =====================================================
-- 现有数据更新脚本：为data_operate_id字段填充值
-- 为t_roles_data_permission表中的现有记录生成data_operate_id值
-- 执行前请备份数据库！
-- =====================================================

-- 开始事务
BEGIN;

-- 更新现有数据（使用数字格式）
UPDATE t_roles_data_permission 
SET data_operate_id = CONCAT(data_id::varchar, '.', operate_type::varchar)
WHERE data_id IS NOT NULL AND operate_type IS NOT NULL;

-- 验证更新结果
SELECT 
    '更新统计' as category,
    COUNT(*) as total_records,
    COUNT(data_operate_id) as updated_records,
    COUNT(*) - COUNT(data_operate_id) as null_records
FROM t_roles_data_permission;

-- 验证数据格式正确性
SELECT 
    '格式验证' as category,
    data_id,
    operate_type,
    data_operate_id,
    CASE 
        WHEN data_operate_id = CONCAT(data_id::varchar, '.', operate_type::varchar) 
        THEN '格式正确' 
        ELSE '格式错误' 
    END as format_check
FROM t_roles_data_permission 
WHERE data_operate_id IS NOT NULL
LIMIT 10;

-- 按操作类型统计更新结果
SELECT 
    '操作类型统计' as category,
    operate_type,
    CASE operate_type
        WHEN 1 THEN '查看'
        WHEN 2 THEN '修改'
        WHEN 3 THEN '下载'
        WHEN 4 THEN '删除'
        ELSE '未知'
    END as operate_type_desc,
    COUNT(*) as count,
    COUNT(data_operate_id) as updated_count
FROM t_roles_data_permission 
GROUP BY operate_type
ORDER BY operate_type;

-- 检查是否有未更新的记录
SELECT 
    '未更新记录检查' as category,
    COUNT(*) as count
FROM t_roles_data_permission 
WHERE (data_id IS NOT NULL AND operate_type IS NOT NULL) 
  AND data_operate_id IS NULL;

-- 提交事务
COMMIT;

-- 如果需要回滚，使用下面这行
-- ROLLBACK;
