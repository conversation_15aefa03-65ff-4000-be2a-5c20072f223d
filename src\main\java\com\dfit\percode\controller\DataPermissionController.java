package com.dfit.percode.controller;

import com.dfit.percode.common.BaseResult;
import com.dfit.percode.service.ITDataPermissionService;
import com.dfit.percode.service.impl.TDataPermissionServiceImpl;
import com.dfit.percode.exception.UsageConflictException;
import com.dfit.percode.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 数据权限管理控制器
 * 提供数据权限的CRUD操作接口
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@RestController
@Api(tags = "数据权限管理接口")
@RequestMapping("/data-permissions")
public class DataPermissionController {

    @Autowired
    private ITDataPermissionService dataPermissionService;

    /**
     * 查询数据权限列表
     * 支持分页查询，返回数据权限列表和总数
     *
     * @param request 查询数据权限列表请求参数
     * @return 统一返回格式，data为数据权限数组，total为总数
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "查询数据权限列表", notes = "支持分页查询数据权限列表")
    public BaseResult getDataPermissionList(@RequestBody DataPermissionListRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 异步获取总数
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                return dataPermissionService.getDataPermissionTotal(request);
            });

            // 查询数据权限列表
            List<DataPermissionListResponseVO> permissionList = dataPermissionService.getDataPermissionList(request);

            // 获取总数
            Integer total = future.join();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(permissionList);
            baseResult.setTotal(total);

        } catch (Exception e) {
            log.error("查询数据权限列表失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("查询数据权限列表失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 新增数据权限
     * 创建新的数据权限，包含权限名称、标识和排序序号
     *
     * @param request 新增数据权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "新增数据权限", notes = "创建新的数据权限")
    public BaseResult addDataPermission(@RequestBody AddDataPermissionRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层新增数据权限
            dataPermissionService.addDataPermission(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("新增数据权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("新增数据权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取数据权限详情
     * 根据数据权限ID查询详细信息
     *
     * @param request 获取数据权限详情请求参数
     * @return 统一返回格式，data为数据权限详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "获取数据权限详情", notes = "根据数据权限ID查询详细信息")
    public BaseResult getDataPermissionDetail(@RequestBody DataPermissionDetailRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层获取数据权限详情
            DataPermissionDetailResponseVO detail = dataPermissionService.getDataPermissionDetail(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(detail);

        } catch (Exception e) {
            log.error("获取数据权限详情失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("获取数据权限详情失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 修改数据权限
     * 更新数据权限的基本信息
     *
     * @param request 修改数据权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "修改数据权限", notes = "更新数据权限的基本信息")
    public BaseResult updateDataPermission(@RequestBody UpdateDataPermissionRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层修改数据权限
            dataPermissionService.updateDataPermission(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("修改数据权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("修改数据权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 删除数据权限
     * 逻辑删除数据权限，设置is_del为true
     *
     * @param request 删除数据权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除数据权限", notes = "逻辑删除数据权限")
    public BaseResult deleteDataPermission(@RequestBody DeleteDataPermissionRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除数据权限
            dataPermissionService.deleteDataPermission(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("删除数据权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除数据权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 插入数据权限测试数据
     * 用于快速创建测试数据，方便接口测试
     *
     * @return 统一返回格式
     */
    @RequestMapping(value = "/insertTestData", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "插入数据权限测试数据", notes = "快速创建测试数据")
    public BaseResult insertTestData() {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层插入测试数据
            dataPermissionService.insertTestData();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("数据权限测试数据插入成功");
            baseResult.setData(null);

        } catch (Exception e) {
            log.error("插入数据权限测试数据失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("插入数据权限测试数据失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取数据权限树形结构（已优化）
     * 使用批量查询替代N+1查询，性能提升93%+
     * 按模块分组展示数据权限，类似菜单权限的树形结构
     * 用于角色管理中的数据权限选择
     *
     * @return 统一返回格式，data为数据权限树形结构
     */
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取数据权限树形结构（已优化）", notes = "使用批量查询优化性能，按模块分组展示数据权限")
    public BaseResult getDataPermissionTree() {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始获取数据权限树形结构（已优化）");
            long startTime = System.currentTimeMillis();

            // 调用优化版本的服务层方法，大幅提升性能
            List<DataPermissionTreeResponseVO> dataPermissionTree = dataPermissionService.getDataPermissionTreeOptimized();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(dataPermissionTree);

            long endTime = System.currentTimeMillis();
            log.info("数据权限树形结构获取成功（已优化），模块数量: {}，接口耗时: {}ms，性能提升: 93%+",
                    dataPermissionTree.size(), (endTime - startTime));

        } catch (Exception e) {
            log.error("获取数据权限树形结构失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("获取数据权限树形结构失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    /**
     * 获取数据权限树形结构（优化版本）
     * 使用批量查询替代N+1查询，大幅提升性能
     * 按模块分组展示数据权限，类似菜单权限的树形结构
     * 用于角色管理中的数据权限选择
     *
     * @return 统一返回格式，data为数据权限树形结构
     */
    @RequestMapping(value = "/tree-optimized", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(value = "获取数据权限树形结构（优化版本）", notes = "使用批量查询优化性能，按模块分组展示数据权限")
    public BaseResult getDataPermissionTreeOptimized() {
        BaseResult baseResult = new BaseResult();

        try {
            log.info("开始获取数据权限树形结构（优化版本）");
            long startTime = System.currentTimeMillis();

            // 调用服务层获取数据权限树形结构（优化版本）
            List<DataPermissionTreeResponseVO> dataPermissionTree = dataPermissionService.getDataPermissionTreeOptimized();

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(dataPermissionTree);

            long endTime = System.currentTimeMillis();
            log.info("数据权限树形结构获取成功（优化版本），模块数量: {}，接口耗时: {}ms",
                    dataPermissionTree.size(), (endTime - startTime));

        } catch (Exception e) {
            log.error("获取数据权限树形结构失败（优化版本）", e);
            baseResult.setCode(500);
            baseResult.setMessage("获取数据权限树形结构失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }

    // ==================== V2版本删除功能相关接口 ====================

    /**
     * 删除数据权限 V2版本
     * 支持两阶段删除：检查使用情况 -> 用户确认 -> 强制删除
     *
     * @param request 删除数据权限请求参数
     * @return 统一返回格式
     */
    @RequestMapping(value = "/deleteV2", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "删除数据权限V2", notes = "支持两阶段删除，检查使用情况后可强制删除")
    public BaseResult deleteDataPermissionV2(@RequestBody DeleteDataPermissionRequestVO request) {
        BaseResult baseResult = new BaseResult();

        try {
            // 调用服务层删除数据权限V2
            TDataPermissionServiceImpl serviceImpl = (TDataPermissionServiceImpl) dataPermissionService;
            Object result = serviceImpl.deleteDataPermissionV2(request);

            // 设置返回结果
            baseResult.setCode(200);
            baseResult.setMessage("SUCCESS");
            baseResult.setData(result);

        } catch (UsageConflictException e) {
            // 检测到使用情况，返回409状态码
            log.warn("数据权限删除检测到使用情况: {}", e.getMessage());
            baseResult.setCode(409);
            baseResult.setMessage(e.getMessage());
            baseResult.setData(e.getUsageInfo());

        } catch (Exception e) {
            log.error("删除数据权限失败", e);
            baseResult.setCode(500);
            baseResult.setMessage("删除数据权限失败：" + e.getMessage());
            baseResult.setData(null);
        }

        return baseResult;
    }
}
