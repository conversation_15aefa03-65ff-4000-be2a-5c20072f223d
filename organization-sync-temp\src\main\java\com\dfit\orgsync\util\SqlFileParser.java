package com.dfit.orgsync.util;

import com.dfit.orgsync.dto.DepartmentSyncDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * SQL文件解析工具
 * 用于解析 department_sync_test.sql 文件
 */
@Component
@Slf4j
public class SqlFileParser {
    
    /**
     * INSERT语句的正则表达式模式
     * 匹配 INSERT INTO `department_sync_test` VALUES (...) 格式
     */
    private static final String INSERT_PATTERN = 
        "INSERT INTO `department_sync_test` VALUES \\(" +
        "'([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', " +
        "'([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)'\\)";
    
    private final Pattern pattern = Pattern.compile(INSERT_PATTERN);
    
    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    private static final DateTimeFormatter DATE_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd");
    
    /**
     * 解析SQL文件
     * 
     * @param filePath SQL文件路径
     * @return 解析后的数据列表
     * @throws IOException 文件读取异常
     */
    public List<DepartmentSyncDto> parseSqlFile(String filePath) throws IOException {
        log.info("开始解析SQL文件: {}", filePath);
        
        List<DepartmentSyncDto> result = new ArrayList<>();
        List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
        
        int totalLines = lines.size();
        int processedLines = 0;
        int validRecords = 0;
        
        for (String line : lines) {
            processedLines++;
            
            if (line.trim().startsWith("INSERT INTO `department_sync_test`")) {
                DepartmentSyncDto dto = parseInsertLine(line);
                if (dto != null && dto.isValid()) {
                    result.add(dto);
                    validRecords++;
                }
            }
            
            // 每处理1000行输出一次进度
            if (processedLines % 1000 == 0) {
                log.info("解析进度: {}/{} 行，有效记录: {}", processedLines, totalLines, validRecords);
            }
        }
        
        log.info("解析完成，共处理 {} 行，提取 {} 条有效记录", processedLines, result.size());
        return result;
    }
    
    /**
     * 解析单行INSERT语句
     * 
     * @param line INSERT语句行
     * @return 解析后的DTO对象，解析失败返回null
     */
    private DepartmentSyncDto parseInsertLine(String line) {
        Matcher matcher = pattern.matcher(line);
        if (matcher.find()) {
            try {
                return new DepartmentSyncDto(
                    matcher.group(1), // orgCode
                    matcher.group(2), // orgName
                    matcher.group(3), // parentCode
                    matcher.group(4), // fullName
                    matcher.group(5), // deptUuid
                    matcher.group(6), // isHistory
                    matcher.group(7), // userPredef14
                    parseDateTime(matcher.group(8)), // updateTime
                    parseDate(matcher.group(9)), // syncDate
                    parseDateTime(matcher.group(10)) // createTime
                );
            } catch (Exception e) {
                log.warn("解析行数据失败: {}", line.substring(0, Math.min(100, line.length())), e);
                return null;
            }
        } else {
            log.debug("行格式不匹配: {}", line.substring(0, Math.min(50, line.length())));
            return null;
        }
    }
    
    /**
     * 解析日期时间字符串
     * 
     * @param dateStr 日期时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    private LocalDateTime parseDateTime(String dateStr) {
        if (!StringUtils.hasText(dateStr) || "null".equalsIgnoreCase(dateStr)) {
            return null;
        }
        
        try {
            return LocalDateTime.parse(dateStr, DATE_TIME_FORMATTER);
        } catch (Exception e) {
            log.debug("日期时间解析失败: {}", dateStr);
            return null;
        }
    }
    
    /**
     * 解析日期字符串
     * 
     * @param dateStr 日期字符串
     * @return LocalDate对象，解析失败返回null
     */
    private LocalDate parseDate(String dateStr) {
        if (!StringUtils.hasText(dateStr) || "null".equalsIgnoreCase(dateStr)) {
            return null;
        }
        
        try {
            return LocalDate.parse(dateStr, DATE_FORMATTER);
        } catch (Exception e) {
            log.debug("日期解析失败: {}", dateStr);
            return null;
        }
    }
}
