-- =====================================================
-- data_operate_id字段功能集成测试执行脚本
-- 用于验证新增字段的功能是否正常工作
-- =====================================================

-- 开始测试
BEGIN;

-- =====================================================
-- 1. 数据库结构验证
-- =====================================================

-- 1.1 检查字段是否存在
SELECT 
    '1.1 字段存在性检查' as test_name,
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 't_roles_data_permission' 
  AND column_name = 'data_operate_id';

-- 1.2 查看字段注释
SELECT 
    '1.2 字段注释检查' as test_name,
    col_description(c.oid, a.attnum) as column_comment
FROM pg_class c
JOIN pg_attribute a ON a.attrelid = c.oid
WHERE c.relname = 't_roles_data_permission' 
  AND a.attname = 'data_operate_id';

-- =====================================================
-- 2. 现有数据验证
-- =====================================================

-- 2.1 统计更新情况
SELECT 
    '2.1 数据更新统计' as test_name,
    COUNT(*) as total_records,
    COUNT(data_operate_id) as updated_records,
    COUNT(*) - COUNT(data_operate_id) as null_records,
    ROUND(COUNT(data_operate_id) * 100.0 / COUNT(*), 2) as update_percentage
FROM t_roles_data_permission;

-- 2.2 验证数据格式正确性
SELECT 
    '2.2 数据格式验证' as test_name,
    data_id,
    operate_type,
    data_operate_id,
    CASE 
        WHEN data_operate_id = CONCAT(data_id::varchar, '.', operate_type::varchar) 
        THEN '✓ 格式正确' 
        ELSE '✗ 格式错误' 
    END as format_check
FROM t_roles_data_permission 
WHERE data_operate_id IS NOT NULL
ORDER BY data_id, operate_type
LIMIT 10;

-- 2.3 按操作类型统计
SELECT 
    '2.3 操作类型统计' as test_name,
    operate_type,
    CASE operate_type
        WHEN 1 THEN '查看'
        WHEN 2 THEN '修改'
        WHEN 3 THEN '下载'
        WHEN 4 THEN '删除'
        ELSE '未知'
    END as operate_type_desc,
    COUNT(*) as total_count,
    COUNT(data_operate_id) as updated_count,
    ROUND(COUNT(data_operate_id) * 100.0 / COUNT(*), 2) as update_rate
FROM t_roles_data_permission 
GROUP BY operate_type
ORDER BY operate_type;

-- 2.4 检查是否有格式错误的记录
SELECT 
    '2.4 格式错误检查' as test_name,
    COUNT(*) as error_count
FROM t_roles_data_permission 
WHERE data_operate_id IS NOT NULL 
  AND data_operate_id != CONCAT(data_id::varchar, '.', operate_type::varchar);

-- =====================================================
-- 3. 数据完整性验证
-- =====================================================

-- 3.1 检查是否有未更新的有效记录
SELECT 
    '3.1 未更新记录检查' as test_name,
    COUNT(*) as missing_count
FROM t_roles_data_permission 
WHERE (data_id IS NOT NULL AND operate_type IS NOT NULL) 
  AND data_operate_id IS NULL;

-- 3.2 检查数据一致性
SELECT 
    '3.2 数据一致性检查' as test_name,
    role_id,
    module_identifier,
    data_id,
    operate_type,
    data_operate_id,
    CASE 
        WHEN data_operate_id = CONCAT(data_id::varchar, '.', operate_type::varchar) 
        THEN '✓ 一致' 
        ELSE '✗ 不一致' 
    END as consistency_check
FROM t_roles_data_permission 
WHERE data_operate_id IS NOT NULL
  AND (data_id IS NULL OR operate_type IS NULL OR 
       data_operate_id != CONCAT(data_id::varchar, '.', operate_type::varchar))
LIMIT 5;

-- =====================================================
-- 4. 功能测试数据准备
-- =====================================================

-- 4.1 创建测试角色（如果不存在）
INSERT INTO t_role (id, role_name, role_description, is_del, create_time, modify_time)
SELECT 999999, '测试角色_data_operate_id', '用于测试data_operate_id字段功能的角色', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
WHERE NOT EXISTS (SELECT 1 FROM t_role WHERE id = 999999);

-- 4.2 测试插入新的权限记录（模拟业务逻辑）
INSERT INTO t_roles_data_permission 
(id, role_id, module_identifier, data_type, data_id, operate_type, data_operate_id, is_del, create_time, modify_time)
VALUES 
(999999001, 999999, 'test.module', 1, 88888, 1, '88888.1', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(999999002, 999999, 'test.module', 1, 88888, 2, '88888.2', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(999999003, 999999, 'test.module', 1, 88889, 3, '88889.3', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(999999004, 999999, 'test.module', 1, 88889, 4, '88889.4', false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- 4.3 验证测试数据插入
SELECT 
    '4.3 测试数据验证' as test_name,
    role_id,
    data_id,
    operate_type,
    data_operate_id,
    CASE 
        WHEN data_operate_id = CONCAT(data_id::varchar, '.', operate_type::varchar) 
        THEN '✓ 测试数据正确' 
        ELSE '✗ 测试数据错误' 
    END as test_result
FROM t_roles_data_permission 
WHERE role_id = 999999
ORDER BY data_id, operate_type;

-- =====================================================
-- 5. 边界条件测试
-- =====================================================

-- 5.1 测试NULL值处理
SELECT 
    '5.1 NULL值处理测试' as test_name,
    'data_id为NULL时data_operate_id应为NULL' as test_case,
    COUNT(*) as count
FROM t_roles_data_permission 
WHERE data_id IS NULL AND data_operate_id IS NOT NULL;

SELECT 
    '5.1 NULL值处理测试' as test_name,
    'operate_type为NULL时data_operate_id应为NULL' as test_case,
    COUNT(*) as count
FROM t_roles_data_permission 
WHERE operate_type IS NULL AND data_operate_id IS NOT NULL;

-- 5.2 测试操作类型范围
SELECT 
    '5.2 操作类型范围测试' as test_name,
    operate_type,
    COUNT(*) as count,
    CASE 
        WHEN operate_type BETWEEN 1 AND 4 THEN '✓ 有效范围'
        ELSE '✗ 无效范围'
    END as range_check
FROM t_roles_data_permission 
WHERE operate_type IS NOT NULL
GROUP BY operate_type
ORDER BY operate_type;

-- =====================================================
-- 6. 性能基准测试
-- =====================================================

-- 6.1 查询性能测试
EXPLAIN ANALYZE
SELECT role_id, data_id, operate_type, data_operate_id
FROM t_roles_data_permission 
WHERE role_id = 1 AND data_operate_id IS NOT NULL
LIMIT 100;

-- 6.2 统计查询性能
EXPLAIN ANALYZE
SELECT 
    COUNT(*) as total,
    COUNT(data_operate_id) as with_data_operate_id
FROM t_roles_data_permission;

-- =====================================================
-- 7. 清理测试数据
-- =====================================================

-- 删除测试数据
DELETE FROM t_roles_data_permission WHERE role_id = 999999;
DELETE FROM t_role WHERE id = 999999;

-- =====================================================
-- 8. 最终验证报告
-- =====================================================

SELECT 
    '=== 最终验证报告 ===' as report_section,
    '' as details;

-- 8.1 字段添加成功率
SELECT 
    '字段添加' as verification_item,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 't_roles_data_permission' 
              AND column_name = 'data_operate_id'
              AND data_type = 'character varying'
        ) THEN '✓ 成功'
        ELSE '✗ 失败'
    END as status;

-- 8.2 数据更新成功率
SELECT 
    '数据更新' as verification_item,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM t_roles_data_permission 
            WHERE (data_id IS NOT NULL AND operate_type IS NOT NULL) 
              AND data_operate_id IS NULL
        ) = 0 THEN '✓ 成功'
        ELSE '✗ 部分失败'
    END as status;

-- 8.3 数据格式正确率
SELECT 
    '数据格式' as verification_item,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM t_roles_data_permission 
            WHERE data_operate_id IS NOT NULL 
              AND data_operate_id != CONCAT(data_id::varchar, '.', operate_type::varchar)
        ) = 0 THEN '✓ 正确'
        ELSE '✗ 存在错误'
    END as status;

-- 8.4 功能完整性
SELECT 
    '功能完整性' as verification_item,
    '✓ 代码修改完成' as status;

-- 提交测试事务
COMMIT;

-- 测试完成提示
SELECT 
    '=== 测试执行完成 ===' as message,
    CURRENT_TIMESTAMP as completion_time;
