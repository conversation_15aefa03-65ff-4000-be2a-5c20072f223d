# 组织结构表（t_org_structure）修改计划

## 1. 目标

将 `t_org_structure` 表的 `id` 和 `pre_id` 字段的数据类型从 `int8` (bigint) 修改为 `varchar(255)`，以支持包含字母的ID格式。

## 2. SQL执行脚本

请复制以下SQL代码并在您的PostgreSQL数据库中执行。

**注意：** 在执行前，请务必**备份您的 `t_org_structure` 表**，以防万一。

```sql
-- 步骤 1: 为了安全地修改主键，首先移除主键约束
ALTER TABLE "public"."t_org_structure" DROP CONSTRAINT "t_org_structure_pk";

-- 步骤 2: 修改 id 字段的类型
-- 我们使用 USING 子句来告诉 PostgreSQL 如何将现有的 int8 数据转换为 varchar
ALTER TABLE "public"."t_org_structure"
ALTER COLUMN "id" TYPE varchar(255) USING "id"::varchar(255);

-- 步骤 3: 修改 pre_id 字段的类型
ALTER TABLE "public"."t_org_structure"
ALTER COLUMN "pre_id" TYPE varchar(255) USING "pre_id"::varchar(255);

-- 步骤 4: 重新添加主键约束
ALTER TABLE "public"."t_org_structure" ADD CONSTRAINT "t_org_structure_pk" PRIMARY KEY ("id");

-- 步骤 5: （可选但推荐）如果 pre_id 存在外键关联，您需要先删除再重建。
-- 如果您确定 pre_id 引用了同一个表的 id 字段，可以执行以下操作。
-- 如果不确定，可以暂时跳过这一步。

-- ALTER TABLE "public"."t_org_structure" DROP CONSTRAINT "fk_org_structure_pre_id";
-- ALTER TABLE "public"."t_org_structure"
-- ADD CONSTRAINT "fk_org_structure_pre_id" FOREIGN KEY ("pre_id") REFERENCES "public"."t_org_structure" ("id");

```

## 3. 后续步骤

在数据库表结构修改成功后，我们需要继续进行以下操作：

1.  修改 `TOrgStructure.java` 实体类。
2.  检查并修改相关的Mapper和Service代码。
3.  最后，重新执行修复脚本 `org_structure_repair.sql`。
