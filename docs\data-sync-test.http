# 数据同步接口测试
# 使用VS Code REST Client插件运行
# 或者复制到Postman/Swagger中测试

### 1. 测试外部系统连接
GET http://localhost:8080/sync/test-connection
Content-Type: application/json

### 1.1 直接测试外部系统API
GET http://localhost:8080/api/data/test
Content-Type: application/json

### 1.2 测试获取部门数据
GET http://localhost:8080/api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 1.3 测试获取员工数据
GET http://localhost:8080/api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 1.1 直接测试外部系统API
GET http://localhost:8080/api/data/test
Content-Type: application/json

### 1.2 测试获取部门数据
GET http://localhost:8080/api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 1.3 测试获取员工数据
GET http://localhost:8080/api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 1.1 直接测试外部系统API
GET http://localhost:8080/api/data/test
Content-Type: application/json

### 1.2 测试获取部门数据
GET http://localhost:8080/api/data/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 1.3 测试获取员工数据
GET http://localhost:8080/api/data/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-02 00:00:00
Content-Type: application/json

### 2. 执行完整数据同步（部门+员工）
POST http://localhost:8080/sync/full
Content-Type: application/json

{}

### 3. 仅同步部门数据
POST http://localhost:8080/sync/departments
Content-Type: application/json

{}

### 4. 仅同步员工数据
POST http://localhost:8080/sync/employees
Content-Type: application/json

{}

### 5. 获取同步状态
GET http://localhost:8080/sync/status
Content-Type: application/json

### 6. 验证同步结果 - 查询部门列表
POST http://localhost:8080/org-structure/search
Content-Type: application/json

{
  "organName": "",
  "preId": null,
  "includeDeleted": false
}

### 7. 验证同步结果 - 查询用户列表
POST http://localhost:8080/users/getUserList
Content-Type: application/json

{
  "currentPage": 1,
  "pageSize": 10,
  "userName": "",
  "userState": null,
  "startTime": "",
  "endTime": ""
}
