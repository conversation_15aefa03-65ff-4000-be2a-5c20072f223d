package com.dfit.percode.service;

import com.dfit.percode.vo.AddMenuModuleRequestVO;
import com.dfit.percode.vo.AddMenuRequestVO;
import com.dfit.percode.vo.DeleteMenuModuleRequestVO;
import com.dfit.percode.vo.DeleteMenuRequestVO;
import com.dfit.percode.vo.MenuDetailRequestVO;
import com.dfit.percode.vo.MenuDetailResponseVO;
import com.dfit.percode.vo.MenuListRequestVO;
import com.dfit.percode.vo.MenuModuleListResponseVO;
import com.dfit.percode.vo.MenuTreeResponseVO;
import com.dfit.percode.vo.ModuleMenuTreeResponseVO;
import com.dfit.percode.vo.UpdateMenuRequestVO;
import com.dfit.percode.vo.CheckIdentifierRequestVO;
import com.dfit.percode.vo.CheckIdentifierResponseVO;

import java.util.List;

/**
 * 菜单模块相关服务接口
 * 按照前端格式要求重新设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface IMenuModuleService {

    /**
     * 新增菜单模块
     * 按照前端弹窗设计实现
     *
     * @param request 新增菜单模块请求参数
     */
    void addMenuModule(AddMenuModuleRequestVO request);

    /**
     * 获取菜单模块列表
     * 返回所有未删除的菜单模块，按排序字段排序
     *
     * @return 菜单模块列表
     */
    List<MenuModuleListResponseVO> getMenuModules();

    /**
     * 删除菜单模块
     * 逻辑删除菜单模块，设置is_del为true
     *
     * @param request 删除菜单模块请求参数
     */
    void deleteMenuModule(DeleteMenuModuleRequestVO request);

    /**
     * 新增菜单
     * 在指定模块下新增菜单项，支持目录、菜单、按钮三种类型
     *
     * @param request 新增菜单请求参数
     */
    void addMenu(AddMenuRequestVO request);

    /**
     * 获取菜单详情
     * 根据菜单ID获取菜单的详细信息，用于编辑表单回显
     *
     * @param request 获取菜单详情请求参数
     * @return 菜单详情数据
     */
    MenuDetailResponseVO getMenuDetail(MenuDetailRequestVO request);

    /**
     * 查询菜单列表
     * 返回树形结构的菜单数据，支持按模块筛选
     *
     * @param request 查询菜单列表请求参数
     * @return 菜单树形结构数据
     */
    List<MenuTreeResponseVO> getMenus(MenuListRequestVO request);

    /**
     * 查询菜单列表（优化版本）
     * 使用一次性查询+内存构建替代递归查询，大幅提升性能
     * 返回树形结构的菜单数据，支持按模块筛选
     *
     * @param request 查询菜单列表请求参数
     * @return 菜单树形结构数据
     */
    List<MenuTreeResponseVO> getMenusOptimized(MenuListRequestVO request);

    /**
     * 查询菜单列表（模块级优化版本）
     * 返回按模块分组的菜单树结构，外层为模块信息，内层保持现有菜单树结构不变
     *
     * @param request 查询菜单列表请求参数
     * @return 模块级菜单树结构数据
     */
    List<ModuleMenuTreeResponseVO> getMenusOptimizedWithModules(MenuListRequestVO request);

    /**
     * 修改菜单
     * 支持修改菜单的所有字段，包括父级关系变更
     * 包含完整的数据验证和层级关系检查
     *
     * @param request 修改菜单请求参数
     */
    void updateMenu(UpdateMenuRequestVO request);

    /**
     * 删除菜单
     * 逻辑删除菜单项，设置is_del为true
     * 包含子菜单检查，防止删除有子菜单的父级菜单
     *
     * @param request 删除菜单请求参数
     */
    void deleteMenu(DeleteMenuRequestVO request);

    /**
     * 检查权限标识符是否重复
     * 支持菜单权限和数据权限的标识符重复检测
     * 包含完整的输入验证和格式检查
     *
     * @param request 检查权限标识符请求参数
     * @return 检查结果
     */
    CheckIdentifierResponseVO checkIdentifier(CheckIdentifierRequestVO request);
}
