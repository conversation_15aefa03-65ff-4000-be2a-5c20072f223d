package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户详情响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 支持多角色显示
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UserDetailResponseVO", description = "用户详情信息")
public class UserDetailResponseVO {

    @ApiModelProperty(value = "用户ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty(value = "用户姓名", example = "张三")
    private String userName;

    @ApiModelProperty(value = "登录账号", example = "zhang001")
    private String account;

    @ApiModelProperty(value = "登录密码", example = "123456")
    private String password;

    @ApiModelProperty(value = "组织归属ID", example = "1")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long organAffiliation;

    @ApiModelProperty(value = "组织归属名称", example = "科研室")
    private String organName;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean isDisable;

    @ApiModelProperty(value = "已分配的角色列表")
    private List<RoleInfoVO> roles;
}
