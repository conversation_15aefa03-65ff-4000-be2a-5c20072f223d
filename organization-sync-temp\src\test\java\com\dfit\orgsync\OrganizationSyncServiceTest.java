package com.dfit.orgsync;

import com.dfit.orgsync.dto.SyncResult;
import com.dfit.orgsync.mapper.OrganizationSyncMapper;
import com.dfit.orgsync.service.OrganizationSyncService;
import com.dfit.orgsync.util.HierarchyParser;
import com.dfit.orgsync.util.SqlFileParser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 组织架构同步服务测试类
 */
@SpringBootTest
@Transactional
@Rollback
@Slf4j
class OrganizationSyncServiceTest {
    
    @Autowired
    private OrganizationSyncService organizationSyncService;
    
    @Autowired
    private OrganizationSyncMapper organizationSyncMapper;
    
    @Autowired
    private HierarchyParser hierarchyParser;
    
    @Autowired
    private SqlFileParser sqlFileParser;
    
    /**
     * 测试完整的数据同步流程
     */
    @Test
    void testSyncOrganizationData() {
        // 准备测试数据文件路径
        String testFilePath = "organization-sync-temp/department_sync_test.sql";
        
        log.info("开始测试组织架构数据同步...");
        
        // 执行同步
        SyncResult result = organizationSyncService.syncOrganizationData(testFilePath);
        
        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isSuccess()).isTrue();
        assertThat(result.getInsertedCount()).isGreaterThan(0);
        assertThat(result.getOrphanNodeCount()).isEqualTo(0);
        assertThat(result.getDuration()).isNotNull();
        
        log.info("同步结果: {}", result);
        
        // 验证数据库中的数据
        int count = organizationSyncMapper.countByDataSource(2);
        assertThat(count).isEqualTo(result.getFinalCount());
        assertThat(count).isGreaterThan(0);
        
        // 验证层级结构
        List<Map<String, Object>> levelStats = organizationSyncMapper.validateHierarchy(2);
        assertThat(levelStats).isNotEmpty();
        
        log.info("层级统计: {}", levelStats);
        
        // 验证一级部门
        List<com.dfit.orgsync.dto.OrganizationTreeNode> level1Departments = 
            organizationSyncMapper.findLevel1Departments(2);
        assertThat(level1Departments).isNotEmpty();
        
        log.info("一级部门数量: {}", level1Departments.size());
        
        log.info("测试完成，同步成功！");
    }
    
    /**
     * 测试层级解析功能
     */
    @Test
    void testHierarchyParsing() {
        log.info("开始测试层级解析功能...");
        
        // 测试一级部门
        List<String> result1 = hierarchyParser.parseHierarchy("工会");
        assertThat(result1).containsExactly("工会");
        log.info("一级部门解析: {} -> {}", "工会", result1);
        
        // 测试多级部门
        List<String> result2 = hierarchyParser.parseHierarchy("炼铁事业部第二炼铁厂高炉五车间炉前班");
        assertThat(result2).hasSize(4);
        assertThat(result2.get(0)).contains("炼铁事业部");
        assertThat(result2.get(result2.size() - 1)).isEqualTo("炼铁事业部第二炼铁厂高炉五车间炉前班");
        log.info("多级部门解析: {} -> {}", "炼铁事业部第二炼铁厂高炉五车间炉前班", result2);
        
        // 测试另一个多级部门
        List<String> result3 = hierarchyParser.parseHierarchy("物流中心产成品储运室炼钢库发货班");
        assertThat(result3).hasSize(3);
        log.info("多级部门解析: {} -> {}", "物流中心产成品储运室炼钢库发货班", result3);
        
        // 测试一级部门识别
        assertThat(hierarchyParser.isLevel1Department("工会")).isTrue();
        assertThat(hierarchyParser.isLevel1Department("炼铁事业部")).isTrue();
        assertThat(hierarchyParser.isLevel1Department("炼铁事业部第二炼铁厂")).isFalse();
        
        log.info("层级解析功能测试完成！");
    }
    
    /**
     * 测试SQL文件解析功能
     */
    @Test
    void testSqlFileParsing() {
        log.info("开始测试SQL文件解析功能...");
        
        try {
            String testFilePath = "organization-sync-temp/department_sync_test.sql";
            var rawData = sqlFileParser.parseSqlFile(testFilePath);
            
            assertThat(rawData).isNotEmpty();
            log.info("解析到 {} 条记录", rawData.size());
            
            // 验证数据结构
            var firstRecord = rawData.get(0);
            assertThat(firstRecord.getFullName()).isNotNull();
            assertThat(firstRecord.getFullName()).isNotEmpty();
            
            log.info("第一条记录: {}", firstRecord.getFullName());
            
            // 统计有效记录
            long validCount = rawData.stream()
                .filter(dto -> dto.isValid())
                .count();
            
            log.info("有效记录数: {}", validCount);
            assertThat(validCount).isGreaterThan(0);
            
        } catch (Exception e) {
            log.error("SQL文件解析测试失败", e);
            throw new RuntimeException(e);
        }
        
        log.info("SQL文件解析功能测试完成！");
    }
    
    /**
     * 测试数据库操作
     */
    @Test
    void testDatabaseOperations() {
        log.info("开始测试数据库操作...");
        
        // 测试获取最大ID
        Long maxId = organizationSyncMapper.getMaxId();
        assertThat(maxId).isNotNull();
        assertThat(maxId).isGreaterThanOrEqualTo(2000000000L);
        log.info("当前最大ID: {}", maxId);
        
        // 测试统计记录数
        int count = organizationSyncMapper.countByDataSource(2);
        log.info("当前同步数据记录数: {}", count);
        
        // 测试查询一级部门
        var level1Departments = organizationSyncMapper.findLevel1Departments(2);
        log.info("当前一级部门数: {}", level1Departments.size());
        
        log.info("数据库操作测试完成！");
    }
    
    /**
     * 性能测试（可选）
     */
    @Test
    void testPerformance() {
        log.info("开始性能测试...");
        
        String testFilePath = "organization-sync-temp/department_sync_test.sql";
        
        long startTime = System.currentTimeMillis();
        SyncResult result = organizationSyncService.syncOrganizationData(testFilePath);
        long endTime = System.currentTimeMillis();
        
        long duration = endTime - startTime;
        
        log.info("性能测试结果:");
        log.info("  执行时间: {} ms", duration);
        log.info("  处理记录数: {}", result.getRawDataCount());
        log.info("  插入记录数: {}", result.getInsertedCount());
        log.info("  平均处理速度: {} 记录/秒", 
            result.getRawDataCount() * 1000.0 / duration);
        
        // 性能断言（根据实际情况调整）
        assertThat(duration).isLessThan(60000); // 应在1分钟内完成
        
        log.info("性能测试完成！");
    }
}
