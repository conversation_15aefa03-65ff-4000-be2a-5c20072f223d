# 重复测试指南

## ✅ **可以重复使用相同时间测试**

是的，您完全可以重复使用相同的时间进行测试！我已经优化了同步逻辑来支持重复执行。

## 🔧 **重复执行优化**

### **部门数据处理**
```java
// 现在支持重复执行
private void saveOrUpdateDepartment(TOrgStructure department) {
    try {
        // 先尝试插入
        orgStructureMapper.insert(department);
        log.debug("保存部门成功: {}", department.getOrganName());
    } catch (Exception e) {
        // 如果是主键冲突，尝试更新
        if (e.getMessage().contains("duplicate key")) {
            log.warn("部门数据已存在，尝试更新: {}", department.getOrganName());
            department.setModifyTime(LocalDateTime.now());
            orgStructureMapper.updateById(department);
            log.info("更新部门成功: {}", department.getOrganName());
        }
    }
}
```

### **用户数据处理**
```java
// 现在支持重复执行
private void saveOrUpdateUser(TUser user) {
    try {
        // 先尝试插入
        userMapper.insert(user);
        log.debug("保存用户成功: {}", user.getUserName());
    } catch (Exception e) {
        // 如果是主键冲突，跳过处理
        if (e.getMessage().contains("duplicate key")) {
            log.warn("用户数据已存在，跳过更新: {}", user.getUserName());
            log.info("跳过重复用户: {}", user.getUserName());
        }
    }
}
```

## 🧪 **重复测试场景**

### **场景1：完全相同的时间范围**
```http
# 第一次执行
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-10 23:59:59

# 第二次执行（相同时间范围）
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-10 23:59:59
```

**预期结果**：
- 第一次：插入新数据
- 第二次：检测到重复数据，跳过或更新

### **场景2：重叠的时间范围**
```http
# 第一次执行
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-05 23:59:59

# 第二次执行（时间重叠）
POST /sync/departments?startDate=2024-01-03 00:00:00&endDate=2024-01-10 23:59:59
```

**预期结果**：
- 重叠部分：跳过重复数据
- 新增部分：插入新数据

### **场景3：测试不同的同步接口**
```http
# 测试部门同步
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59

# 测试员工同步
POST /sync/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59

# 测试完整同步
POST /sync/full?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
```

## 📊 **重复执行的日志示例**

### **第一次执行**
```
INFO - 正在处理部门: orgCode=TECH001, orgName=技术部
INFO - 部门数据已保存到 t_org_structure 表: id=1234567890, orgName=技术部
INFO - 部门数据同步汇总: 主表(t_org_structure)=21 条, 子表(t_department_child)=45 条
```

### **第二次执行（重复）**
```
INFO - 正在处理部门: orgCode=TECH001, orgName=技术部
WARN - 部门数据已存在，尝试更新: 技术部
INFO - 更新部门成功: 技术部
INFO - 部门数据同步汇总: 主表(t_org_structure)=21 条, 子表(t_department_child)=45 条
```

## 🔍 **数据验证方法**

### **1. 检查数据库记录数**
```sql
-- 查看部门数据
SELECT COUNT(*) FROM t_org_structure WHERE sync_status = 'SYNCED';

-- 查看用户数据
SELECT COUNT(*) FROM t_user WHERE sync_status = 'SYNCED';

-- 查看同步日志
SELECT sync_type, sync_status, COUNT(*) 
FROM t_sync_log 
GROUP BY sync_type, sync_status;
```

### **2. 检查时间戳**
```sql
-- 查看最新的同步时间
SELECT organ_name, create_time, modify_time 
FROM t_org_structure 
WHERE sync_status = 'SYNCED' 
ORDER BY modify_time DESC 
LIMIT 10;
```

### **3. 检查重复数据**
```sql
-- 检查是否有重复的部门
SELECT organ_name, COUNT(*) 
FROM t_org_structure 
WHERE sync_status = 'SYNCED' 
GROUP BY organ_name 
HAVING COUNT(*) > 1;
```

## ⚠️ **注意事项**

### **1. ID生成策略**
- 每次同步都会生成新的雪花ID
- 重复执行时，相同的外部数据会有不同的内部ID

### **2. 时间戳更新**
- 重复执行时，`modify_time`会更新为当前时间
- `create_time`保持不变

### **3. 扩展表数据**
- 部门子表、员工岗位等扩展表可能会产生重复记录
- 建议定期清理重复数据

### **4. 性能考虑**
- 重复执行会增加数据库负载
- 建议在测试环境中进行重复测试

## 🛠️ **清理重复数据**

如果需要清理重复数据：

```sql
-- 清理同步数据（谨慎操作）
DELETE FROM t_org_structure WHERE sync_status = 'SYNCED';
DELETE FROM t_user WHERE sync_status = 'SYNCED';
DELETE FROM t_department_child WHERE sync_status = 'SYNCED';
DELETE FROM t_employee_position WHERE sync_status = 'SYNCED';
DELETE FROM t_employee_title WHERE sync_status = 'SYNCED';
DELETE FROM t_employee_system WHERE sync_status = 'SYNCED';
DELETE FROM t_sync_log;

-- 重置序列（如果使用序列）
-- ALTER SEQUENCE your_sequence_name RESTART WITH 1;
```

## 📋 **测试建议**

### **1. 渐进式测试**
```http
# 先测试小范围
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59

# 再测试大范围
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-10 23:59:59
```

### **2. 功能验证测试**
```http
# 测试各个接口
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
POST /sync/employees?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
POST /sync/full?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
```

### **3. 重复执行测试**
```http
# 重复执行相同的请求，验证处理逻辑
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
POST /sync/departments?startDate=2024-01-01 00:00:00&endDate=2024-01-01 23:59:59
```

现在您可以放心地重复使用相同的时间进行测试了！系统会智能处理重复数据，避免冲突。
