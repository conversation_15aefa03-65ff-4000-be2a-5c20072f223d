package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取菜单详情请求VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MenuDetailRequestVO", description = "获取菜单详情请求参数")
public class MenuDetailRequestVO {
    
    @ApiModelProperty(value = "菜单ID", required = true, example = "1930307870503604224")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long menuId;
}
