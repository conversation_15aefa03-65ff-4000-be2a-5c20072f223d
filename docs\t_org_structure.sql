/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:17
*/


-- ----------------------------
-- Table structure for t_org_structure
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_org_structure";
CREATE TABLE "public"."t_org_structure" (
  "id" int8 NOT NULL,
  "organ_name" varchar(255) COLLATE "pg_catalog"."default",
  "pre_id" int8,
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6),
  "data_source" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."t_org_structure"."organ_name" IS '组织名称';
COMMENT ON COLUMN "public"."t_org_structure"."pre_id" IS '父ID，根节点为空';
COMMENT ON COLUMN "public"."t_org_structure"."order_info" IS '顺序，从1开始';
COMMENT ON COLUMN "public"."t_org_structure"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."t_org_structure"."data_source" IS '数据来源，1页面输入，2数据同步';
COMMENT ON TABLE "public"."t_org_structure" IS '组织架构表';

-- ----------------------------
-- Records of t_org_structure
-- ----------------------------
INSERT INTO "public"."t_org_structure" VALUES (1935961676675420160, '1234', 1935943091236245504, 1, 'f', '2025-06-20 15:23:26.75726', '2025-06-20 15:23:31.841411', 1);
INSERT INTO "public"."t_org_structure" VALUES (1935952518400053248, 'sdffff', NULL, 1, 'f', '2025-06-20 14:47:03.288933', '2025-06-20 17:52:10.27745', 1);
INSERT INTO "public"."t_org_structure" VALUES (1932982339693056000, 'avav', NULL, 1, 'f', '2025-06-12 10:04:37.49165', '2025-06-20 18:05:42.595533', 1);
INSERT INTO "public"."t_org_structure" VALUES (1935962646998290432, '撒旦飞洒', NULL, 1, 'f', '2025-06-20 15:27:18.132757', '2025-06-20 18:05:48.347411', 1);
INSERT INTO "public"."t_org_structure" VALUES (1932979029460258816, 'test', NULL, 1, 'f', '2025-06-12 09:51:28.269639', '2025-06-20 18:06:10.794858', 1);
INSERT INTO "public"."t_org_structure" VALUES (1935943091236245504, 'test123', NULL, 1, 'f', '2025-06-20 14:09:35.673483', '2025-06-20 18:06:15.428404', 1);
INSERT INTO "public"."t_org_structure" VALUES (1932981702259511296, 'aa', NULL, 1, 'f', '2025-06-12 10:02:05.522763', '2025-06-20 18:06:18.789116', 1);
INSERT INTO "public"."t_org_structure" VALUES (1932981618243407872, 'a', NULL, 1, 'f', '2025-06-12 10:01:45.484875', '2025-06-20 18:06:24.182366', 1);
INSERT INTO "public"."t_org_structure" VALUES (5002, '技术中心', 5001, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:07:05.265535', 1);
INSERT INTO "public"."t_org_structure" VALUES (5007, '汽车钢组', 5002, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:07:24.151355', 1);
INSERT INTO "public"."t_org_structure" VALUES (5008, '深冲组', 5002, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:09:17.577597', 1);
INSERT INTO "public"."t_org_structure" VALUES (5009, 'α组', 5002, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:09:33.760522', 1);
INSERT INTO "public"."t_org_structure" VALUES (5010, '深冲压组', 5002, 4, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:10:23.413487', 1);
INSERT INTO "public"."t_org_structure" VALUES (5003, '制造部', 5001, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:10:36.966984', 1);
INSERT INTO "public"."t_org_structure" VALUES (5004, '热轧厂', 5001, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:10:54.590953', 1);
INSERT INTO "public"."t_org_structure" VALUES (5005, '炼钢厂', 5001, 4, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:11:03.276569', 1);
INSERT INTO "public"."t_org_structure" VALUES (5006, '财务管理中心', 5001, 5, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:11:07.028383', 1);
INSERT INTO "public"."t_org_structure" VALUES (5011, '理化检验中心', 5003, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:13:59.855838', 1);
INSERT INTO "public"."t_org_structure" VALUES (5012, '质量检验室', 5003, 2, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:14:51.513129', 1);
INSERT INTO "public"."t_org_structure" VALUES (5013, '冶金技术室', 5003, 3, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:15:22.149126', 1);
INSERT INTO "public"."t_org_structure" VALUES (1936005019660849152, '生产管控中心', 5003, 1, 'f', '2025-06-20 18:15:40.530952', '2025-06-20 18:15:40.530952', 1);
INSERT INTO "public"."t_org_structure" VALUES (1936005127085363200, '冶金规范室', 5003, 1, 'f', '2025-06-20 18:16:06.143425', '2025-06-20 18:16:06.143425', 1);
INSERT INTO "public"."t_org_structure" VALUES (1936005221369122816, '废钢管理处', 5003, 1, 'f', '2025-06-20 18:16:28.622627', '2025-06-20 18:16:28.622627', 1);
INSERT INTO "public"."t_org_structure" VALUES (1936005302730231808, '热轧产品室', 5003, 1, 'f', '2025-06-20 18:16:48.02046', '2025-06-20 18:16:48.02046', 1);
INSERT INTO "public"."t_org_structure" VALUES (1936005370686345216, '产品服务处', 5003, 1, 'f', '2025-06-20 18:17:04.222181', '2025-06-20 18:17:04.222181', 1);
INSERT INTO "public"."t_org_structure" VALUES (5001, '宁波钢铁', 0, 1, 'f', '2025-06-11 01:58:47.668562', '2025-06-20 18:06:52.307838', 1);

-- ----------------------------
-- Indexes structure for table t_org_structure
-- ----------------------------
CREATE INDEX "idx_org_structure_pre_id" ON "public"."t_org_structure" USING btree (
  "pre_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Checks structure for table t_org_structure
-- ----------------------------
ALTER TABLE "public"."t_org_structure" ADD CONSTRAINT "chk_org_structure_data_source" CHECK ((data_source = ANY (ARRAY[1, 2])));

-- ----------------------------
-- Primary Key structure for table t_org_structure
-- ----------------------------
ALTER TABLE "public"."t_org_structure" ADD CONSTRAINT "t_org_structure_pk" PRIMARY KEY ("id");
