2025-06-11 08:58:23.652 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 08:58:25.150 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 09:43:50.139 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 1668 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 09:43:50.195 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 09:43:50.519 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 09:44:00.736 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 09:44:00.750 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 09:44:00.871 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 09:44:00.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 09:44:00.905 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 09:44:00.956 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 49 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 09:44:01.006 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 09:44:01.006 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 09:44:01.049 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 JPA repository interfaces.
2025-06-11 09:44:01.154 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 09:44:01.158 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 09:44:01.273 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-11 09:44:04.184 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 09:44:04.241 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 09:44:04.243 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 09:44:04.244 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 09:44:06.383 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 09:44:06.384 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 15591 ms
2025-06-11 09:44:06.769 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 09:44:07.726 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 09:44:09.248 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 09:44:09.504 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 09:44:10.753 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 09:44:11.941 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 09:44:13.699 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 09:44:13.757 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 09:44:22.835 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 09:44:31.091 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 09:44:31.126 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 09:44:32.235 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 44.58 seconds (JVM running for 55.476)
2025-06-11 09:44:44.062 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 09:44:44.103 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 09:44:44.108 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-11 10:02:01.595 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:02:01.613 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:02:19.543 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 35956 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:02:19.546 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:02:19.554 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:02:22.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:02:22.456 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:02:22.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:02:22.509 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:02:22.510 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:02:22.526 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:02:22.544 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:02:22.545 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:02:22.573 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-11 10:02:22.603 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:02:22.607 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:02:22.640 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-11 10:02:24.055 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:02:24.107 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:02:24.110 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:02:24.110 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:02:24.741 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:02:24.742 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5100 ms
2025-06-11 10:02:25.003 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:02:25.332 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:02:26.293 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:02:26.499 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:02:26.924 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:02:27.229 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:02:27.694 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:02:27.714 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:02:32.413 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:02:35.388 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:02:35.404 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:02:37.143 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 19.092 seconds (JVM running for 23.658)
2025-06-11 10:02:51.485 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:02:51.486 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:02:51.492 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-11 10:02:51.973 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:02:52.219 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:02:55.749 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:02:55.824 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:06:03.100 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:06:03.138 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:06:23.768 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 31404 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:06:23.772 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:06:23.784 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:06:26.920 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:06:26.927 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:06:27.015 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 70 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:06:27.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:06:27.049 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:06:27.087 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:06:27.107 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:06:27.108 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:06:27.138 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 JPA repository interfaces.
2025-06-11 10:06:27.175 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:06:27.178 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:06:27.234 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-06-11 10:06:28.658 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:06:28.688 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:06:28.689 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:06:28.689 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:06:29.160 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:06:29.161 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5290 ms
2025-06-11 10:06:29.276 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:06:29.560 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:06:30.464 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:06:30.627 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:06:30.926 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:06:31.299 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:06:31.957 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:06:32.001 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:06:37.368 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:06:41.702 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:06:41.723 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:06:43.100 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.681 seconds (JVM running for 25.166)
2025-06-11 10:07:04.233 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:07:04.234 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:07:04.239 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-11 10:07:04.484 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:07:04.642 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:07:07.303 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:07:07.397 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:12:14.391 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:12:14.401 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:12:34.766 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 27768 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:12:34.769 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:12:34.801 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:12:37.339 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:12:37.343 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:12:37.398 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:12:37.406 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:12:37.407 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:12:37.433 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:12:37.462 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:12:37.463 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:12:37.499 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-11 10:12:37.548 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:12:37.552 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:12:37.641 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-11 10:12:39.019 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:12:39.038 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:12:39.038 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:12:39.039 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:12:39.382 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:12:39.382 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4530 ms
2025-06-11 10:12:39.519 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:12:39.768 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:12:40.654 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:12:40.752 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:12:40.971 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:12:41.200 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:12:41.733 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:12:41.758 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:12:45.369 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:12:47.994 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:12:48.010 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:12:49.138 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.291 seconds (JVM running for 22.904)
2025-06-11 10:12:54.448 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:12:54.448 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:12:54.453 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-11 10:12:54.740 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:12:54.903 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:13:12.461 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:13:12.549 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 回退到原始查询方法
2025-06-11 10:14:51.384 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:14:51.392 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:15:13.734 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 44120 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:15:13.737 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:15:13.880 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:15:17.385 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:15:17.389 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:15:17.436 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:15:17.444 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:15:17.445 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:15:17.459 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:15:17.473 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:15:17.473 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:15:17.502 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-11 10:15:17.533 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:15:17.535 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:15:17.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-11 10:15:19.996 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:15:20.023 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:15:20.024 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:15:20.027 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:15:20.663 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:15:20.664 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6713 ms
2025-06-11 10:15:20.961 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:15:21.371 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:15:22.323 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:15:22.454 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:15:22.938 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:15:23.412 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:15:24.146 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:15:24.201 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:15:29.591 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:15:34.022 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:15:34.047 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:15:35.927 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 24.272 seconds (JVM running for 30.882)
2025-06-11 10:15:42.221 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:15:42.221 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:15:42.224 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-11 10:15:42.493 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:15:42.663 [http-nio-8285-exec-1] WARN  com.dfit.percode.service.impl.UserServiceImpl - 发现部门ID为空的记录，跳过: null
2025-06-11 10:15:42.666 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 172ms
2025-06-11 10:17:52.182 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:17:52.197 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:18:13.805 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 14776 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:18:13.807 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:18:13.920 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:18:17.130 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:18:17.135 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:18:17.178 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:18:17.191 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:18:17.194 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:18:17.219 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:18:17.240 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:18:17.241 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:18:17.270 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 JPA repository interfaces.
2025-06-11 10:18:17.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:18:17.336 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:18:17.385 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-11 10:18:19.110 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:18:19.136 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:18:19.137 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:18:19.140 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:18:19.853 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:18:19.854 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5891 ms
2025-06-11 10:18:20.313 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:18:20.672 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:18:21.885 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:18:22.012 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:18:22.282 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:18:22.534 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:18:23.014 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:18:23.035 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:18:26.692 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:18:29.649 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:18:29.664 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:18:30.517 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.139 seconds (JVM running for 22.129)
2025-06-11 10:18:36.218 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:18:36.219 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:18:36.222 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-11 10:18:36.414 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:18:36.551 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 优化查询返回数据条数: 17
2025-06-11 10:18:36.554 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 140ms
2025-06-11 10:19:53.048 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:19:53.054 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:20:06.886 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 26516 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:20:06.890 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:20:06.922 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:20:09.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:20:09.953 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:20:10.045 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:20:10.071 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:20:10.073 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:20:10.116 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:20:10.144 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:20:10.145 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:20:10.179 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-11 10:20:10.223 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:20:10.227 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:20:10.315 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-06-11 10:20:11.496 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:20:11.507 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:20:11.508 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:20:11.509 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:20:11.808 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:20:11.809 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4814 ms
2025-06-11 10:20:11.911 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:20:12.083 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:20:12.911 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:20:12.996 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:20:13.197 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:20:13.420 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:20:14.023 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:20:14.100 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:20:18.405 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:20:21.493 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:20:21.513 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:20:22.270 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 16.505 seconds (JVM running for 20.818)
2025-06-11 10:20:52.355 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:20:52.356 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:20:52.361 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-11 10:20:54.036 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:20:54.328 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 优化查询返回数据条数: 17
2025-06-11 10:20:54.332 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门数量: 0, 用户映射数量: 0
2025-06-11 10:20:54.332 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 最终根部门数量: 0
2025-06-11 10:20:54.332 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 293ms
2025-06-11 10:24:04.335 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:24:04.346 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:24:24.815 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 10116 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:24:24.819 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:24:24.836 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:24:27.770 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:24:27.780 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:24:27.828 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 35 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:24:27.840 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:24:27.841 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:24:27.867 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:24:27.885 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:24:27.885 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:24:27.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 JPA repository interfaces.
2025-06-11 10:24:27.962 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:24:27.966 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:24:28.002 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-11 10:24:29.982 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:24:30.006 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:24:30.007 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:24:30.007 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:24:30.470 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:24:30.471 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5565 ms
2025-06-11 10:24:30.649 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:24:30.978 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:24:31.949 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:24:32.075 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:24:32.402 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:24:32.731 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:24:33.284 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:24:33.307 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:24:38.690 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:24:42.024 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:24:42.052 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:24:43.882 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.423 seconds (JVM running for 24.824)
2025-06-11 10:25:41.479 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:25:41.480 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:25:41.486 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-11 10:25:41.836 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:25:42.026 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 优化查询返回数据条数: 17
2025-06-11 10:25:42.028 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.028 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.029 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.029 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.029 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.030 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.031 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.031 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.031 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.031 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.032 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.032 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.033 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.033 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.033 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.033 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.034 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据: orgId=null (null), orgName=null, userId=null (null)
2025-06-11 10:25:42.034 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门数量: 0, 用户映射数量: 0
2025-06-11 10:25:42.035 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 最终根部门数量: 0
2025-06-11 10:25:42.035 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 198ms
2025-06-11 10:29:54.046 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:29:54.085 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:30:15.857 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 1916 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 10:30:15.859 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 10:30:15.875 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 10:30:19.444 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:30:19.449 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:30:19.493 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 36 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 10:30:19.502 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:30:19.504 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 10:30:19.521 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 10:30:19.539 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:30:19.540 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 10:30:19.572 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 JPA repository interfaces.
2025-06-11 10:30:19.607 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 10:30:19.612 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:30:19.648 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-11 10:30:21.390 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 10:30:21.419 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 10:30:21.420 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:30:21.420 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 10:30:22.013 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:30:22.013 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6041 ms
2025-06-11 10:30:22.188 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:30:23.176 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:30:24.700 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 10:30:24.998 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 10:30:25.463 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 10:30:25.854 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 10:30:26.423 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 10:30:26.452 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:30:30.723 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 10:30:34.116 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 10:30:34.138 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 10:30:35.671 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 21.137 seconds (JVM running for 26.23)
2025-06-11 10:30:41.400 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:30:41.400 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:30:41.404 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-11 10:30:41.811 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 10:30:41.812 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 使用原始查询方法（已优化根部门查询条件）
2025-06-11 10:30:43.619 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 1807ms
2025-06-11 10:31:10.782 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 10:31:10.783 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 10:31:10.928 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 10
2025-06-11 10:31:11.617 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 12
2025-06-11 10:31:11.619 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 10:31:28.978 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 10:31:28.987 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 11:10:46.797 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 15328 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 11:10:46.807 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 11:10:47.154 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 11:10:54.797 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 11:10:54.804 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 11:10:54.924 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 84 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 11:10:54.975 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 11:10:55.045 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 11:10:55.269 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 218 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 11:10:55.420 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 11:10:55.421 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 11:10:55.517 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 0 JPA repository interfaces.
2025-06-11 11:10:55.636 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 11:10:55.640 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 11:10:55.720 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-06-11 11:11:00.585 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 11:11:00.632 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 11:11:00.633 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 11:11:00.634 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 11:11:02.939 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 11:11:02.941 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 15833 ms
2025-06-11 11:11:03.594 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 11:11:06.319 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 11:11:08.122 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 11:11:08.646 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 11:11:09.973 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 11:11:11.053 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 11:11:12.655 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 11:11:12.787 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 11:11:30.652 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 11:11:41.331 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 11:11:41.372 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 11:11:46.633 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 65.599 seconds (JVM running for 84.12)
2025-06-11 11:12:59.647 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 11:12:59.648 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 11:12:59.661 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2025-06-11 11:20:18.998 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:20:19.001 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:20:19.630 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 1
2025-06-11 11:20:19.798 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 1
2025-06-11 11:20:19.802 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:23:10.379 [http-nio-8285-exec-1] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character (',' (code 44)): expected a value; nested exception is com.fasterxml.jackson.databind.JsonMappingException: Unexpected character (',' (code 44)): expected a value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 3, column: 16] (through reference chain: java.util.ArrayList[0])]
2025-06-11 11:23:17.314 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户ID为空，创建新用户: userName=测试用户001, account=null
2025-06-11 11:23:17.317 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始创建新用户: userName=测试用户001, account=null
2025-06-11 11:23:17.328 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 生成用户ID: 1932639748158001152
2025-06-11 11:23:17.457 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 新用户创建成功: userId=1932639748158001152, userName=测试用户001, account=测试用户001
2025-06-11 11:23:17.458 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 新用户创建成功，用户ID: 1932639748158001152
2025-06-11 11:24:41.513 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始按部门分页查询用户列表
2025-06-11 11:24:41.520 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门ID: 1002, 包含子部门: true, 页码: 1, 页大小: 10
2025-06-11 11:24:41.677 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:24:41.741 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:24:41.742 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 按部门查询用户列表完成
2025-06-11 11:25:04.113 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始按部门分页查询用户列表
2025-06-11 11:25:04.114 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门ID: 5001, 包含子部门: true, 页码: 1, 页大小: 10
2025-06-11 11:25:04.197 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 10
2025-06-11 11:25:04.861 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 12
2025-06-11 11:25:04.862 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 按部门查询用户列表完成
2025-06-11 11:27:56.365 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始更新用户信息
2025-06-11 11:27:56.368 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户ID: 6001
2025-06-11 11:27:56.369 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色数量: 1
2025-06-11 11:27:56.430 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户基本信息更新完成
2025-06-11 11:27:56.484 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 旧角色关联删除完成
2025-06-11 11:27:56.485 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 正在处理角色 1: roleId=7001, roleName=系统管理员
2025-06-11 11:27:56.486 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 生成的关联ID: 1932640919031517184
2025-06-11 11:27:56.554 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色 系统管理员 插入成功
2025-06-11 11:27:56.554 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户信息更新完成
2025-06-11 11:28:20.326 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:28:20.326 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:28:20.395 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:28:20.469 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:28:20.469 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:28:34.606 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:28:34.607 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:28:34.731 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:28:34.790 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:28:34.790 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:28:41.305 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:28:41.306 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:28:41.399 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:28:41.480 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:28:41.484 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:29:07.708 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:29:07.708 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:29:07.784 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 1
2025-06-11 11:29:07.905 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 1
2025-06-11 11:29:07.905 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:30:02.086 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:30:02.086 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:30:02.155 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 1
2025-06-11 11:30:02.280 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 1
2025-06-11 11:30:02.281 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:30:39.101 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:30:39.102 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:30:39.183 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 1
2025-06-11 11:30:39.336 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 1
2025-06-11 11:30:39.337 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:30:46.370 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始分页查询用户列表
2025-06-11 11:30:46.372 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 页码: 1, 页大小: 10
2025-06-11 11:30:46.445 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 10
2025-06-11 11:30:47.075 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 12
2025-06-11 11:30:47.075 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户列表查询完成
2025-06-11 11:31:15.937 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色选项列表
2025-06-11 11:31:16.011 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取到角色选项数量: 9
2025-06-11 11:31:38.374 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:31:38.375 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 123456789, 页码: 1, 页大小: 10
2025-06-11 11:31:38.439 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:31:38.509 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:31:38.510 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
2025-06-11 11:32:06.203 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:32:06.204 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001, 页码: null, 页大小: 10
2025-06-11 11:34:04.233 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:34:04.234 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001, 页码: 1, 页大小: 10
2025-06-11 11:34:04.376 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:34:04.438 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:34:04.439 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
2025-06-11 11:34:08.824 [http-nio-8285-exec-1] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('}' (code 125)): was expecting double-quote to start field name; nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('}' (code 125)): was expecting double-quote to start field name<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 7, column: 2]]
2025-06-11 11:34:10.619 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:34:10.619 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001, 页码: 1, 页大小: 10
2025-06-11 11:34:10.695 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:34:10.764 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:34:10.766 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
2025-06-11 11:34:18.584 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:34:18.585 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001, 页码: 1, 页大小: 10
2025-06-11 11:34:18.647 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 0
2025-06-11 11:34:18.719 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 0
2025-06-11 11:34:18.720 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
2025-06-11 11:34:24.694 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取角色用户分配列表
2025-06-11 11:34:24.695 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001, 页码: 1, 页大小: 10
2025-06-11 11:34:24.812 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到用户数量: 10
2025-06-11 11:34:24.873 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 总记录数: 13
2025-06-11 11:34:24.874 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色用户分配列表查询完成
2025-06-11 12:02:23.936 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:02:23.985 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 12:03:09.585 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 20624 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 12:03:09.588 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 12:03:09.620 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 12:03:17.711 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:03:17.757 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:03:17.990 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 122 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 12:03:18.031 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:03:18.034 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:03:18.064 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 12:03:18.139 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:03:18.142 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 12:03:18.223 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 58 ms. Found 0 JPA repository interfaces.
2025-06-11 12:03:18.310 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:03:18.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:03:18.379 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
2025-06-11 12:03:21.481 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 12:03:21.524 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 12:03:21.525 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 12:03:21.525 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 12:03:22.441 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 12:03:22.441 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 12544 ms
2025-06-11 12:03:22.895 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 12:03:23.640 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 12:03:25.209 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 12:03:25.527 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 12:03:26.217 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 12:03:27.014 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 12:03:28.096 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 12:03:28.155 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:03:37.296 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 12:03:44.703 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 12:03:44.743 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 12:03:49.541 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 43.239 seconds (JVM running for 53.431)
2025-06-11 12:05:28.191 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 12:05:28.192 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 12:05:28.203 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2025-06-11 12:06:12.686 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 12:06:12.686 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 使用原始查询方法（已优化根部门查询条件）
2025-06-11 12:06:14.417 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 1731ms
2025-06-11 12:06:33.796 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- RECURSIVE 优化版本
2025-06-11 12:08:56.042 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:08:56.056 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 12:09:10.868 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 40704 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 12:09:10.870 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 12:09:10.888 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 12:09:14.551 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:09:14.556 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:09:14.602 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 12:09:14.612 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:09:14.614 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:09:14.633 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 12:09:14.666 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:09:14.666 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 12:09:14.690 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-11 12:09:14.734 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:09:14.736 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:09:14.761 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-11 12:09:15.325 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [com.dfit.percode.service.impl.UserServiceImpl] for bean with name 'userServiceImpl' defined in file [G:\fushun\permissionCode\target\classes\com\dfit\percode\service\impl\UserServiceImpl.class]: problem with class file or dependent class; nested exception is java.lang.UnsupportedClassVersionError: com/dfit/percode/service/impl/UserServiceImpl has been compiled by a more recent version of the Java Runtime (class file version 61.0), this version of the Java Runtime only recognizes class file versions up to 55.0
2025-06-11 12:09:15.349 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-11 12:10:22.509 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 44300 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 12:10:22.512 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 12:10:22.534 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 12:10:26.459 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:10:26.466 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:10:26.514 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 37 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 12:10:26.522 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:10:26.523 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:10:26.547 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 12:10:26.586 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:10:26.586 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 12:10:26.615 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-11 12:10:26.671 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:10:26.674 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:10:26.718 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
2025-06-11 12:10:27.277 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [com.dfit.percode.service.impl.UserServiceImpl] for bean with name 'userServiceImpl' defined in file [G:\fushun\permissionCode\target\classes\com\dfit\percode\service\impl\UserServiceImpl.class]: problem with class file or dependent class; nested exception is java.lang.UnsupportedClassVersionError: com/dfit/percode/service/impl/UserServiceImpl has been compiled by a more recent version of the Java Runtime (class file version 61.0), this version of the Java Runtime only recognizes class file versions up to 55.0
2025-06-11 12:10:27.298 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-11 12:13:29.815 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 38220 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 12:13:29.819 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 12:13:29.839 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 12:13:33.435 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:13:33.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:13:33.494 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 42 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 12:13:33.504 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:13:33.507 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:13:33.527 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 12:13:33.548 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:13:33.549 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 12:13:33.581 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-11 12:13:33.622 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:13:33.627 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:13:33.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-06-11 12:13:35.045 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 12:13:35.065 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 12:13:35.066 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 12:13:35.067 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 12:13:35.457 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 12:13:35.458 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5528 ms
2025-06-11 12:13:35.692 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 12:13:35.964 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 12:13:36.978 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 12:13:37.204 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 12:13:37.538 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 12:13:37.807 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 12:13:38.415 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 12:13:38.435 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:13:42.346 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 12:13:45.691 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 12:13:45.717 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 12:13:46.871 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.412 seconds (JVM running for 23.0)
2025-06-11 12:13:56.470 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 12:13:56.471 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 12:13:56.477 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 6 ms
2025-06-11 12:13:56.822 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- RECURSIVE 优化版本
2025-06-11 12:13:57.005 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回数据条数: 17
2025-06-11 12:13:57.014 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门数量: 0, 用户映射数量: 0
2025-06-11 12:13:57.014 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 优化版本最终根部门数量: 0
2025-06-11 12:13:57.015 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 优化版本获取所有用户完成，耗时: 192ms
2025-06-11 12:16:50.359 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:16:50.367 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 12:17:06.071 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 18560 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 12:17:06.073 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 12:17:06.105 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 12:17:09.002 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:17:09.013 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:17:09.073 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 48 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 12:17:09.085 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:17:09.086 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 12:17:09.106 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 12:17:09.129 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:17:09.130 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 12:17:09.158 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-11 12:17:09.202 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 12:17:09.205 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:17:09.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
2025-06-11 12:17:10.887 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 12:17:10.909 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 12:17:10.911 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 12:17:10.912 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 12:17:11.316 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 12:17:11.316 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5113 ms
2025-06-11 12:17:11.450 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 12:17:11.699 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 12:17:12.545 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 12:17:12.627 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 12:17:12.849 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 12:17:13.053 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 12:17:13.768 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 12:17:13.801 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 12:17:18.489 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 12:17:21.840 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 12:17:21.865 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 12:17:23.175 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 18.409 seconds (JVM running for 22.72)
2025-06-11 12:17:35.193 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 12:17:35.193 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 12:17:35.196 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-11 12:17:35.693 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- RECURSIVE 优化版本
2025-06-11 12:17:35.871 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回数据条数: 17
2025-06-11 12:17:35.874 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1, level=0, deptid=5001, userid=6001, parentid=0, deptname=科技集团总公司, username=张总经理}
2025-06-11 12:17:35.874 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5001, deptName=科技集团总公司, parentId=0, userId=6001, userName=张总经理
2025-06-11 12:17:35.875 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5001, name=科技集团总公司, level=0, parentId=0
2025-06-11 12:17:35.876 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6001, name=张总经理, 所属部门=5001
2025-06-11 12:17:35.877 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1, level=1, deptid=5002, userid=6002, parentid=5001, deptname=技术研发中心, username=李技术总监}
2025-06-11 12:17:35.877 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5002, deptName=技术研发中心, parentId=5001, userId=6002, userName=李技术总监
2025-06-11 12:17:35.877 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5002, name=技术研发中心, level=1, parentId=5001
2025-06-11 12:17:35.877 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6002, name=李技术总监, 所属部门=5002
2025-06-11 12:17:35.878 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,2, level=1, deptid=5003, userid=6003, parentid=5001, deptname=产品运营中心, username=王产品总监}
2025-06-11 12:17:35.878 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5003, deptName=产品运营中心, parentId=5001, userId=6003, userName=王产品总监
2025-06-11 12:17:35.878 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5003, name=产品运营中心, level=1, parentId=5001
2025-06-11 12:17:35.879 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6003, name=王产品总监, 所属部门=5003
2025-06-11 12:17:35.879 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,3, level=1, deptid=5004, parentid=5001, deptname=市场营销中心}
2025-06-11 12:17:35.879 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5004, deptName=市场营销中心, parentId=5001, userId=null, userName=null
2025-06-11 12:17:35.880 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5004, name=市场营销中心, level=1, parentId=5001
2025-06-11 12:17:35.881 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,4, level=1, deptid=5005, parentid=5001, deptname=人力资源中心}
2025-06-11 12:17:35.882 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5005, deptName=人力资源中心, parentId=5001, userId=null, userName=null
2025-06-11 12:17:35.883 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5005, name=人力资源中心, level=1, parentId=5001
2025-06-11 12:17:35.883 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,5, level=1, deptid=5006, parentid=5001, deptname=财务管理中心}
2025-06-11 12:17:35.884 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5006, deptName=财务管理中心, parentId=5001, userId=null, userName=null
2025-06-11 12:17:35.884 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5006, name=财务管理中心, level=1, parentId=5001
2025-06-11 12:17:35.884 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,1, level=2, deptid=5007, userid=6011, parentid=5002, deptname=前端开发部, username=冯离职员工}
2025-06-11 12:17:35.884 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5007, deptName=前端开发部, parentId=5002, userId=6011, userName=冯离职员工
2025-06-11 12:17:35.884 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5007, name=前端开发部, level=2, parentId=5002
2025-06-11 12:17:35.885 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6011, name=冯离职员工, 所属部门=5007
2025-06-11 12:17:35.885 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,1, level=2, deptid=5007, userid=6007, parentid=5002, deptname=前端开发部, username=周前端开发}
2025-06-11 12:17:35.885 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5007, deptName=前端开发部, parentId=5002, userId=6007, userName=周前端开发
2025-06-11 12:17:35.888 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6007, name=周前端开发, 所属部门=5007
2025-06-11 12:17:35.889 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,1, level=2, deptid=5007, userid=6004, parentid=5002, deptname=前端开发部, username=赵前端经理}
2025-06-11 12:17:35.890 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5007, deptName=前端开发部, parentId=5002, userId=6004, userName=赵前端经理
2025-06-11 12:17:35.892 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6004, name=赵前端经理, 所属部门=5007
2025-06-11 12:17:35.892 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,2, level=2, deptid=5008, userid=6008, parentid=5002, deptname=后端开发部, username=吴后端开发}
2025-06-11 12:17:35.892 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5008, deptName=后端开发部, parentId=5002, userId=6008, userName=吴后端开发
2025-06-11 12:17:35.893 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5008, name=后端开发部, level=2, parentId=5002
2025-06-11 12:17:35.893 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6008, name=吴后端开发, 所属部门=5008
2025-06-11 12:17:35.893 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,2, level=2, deptid=5008, userid=6005, parentid=5002, deptname=后端开发部, username=钱后端经理}
2025-06-11 12:17:35.893 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5008, deptName=后端开发部, parentId=5002, userId=6005, userName=钱后端经理
2025-06-11 12:17:35.893 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6005, name=钱后端经理, 所属部门=5008
2025-06-11 12:17:35.894 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,3, level=2, deptid=5009, userid=6006, parentid=5002, deptname=测试质量部, username=孙测试经理}
2025-06-11 12:17:35.894 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5009, deptName=测试质量部, parentId=5002, userId=6006, userName=孙测试经理
2025-06-11 12:17:35.894 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5009, name=测试质量部, level=2, parentId=5002
2025-06-11 12:17:35.894 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6006, name=孙测试经理, 所属部门=5009
2025-06-11 12:17:35.894 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,3, level=2, deptid=5009, userid=6009, parentid=5002, deptname=测试质量部, username=郑测试工程师}
2025-06-11 12:17:35.895 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5009, deptName=测试质量部, parentId=5002, userId=6009, userName=郑测试工程师
2025-06-11 12:17:35.895 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6009, name=郑测试工程师, 所属部门=5009
2025-06-11 12:17:35.896 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,1,4, level=2, deptid=5010, userid=6010, parentid=5002, deptname=运维安全部, username=王运维工程师}
2025-06-11 12:17:35.896 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5010, deptName=运维安全部, parentId=5002, userId=6010, userName=王运维工程师
2025-06-11 12:17:35.896 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5010, name=运维安全部, level=2, parentId=5002
2025-06-11 12:17:35.896 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6010, name=王运维工程师, 所属部门=5010
2025-06-11 12:17:35.896 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,2,1, level=2, deptid=5011, parentid=5003, deptname=产品设计部}
2025-06-11 12:17:35.897 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5011, deptName=产品设计部, parentId=5003, userId=null, userName=null
2025-06-11 12:17:35.898 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5011, name=产品设计部, level=2, parentId=5003
2025-06-11 12:17:35.898 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,2,2, level=2, deptid=5012, parentid=5003, deptname=用户运营部}
2025-06-11 12:17:35.899 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5012, deptName=用户运营部, parentId=5003, userId=null, userName=null
2025-06-11 12:17:35.900 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5012, name=用户运营部, level=2, parentId=5003
2025-06-11 12:17:35.901 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 原始数据行: {sort_path=1,2,3, level=2, deptid=5013, userid=6012, parentid=5003, deptname=数据分析部, username=陈实习生}
2025-06-11 12:17:35.901 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 转换后数据: deptId=5013, deptName=数据分析部, parentId=5003, userId=6012, userName=陈实习生
2025-06-11 12:17:35.902 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 创建部门: id=5013, name=数据分析部, level=2, parentId=5003
2025-06-11 12:17:35.904 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加用户: id=6012, name=陈实习生, 所属部门=5013
2025-06-11 12:17:35.904 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门数量: 13, 用户映射数量: 8
2025-06-11 12:17:35.905 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加根部门: id=5001, name=科技集团总公司
2025-06-11 12:17:35.907 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5002, name=技术研发中心, parentId=5001
2025-06-11 12:17:35.907 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5003, name=产品运营中心, parentId=5001
2025-06-11 12:17:35.908 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5004, name=市场营销中心, parentId=5001
2025-06-11 12:17:35.908 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5005, name=人力资源中心, parentId=5001
2025-06-11 12:17:35.908 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5006, name=财务管理中心, parentId=5001
2025-06-11 12:17:35.908 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5007, name=前端开发部, parentId=5002
2025-06-11 12:17:35.909 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5007, name=前端开发部, parentId=5002
2025-06-11 12:17:35.909 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5007, name=前端开发部, parentId=5002
2025-06-11 12:17:35.909 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5008, name=后端开发部, parentId=5002
2025-06-11 12:17:35.910 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5008, name=后端开发部, parentId=5002
2025-06-11 12:17:35.910 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5009, name=测试质量部, parentId=5002
2025-06-11 12:17:35.910 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5009, name=测试质量部, parentId=5002
2025-06-11 12:17:35.910 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5010, name=运维安全部, parentId=5002
2025-06-11 12:17:35.911 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5011, name=产品设计部, parentId=5003
2025-06-11 12:17:35.911 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5012, name=用户运营部, parentId=5003
2025-06-11 12:17:35.911 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 添加子部门: id=5013, name=数据分析部, parentId=5003
2025-06-11 12:17:35.911 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 优化版本最终根部门数量: 1
2025-06-11 12:17:35.912 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 优化版本获取所有用户完成，耗时: 218ms
2025-06-11 12:18:02.350 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 12:18:02.350 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 使用原始查询方法（已优化根部门查询条件）
2025-06-11 12:18:03.948 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 1598ms
2025-06-11 12:18:45.543 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 优化版本
2025-06-11 12:18:45.544 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 使用原始查询方法（已优化根部门查询条件）
2025-06-11 12:18:47.002 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成，耗时: 1458ms
2025-06-11 17:32:28.575 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 3608 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 17:32:28.578 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 17:32:28.597 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 17:32:32.419 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:32.425 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:32:32.480 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 38 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 17:32:32.486 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:32.488 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:32:32.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 17:32:32.529 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:32.529 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 17:32:32.558 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-11 17:32:32.600 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:32.606 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 17:32:32.670 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Redis repository interfaces.
2025-06-11 17:32:34.619 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 17:32:34.653 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:34.654 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:32:34.654 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 17:32:35.226 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:32:35.227 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 6427 ms
2025-06-11 17:32:35.432 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 17:32:35.860 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 17:32:37.023 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 17:32:37.255 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 17:32:37.669 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 17:32:38.140 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 17:32:38.233 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 25228 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 17:32:38.235 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 17:32:38.270 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 17:32:39.080 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 17:32:39.117 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:32:41.354 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:41.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:32:41.401 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 34 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 17:32:41.411 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:41.412 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:32:41.430 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 17:32:41.447 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:41.449 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 17:32:41.479 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-11 17:32:41.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:32:41.512 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 17:32:41.544 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-11 17:32:43.162 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 17:32:43.225 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:43.226 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:32:43.226 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 17:32:43.714 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:32:43.714 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5341 ms
2025-06-11 17:32:44.010 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 17:32:44.438 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 17:32:44.557 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 17:32:45.501 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 17:32:45.597 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 17:32:45.867 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 17:32:46.142 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 17:32:46.644 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 17:32:46.672 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:32:50.384 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:50.431 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 17:32:50.870 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 17:32:51.837 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 24.878 seconds (JVM running for 32.611)
2025-06-11 17:32:55.982 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:56.019 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8285 is already in use
2025-06-11 17:32:56.242 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:32:56.321 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 17:32:56.328 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:56.328 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-11 17:32:56.524 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:56.525 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8285"]
2025-06-11 17:32:56.541 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-11 17:33:03.970 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:33:03.977 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 17:33:13.915 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 28512 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 17:33:13.918 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 17:33:13.935 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 17:33:16.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:33:16.335 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:33:16.388 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 17:33:16.396 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:33:16.397 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 17:33:16.420 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 17:33:16.440 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:33:16.441 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 17:33:16.469 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 JPA repository interfaces.
2025-06-11 17:33:16.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 17:33:16.506 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 17:33:16.541 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-11 17:33:17.987 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 17:33:18.009 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 17:33:18.011 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 17:33:18.011 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 17:33:18.573 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 17:33:18.578 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4580 ms
2025-06-11 17:33:19.162 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 17:33:19.697 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 17:33:20.929 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 17:33:21.018 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 17:33:21.199 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 17:33:21.358 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 17:33:21.705 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 17:33:21.716 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 17:33:25.767 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 17:33:27.831 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 17:33:27.851 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 17:33:28.621 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.765 seconds (JVM running for 20.179)
2025-06-11 17:33:39.169 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 17:33:39.170 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 17:33:39.174 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-11 17:33:39.513 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取所有用户（组织架构树形结构）- 使用 RECURSIVE 优化
2025-06-11 17:33:39.648 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回数据条数: 17
2025-06-11 17:33:39.655 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 获取所有用户完成（RECURSIVE 优化），耗时: 141ms
2025-06-11 19:05:17.570 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:05:17.572 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 1002, 包含已删除: false, 最大层级: 0
2025-06-11 19:05:21.472 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:10:01.108 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:10:01.125 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:10:01.301 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:10:31.722 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:10:31.896 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 19:11:21.373 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 3764 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 19:11:21.376 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 19:11:21.446 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 19:11:26.277 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:11:26.280 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:11:26.309 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 19:11:26.316 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:11:26.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:11:26.328 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 19:11:26.349 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:11:26.349 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 19:11:26.389 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-11 19:11:26.434 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:11:26.436 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 19:11:26.458 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-11 19:11:27.284 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.CannotLoadBeanClassException: Error loading class [com.dfit.percode.service.impl.TOrgStructureServiceImpl] for bean with name 'TOrgStructureServiceImpl' defined in file [G:\fushun\permissionCode\target\classes\com\dfit\percode\service\impl\TOrgStructureServiceImpl.class]: problem with class file or dependent class; nested exception is java.lang.UnsupportedClassVersionError: com/dfit/percode/service/impl/TOrgStructureServiceImpl has been compiled by a more recent version of the Java Runtime (class file version 61.0), this version of the Java Runtime only recognizes class file versions up to 55.0
2025-06-11 19:11:27.298 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-11 19:13:15.275 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 20632 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 19:13:15.277 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 19:13:15.294 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 19:13:16.917 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:13:16.919 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:13:16.944 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 19:13:16.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:13:16.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:13:16.961 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 19:13:16.973 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:13:16.974 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 19:13:16.993 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-11 19:13:17.013 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:13:17.015 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 19:13:17.042 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-11 19:13:19.417 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 19:13:19.438 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 19:13:19.439 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 19:13:19.440 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 19:13:20.422 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 19:13:20.423 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5061 ms
2025-06-11 19:13:20.752 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 19:13:21.418 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 19:13:22.828 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 19:13:22.960 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 19:13:23.235 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 19:13:23.515 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 19:13:24.056 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 19:13:24.077 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:13:27.940 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:13:33.311 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 19:13:33.344 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 19:13:34.174 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 20.033 seconds (JVM running for 25.041)
2025-06-11 19:13:38.776 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 19:13:38.777 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 19:13:38.781 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-11 19:13:39.234 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:13:39.235 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:13:39.492 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:21:11.540 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:21:11.547 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 19:21:26.054 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 29440 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 19:21:26.056 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 19:21:26.077 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 19:21:27.830 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:21:27.834 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:21:27.864 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 19:21:27.870 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:21:27.871 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:21:27.887 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 19:21:27.900 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:21:27.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 19:21:27.926 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-11 19:21:27.952 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:21:27.954 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 19:21:27.983 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-11 19:21:29.005 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 19:21:29.017 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 19:21:29.018 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 19:21:29.018 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 19:21:29.307 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 19:21:29.307 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3175 ms
2025-06-11 19:21:29.427 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 19:21:29.666 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 19:21:30.511 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 19:21:30.631 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 19:21:30.940 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 19:21:31.162 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 19:21:31.508 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 19:21:31.521 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:21:33.941 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:21:36.471 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 19:21:36.491 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 19:21:37.327 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.357 seconds (JVM running for 15.145)
2025-06-11 19:21:40.291 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 19:21:40.292 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 19:21:40.299 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 7 ms
2025-06-11 19:21:41.307 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:21:41.307 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:21:41.525 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:24:27.105 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:24:27.111 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 19:24:35.987 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 27984 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 19:24:35.989 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 19:24:36.006 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 19:24:37.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:24:37.685 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:24:37.709 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 19:24:37.713 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:24:37.713 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:24:37.723 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 19:24:37.731 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:24:37.732 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 19:24:37.745 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-11 19:24:37.767 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:24:37.769 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 19:24:37.792 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-11 19:24:38.723 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 19:24:38.736 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 19:24:38.737 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 19:24:38.737 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 19:24:39.118 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 19:24:39.119 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3048 ms
2025-06-11 19:24:39.289 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 19:24:39.495 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 19:24:40.257 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 19:24:40.341 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 19:24:40.546 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 19:24:40.709 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 19:24:41.054 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 19:24:41.069 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:24:43.484 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:24:45.777 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 19:24:45.792 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 19:24:46.601 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.539 seconds (JVM running for 14.273)
2025-06-11 19:24:51.096 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 19:24:51.096 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 19:24:51.098 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-11 19:24:51.349 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:24:51.349 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:24:51.351 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始查询根部门，includeDeleted: false
2025-06-11 19:24:51.466 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 根部门查询成功，数量: 1
2025-06-11 19:24:51.533 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:27:20.265 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:27:20.302 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 19:27:29.001 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 44960 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-11 19:27:29.003 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-11 19:27:29.025 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-11 19:27:30.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:27:30.886 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:27:30.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-11 19:27:30.929 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:27:30.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-11 19:27:30.942 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-11 19:27:30.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:27:30.952 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-11 19:27:30.972 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-11 19:27:30.994 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-11 19:27:30.996 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 19:27:31.019 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-11 19:27:31.894 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-11 19:27:31.904 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-11 19:27:31.904 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 19:27:31.904 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-11 19:27:32.115 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 19:27:32.115 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3016 ms
2025-06-11 19:27:32.197 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 19:27:32.360 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 19:27:33.101 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-11 19:27:33.185 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-11 19:27:33.439 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-11 19:27:33.634 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-11 19:27:34.051 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-11 19:27:34.066 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 19:27:36.380 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-11 19:27:38.676 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-11 19:27:38.694 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-11 19:27:39.343 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.337 seconds (JVM running for 13.789)
2025-06-11 19:27:44.962 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 19:27:44.962 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 19:27:44.963 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 19:27:45.161 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:27:45.161 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:27:45.163 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始查询根部门，includeDeleted: false
2025-06-11 19:27:45.294 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 根部门查询成功，数量: 1
2025-06-11 19:27:45.362 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:27:48.214 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功，根部门数量: 1
2025-06-11 19:33:02.888 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树
2025-06-11 19:33:02.889 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: 5013, 包含已删除: false, 最大层级: 0
2025-06-11 19:33:02.889 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始查询根部门，includeDeleted: false
2025-06-11 19:33:03.003 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 根部门查询成功，数量: 1
2025-06-11 19:33:03.058 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门数量: 1
2025-06-11 19:33:05.923 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功，根部门数量: 1
2025-06-11 21:00:00.035 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-06-11 21:00:00.919 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
2025-06-11 21:00:01.065 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工岗位孤儿记录: 0 条
2025-06-11 21:00:01.126 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工职称孤儿记录: 0 条
2025-06-11 21:00:01.188 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工系统标识孤儿记录: 0 条
2025-06-11 21:00:01.189 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 孤儿记录关联汇总: 岗位表=0 条, 职称表=0 条, 系统表=0 条, 总计=0 条
2025-06-11 21:00:01.254 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 孤儿记录关联任务执行成功 ===
2025-06-11 23:34:28.001 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-11 23:34:28.032 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
