import java.sql.*;

public class SimpleCheckDuplicates {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== 简单检查重复部门名称 ===");
        
        try {
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            
            // 查找重复的部门名称
            PreparedStatement stmt = conn.prepareStatement(
                "SELECT organ_name, COUNT(*) as count " +
                "FROM t_org_structure " +
                "WHERE data_source = 2 " +
                "GROUP BY organ_name " +
                "HAVING COUNT(*) > 1 " +
                "ORDER BY count DESC"
            );
            
            ResultSet rs = stmt.executeQuery();
            
            int duplicateGroups = 0;
            int totalDuplicates = 0;
            
            System.out.println("重复的部门名称：");
            
            while (rs.next()) {
                String organName = rs.getString("organ_name");
                int count = rs.getInt("count");
                
                duplicateGroups++;
                totalDuplicates += count;
                
                System.out.println(duplicateGroups + ". " + organName + " (重复" + count + "次)");
            }
            
            rs.close();
            stmt.close();
            conn.close();
            
            System.out.println("\n统计结果：");
            System.out.println("重复组数: " + duplicateGroups);
            System.out.println("重复记录总数: " + totalDuplicates);
            System.out.println("需要删除的多余记录: " + (totalDuplicates - duplicateGroups));
            
        } catch (SQLException e) {
            System.err.println("检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
