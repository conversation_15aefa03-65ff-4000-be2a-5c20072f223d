package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 移动部门响应VO类
 * 返回移动操作的详细结果信息
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MoveOrgStructureResponseVO", description = "移动部门响应数据")
public class MoveOrgStructureResponseVO {
    
    @ApiModelProperty(value = "移动的部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orgId;
    
    @ApiModelProperty(value = "部门名称")
    private String organName;
    
    @ApiModelProperty(value = "原父部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long oldParentId;
    
    @ApiModelProperty(value = "原父部门名称")
    private String oldParentName;
    
    @ApiModelProperty(value = "新父部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long newParentId;
    
    @ApiModelProperty(value = "新父部门名称")
    private String newParentName;
    
    @ApiModelProperty(value = "新的排序序号")
    private Integer newOrderInfo;
    
    @ApiModelProperty(value = "移动操作是否成功")
    private Boolean success;
    
    @ApiModelProperty(value = "操作详细信息")
    private String message;
    
    @ApiModelProperty(value = "操作时间")
    private String operateTime;
}
