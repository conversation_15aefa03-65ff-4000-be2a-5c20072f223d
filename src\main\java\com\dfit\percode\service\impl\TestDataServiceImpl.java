package com.dfit.percode.service.impl;

import com.dfit.percode.mapper.TestDataMapper;
import com.dfit.percode.service.ITestDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 测试数据服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Service
public class TestDataServiceImpl implements ITestDataService {

    @Autowired
    private TestDataMapper testDataMapper;

    /**
     * 插入菜单测试数据
     * 包含菜单模块和菜单权限的完整测试数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertMenuTestData() {
        log.info("开始插入菜单测试数据");

        try {
            // 1. 插入菜单模块数据
            testDataMapper.insertMenuModules();
            log.info("菜单模块数据插入完成");

            // 2. 插入系统管理模块菜单
            testDataMapper.insertSystemMenus();
            log.info("系统管理模块菜单插入完成");

            // 3. 插入权限管理模块菜单
            testDataMapper.insertPermissionMenus();
            log.info("权限管理模块菜单插入完成");

            // 4. 插入业务管理模块菜单
            testDataMapper.insertBusinessMenus();
            log.info("业务管理模块菜单插入完成");

            // 5. 插入报表中心模块菜单
            testDataMapper.insertReportMenus();
            log.info("报表中心模块菜单插入完成");

            // 6. 插入监控中心模块菜单
            testDataMapper.insertMonitorMenus();
            log.info("监控中心模块菜单插入完成");

            log.info("所有菜单测试数据插入成功");

        } catch (Exception e) {
            log.error("插入菜单测试数据失败", e);
            throw new RuntimeException("插入菜单测试数据失败：" + e.getMessage());
        }
    }

    /**
     * 清理测试数据
     * 删除所有测试插入的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cleanTestData() {
        log.info("开始清理测试数据");

        try {
            // 1. 清理菜单权限数据
            testDataMapper.cleanMenuPermissions();
            log.info("菜单权限测试数据清理完成");

            // 2. 清理菜单模块数据
            testDataMapper.cleanMenuModules();
            log.info("菜单模块测试数据清理完成");

            log.info("所有测试数据清理成功");

        } catch (Exception e) {
            log.error("清理测试数据失败", e);
            throw new RuntimeException("清理测试数据失败：" + e.getMessage());
        }
    }
}
