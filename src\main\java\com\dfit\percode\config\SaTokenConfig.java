package com.dfit.percode.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import com.dfit.percode.common.SecurityConstants;
import com.dfit.percode.interceptor.HybridAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token配置类
 * 配置JWT模式和相关拦截器
 * 支持混合认证：自定义JWT + Sa-Token
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    @Autowired
    private HybridAuthInterceptor hybridAuthInterceptor;

    /**
     * 获取StpLogic对象 (使用JWT模式)
     *
     * @return StpLogic对象
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        // 使用JWT模式，Token信息完全自包含，无需依赖Redis等存储
        return new StpLogicJwtForSimple();
    }

    /**
     * 注册拦截器，支持混合认证
     * 执行顺序：混合认证拦截器 -> Sa-Token拦截器
     * 使用统一的SecurityConstants.WHITE_LIST配置
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 1. 注册混合认证拦截器（优先级最高）
        registry.addInterceptor(hybridAuthInterceptor)
                .addPathPatterns("/**")    // 拦截所有路径
                .excludePathPatterns(SecurityConstants.WHITE_LIST)  // 使用统一白名单配置
                .order(1);  // 设置最高优先级

        // 2. 注册Sa-Token拦截器（作为备份和补充）
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 指定一条 match 规则
            // 这里可以添加路径匹配规则，暂时不做限制
        }))
        .addPathPatterns("/**")    // 拦截所有路径
        .excludePathPatterns(SecurityConstants.WHITE_LIST)  // 使用统一白名单配置
        .order(2);  // 设置较低优先级
    }
}