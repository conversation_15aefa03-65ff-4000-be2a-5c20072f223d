package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 未授权用户列表项VO类
 * 用于显示未授权用户的基本信息
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "UnauthorizedUserItemVO", description = "未授权用户列表项")
public class UnauthorizedUserItemVO {
    
    @ApiModelProperty("用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("登录账号")
    private String account;

    @ApiModelProperty("部门名称")
    private String department;

    @ApiModelProperty(value = "用户状态：false-正常，true-停用", example = "false")
    private Boolean userState;

    @ApiModelProperty("创建时间")
    private String createTime;
}
