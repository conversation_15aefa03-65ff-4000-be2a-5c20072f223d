package com.dfit.percode.vo;

import com.dfit.percode.config.LongDeserializer;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

// import javax.validation.constraints.NotBlank;
// import javax.validation.constraints.NotNull;

/**
 * 新增部门请求VO类
 * 按照数据库字段设计
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "AddOrgStructureRequestVO", description = "新增部门请求参数")
public class AddOrgStructureRequestVO {

    @ApiModelProperty(value = "组织名称", example = "研发部", required = true)
     @NotBlank(message = "组织名称不能为空")
    private String organName;

    @ApiModelProperty(value = "父部门ID（根部门传null或0）", example = "1001")
    @JsonDeserialize(using = LongDeserializer.class)
    private Long preId;

    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;

    @ApiModelProperty(value = "数据来源，1页面输入，2数据同步", example = "1")
    private Integer dataSource = 1;
}
