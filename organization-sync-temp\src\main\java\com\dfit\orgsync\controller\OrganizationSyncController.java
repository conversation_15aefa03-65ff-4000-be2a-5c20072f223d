package com.dfit.orgsync.controller;

import com.dfit.orgsync.dto.SyncResult;
import com.dfit.orgsync.service.OrganizationSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 组织架构同步控制器
 * 提供组织架构数据同步的REST API接口
 */
@RestController
@RequestMapping("/api/organization-sync")
@Slf4j
public class OrganizationSyncController {
    
    @Autowired
    private OrganizationSyncService organizationSyncService;
    
    /**
     * 同步组织架构数据
     * 
     * @param filePath SQL文件路径
     * @return 同步结果
     */
    @PostMapping("/sync")
    public ResponseEntity<Map<String, Object>> syncData(@RequestParam String filePath) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("收到组织架构同步请求，文件路径: {}", filePath);
            
            // 执行同步
            SyncResult result = organizationSyncService.syncOrganizationData(filePath);
            
            response.put("success", true);
            response.put("message", "同步完成");
            response.put("data", result);
            
            log.info("组织架构同步成功: {}", result);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("组织架构同步失败", e);
            
            response.put("success", false);
            response.put("message", "同步失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 使用默认文件路径同步数据
     * 
     * @return 同步结果
     */
    @PostMapping("/sync-default")
    public ResponseEntity<Map<String, Object>> syncDataWithDefaultPath() {
        String defaultPath = "organization-sync-temp/department_sync_test.sql";
        return syncData(defaultPath);
    }
    
    /**
     * 获取同步状态信息
     * 
     * @return 状态信息
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以实现获取当前同步状态的逻辑
            Map<String, Object> status = new HashMap<>();
            status.put("isRunning", false); // 实际实现中可以维护同步状态
            status.put("lastSyncTime", null); // 可以从数据库查询最后同步时间
            status.put("version", "1.0.0");
            status.put("description", "组织架构数据同步工具");
            
            response.put("success", true);
            response.put("data", status);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取同步状态失败", e);
            
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "OrganizationSyncService");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取帮助信息
     * 
     * @return 帮助信息
     */
    @GetMapping("/help")
    public ResponseEntity<Map<String, Object>> help() {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> endpoints = new HashMap<>();
        
        endpoints.put("POST /api/organization-sync/sync", "同步组织架构数据，需要filePath参数");
        endpoints.put("POST /api/organization-sync/sync-default", "使用默认路径同步数据");
        endpoints.put("GET /api/organization-sync/status", "获取同步状态信息");
        endpoints.put("GET /api/organization-sync/health", "健康检查");
        endpoints.put("GET /api/organization-sync/help", "获取帮助信息");
        
        response.put("service", "组织架构数据同步服务");
        response.put("version", "1.0.0");
        response.put("description", "用于将department_sync_test.sql数据转换并同步到t_org_structure表");
        response.put("endpoints", endpoints);
        
        Map<String, Object> usage = new HashMap<>();
        usage.put("defaultFilePath", "organization-sync-temp/department_sync_test.sql");
        usage.put("dataSource", "2 (表示数据同步)");
        usage.put("idRange", "从**********开始分配");
        
        response.put("usage", usage);
        
        return ResponseEntity.ok(response);
    }
}
