2025-06-16 14:13:35.556 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 38968 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 14:13:35.564 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 14:13:35.567 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 14:13:38.027 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:13:38.030 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:13:38.057 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 14:13:38.060 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:13:38.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:13:38.072 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 14:13:38.080 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:13:38.080 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 14:13:38.110 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-16 14:13:38.132 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:13:38.134 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 14:13:38.152 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-16 14:13:39.479 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 14:13:39.494 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 14:13:39.496 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 14:13:39.497 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 14:13:40.026 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 14:13:40.027 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4261 ms
2025-06-16 14:13:40.143 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 14:13:40.367 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 14:13:42.564 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 14:13:42.867 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 14:13:43.183 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 14:13:43.484 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 14:13:43.995 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 14:13:44.014 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 14:13:47.141 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 14:13:50.504 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 14:13:50.516 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 14:13:51.493 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.339 seconds (JVM running for 24.72)
2025-06-16 14:15:53.797 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 14:15:53.797 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 14:15:53.811 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-16 14:35:18.011 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 14:35:18.026 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 14:35:33.306 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 41900 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 14:35:33.308 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 14:35:33.318 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 14:35:34.807 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:35:34.810 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:35:34.844 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 14:35:34.852 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:35:34.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:35:34.873 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 14:35:34.882 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:35:34.883 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 14:35:34.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-16 14:35:34.922 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:35:34.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 14:35:34.954 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-16 14:35:36.013 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 14:35:36.030 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 14:35:36.031 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 14:35:36.031 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 14:35:36.347 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 14:35:36.347 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2956 ms
2025-06-16 14:35:36.475 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 14:35:36.665 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 14:35:37.486 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 14:35:37.534 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 14:35:37.664 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 14:35:37.824 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 14:35:38.153 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 14:35:38.165 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 14:35:40.281 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 14:35:42.280 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 14:35:42.294 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 14:35:42.888 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.593 seconds (JVM running for 13.11)
2025-06-16 14:35:49.850 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 14:35:49.851 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 14:35:49.852 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 14:36:08.885 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-16 14:36:08.887 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001
2025-06-16 14:36:09.077 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-16 14:40:26.270 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 14:40:26.284 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 14:40:42.416 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 40368 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 14:40:42.418 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 14:40:42.428 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 14:40:43.750 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:40:43.753 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:40:43.775 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 14:40:43.778 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:40:43.779 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 14:40:43.788 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 14:40:43.796 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:40:43.797 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 14:40:43.810 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-16 14:40:43.827 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 14:40:43.829 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 14:40:43.846 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-16 14:40:44.799 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 14:40:44.811 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 14:40:44.812 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 14:40:44.813 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 14:40:45.087 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 14:40:45.087 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2607 ms
2025-06-16 14:40:45.198 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 14:40:45.378 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 14:40:46.426 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 14:40:46.480 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 14:40:46.638 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 14:40:46.799 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 14:40:47.308 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 14:40:47.329 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 14:40:49.743 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 14:40:51.707 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 14:40:51.723 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 14:40:52.660 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.239 seconds (JVM running for 13.929)
2025-06-16 14:40:57.411 [http-nio-8285-exec-3] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 14:40:57.411 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 14:40:57.413 [http-nio-8285-exec-3] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 14:40:57.556 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-16 14:40:57.557 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 7001
2025-06-16 14:40:57.725 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-16 14:40:57.725 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-16 14:40:57.726 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 168ms，根部门数量: 1
2025-06-16 15:10:24.351 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 15:10:24.359 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 15:10:36.267 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 38128 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 15:10:36.268 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 15:10:36.272 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 15:10:37.870 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 15:10:37.874 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 15:10:37.914 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 15:10:37.921 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 15:10:37.923 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 15:10:37.938 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 15:10:37.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 15:10:37.951 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 15:10:37.972 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-16 15:10:37.989 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 15:10:37.991 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 15:10:38.016 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-16 15:10:38.881 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 15:10:38.892 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 15:10:38.893 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 15:10:38.893 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 15:10:39.249 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 15:10:39.249 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2928 ms
2025-06-16 15:10:39.356 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 15:10:39.503 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 15:10:40.523 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 15:10:40.604 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 15:10:40.830 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 15:10:41.019 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 15:10:41.422 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 15:10:41.441 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 15:10:43.631 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 15:10:45.620 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 15:10:45.638 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 15:10:46.646 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.255 seconds (JVM running for 14.625)
2025-06-16 15:11:37.430 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 15:11:37.431 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 15:11:37.434 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-16 15:11:45.185 [http-nio-8285-exec-6] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`)<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 55] (through reference chain: com.dfit.percode.vo.ConfigureDataOperateRequestVO["operateTypes"])]
2025-06-16 15:12:10.626 [http-nio-8285-exec-7] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`)<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 59] (through reference chain: com.dfit.percode.vo.ConfigureDataOperateRequestVO["operateTypes"])]
2025-06-16 15:12:17.605 [http-nio-8285-exec-9] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`)<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 59] (through reference chain: com.dfit.percode.vo.ConfigureDataOperateRequestVO["operateTypes"])]
2025-06-16 15:13:17.803 [http-nio-8285-exec-8] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`); nested exception is com.fasterxml.jackson.databind.exc.MismatchedInputException: Cannot deserialize value of type `java.util.ArrayList<java.lang.Integer>` from String value (token `JsonToken.VALUE_STRING`)<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 61] (through reference chain: com.dfit.percode.vo.ConfigureDataOperateRequestVO["operateTypes"])]
2025-06-16 15:13:53.005 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.DataOperateController - 开始配置模块操作权限
2025-06-16 15:13:53.006 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.DataOperateController - 模块标识: department_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:13:53.204 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置模块操作权限
2025-06-16 15:13:53.207 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块标识: department_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:13:53.433 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 已删除现有的模块操作权限配置
2025-06-16 15:13:53.570 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=1
2025-06-16 15:13:53.660 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=2
2025-06-16 15:13:53.751 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=3
2025-06-16 15:13:53.832 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=4
2025-06-16 15:13:53.833 [http-nio-8285-exec-10] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块操作权限配置成功，模块标识: department_data_module, 配置的操作类型数量: 4
2025-06-16 15:13:53.912 [http-nio-8285-exec-10] INFO  com.dfit.percode.controller.DataOperateController - 模块操作权限配置成功
2025-06-16 15:14:25.088 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.DataOperateController - 开始配置模块操作权限
2025-06-16 15:14:25.088 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.DataOperateController - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:14:25.089 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置模块操作权限
2025-06-16 15:14:25.089 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:14:25.271 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 已删除现有的模块操作权限配置
2025-06-16 15:14:25.351 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=1
2025-06-16 15:14:25.447 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=2
2025-06-16 15:14:25.529 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=3
2025-06-16 15:14:25.630 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=4
2025-06-16 15:14:25.631 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块操作权限配置成功，模块标识: employee_data_module, 配置的操作类型数量: 4
2025-06-16 15:14:25.711 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.DataOperateController - 模块操作权限配置成功
2025-06-16 15:14:52.070 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.DataOperateController - 开始配置模块操作权限
2025-06-16 15:14:52.071 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.DataOperateController - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:14:52.071 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置模块操作权限
2025-06-16 15:14:52.072 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:14:52.238 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 已删除现有的模块操作权限配置
2025-06-16 15:14:52.326 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=1
2025-06-16 15:14:52.416 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=2
2025-06-16 15:14:52.511 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=3
2025-06-16 15:14:52.591 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=4
2025-06-16 15:14:52.592 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块操作权限配置成功，模块标识: employee_data_module, 配置的操作类型数量: 4
2025-06-16 15:14:52.690 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.DataOperateController - 模块操作权限配置成功
2025-06-16 15:15:06.296 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.DataOperateController - 开始配置模块操作权限
2025-06-16 15:15:06.296 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.DataOperateController - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:15:06.297 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 开始配置模块操作权限
2025-06-16 15:15:06.297 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块标识: employee_data_module, 操作类型: [1, 2, 3, 4]
2025-06-16 15:15:06.495 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 已删除现有的模块操作权限配置
2025-06-16 15:15:06.591 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=1
2025-06-16 15:15:06.673 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=2
2025-06-16 15:15:06.767 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=3
2025-06-16 15:15:06.893 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 插入模块操作权限配置: 操作类型=4
2025-06-16 15:15:06.893 [http-nio-8285-exec-2] INFO  c.d.percode.service.impl.TDataOperateServiceImpl - 模块操作权限配置成功，模块标识: employee_data_module, 配置的操作类型数量: 4
2025-06-16 15:15:06.991 [http-nio-8285-exec-2] INFO  com.dfit.percode.controller.DataOperateController - 模块操作权限配置成功
2025-06-16 16:27:44.756 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:27:45.305 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 16:27:59.286 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 39864 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 16:27:59.298 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 16:27:59.301 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 16:28:02.573 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:28:02.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:28:02.639 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 47 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 16:28:02.658 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:28:02.659 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:28:02.764 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 101 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 16:28:02.798 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:28:02.798 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 16:28:02.845 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 JPA repository interfaces.
2025-06-16 16:28:02.878 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:28:02.883 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 16:28:02.925 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-16 16:28:04.466 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 16:28:04.481 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 16:28:04.482 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 16:28:04.483 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 16:28:04.762 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 16:28:04.762 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 5321 ms
2025-06-16 16:28:04.856 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 16:28:05.029 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 16:28:05.778 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 16:28:05.835 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 16:28:06.003 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 16:28:06.133 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 16:28:06.510 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 16:28:06.533 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:28:09.296 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 16:28:11.569 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 16:28:11.595 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 16:28:12.598 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.272 seconds (JVM running for 17.294)
2025-06-16 16:28:18.085 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 16:28:18.085 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 16:28:18.087 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 16:28:49.967 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 16:28:49.985 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 16:28:50.106 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 16:28:50.556 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 569ms，模块数量: 7
2025-06-16 16:28:50.556 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
2025-06-16 16:29:18.791 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 16:29:18.912 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 16:29:18.971 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 16:29:18.973 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 180ms
2025-06-16 16:34:07.156 [http-nio-8285-exec-5] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:34:07.375 [http-nio-8285-exec-5] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共10条记录
2025-06-16 16:35:47.809 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:35:48.022 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:44:30.462 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:44:30.468 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 16:44:42.133 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 24780 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 16:44:42.138 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 16:44:42.156 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 16:44:45.260 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:44:45.264 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:44:45.299 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 16:44:45.303 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:44:45.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:44:45.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 16:44:45.328 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:44:45.328 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 16:44:45.354 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-06-16 16:44:45.378 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:44:45.380 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 16:44:45.408 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-16 16:44:46.266 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 16:44:46.278 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 16:44:46.278 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 16:44:46.278 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 16:44:46.534 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 16:44:46.535 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4310 ms
2025-06-16 16:44:46.643 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 16:44:46.844 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 16:44:47.661 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 16:44:47.728 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 16:44:47.892 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 16:44:48.039 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 16:44:48.378 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 16:44:48.403 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:44:51.099 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 16:44:53.119 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 16:44:53.135 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 16:44:53.936 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.989 seconds (JVM running for 16.208)
2025-06-16 16:45:04.920 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 16:45:04.920 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 16:45:04.922 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 16:45:05.207 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:45:05.358 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:45:10.707 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:45:10.767 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:45:40.062 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 16:45:40.186 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 16:45:40.252 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 16:45:40.253 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 190ms
2025-06-16 16:52:37.212 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:52:37.218 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 16:52:48.407 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 37812 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 16:52:48.409 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 16:52:48.426 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 16:52:50.788 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:52:50.794 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:52:50.826 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 16:52:50.830 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:52:50.831 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:52:50.844 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 16:52:50.851 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:52:50.852 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 16:52:50.868 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-16 16:52:50.891 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:52:50.894 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 16:52:50.915 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-16 16:52:51.699 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 16:52:51.710 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 16:52:51.711 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 16:52:51.711 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 16:52:51.999 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 16:52:52.000 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3515 ms
2025-06-16 16:52:52.156 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 16:52:52.362 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 16:52:53.130 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 16:52:53.202 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 16:52:53.391 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 16:52:53.556 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 16:52:53.865 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 16:52:53.877 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:52:57.171 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 16:52:59.303 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 16:52:59.322 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 16:53:00.432 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.046 seconds (JVM running for 16.763)
2025-06-16 16:53:07.752 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 16:53:07.755 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 16:53:07.758 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-16 16:53:08.108 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:53:08.263 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:53:15.076 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 16:53:15.205 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 16:53:15.269 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 16:53:15.270 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 193ms
2025-06-16 16:59:05.755 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:59:05.778 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 16:59:17.230 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 40304 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 16:59:17.232 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 16:59:17.242 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 16:59:19.208 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:59:19.211 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:59:19.244 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 16:59:19.248 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:59:19.249 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 16:59:19.262 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 16:59:19.273 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:59:19.273 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 16:59:19.292 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-16 16:59:19.314 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 16:59:19.317 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 16:59:19.344 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-06-16 16:59:20.683 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 16:59:20.701 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 16:59:20.702 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 16:59:20.703 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 16:59:21.238 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 16:59:21.239 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3927 ms
2025-06-16 16:59:21.667 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 16:59:22.163 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 16:59:23.412 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 16:59:23.479 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 16:59:23.625 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 16:59:23.776 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 16:59:24.210 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 16:59:24.224 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 16:59:26.710 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 16:59:28.935 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 16:59:28.947 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 16:59:29.643 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.625 seconds (JVM running for 16.794)
2025-06-16 16:59:35.754 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 16:59:35.755 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 16:59:35.756 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-16 16:59:36.112 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:59:36.259 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:59:38.825 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 16:59:38.902 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 16:59:44.390 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 16:59:44.513 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 16:59:44.575 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 16:59:44.575 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 185ms
2025-06-16 16:59:58.150 [http-nio-8285-exec-4] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 16:59:58.150 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 16:59:58.219 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 16:59:58.663 [http-nio-8285-exec-4] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 512ms，模块数量: 7
2025-06-16 16:59:58.664 [http-nio-8285-exec-4] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
2025-06-16 17:08:01.037 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:08:01.046 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 17:08:10.900 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 7560 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 17:08:10.902 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 17:08:10.918 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 17:08:12.469 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:08:12.473 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:08:12.503 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 17:08:12.508 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:08:12.509 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:08:12.523 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 17:08:12.535 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:08:12.536 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 17:08:12.553 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-16 17:08:12.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:08:12.585 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 17:08:12.613 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-16 17:08:13.562 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 17:08:13.572 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 17:08:13.573 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 17:08:13.573 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 17:08:13.822 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 17:08:13.823 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2845 ms
2025-06-16 17:08:13.926 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 17:08:14.122 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 17:08:14.916 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 17:08:14.969 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 17:08:15.155 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 17:08:15.356 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 17:08:15.825 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 17:08:15.851 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:08:18.547 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 17:08:20.321 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 17:08:20.337 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 17:08:21.189 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.343 seconds (JVM running for 14.194)
2025-06-16 17:08:27.292 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 17:08:27.292 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 17:08:27.295 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-16 17:08:27.594 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 17:08:27.774 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 17:08:27.834 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 17:08:27.835 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 239ms
2025-06-16 17:08:36.900 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 17:08:36.908 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 17:08:36.973 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 17:08:37.397 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 489ms，模块数量: 7
2025-06-16 17:08:37.398 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
2025-06-16 17:09:01.701 [http-nio-8285-exec-8] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 17:09:01.701 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 17:09:01.771 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 17:09:02.175 [http-nio-8285-exec-8] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 474ms，模块数量: 7
2025-06-16 17:09:02.175 [http-nio-8285-exec-8] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
2025-06-16 17:19:39.550 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:19:39.559 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 17:19:49.382 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 28648 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 17:19:49.386 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 17:19:49.406 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 17:19:51.288 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:19:51.293 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:19:51.332 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 17:19:51.337 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:19:51.338 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:19:51.358 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 17:19:51.370 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:19:51.371 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 17:19:51.393 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-16 17:19:51.414 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:19:51.415 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 17:19:51.442 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-16 17:19:52.392 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 17:19:52.408 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 17:19:52.409 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 17:19:52.409 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 17:19:52.682 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 17:19:52.682 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3215 ms
2025-06-16 17:19:52.817 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 17:19:53.424 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 17:19:54.427 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 17:19:54.492 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 17:19:54.660 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 17:19:54.814 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 17:19:55.129 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 17:19:55.145 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:19:58.171 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 17:20:00.304 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 17:20:00.327 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 17:20:01.195 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.745 seconds (JVM running for 15.47)
2025-06-16 17:26:57.702 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 17:26:57.702 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 17:26:57.707 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-16 17:26:57.879 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 17:26:57.895 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 17:26:58.070 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 17:26:58.934 [http-nio-8285-exec-1] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 1039ms，模块数量: 7
2025-06-16 17:26:58.934 [http-nio-8285-exec-1] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
2025-06-16 17:27:49.217 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始查询数据权限列表
2025-06-16 17:27:49.389 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限列表查询完成，共4条记录
2025-06-16 17:28:10.395 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 17:28:10.578 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 17:28:10.644 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 17:28:10.644 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 249ms
2025-06-16 17:33:18.331 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:33:18.342 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-16 17:33:32.829 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 39240 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-16 17:33:32.830 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-16 17:33:32.845 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-16 17:33:34.624 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:33:34.628 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:33:34.668 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-16 17:33:34.675 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:33:34.677 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-16 17:33:34.696 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-16 17:33:34.712 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:33:34.713 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-16 17:33:34.749 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 JPA repository interfaces.
2025-06-16 17:33:34.780 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-16 17:33:34.782 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-16 17:33:34.807 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
2025-06-16 17:33:35.640 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-16 17:33:35.654 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-16 17:33:35.655 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-16 17:33:35.655 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-16 17:33:35.915 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-16 17:33:35.915 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3007 ms
2025-06-16 17:33:36.018 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-16 17:33:36.177 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-16 17:33:36.984 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-16 17:33:37.079 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-16 17:33:37.296 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-16 17:33:37.486 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-16 17:33:37.825 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-16 17:33:37.839 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-16 17:33:40.740 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-16 17:33:42.622 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-16 17:33:42.635 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-16 17:33:43.447 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.881 seconds (JVM running for 14.607)
2025-06-16 17:33:47.764 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-16 17:33:47.765 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-16 17:33:47.772 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-16 17:33:48.277 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 开始获取角色详情，角色ID: 7001
2025-06-16 17:33:48.471 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的菜单权限数量: 10
2025-06-16 17:33:48.528 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色 7001 关联的数据权限数量: 2
2025-06-16 17:33:48.658 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.TRoleServiceImpl - 角色详情获取完成，角色ID: 7001，耗时: 378ms
2025-06-16 17:34:35.192 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 开始获取数据权限树形结构
2025-06-16 17:34:35.202 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 开始获取数据权限树形结构
2025-06-16 17:34:35.264 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 查询到模块数量: 7
2025-06-16 17:34:36.153 [http-nio-8285-exec-2] INFO  c.d.p.service.impl.TDataPermissionServiceImpl - 数据权限树形结构获取完成，耗时: 951ms，模块数量: 7
2025-06-16 17:34:36.153 [http-nio-8285-exec-2] INFO  c.dfit.percode.controller.DataPermissionController - 数据权限树形结构获取成功，模块数量: 7
