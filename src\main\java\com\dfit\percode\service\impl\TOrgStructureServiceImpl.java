package com.dfit.percode.service.impl;

import com.dfit.percode.entity.TOrgStructure;
import com.dfit.percode.mapper.TOrgStructureMapper;
import com.dfit.percode.service.ITOrgStructureService;
import com.dfit.percode.util.SnowflakeIdGenerator;
import com.dfit.percode.vo.SearchOrgStructureRequestVO;
import com.dfit.percode.vo.SearchOrgStructureResponseVO;
import com.dfit.percode.vo.AddOrgStructureRequestVO;
import com.dfit.percode.vo.AddOrgStructureResponseVO;
import com.dfit.percode.vo.UpdateOrgStructureRequestVO;
import com.dfit.percode.vo.UpdateOrgStructureResponseVO;
import com.dfit.percode.vo.DeleteOrgStructureRequestVO;
import com.dfit.percode.vo.DeleteOrgStructureResponseVO;
import com.dfit.percode.vo.OrgStructureTreeRequestVO;
import com.dfit.percode.vo.OrgStructureTreeResponseVO;
import com.dfit.percode.vo.MoveOrgStructureRequestVO;
import com.dfit.percode.vo.MoveOrgStructureResponseVO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dfit.percode.userPerm.RoleEntity;
import com.dfit.percode.userPerm.UserData;
import com.dfit.percode.userPerm.UserTree;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 组织架构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Slf4j
@Service
public class TOrgStructureServiceImpl extends ServiceImpl<TOrgStructureMapper, TOrgStructure> implements ITOrgStructureService {

    @Autowired
    TOrgStructureMapper tOrgStructureMapper;

    @Override
    public List<UserTree> choseUser() {
        //根目录
        List<UserTree> entities = tOrgStructureMapper.findAllDepartments();
        //子节点
        return buildTree(entities);
    }

    @Override
    public List<RoleEntity> roleSelected() {
        return tOrgStructureMapper.roleSelected();
    }

    /**
     * 搜索部门
     * 支持按组织名称模糊搜索、父部门ID精确匹配、是否包含已删除部门
     *
     * @param request 搜索请求参数
     * @return 搜索结果列表
     */
    @Override
    public List<SearchOrgStructureResponseVO> searchOrgStructure(SearchOrgStructureRequestVO request) {
        log.info("开始搜索部门");
        log.info("搜索参数: organName={}, preId={}, includeDeleted={}",
                request.getOrganName(), request.getPreId(), request.getIncludeDeleted());

        // 调用Mapper层搜索
        List<SearchOrgStructureResponseVO> resultList = tOrgStructureMapper.searchOrgStructure(
            request.getOrganName(),
            request.getPreId(),
            request.getIncludeDeleted()
        );

        // 为每个结果计算完整路径和层级
        for (SearchOrgStructureResponseVO result : resultList) {
            // 计算完整路径
            String fullPath = buildFullPath(result.getId());
            result.setFullPath(fullPath);

            // 计算层级（根据路径中的"/"数量）
            int level = fullPath.split("/").length;
            result.setLevel(level);
        }

        log.info("部门搜索完成，共找到{}条记录", resultList.size());
        return resultList;
    }

    /**
     * 新增部门
     * 创建新的组织架构节点，支持父子层级关系
     *
     * @param request 新增部门请求参数
     * @return 新增部门的详细信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AddOrgStructureResponseVO addOrgStructure(AddOrgStructureRequestVO request) {
        log.info("开始新增部门");
        log.info("部门名称: {}, 父部门ID: {}, 排序序号: {}",
                request.getOrganName(), request.getPreId(), request.getOrderInfo());

        // 1. 验证父部门是否存在（如果不是根部门）
        if (request.getPreId() != null && request.getPreId() > 0) {
            TOrgStructure parentOrg = this.getById(request.getPreId());
            if (parentOrg == null || (parentOrg.getIsDel() != null && parentOrg.getIsDel())) {
                log.error("父部门不存在或已删除，父部门ID: {}", request.getPreId());
                throw new RuntimeException("指定的父部门不存在或已删除");
            }
        }

        // 2. 检查同一父部门下是否已存在相同名称的部门
        Long parentId = (request.getPreId() != null && request.getPreId() > 0) ? request.getPreId() : null;
        int existsCount = tOrgStructureMapper.checkOrgNameExists(request.getOrganName(), parentId);
        if (existsCount > 0) {
            log.error("同一父部门下已存在相同名称的部门: {}", request.getOrganName());
            throw new RuntimeException("同一父部门下已存在相同名称的部门");
        }

        // 3. 生成ID
        SnowflakeIdGenerator idGenerator = new SnowflakeIdGenerator(1, 1);
        long orgId = idGenerator.generateId();

        // 4. 设置默认排序序号
        Integer orderInfo = request.getOrderInfo();
        if (orderInfo == null) {
            orderInfo = 1;
        }

        // 5. 设置数据来源（默认为页面输入）
        Integer dataSource = request.getDataSource();
        if (dataSource == null) {
            dataSource = 1; // 默认为页面输入
        }

        // 6. 插入部门记录
        tOrgStructureMapper.insertOrgStructure(
                orgId,
                request.getOrganName(),
                parentId,
                orderInfo,
                dataSource
        );

        // 6. 查询新增的部门信息并返回
        TOrgStructure newOrgStructure = this.getById(orgId);
        if (newOrgStructure == null) {
            log.error("新增部门后查询失败，部门ID: {}", orgId);
            throw new RuntimeException("新增部门后查询失败");
        }

        // 7. 转换为响应VO
        AddOrgStructureResponseVO response = convertToAddResponseVO(newOrgStructure);

        // 8. 构建完整路径和层级
        String fullPath = buildFullPath(orgId);
        response.setFullPath(fullPath);

        int level = fullPath.split("/").length;
        response.setLevel(level);

        log.info("部门新增成功，ID: {}, 完整路径: {}", orgId, fullPath);
        return response;
    }

    /**
     * 修改部门
     * 主要用于部门重命名和排序调整
     *
     * @param request 修改部门请求参数
     * @return 修改后的部门详细信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateOrgStructureResponseVO updateOrgStructure(UpdateOrgStructureRequestVO request) {
        log.info("开始修改部门");
        log.info("部门ID: {}, 新名称: {}, 排序序号: {}",
                request.getId(), request.getOrganName(), request.getOrderInfo());

        // 1. 验证部门是否存在
        TOrgStructure existingOrg = this.getById(request.getId());
        if (existingOrg == null || (existingOrg.getIsDel() != null && existingOrg.getIsDel())) {
            log.error("部门不存在或已删除，部门ID: {}", request.getId());
            throw new RuntimeException("指定的部门不存在或已删除");
        }

        // 2. 检查新名称在同一父部门下是否已存在（排除当前部门）
        int existsCount = tOrgStructureMapper.checkOrgNameExistsForUpdate(
                request.getOrganName(),
                existingOrg.getPreId(),
                request.getId()
        );
        if (existsCount > 0) {
            log.error("同一父部门下已存在相同名称的部门: {}", request.getOrganName());
            throw new RuntimeException("同一父部门下已存在相同名称的部门");
        }

        // 3. 设置默认排序序号（如果未提供）
        Integer orderInfo = request.getOrderInfo();
        if (orderInfo == null) {
            orderInfo = existingOrg.getOrderInfo(); // 保持原有排序
        }

        // 4. 处理数据来源字段
        Integer dataSource = request.getDataSource();
        if (dataSource == null) {
            dataSource = existingOrg.getDataSource() != null ? existingOrg.getDataSource() : 1; // 默认为页面输入
        }

        // 5. 执行更新操作
        int updateCount = tOrgStructureMapper.updateOrgStructure(
                request.getId(),
                request.getOrganName(),
                orderInfo,
                dataSource
        );

        if (updateCount == 0) {
            log.error("部门更新失败，可能部门不存在或已删除，部门ID: {}", request.getId());
            throw new RuntimeException("部门更新失败");
        }

        // 5. 查询更新后的部门信息并返回
        UpdateOrgStructureResponseVO response = tOrgStructureMapper.getUpdatedOrgStructureById(request.getId());

        // 6. 构建完整路径和层级
        String fullPath = buildFullPath(request.getId());
        response.setFullPath(fullPath);

        int level = fullPath.split("/").length;
        response.setLevel(level);

        log.info("部门修改成功，ID: {}, 新名称: {}, 完整路径: {}",
                request.getId(), request.getOrganName(), fullPath);
        return response;
    }

    /**
     * 删除部门
     * 支持级联删除子部门，逻辑删除方式
     *
     * @param request 删除部门请求参数
     * @return 删除操作的详细信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeleteOrgStructureResponseVO deleteOrgStructure(DeleteOrgStructureRequestVO request) {
        log.info("开始删除部门");
        log.info("部门ID: {}, 级联删除: {}", request.getId(), request.getCascadeDelete());

        // 1. 验证部门是否存在
        TOrgStructure existingOrg = this.getById(request.getId());
        if (existingOrg == null || (existingOrg.getIsDel() != null && existingOrg.getIsDel())) {
            log.error("部门不存在或已删除，部门ID: {}", request.getId());
            throw new RuntimeException("指定的部门不存在或已删除");
        }

        // 2. 获取部门基本信息
        DeleteOrgStructureResponseVO response = tOrgStructureMapper.getOrgBasicInfo(request.getId());
        if (response == null) {
            log.error("无法获取部门信息，部门ID: {}", request.getId());
            throw new RuntimeException("无法获取部门信息");
        }

        // 3. 查找所有子部门
        List<Long> childOrgIds = new ArrayList<>();
        // 默认进行级联删除，只有明确设置为false时才不级联删除
        boolean shouldCascadeDelete = request.getCascadeDelete() == null || request.getCascadeDelete();
        if (shouldCascadeDelete) {
            childOrgIds = tOrgStructureMapper.findAllChildOrgIds(request.getId());
            log.info("找到{}个子部门需要删除", childOrgIds.size());
        } else {
            log.info("不进行级联删除，只删除当前部门");
            // 检查是否有子部门，如果有则不允许删除
            List<Long> existingChildIds = tOrgStructureMapper.findAllChildOrgIds(request.getId());
            if (!existingChildIds.isEmpty()) {
                log.error("部门下有{}个子部门，不允许非级联删除", existingChildIds.size());
                throw new RuntimeException("部门下还有子部门，请先删除子部门或选择级联删除");
            }
        }

        // 4. 检查是否有用户关联（包括子部门）
        List<Long> allOrgIds = new ArrayList<>();
        allOrgIds.add(request.getId());
        allOrgIds.addAll(childOrgIds);

        int userCount = tOrgStructureMapper.checkUsersInOrgs(allOrgIds);
        if (userCount > 0) {
            log.error("部门或其子部门下还有{}个用户，无法删除", userCount);
            throw new RuntimeException("部门或其子部门下还有用户，请先转移用户后再删除");
        }

        // 5. 执行删除操作
        int deletedCount = 0;

        // 先删除子部门
        if (!childOrgIds.isEmpty()) {
            deletedCount += tOrgStructureMapper.batchDeleteOrgStructure(childOrgIds);
            log.info("成功删除{}个子部门", deletedCount);
        }

        // 再删除主部门
        int mainDeleteCount = tOrgStructureMapper.deleteOrgStructure(request.getId());
        if (mainDeleteCount == 0) {
            log.error("主部门删除失败，可能部门不存在或已删除，部门ID: {}", request.getId());
            throw new RuntimeException("部门删除失败");
        }
        deletedCount += mainDeleteCount;

        // 6. 设置返回信息
        response.setDeletedChildCount(childOrgIds.size());
        response.setDeletedChildIds(childOrgIds);
        response.setDeleteTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        log.info("部门删除成功，主部门ID: {}, 删除子部门数量: {}, 总删除数量: {}",
                request.getId(), childOrgIds.size(), deletedCount);
        return response;
    }

    /**
     * 获取部门结构树（优化版本）
     * 用于移动部门时显示可选择的部门树形结构
     * 优化：使用批量查询减少数据库访问次数，提升性能
     *
     * @param request 获取部门树请求参数
     * @return 部门树形结构列表
     */
    @Override
    public List<OrgStructureTreeResponseVO> getOrgStructureTree(OrgStructureTreeRequestVO request) {
        log.info("开始获取部门结构树（优化版本）");
        log.info("排除部门ID: {}, 包含已删除: {}, 最大层级: {}",
                request.getExcludeOrgId(), request.getIncludeDeleted(), request.getMaxLevel());

        long startTime = System.currentTimeMillis();

        try {
            // 1. 获取要排除的部门ID列表（包括自身和所有子部门）
            List<Long> excludeIds = new ArrayList<>();
            if (request.getExcludeOrgId() != null) {
                excludeIds.add(request.getExcludeOrgId());
                // 获取所有子部门ID
                List<Long> childIds = tOrgStructureMapper.findAllChildOrgIds(request.getExcludeOrgId());
                excludeIds.addAll(childIds);
                log.info("排除部门数量: {}", excludeIds.size());
            }

            // 2. 一次性查询所有部门数据（批量查询优化）
            List<OrgStructureTreeResponseVO> allOrgs = tOrgStructureMapper.findAllOrgStructuresForTree(
                request.getIncludeDeleted()
            );
            log.info("批量查询所有部门成功，数量: {}", allOrgs.size());

            // 3. 过滤掉被排除的部门
            List<OrgStructureTreeResponseVO> filteredOrgs = allOrgs.stream()
                .filter(org -> !excludeIds.contains(org.getId()))
                .collect(Collectors.toList());

            // 4. 构建部门ID到部门对象的映射（提升查找效率）
            Map<Long, OrgStructureTreeResponseVO> orgMap = filteredOrgs.stream()
                .collect(Collectors.toMap(OrgStructureTreeResponseVO::getId, org -> org));

            // 5. 构建父子关系映射
            Map<Long, List<OrgStructureTreeResponseVO>> parentChildMap = new HashMap<>();
            List<OrgStructureTreeResponseVO> rootOrgs = new ArrayList<>();

            for (OrgStructureTreeResponseVO org : filteredOrgs) {
                // 设置基本属性
                org.setSelectable(true);
                org.setChildren(new ArrayList<>());
                org.setChildCount(0);

                if (org.getPreId() == null || org.getPreId() == 0) {
                    // 根部门
                    org.setLevel(1);
                    rootOrgs.add(org);
                } else {
                    // 子部门
                    parentChildMap.computeIfAbsent(org.getPreId(), k -> new ArrayList<>()).add(org);
                }
            }

            // 6. 递归构建树形结构（内存操作，无数据库查询）
            for (OrgStructureTreeResponseVO rootOrg : rootOrgs) {
                buildOptimizedOrgTree(rootOrg, parentChildMap, orgMap, request.getMaxLevel(), 1);
            }

            long endTime = System.currentTimeMillis();
            log.info("部门结构树获取成功（优化版本），根部门数量: {}, 耗时: {}ms",
                    rootOrgs.size(), (endTime - startTime));

            return rootOrgs;

        } catch (Exception e) {
            log.error("获取部门结构树失败", e);
            throw e;
        }
    }

    /**
     * 优化的递归构建部门树形结构（内存操作，无数据库查询）
     * 使用预构建的映射关系，避免重复数据库查询
     *
     * @param parentOrg 父部门节点
     * @param parentChildMap 父子关系映射
     * @param orgMap 部门ID到部门对象的映射
     * @param maxLevel 最大层级深度（0表示不限制）
     * @param currentLevel 当前层级
     */
    private void buildOptimizedOrgTree(OrgStructureTreeResponseVO parentOrg,
                                     Map<Long, List<OrgStructureTreeResponseVO>> parentChildMap,
                                     Map<Long, OrgStructureTreeResponseVO> orgMap,
                                     Integer maxLevel, int currentLevel) {

        // 检查是否达到最大层级限制
        if (maxLevel != null && maxLevel > 0 && currentLevel >= maxLevel) {
            return;
        }

        // 设置当前节点的层级
        parentOrg.setLevel(currentLevel);

        // 获取子部门列表
        List<OrgStructureTreeResponseVO> children = parentChildMap.get(parentOrg.getId());

        if (children != null && !children.isEmpty()) {
            // 递归构建子部门树
            for (OrgStructureTreeResponseVO child : children) {
                child.setLevel(currentLevel + 1);
                buildOptimizedOrgTree(child, parentChildMap, orgMap, maxLevel, currentLevel + 1);
            }

            // 按排序字段排序
            children.sort((a, b) -> {
                if (a.getOrderInfo() == null && b.getOrderInfo() == null) return 0;
                if (a.getOrderInfo() == null) return 1;
                if (b.getOrderInfo() == null) return -1;
                return a.getOrderInfo().compareTo(b.getOrderInfo());
            });

            parentOrg.setChildren(children);
            parentOrg.setChildCount(children.size());
        } else {
            parentOrg.setChildren(new ArrayList<>());
            parentOrg.setChildCount(0);
        }
    }

    /**
     * 递归构建部门树形结构（原版本，保留作为备用）
     *
     * @param parentOrg 父部门节点
     * @param includeDeleted 是否包含已删除的部门
     * @param excludeIds 要排除的部门ID列表
     * @param maxLevel 最大层级深度（0表示不限制）
     * @param currentLevel 当前层级
     */
    @Deprecated
    private void buildOrgTree(OrgStructureTreeResponseVO parentOrg, Boolean includeDeleted,
                             List<Long> excludeIds, Integer maxLevel, int currentLevel) {

        // 检查是否达到最大层级限制
        if (maxLevel != null && maxLevel > 0 && currentLevel >= maxLevel) {
            return;
        }

        // 设置当前节点的层级和可选择性
        parentOrg.setLevel(currentLevel);
        parentOrg.setSelectable(!excludeIds.contains(parentOrg.getId()));

        // 构建完整路径
        String fullPath = buildFullPath(parentOrg.getId());
        parentOrg.setFullPath(fullPath);

        // 查询子部门
        List<OrgStructureTreeResponseVO> children = tOrgStructureMapper.findChildOrgStructures(
            parentOrg.getId(), includeDeleted
        );

        // 过滤掉被排除的子部门
        List<OrgStructureTreeResponseVO> filteredChildren = children.stream()
            .filter(child -> !excludeIds.contains(child.getId()))
            .collect(Collectors.toList());

        if (!filteredChildren.isEmpty()) {
            // 递归构建子部门树
            for (OrgStructureTreeResponseVO child : filteredChildren) {
                buildOrgTree(child, includeDeleted, excludeIds, maxLevel, currentLevel + 1);
            }
            parentOrg.setChildren(filteredChildren);
            parentOrg.setChildCount(filteredChildren.size());
        } else {
            parentOrg.setChildren(new ArrayList<>());
            parentOrg.setChildCount(0);
        }
    }

    /**
     * 移动部门
     * 将指定部门移动到新的父部门下，并重新排序
     *
     * @param request 移动部门请求参数
     * @return 移动操作的详细结果信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MoveOrgStructureResponseVO moveOrgStructure(MoveOrgStructureRequestVO request) {
        log.info("开始移动部门");
        log.info("部门ID: {}, 新父部门ID: {}, 新排序位置: {}",
                request.getOrgId(), request.getNewParentId(), request.getNewOrderInfo());

        MoveOrgStructureResponseVO response = new MoveOrgStructureResponseVO();
        response.setOrgId(request.getOrgId());
        response.setOperateTime(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        try {
            // 1. 验证要移动的部门是否存在
            TOrgStructure orgToMove = tOrgStructureMapper.selectById(request.getOrgId());
            if (orgToMove == null || Boolean.TRUE.equals(orgToMove.getIsDel())) {
                response.setSuccess(false);
                response.setMessage("要移动的部门不存在或已被删除");
                return response;
            }

            response.setOrganName(orgToMove.getOrganName());
            response.setOldParentId(orgToMove.getPreId());

            // 2. 获取原父部门名称
            if (orgToMove.getPreId() != null) {
                TOrgStructure oldParent = tOrgStructureMapper.selectById(orgToMove.getPreId());
                response.setOldParentName(oldParent != null ? oldParent.getOrganName() : "未知部门");
            } else {
                response.setOldParentName("根级别");
            }

            // 3. 验证新父部门（如果不为空）
            if (request.getNewParentId() != null && request.getNewParentId() != 0) {
                TOrgStructure newParent = tOrgStructureMapper.selectById(request.getNewParentId());
                if (newParent == null || Boolean.TRUE.equals(newParent.getIsDel())) {
                    response.setSuccess(false);
                    response.setMessage("目标父部门不存在或已被删除");
                    return response;
                }
                response.setNewParentId(request.getNewParentId());
                response.setNewParentName(newParent.getOrganName());

                // 4. 验证不能移动到自己或子部门下（防止循环引用）
                if (isDescendantOrSelf(request.getOrgId(), request.getNewParentId())) {
                    response.setSuccess(false);
                    response.setMessage("不能将部门移动到自己或子部门下");
                    return response;
                }
            } else {
                // 移动到根级别
                response.setNewParentId(null);
                response.setNewParentName("根级别");
            }

            // 5. 计算新的排序位置
            Integer newOrderInfo = request.getNewOrderInfo();
            if (newOrderInfo == null || newOrderInfo <= 0) {
                // 如果没有指定位置，则排到最后
                Integer maxOrder = tOrgStructureMapper.getMaxOrderInfoByParentId(
                    request.getNewParentId() == 0 ? null : request.getNewParentId()
                );
                newOrderInfo = maxOrder + 1;
            } else {
                // 如果指定了位置，需要调整其他部门的排序
                tOrgStructureMapper.adjustOrderInfoForMove(
                    request.getNewParentId() == 0 ? null : request.getNewParentId(),
                    newOrderInfo,
                    request.getOrgId()
                );
            }

            response.setNewOrderInfo(newOrderInfo);

            // 6. 执行移动操作
            int updateCount = tOrgStructureMapper.updateOrgParentAndOrder(
                request.getOrgId(),
                request.getNewParentId() == 0 ? null : request.getNewParentId(),
                newOrderInfo
            );

            if (updateCount > 0) {
                response.setSuccess(true);
                response.setMessage("部门移动成功");
                log.info("部门移动成功，部门ID: {}, 从 {} 移动到 {}",
                        request.getOrgId(), response.getOldParentName(), response.getNewParentName());
            } else {
                response.setSuccess(false);
                response.setMessage("部门移动失败，数据库更新失败");
            }

        } catch (Exception e) {
            log.error("移动部门时发生异常", e);
            response.setSuccess(false);
            response.setMessage("移动部门失败：" + e.getMessage());
            throw e; // 重新抛出异常以触发事务回滚
        }

        return response;
    }

    /**
     * 检查目标部门是否是源部门的子部门或自身
     * 用于防止循环引用
     *
     * @param sourceOrgId 源部门ID
     * @param targetOrgId 目标部门ID
     * @return true表示目标是源的子部门或自身，false表示可以移动
     */
    private boolean isDescendantOrSelf(Long sourceOrgId, Long targetOrgId) {
        // 如果目标部门就是源部门自身
        if (sourceOrgId.equals(targetOrgId)) {
            return true;
        }

        // 获取源部门的所有子部门ID
        List<Long> childIds = tOrgStructureMapper.findAllChildOrgIds(sourceOrgId);

        // 检查目标部门是否在子部门列表中
        return childIds.contains(targetOrgId);
    }

    // 构建树形结构
    private List<UserTree> buildTree(List<UserTree> entities) {
        List<UserTree> tree = new ArrayList<>();
        for (UserTree entity : entities) {
            // 递归查找子文件夹/文件
            List<UserTree> children = tOrgStructureMapper.findByParentId(entity.getId());
            List<UserData> userList = tOrgStructureMapper.findUserByDepartId(entity.getId());
            if (!children.isEmpty()) {
                entity.setDepartChildren(buildTree(children));  // 设置子节点
            }
            if(!userList.isEmpty()){
                entity.setUser(userList);
            }
            tree.add(entity);
        }
        return tree;
    }

    /**
     * 转换 TOrgStructure 实体为 AddOrgStructureResponseVO
     * 避免使用有问题的自定义查询，使用标准的实体查询
     *
     * @param orgStructure 部门实体
     * @return 新增部门响应VO
     */
    private AddOrgStructureResponseVO convertToAddResponseVO(TOrgStructure orgStructure) {
        AddOrgStructureResponseVO response = new AddOrgStructureResponseVO();

        // 设置基本信息
        response.setId(orgStructure.getId());
        response.setOrganName(orgStructure.getOrganName());
        response.setPreId(orgStructure.getPreId());
        response.setOrderInfo(orgStructure.getOrderInfo());
        response.setDataSource(orgStructure.getDataSource());

        // 查询父部门名称
        if (orgStructure.getPreId() != null) {
            TOrgStructure parentOrg = this.getById(orgStructure.getPreId());
            if (parentOrg != null) {
                response.setParentName(parentOrg.getOrganName());
            }
        }

        return response;
    }

    /**
     * 构建部门的完整路径
     * 递归向上查找父部门，构建完整的路径字符串
     *
     * @param orgId 部门ID
     * @return 完整路径，如："公司/研发部/前端开发组"
     */
    private String buildFullPath(Long orgId) {
        if (orgId == null) {
            return "";
        }

        // 查询当前部门信息（只查询必要字段，避免时间字段问题）
        TOrgStructure currentOrg = tOrgStructureMapper.getOrgForPath(orgId);
        if (currentOrg == null) {
            return "";
        }

        // 如果是根部门（preId为null），直接返回当前部门名称
        if (currentOrg.getPreId() == null) {
            return currentOrg.getOrganName();
        }

        // 递归构建父部门路径
        String parentPath = buildFullPath(currentOrg.getPreId());
        if (parentPath.isEmpty()) {
            return currentOrg.getOrganName();
        } else {
            return parentPath + "/" + currentOrg.getOrganName();
        }
    }
}
