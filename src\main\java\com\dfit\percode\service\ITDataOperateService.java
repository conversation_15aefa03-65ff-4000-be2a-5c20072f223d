package com.dfit.percode.service;

import com.dfit.percode.entity.TDataOperate;
import com.dfit.percode.vo.ConfigureDataOperateRequestVO;
import com.dfit.percode.vo.ConfigureDataOperateListRequestVO;
import com.dfit.percode.vo.DataOperateConfigResponseVO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 数据操作权限服务接口
 * 提供数据操作权限的配置和查询功能
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
public interface ITDataOperateService extends IService<TDataOperate> {

    /**
     * 配置数据操作权限（模块级别）
     * 为指定的模块配置可执行的操作类型（新增、修改、删除等）
     *
     * @param request 配置模块操作权限请求参数
     */
    void configureDataOperate(ConfigureDataOperateRequestVO request);

    /**
     * 配置数据操作权限（数据级别）
     * 为多个数据权限分别配置可执行的操作类型
     *
     * @param request 配置数据权限操作权限请求参数
     */
    void configureDataOperateList(ConfigureDataOperateListRequestVO request);

    /**
     * 获取数据操作权限配置
     * 查询指定数据权限的操作类型配置
     *
     * @param dataIdentifier 数据标识
     * @param moduleIdentifier 模块标识
     * @return 数据操作权限配置信息
     */
    DataOperateConfigResponseVO getDataOperateConfig(String dataIdentifier, String moduleIdentifier);

    /**
     * 调试接口：查看角色数据权限详情
     * 用于调试级联删除功能
     *
     * @param roleId 角色ID
     * @param moduleIdentifier 模块标识符
     * @return 角色数据权限详情列表
     */
    java.util.List<java.util.Map<String, Object>> debugRoleDataPermissions(Long roleId, String moduleIdentifier);
}
