package com.dfit.percode.mapper;

import com.dfit.percode.entity.TDataPermission;
import com.dfit.percode.vo.*;
import com.dfit.percode.vo.response.RoleUsageVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 数据权限Mapper接口
 * 提供数据权限的数据库操作
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Mapper
public interface TDataPermissionMapper extends BaseMapper<TDataPermission> {

    /**
     * 分页查询数据权限列表
     */
    @Select("<script>" +
            "SELECT " +
            "dp.id as \"id\", " +
            "dp.name as \"name\", " +
            "dp.data_identifier as \"dataIdentifier\", " +
            "dp.module_identifier as \"moduleIdentifier\", " +
            "dm.module_name as \"moduleName\", " +
            "dp.data_type as \"dataType\", " +
            "dp.order_info as \"orderInfo\", " +
            "dp.is_disable as \"isDisable\", " +
            "dp.create_time as \"createTime\", " +
            "dp.modify_time as \"modifyTime\" " +
            "FROM t_data_permission dp " +
            "LEFT JOIN t_data_module dm ON dp.module_identifier = dm.module_identifier AND dm.is_del = false " +
            "WHERE dp.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND dp.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            "AND dp.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='isDisable != null'>" +
            "AND dp.is_disable = #{isDisable} " +
            "</if>" +
            "ORDER BY dp.order_info ASC, dp.create_time DESC " +
            "<if test='pageNum != null and pageSize != null'>" +
            "LIMIT #{pageSize} OFFSET #{pageNum} " +
            "</if>" +
            "</script>")
    List<DataPermissionListResponseVO> getDataPermissionList(DataPermissionListRequestVO request);

    /**
     * 获取数据权限总数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM t_data_permission dp " +
            "WHERE dp.is_del = false " +
            "<if test='moduleIdentifier != null and moduleIdentifier != \"\"'>" +
            "AND dp.module_identifier = #{moduleIdentifier} " +
            "</if>" +
            "<if test='name != null and name != \"\"'>" +
            "AND dp.name LIKE CONCAT('%', #{name}, '%') " +
            "</if>" +
            "<if test='isDisable != null'>" +
            "AND dp.is_disable = #{isDisable} " +
            "</if>" +
            "</script>")
    Integer getDataPermissionTotal(DataPermissionListRequestVO request);

    /**
     * 检查数据标识是否已存在
     */
    @Select("SELECT COUNT(*) FROM t_data_permission " +
            "WHERE data_identifier = #{dataIdentifier} " +
            "AND module_identifier = #{moduleIdentifier} " +
            "AND is_del = false")
    int checkDataIdentifierExists(@Param("dataIdentifier") String dataIdentifier,
                                  @Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 检查模块是否存在
     */
    @Select("SELECT COUNT(*) FROM t_data_module " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false")
    int checkModuleExists(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 插入数据权限记录
     */
    @Insert("INSERT INTO t_data_permission (" +
            "id, name, pre_id, module_identifier, data_type, order_info, " +
            "is_disable, is_del, create_time, modify_time, data_identifier" +
            ") VALUES (" +
            "#{id}, #{name}, #{preId}, #{moduleIdentifier}, #{dataType}, #{orderInfo}, " +
            "#{isDisable}, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, #{dataIdentifier}" +
            ")")
    void insertDataPermission(@Param("id") Long id,
                             @Param("name") String name,
                             @Param("preId") Long preId,
                             @Param("moduleIdentifier") String moduleIdentifier,
                             @Param("dataType") Integer dataType,
                             @Param("orderInfo") Integer orderInfo,
                             @Param("isDisable") Boolean isDisable,
                             @Param("dataIdentifier") String dataIdentifier);

    /**
     * 获取数据权限详情
     */
    @Select("SELECT " +
            "dp.id as \"id\", " +
            "dp.name as \"name\", " +
            "dp.pre_id as \"preId\", " +
            "dp.data_identifier as \"dataIdentifier\", " +
            "dp.module_identifier as \"moduleIdentifier\", " +
            "dm.module_name as \"moduleName\", " +
            "dp.data_type as \"dataType\", " +
            "dp.order_info as \"orderInfo\", " +
            "dp.is_disable as \"isDisable\", " +
            "dp.create_time as \"createTime\", " +
            "dp.modify_time as \"modifyTime\" " +
            "FROM t_data_permission dp " +
            "LEFT JOIN t_data_module dm ON dp.module_identifier = dm.module_identifier AND dm.is_del = false " +
            "WHERE dp.id = #{dataId} AND dp.is_del = false")
    DataPermissionDetailResponseVO getDataPermissionDetail(@Param("dataId") Long dataId);

    /**
     * 检查数据权限是否存在（根据ID）
     */
    @Select("SELECT COUNT(*) FROM t_data_permission " +
            "WHERE id = #{dataId} AND is_del = false")
    int checkDataPermissionExistsById(@Param("dataId") Long dataId);

    /**
     * 检查数据标识是否已存在（排除指定ID）
     */
    @Select("SELECT COUNT(*) FROM t_data_permission " +
            "WHERE data_identifier = #{dataIdentifier} " +
            "AND module_identifier = #{moduleIdentifier} " +
            "AND id != #{excludeId} " +
            "AND is_del = false")
    int checkDataIdentifierExistsExcludeId(@Param("dataIdentifier") String dataIdentifier,
                                          @Param("moduleIdentifier") String moduleIdentifier,
                                          @Param("excludeId") Long excludeId);

    /**
     * 更新数据权限
     */
    @Update("UPDATE t_data_permission SET " +
            "name = #{name}, " +
            "order_info = #{orderInfo}, " +
            "is_disable = #{isDisable}, " +
            "data_identifier = #{dataIdentifier}, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{dataId}")
    void updateDataPermission(@Param("dataId") Long dataId,
                             @Param("name") String name,
                             @Param("orderInfo") Integer orderInfo,
                             @Param("isDisable") Boolean isDisable,
                             @Param("dataIdentifier") String dataIdentifier);

    /**
     * 检查数据权限是否被角色使用
     */
    @Select("SELECT COUNT(*) FROM t_roles_data_permission " +
            "WHERE data_id = #{dataId} AND is_del = false")
    int checkDataPermissionInUse(@Param("dataId") Long dataId);

    /**
     * 逻辑删除数据权限
     */
    @Update("UPDATE t_data_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE id = #{dataId}")
    void deleteDataPermissionById(@Param("dataId") Long dataId);

    /**
     * 插入测试数据
     */
    @Insert("<script>" +
            "INSERT INTO t_data_permission (id, name, pre_id, module_identifier, data_type, order_info, is_disable, is_del, create_time, modify_time, data_identifier) VALUES " +
            "(2001, '用户基础数据', 0, 'user_data_module', 1, 1, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user_basic_data'), " +
            "(2002, '用户档案数据', 0, 'user_data_module', 1, 2, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user_profile_data'), " +
            "(2003, '用户认证数据', 0, 'user_data_module', 1, 3, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'user_auth_data'), " +
            "(2004, '订单基础数据', 0, 'order_data_module', 1, 1, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'order_basic_data'), " +
            "(2005, '订单详情数据', 0, 'order_data_module', 1, 2, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'order_detail_data'), " +
            "(2006, '订单支付数据', 0, 'order_data_module', 1, 3, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'order_payment_data'), " +
            "(2007, '产品基础数据', 0, 'product_data_module', 1, 1, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'product_basic_data'), " +
            "(2008, '产品库存数据', 0, 'product_data_module', 1, 2, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'product_inventory_data')" +
            "</script>")
    void insertTestData();

    /**
     * 获取数据权限树形结构
     * 按模块分组展示数据权限，包含模块的操作权限信息
     *
     * @return 数据权限树形结构列表
     */
    @Select("SELECT " +
            "dm.id as \"moduleId\", " +
            "dm.module_name as \"moduleName\", " +
            "dm.module_identifier as \"moduleIdentifier\" " +
            "FROM t_data_module dm " +
            "WHERE dm.is_del = false " +
            "ORDER BY dm.order_info")
    List<DataPermissionTreeResponseVO> getDataPermissionModules();

    /**
     * 根据模块标识获取数据权限列表
     *
     * @param moduleIdentifier 模块标识
     * @return 数据权限列表
     */
    @Select("SELECT " +
            "dp.id as \"dataId\", " +
            "dp.name as \"dataName\", " +
            "dp.data_identifier as \"dataIdentifier\", " +
            "dp.data_type as \"dataType\", " +
            "dp.order_info as \"orderInfo\", " +
            "dp.is_disable as \"isDisable\" " +
            "FROM t_data_permission dp " +
            "WHERE dp.module_identifier = #{moduleIdentifier} " +
            "AND dp.is_del = false " +
            "ORDER BY dp.order_info")
    List<DataPermissionTreeResponseVO.DataPermissionTreeItemVO> getDataPermissionsByModule(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 获取模块的操作权限列表
     *
     * @param moduleIdentifier 模块标识
     * @return 操作权限列表
     */
    @Select("SELECT operate_type " +
            "FROM t_data_operate " +
            "WHERE module_identifier = #{moduleIdentifier} " +
            "AND is_del = false " +
            "AND (data_identifier IS NULL OR data_identifier = '') " +
            "ORDER BY operate_type")
    List<Integer> getModuleOperateTypes(@Param("moduleIdentifier") String moduleIdentifier);

    /**
     * 获取特定数据权限的操作权限列表
     *
     * @param dataIdentifier 数据权限标识符
     * @return 操作权限列表
     */
    @Select("SELECT operate_type " +
            "FROM t_data_operate " +
            "WHERE data_identifier = #{dataIdentifier} " +
            "AND is_del = false " +
            "ORDER BY operate_type")
    List<Integer> getDataOperateTypes(@Param("dataIdentifier") String dataIdentifier);

    /**
     * 根据数据权限ID获取数据权限标识符
     *
     * @param dataId 数据权限ID
     * @return 数据权限标识符
     */
    @Select("SELECT data_identifier " +
            "FROM t_data_permission " +
            "WHERE id = #{dataId} AND is_del = false")
    String getDataIdentifierById(@Param("dataId") Long dataId);

    /**
     * 根据数据标识符获取数据权限ID
     * 避免查询完整实体，只返回ID
     *
     * @param dataIdentifier 数据标识符
     * @return 数据权限ID
     */
    @Select("SELECT id " +
            "FROM t_data_permission " +
            "WHERE data_identifier = #{dataIdentifier} AND is_del = false")
    Long getDataIdByIdentifier(@Param("dataIdentifier") String dataIdentifier);

    /**
     * 批量获取所有模块的操作权限
     * 用于性能优化，一次性查询所有模块的操作权限，避免N+1查询问题
     *
     * @return 模块操作权限映射，key为模块标识符，value为操作权限列表
     */
    @Select("SELECT " +
            "module_identifier as \"moduleIdentifier\", " +
            "operate_type as \"operateType\" " +
            "FROM t_data_operate " +
            "WHERE is_del = false " +
            "AND (data_identifier IS NULL OR data_identifier = '') " +
            "ORDER BY module_identifier, operate_type")
    @Results({
        @Result(property = "moduleIdentifier", column = "moduleIdentifier"),
        @Result(property = "operateType", column = "operateType")
    })
    List<ModuleOperateTypeVO> getAllModuleOperateTypes();

    /**
     * 批量获取所有数据权限的操作权限
     * 用于性能优化，一次性查询所有数据权限的操作权限，避免N+1查询问题
     *
     * @return 数据权限操作权限映射，key为数据权限标识符，value为操作权限列表
     */
    @Select("SELECT " +
            "data_identifier as \"dataIdentifier\", " +
            "operate_type as \"operateType\" " +
            "FROM t_data_operate " +
            "WHERE is_del = false " +
            "AND data_identifier IS NOT NULL " +
            "AND data_identifier != '' " +
            "ORDER BY data_identifier, operate_type")
    @Results({
        @Result(property = "dataIdentifier", column = "dataIdentifier"),
        @Result(property = "operateType", column = "operateType")
    })
    List<DataOperateTypeVO> getAllDataOperateTypes();

    /**
     * 模块操作权限VO类
     * 用于批量查询模块操作权限的结果映射
     */
    class ModuleOperateTypeVO {
        private String moduleIdentifier;
        private Integer operateType;

        public String getModuleIdentifier() {
            return moduleIdentifier;
        }

        public void setModuleIdentifier(String moduleIdentifier) {
            this.moduleIdentifier = moduleIdentifier;
        }

        public Integer getOperateType() {
            return operateType;
        }

        public void setOperateType(Integer operateType) {
            this.operateType = operateType;
        }
    }

    /**
     * 数据权限操作权限VO类
     * 用于批量查询数据权限操作权限的结果映射
     */
    class DataOperateTypeVO {
        private String dataIdentifier;
        private Integer operateType;

        public String getDataIdentifier() {
            return dataIdentifier;
        }

        public void setDataIdentifier(String dataIdentifier) {
            this.dataIdentifier = dataIdentifier;
        }

        public Integer getOperateType() {
            return operateType;
        }

        public void setOperateType(Integer operateType) {
            this.operateType = operateType;
        }
    }

    // ==================== V2版本删除功能相关方法 ====================

    /**
     * 查询使用指定数据权限的角色列表
     * 用于V2版本删除功能中显示使用情况
     *
     * @param dataId 数据权限ID
     * @return 角色列表
     */
    @Select("SELECT DISTINCT r.id, r.role_name " +
            "FROM t_role r " +
            "JOIN t_roles_data_permission rdp ON r.id = rdp.role_id " +
            "WHERE rdp.data_id = #{dataId} " +
            "AND r.is_del = false " +
            "AND rdp.is_del = false")
    List<RoleUsageVO> getRolesByDataId(@Param("dataId") Long dataId);

    /**
     * 根据数据权限ID获取数据权限名称
     * 用于V2版本删除功能中显示数据权限信息
     *
     * @param dataId 数据权限ID
     * @return 数据权限名称
     */
    @Select("SELECT name FROM t_data_permission " +
            "WHERE id = #{dataId} " +
            "AND is_del = false")
    String getDataPermissionNameById(@Param("dataId") Long dataId);

    /**
     * 级联删除数据权限的角色权限关联
     * 用于V2版本强制删除功能
     *
     * @param dataId 数据权限ID
     */
    @Update("UPDATE t_roles_data_permission SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE data_id = #{dataId}")
    void deleteDataRolePermissions(@Param("dataId") Long dataId);

    /**
     * 级联删除数据权限的操作配置
     * 用于V2版本强制删除功能
     *
     * @param dataId 数据权限ID
     */
    @Update("UPDATE t_data_operate SET " +
            "is_del = true, " +
            "modify_time = CURRENT_TIMESTAMP " +
            "WHERE data_identifier = (" +
            "  SELECT data_identifier FROM t_data_permission " +
            "  WHERE id = #{dataId} AND is_del = false" +
            ")")
    void deleteDataOperatesByDataId(@Param("dataId") Long dataId);
}
