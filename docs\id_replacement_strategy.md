# SQL脚本ID替换策略与实现

## 1. ID替换策略

我们将采用以下策略来将 `org_structure_repair.sql` 中的字符串ID替换为符合 `int8` 类型的纯数字ID：

1.  **唯一性**：为每个唯一的字符串ID（如 'X17000000'）生成一个唯一的、符合 `int8` 范围的数字ID。
2.  **关系保持**：在替换过程中，必须保持原始数据中的父子关系 (`pre_id`)。
3.  **ID生成**：我们将使用一个简单的自增序列（例如，从 `1000000000000000000` 开始）来生成新的数字ID，以避免与现有数据冲突。
4.  **映射关系**：创建一个从旧的字符串ID到新的数字ID的映射表（`Map<String, Long>`），以便在处理 `pre_id` 时能够正确查找。

## 2. Java实现

为了自动化这个过程，我们可以创建一个Java工具类 `OrgStructureSqlConverter.java`。这个类将读取原始的SQL文件，解析每一行 `INSERT` 语句，应用上述策略，然后生成一个新的SQL文件 `docs/org_structure_repair_numeric.sql`。

请在 `src/main/java/com/dfit/percode/util/` 目录下创建以下Java文件。

### `OrgStructureSqlConverter.java`

```java
package com.dfit.percode.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OrgStructureSqlConverter {

    // ID生成器的起始值，确保足够大以避免与现有ID冲突
    private static final long START_ID = 1000000000000000000L;
    private static final AtomicLong idGenerator = new AtomicLong(START_ID);
    private static final Map<String, Long> idMapping = new HashMap<>();

    public static void main(String[] args) {
        String inputFile = "docs/org_structure_repair.sql";
        String outputFile = "docs/org_structure_repair_numeric.sql";

        try {
            convertSqlFile(inputFile, outputFile);
            System.out.println("SQL file conversion successful!");
            System.out.println("Output file: " + outputFile);
        } catch (IOException e) {
            System.err.println("Error during SQL file conversion: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void convertSqlFile(String inputFile, String outputFile) throws IOException {
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {

            String line;
            // 正则表达式用于匹配 INSERT 语句中的 id 和 pre_id
            Pattern pattern = Pattern.compile("VALUES \\('([^']*)', '([^']*)', '([^']*)',");

            while ((line = reader.readLine()) != null) {
                if (line.trim().startsWith("INSERT INTO")) {
                    Matcher matcher = pattern.matcher(line);
                    if (matcher.find()) {
                        String oldIdStr = matcher.group(1);
                        String organName = matcher.group(2);
                        String oldPreIdStr = matcher.group(3);

                        // 为新的ID生成或获取映射
                        long newId = idMapping.computeIfAbsent(oldIdStr, k -> idGenerator.getAndIncrement());
                        
                        // 处理 pre_id
                        Long newPreId = null;
                        if (!"0".equals(oldPreIdStr) && !oldPreIdStr.isEmpty()) {
                            newPreId = idMapping.computeIfAbsent(oldPreIdStr, k -> idGenerator.getAndIncrement());
                        }

                        // 构建新的 INSERT 语句
                        String newPreIdValue = (newPreId == null) ? "NULL" : String.valueOf(newPreId);
                         if ("0".equals(oldPreIdStr)) {
                            newPreIdValue = "0";
                        }
                        
                        String newLine = line.replace("'" + oldIdStr + "'", String.valueOf(newId))
                                             .replace("'" + oldPreIdStr + "'", newPreIdValue);
                        
                        writer.write(newLine);
                    } else {
                        // 如果正则不匹配，则原样写入
                        writer.write(line);
                    }
                } else {
                    // 非 INSERT 语句直接写入
                    writer.write(line);
                }
                writer.newLine();
            }
        }
    }
}
```

## 3. 如何使用

1.  将上述代码保存为 `src/main/java/com/dfit/percode/util/OrgStructureSqlConverter.java`。
2.  在您的IDE中运行这个Java类的 `main` 方法。
3.  它会自动读取 `docs/org_structure_repair.sql` 并生成一个新的文件 `docs/org_structure_repair_numeric.sql`。
4.  检查生成的新文件，确认ID和父子关系都已正确转换。
5.  在数据库中执行 `docs/org_structure_repair_numeric.sql`。
