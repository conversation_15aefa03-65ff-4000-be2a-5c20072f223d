package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 部门用户查询请求 VO
 * 用于按部门查询用户列表
 * 
 * <AUTHOR>
 * @since 2025-06-11
 */
@Data
@ApiModel(value = "DepartmentUsersRequestVO", description = "部门用户查询请求参数")
public class DepartmentUsersRequestVO {
    
    @ApiModelProperty(value = "部门ID", required = true)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long departmentId;
    
    @ApiModelProperty(value = "是否包含子部门用户", example = "false")
    private Boolean includeChildren = false;
    
    @ApiModelProperty(value = "是否包含已停用用户", example = "false")
    private Boolean includeDisabled = false;
    
    @ApiModelProperty(value = "用户名搜索关键词")
    private String userName;
    
    @ApiModelProperty(value = "账号搜索关键词")
    private String account;
    
    @ApiModelProperty(value = "是否启用分页", example = "false")
    private Boolean enablePaging = false;
    
    @ApiModelProperty(value = "当前页码（从1开始）", example = "1")
    private Integer currentPage = 1;
    
    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize = 20;
    
    @ApiModelProperty(value = "排序字段", example = "userName")
    private String sortField = "userName";
    
    @ApiModelProperty(value = "排序方向", example = "ASC", allowableValues = "ASC,DESC")
    private String sortDirection = "ASC";
}
