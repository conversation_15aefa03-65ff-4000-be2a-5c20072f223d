package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 菜单模块列表响应VO类
 * 按照前端格式要求设计（驼峰命名）
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "MenuModuleListResponseVO", description = "菜单模块列表响应数据")
public class MenuModuleListResponseVO {
    
    @ApiModelProperty(value = "模块ID", example = "1001")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "模块名称", example = "系统管理")
    private String moduleName;
    
    @ApiModelProperty(value = "模块标识", example = "system_management")
    private String moduleIdentifier;
    
    @ApiModelProperty(value = "排序序号", example = "1")
    private Integer orderInfo;
}
