package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 配置模块操作权限请求VO类
 * 模块级别的统一权限配置，该模块下所有数据都继承这些操作权限
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "ConfigureDataOperateRequestVO", description = "配置模块操作权限请求参数")
public class ConfigureDataOperateRequestVO {

    @ApiModelProperty(value = "模块标识", required = true, example = "user_data_module")
    private String moduleIdentifier;

    @ApiModelProperty(value = "操作类型列表", required = true, example = "[1, 2, 3]",
                     notes = "1-新增，2-修改，3-删除，4-查看")
    private List<Integer> operateTypes;
}
