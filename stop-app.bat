@echo off
chcp 65001 >nul

echo ==========================================
echo 停止权限管理系统应用
echo ==========================================

set JAR_NAME=percode-0.0.1-SNAPSHOT.jar

echo [INFO] 查找运行中的应用进程...

REM 查找包含JAR文件名的Java进程
for /f "tokens=1,2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| findstr "%JAR_NAME%"') do (
    set PROCESS_NAME=%%i
    set PID=%%j
    set PID=!PID:"=!
    set PROCESS_NAME=!PROCESS_NAME:"=!
    
    echo [INFO] 找到进程: !PROCESS_NAME! PID: !PID!
    echo [INFO] 正在停止进程...
    
    taskkill /pid !PID! /f
    if errorlevel 1 (
        echo [ERROR] 停止进程失败
    ) else (
        echo [INFO] 进程已停止
    )
)

REM 如果上面的方法没找到，尝试通过端口查找
echo [INFO] 检查端口8285占用情况...
for /f "tokens=5" %%i in ('netstat -ano ^| findstr ":8285"') do (
    set PORT_PID=%%i
    echo [INFO] 端口8285被进程 !PORT_PID! 占用
    echo [INFO] 正在停止进程...
    taskkill /pid !PORT_PID! /f
)

echo [INFO] 应用停止完成
pause
