package com.dfit.percode.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 检查权限标识符请求VO类
 * 用于检测菜单权限和数据权限的标识符是否重复
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "CheckIdentifierRequestVO", description = "检查权限标识符请求参数")
public class CheckIdentifierRequestVO {
    
    @ApiModelProperty(value = "权限标识符", required = true, example = "user:list:view")
    @NotBlank(message = "权限标识符不能为空")
    @Size(max = 100, message = "权限标识符长度不能超过100个字符")
    private String identifier;
    
    @ApiModelProperty(value = "权限类型：menu-菜单权限，data-数据权限", required = true, example = "menu")
    @NotBlank(message = "权限类型不能为空")
    @Pattern(regexp = "^(menu|data)$", message = "权限类型只能是menu或data")
    private String type;
    
    @ApiModelProperty(value = "排除的ID（编辑时使用，新增时传null）", example = "123")
    private Long excludeId;
}
