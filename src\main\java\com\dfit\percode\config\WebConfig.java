package com.dfit.percode.config;

import com.dfit.percode.common.SecurityConstants;
import com.dfit.percode.listener.UserActionInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 配置拦截器和静态资源
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Autowired
    private UserActionInterceptor userActionInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 重新启用用户行为拦截器，用于日志记录和行为监控
        // 使用统一的SecurityConstants.WHITE_LIST配置
        registry.addInterceptor(userActionInterceptor)
                .addPathPatterns("/**")  // 对所有路径进行拦截
                .excludePathPatterns(SecurityConstants.WHITE_LIST);  // 使用统一白名单配置
    }

    /**
     * 配置Swagger UI资源处理器
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

    /**
     * 配置Swagger UI首页重定向
     */
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/swagger-ui.html", "/swagger-ui/index.html");
        registry.addRedirectViewController("/", "/swagger-ui/index.html");
    }
}
