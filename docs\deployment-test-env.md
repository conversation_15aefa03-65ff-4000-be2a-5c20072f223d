# 测试环境部署指南

## 📋 环境配置清单

### 1. 服务器要求
- **操作系统**: Linux (推荐 CentOS 7.9+)
- **Java版本**: JDK 8+ (推荐 JDK 11)
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘**: 最低 10GB 可用空间
- **网络**: 确保可访问数据库服务器

### 2. 必需的外部服务
- **PostgreSQL数据库**: 版本 12+
- **MinIO对象存储** (可选): 如果需要文件上传功能

## 🔧 部署前准备

### 1. 修改配置文件
编辑 `src/main/resources/application-test.yml`，修改以下配置：

```yaml
# 数据库配置 - 必须修改
spring:
  datasource:
    url: ******************************************
    username: 数据库用户名
    password: 数据库密码

# MinIO配置 - 如果使用文件上传功能
minio:
  endpoint: http://新服务器IP:9000
  access-key: minio用户名
  secret-key: minio密码

# Smart Desktop配置 - 如果集成外部系统
smart-desktop:
  auth-url: http://外部系统IP:端口/resource/findAllResourceNoPage
```

### 2. 数据库准备
确保目标数据库已创建并导入了必要的表结构和数据：

```sql
-- 创建数据库
CREATE DATABASE rbac;

-- 导入表结构和数据
-- 使用最新的 public704.sql 或相应的数据库脚本
```

### 3. 编译项目
```bash
# 在项目根目录执行
mvn clean package -DskipTests
```

## 🚀 部署步骤

### 方式1: 使用部署脚本 (推荐)

1. **上传文件到服务器**
   ```bash
   # 上传JAR包和部署脚本
   scp target/percode-0.0.1-SNAPSHOT.jar user@server:/opt/permission-system/
   scp deploy-test.sh user@server:/opt/permission-system/
   ```

2. **执行部署**
   ```bash
   cd /opt/permission-system
   chmod +x deploy-test.sh
   ./deploy-test.sh
   ```

### 方式2: 手动部署

1. **停止旧进程**
   ```bash
   # 查找进程
   ps aux | grep percode
   
   # 停止进程
   kill -15 <PID>
   ```

2. **启动新应用**
   ```bash
   nohup java -Xms512m -Xmx1024m \
     -Dspring.profiles.active=test \
     -Dfile.encoding=UTF-8 \
     -Duser.timezone=Asia/Shanghai \
     -jar percode-0.0.1-SNAPSHOT.jar \
     > logs/app.log 2>&1 &
   ```

## ✅ 部署验证

### 1. 检查应用状态
```bash
# 查看进程
ps aux | grep percode

# 查看端口
netstat -tuln | grep 8285

# 查看日志
tail -f logs/app.log
```

### 2. 接口测试
```bash
# 健康检查
curl http://localhost:8285/actuator/health

# API测试
curl http://localhost:8285/swagger-ui.html
```

### 3. 功能验证
- 访问 Swagger 文档: `http://服务器IP:8285/swagger-ui.html`
- 测试登录接口
- 验证权限查询功能
- 检查超级管理员功能

## 🔍 故障排查

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tuln | grep 8285
   lsof -i :8285
   
   # 修改端口 (在application-test.yml中)
   server:
     port: 8286
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库连接
   telnet 数据库IP 5432
   
   # 检查数据库配置
   grep -A 5 "datasource" application-test.yml
   ```

3. **内存不足**
   ```bash
   # 调整JVM参数
   -Xms256m -Xmx512m  # 减少内存使用
   ```

4. **日志查看**
   ```bash
   # 实时查看日志
   tail -f logs/app.log
   
   # 查看错误日志
   grep -i error logs/app.log
   
   # 查看启动日志
   grep -i "started" logs/app.log
   ```

## 📝 环境管理

### 切换环境
修改 `application.yml` 中的 active profile：
```yaml
spring:
  profiles:
    active: test  # 改为 test 环境
```

### 配置热更新
某些配置支持热更新，无需重启应用：
- 日志级别
- 超级管理员配置 (需要重启)
- 数据库连接池配置 (需要重启)

### 监控和维护
- 定期检查日志文件大小
- 监控应用内存使用情况
- 定期备份数据库
- 关注应用性能指标

## 🔐 安全注意事项

1. **配置文件安全**
   - 不要在版本控制中提交敏感信息
   - 使用环境变量或外部配置文件

2. **网络安全**
   - 配置防火墙规则
   - 限制数据库访问IP
   - 使用HTTPS (生产环境)

3. **访问控制**
   - 定期更新超级管理员配置
   - 监控异常登录行为
   - 定期审查用户权限

## 📞 技术支持

如遇到部署问题，请提供以下信息：
- 服务器环境信息 (`uname -a`, `java -version`)
- 错误日志 (`logs/app.log`)
- 配置文件内容 (脱敏后)
- 网络连接测试结果
