2025-07-01 11:35:03.504 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 35296 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-01 11:35:03.512 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-01 11:35:03.538 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-01 11:35:06.120 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 11:35:06.133 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-01 11:35:06.168 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 27 ms. Found 0 Elasticsearch repository interfaces.
2025-07-01 11:35:06.172 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 11:35:06.172 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-01 11:35:06.183 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-01 11:35:06.192 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 11:35:06.192 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 11:35:06.209 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-07-01 11:35:06.278 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 11:35:06.280 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-01 11:35:06.311 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-01 11:35:07.638 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-01 11:35:07.652 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-01 11:35:07.653 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-01 11:35:07.653 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-01 11:35:08.323 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-01 11:35:08.323 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4671 ms
2025-07-01 11:35:08.556 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-01 11:35:08.784 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-01 11:35:09.931 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-01 11:35:10.070 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-01 11:35:10.354 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-01 11:35:10.542 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-01 11:35:11.157 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-01 11:35:11.175 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 11:35:11.210 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-01 11:35:11.210 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-01 11:35:11.213 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-01 11:35:11.213 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-01 11:35:14.427 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-01 11:35:18.392 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-01 11:35:18.421 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-01 11:35:18.625 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 11:35:18.626 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-01 11:35:18.629 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-01 11:35:19.456 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.076 seconds (JVM running for 25.786)
2025-07-01 14:51:02.262 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 14:51:02.292 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-01 14:51:35.603 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 32268 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-01 14:51:35.604 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-01 14:51:35.614 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-01 14:51:37.228 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 14:51:37.230 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-01 14:51:37.253 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-07-01 14:51:37.256 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 14:51:37.257 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-01 14:51:37.267 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-01 14:51:37.274 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 14:51:37.274 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 14:51:37.293 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-07-01 14:51:37.309 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 14:51:37.312 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-01 14:51:37.333 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-01 14:51:38.194 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-01 14:51:38.222 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-01 14:51:38.223 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-01 14:51:38.223 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-01 14:51:38.591 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-01 14:51:38.591 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2882 ms
2025-07-01 14:51:38.712 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-01 14:51:38.946 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-01 14:51:39.813 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-01 14:51:39.913 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-01 14:51:40.153 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-01 14:51:40.333 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-01 14:51:40.728 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-01 14:51:40.739 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 14:51:40.753 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-01 14:51:40.754 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-01 14:51:40.755 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-01 14:51:40.755 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-01 14:51:43.221 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-01 14:51:46.473 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-01 14:51:46.484 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-01 14:51:46.690 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 14:51:46.690 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-01 14:51:46.692 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-01 14:51:47.339 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.653 seconds (JVM running for 18.124)
2025-07-01 16:21:11.072 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 16:21:11.346 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-01 16:21:47.901 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 45432 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-01 16:21:47.902 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-01 16:21:47.910 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-01 16:21:49.644 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:21:49.646 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-01 16:21:49.669 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-07-01 16:21:49.673 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:21:49.673 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-01 16:21:49.684 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-01 16:21:49.692 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:21:49.692 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 16:21:49.708 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-07-01 16:21:49.733 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:21:49.735 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-01 16:21:49.757 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-01 16:21:50.689 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-01 16:21:50.723 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-01 16:21:50.723 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-01 16:21:50.723 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-01 16:21:51.143 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-01 16:21:51.143 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3176 ms
2025-07-01 16:21:51.276 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-01 16:21:51.460 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-01 16:21:52.437 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-01 16:21:52.597 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-01 16:21:52.909 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-01 16:21:53.204 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-01 16:21:53.772 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-01 16:21:53.785 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 16:21:53.802 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-01 16:21:53.802 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-01 16:21:53.804 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-01 16:21:53.804 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-01 16:21:56.682 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-01 16:21:59.103 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-01 16:21:59.115 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-01 16:21:59.308 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 16:21:59.309 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-01 16:21:59.312 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-01 16:22:00.131 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.261 seconds (JVM running for 17.273)
2025-07-01 16:30:46.979 [http-nio-8285-exec-9] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:30:47.124 [http-nio-8285-exec-9] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:36:01.407 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:36:55.953 [http-nio-8285-exec-2] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:37:03.697 [http-nio-8285-exec-10] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:43:08.766 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:43:08.780 [http-nio-8285-exec-2] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:43:08.805 [http-nio-8285-exec-10] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:49:44.902 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 16:49:44.935 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-01 16:50:03.040 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 39372 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-01 16:50:03.043 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-01 16:50:03.064 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-01 16:50:05.231 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:50:05.233 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-01 16:50:05.259 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-07-01 16:50:05.263 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:50:05.263 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-01 16:50:05.275 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-01 16:50:05.282 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:50:05.282 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 16:50:05.304 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-07-01 16:50:05.328 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 16:50:05.330 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-01 16:50:05.352 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-07-01 16:50:06.428 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-01 16:50:06.456 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-01 16:50:06.457 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-01 16:50:06.457 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-01 16:50:06.752 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-01 16:50:06.752 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3623 ms
2025-07-01 16:50:06.859 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-01 16:50:07.018 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-01 16:50:09.129 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-01 16:50:09.245 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-01 16:50:09.698 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-01 16:50:09.891 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-01 16:50:10.294 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-01 16:50:10.308 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 16:50:10.356 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-01 16:50:10.357 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-01 16:50:10.359 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-01 16:50:10.359 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-01 16:50:12.701 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-01 16:50:15.049 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-01 16:50:15.061 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-01 16:50:15.241 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 16:50:15.241 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-01 16:50:15.243 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-01 16:50:15.878 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.954 seconds (JVM running for 18.465)
2025-07-01 16:51:10.734 [http-nio-8285-exec-10] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:51:10.830 [http-nio-8285-exec-10] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:51:40.011 [http-nio-8285-exec-2] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:51:40.018 [http-nio-8285-exec-2] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:51:53.867 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:51:53.879 [http-nio-8285-exec-1] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:52:05.922 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 16:59:41.133 [http-nio-8285-exec-1] WARN  com.dfit.percode.interceptor.HybridAuthInterceptor - 混合认证失败：所有认证方式都失败, URI: /auth/permissions
2025-07-01 16:59:42.162 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 16:59:42.168 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-01 17:01:05.332 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 42816 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-01 17:01:05.336 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-01 17:01:05.360 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-01 17:01:07.560 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 17:01:07.570 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-01 17:01:07.626 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 46 ms. Found 0 Elasticsearch repository interfaces.
2025-07-01 17:01:07.638 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 17:01:07.640 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-01 17:01:07.668 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 25 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-01 17:01:07.695 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 17:01:07.698 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-01 17:01:07.725 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-07-01 17:01:07.767 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-01 17:01:07.772 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-01 17:01:07.818 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-07-01 17:01:09.779 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-01 17:01:09.800 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-01 17:01:09.802 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-01 17:01:09.802 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-01 17:01:10.047 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-01 17:01:10.048 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4620 ms
2025-07-01 17:01:10.183 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-01 17:01:10.373 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-01 17:01:11.223 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-01 17:01:11.271 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-01 17:01:11.451 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-01 17:01:11.639 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-01 17:01:11.990 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-01 17:01:12.005 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-01 17:01:12.027 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-01 17:01:12.027 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-01 17:01:12.029 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-01 17:01:12.030 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-01 17:01:16.104 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-01 17:01:18.056 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-01 17:01:18.070 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-01 17:01:18.306 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-01 17:01:18.307 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-01 17:01:18.309 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-07-01 17:01:19.253 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 15.517 seconds (JVM running for 18.221)
2025-07-01 17:16:55.858 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 17:16:56.452 [http-nio-8285-exec-3] INFO  com.dfit.percode.interceptor.HybridAuthInterceptor - 🔥 超级管理员访问 - 用户ID: 1938155631131365376, URI: /auth/permissions, Method: GET, IP: 0:0:0:0:0:0:0:1
2025-07-01 17:16:56.532 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: test
2025-07-01 17:16:56.534 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1938155631131365376, 模块标识: test
2025-07-01 17:16:56.534 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 🔥 检测到超级管理员，返回全量权限，用户ID: 1938155631131365376
2025-07-01 17:16:56.534 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员权限，用户ID: 1938155631131365376, 模块标识: test
2025-07-01 17:16:56.534 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员模块级菜单权限，模块标识: test
2025-07-01 17:16:56.845 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 查询到模块总数: 2
2025-07-01 17:16:56.846 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员模块级菜单权限查询完成，模块数量: 0，耗时: 310ms
2025-07-01 17:16:56.965 [http-nio-8285-exec-3] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员权限查询完成，用户ID: 1938155631131365376, 根菜单数量: 0, 按钮数量: 0, 耗时: 431ms
2025-07-01 17:16:56.966 [http-nio-8285-exec-3] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 超级管理员权限查询完成，用户ID: 1938155631131365376, 耗时: 432ms
2025-07-01 17:16:56.966 [http-nio-8285-exec-3] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功（混合认证），用户ID: 1938155631131365376, 认证方式: SuperAdmin, 菜单数量: 0, 按钮数量: 0, 耗时: 433ms
2025-07-01 17:16:57.015 [http-nio-8285-exec-3] INFO  com.dfit.percode.listener.UserActionInterceptor - 请求处理完成 - URI: /auth/permissions, 处理时间: 1156ms
