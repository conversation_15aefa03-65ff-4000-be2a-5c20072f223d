# 数据库扩展设置说明

## 📋 **数据库版本差异说明**

### **问题背景**
- **当前项目**：使用 PostgreSQL 数据库
- **外部系统**：使用 MySQL 数据库
- **语法差异**：两种数据库的SQL语法存在差异

### **解决方案**
我们提供了4个版本的SQL文件：

1. **database-extension-for-sync.sql** - 原始版本（可能有兼容性问题）
2. **database-extension-for-sync-postgresql.sql** - PostgreSQL完整版本（使用DO块处理）
3. **database-extension-simple.sql** - 简化版本（新版PostgreSQL推荐）
4. **database-extension-postgresql-old.sql** - 旧版PostgreSQL兼容版本（推荐使用）

## 🚀 **推荐执行步骤**

### **步骤1：根据PostgreSQL版本选择SQL文件**

#### **如果遇到JSONB类型错误（推荐）**
```sql
-- 使用旧版PostgreSQL兼容版本
\i docs/database-extension-postgresql-old.sql
```

#### **如果PostgreSQL版本较新**
```sql
-- 执行简化版本的SQL文件
\i docs/database-extension-simple.sql
```

**优点**：
- 语法简单，兼容性好
- 如果字段已存在会报错，但不影响其他操作
- 容易排查问题
- 旧版本使用TEXT代替JSONB

### **步骤2：如果遇到字段已存在错误**

如果执行时遇到类似错误：
```
ERROR: column "dept_uuid" of relation "t_org_structure" already exists
```

**解决方法**：
1. 忽略这些错误（字段已存在是正常的）
2. 或者使用PostgreSQL完整版本：

```sql
-- 使用PostgreSQL完整版本（自动检查字段是否存在）
\i docs/database-extension-for-sync-postgresql.sql
```

### **步骤3：验证表结构**

执行完成后，验证表结构是否正确：

```sql
-- 检查t_org_structure表的新字段
\d t_org_structure

-- 检查t_user表的新字段
\d t_user

-- 检查新创建的表
\d t_department_child
\d t_employee_position
\d t_employee_title
\d t_employee_system
\d t_sync_log
```

### **步骤4：检查索引**

```sql
-- 查看新创建的索引
\di idx_org_structure_org_code
\di idx_user_mdm_id
\di idx_employee_position_user_id
```

## 📊 **表结构说明**

### **扩展的现有表**

#### **t_org_structure（部门表）**
新增字段：
- `dept_uuid` - 外部系统部门唯一标识符
- `org_code` - 外部系统组织代码
- `parent_code` - 外部系统父级部门代码
- `full_name` - 部门全称
- `is_history` - 是否历史部门
- `description` - 部门描述
- `fax` - 传真
- `web_address` - 网站地址
- `org_manager` - 部门经理
- `post_code` - 邮政编码
- `org_type` - 组织类型
- `org_level` - 组织层级
- `org_path` - 组织路径
- `full_org_code` - 完整组织代码
- `external_id` - 外部系统中的原始ID
- `external_update_time` - 外部系统中的更新时间
- `sync_status` - 同步状态
- `last_sync_time` - 最后同步时间

#### **t_user（用户表）**
新增字段：
- `mdm_id` - 外部MDM系统中的唯一标识符
- `mdm_hrdwnm` - MDM系统中的硬件标识
- `employee_code` - 员工编号
- `gender` - 性别
- `mobile` - 手机号码
- `id_card` - 身份证号码
- `birth_date` - 出生日期
- `email` - 电子邮箱
- `org_type` - 组织类型
- `org_level1` - 组织层级1
- `org_level2` - 组织层级2
- `org_level3` - 组织层级3
- `wechat` - 微信号
- `tel` - 电话号码
- `note` - 备注
- `employee_status` - 员工状态
- `user_type` - 用户类型
- `id_name` - 身份证和姓名组合
- `external_id` - 外部系统中的原始ID
- `external_org_code` - 外部系统中的组织代码
- `external_update_time` - 外部系统中的更新时间
- `sync_status` - 同步状态
- `last_sync_time` - 最后同步时间

### **新创建的表**

#### **t_department_child（部门子表）**
存储部门在其他系统中的标识信息

#### **t_employee_position（员工岗位关联表）**
存储员工的岗位信息

#### **t_employee_title（员工职称表）**
存储员工的职称信息

#### **t_employee_system（员工系统标识表）**
存储员工在其他系统中的标识信息

#### **t_sync_log（同步日志表）**
记录数据同步的日志信息

## ⚠️ **注意事项**

1. **备份数据库**：执行前请备份当前数据库
2. **权限检查**：确保有CREATE TABLE和ALTER TABLE权限
3. **字段冲突**：如果字段已存在，会报错但不影响功能
4. **索引创建**：如果索引已存在，会报错但不影响功能
5. **JSONB类型**：确保PostgreSQL版本支持JSONB类型

## 🔧 **故障排除**

### **常见错误1：字段已存在**
```
ERROR: column "dept_uuid" of relation "t_org_structure" already exists
```
**解决**：忽略此错误，继续执行其他语句

### **常见错误2：表已存在**
```
ERROR: relation "t_department_child" already exists
```
**解决**：忽略此错误，表已创建成功

### **常见错误3：索引已存在**
```
ERROR: relation "idx_org_structure_org_code" already exists
```
**解决**：忽略此错误，索引已创建成功

### **常见错误4：JSONB类型不支持**
```
ERROR: type "jsonb" does not exist
```
**解决**：使用旧版PostgreSQL兼容版本
```sql
\i docs/database-extension-postgresql-old.sql
```
此版本使用TEXT类型代替JSONB，完全兼容旧版PostgreSQL

## ✅ **执行完成检查清单**

- [ ] t_org_structure表新增了18个字段
- [ ] t_user表新增了22个字段
- [ ] 创建了t_department_child表
- [ ] 创建了t_employee_position表
- [ ] 创建了t_employee_title表
- [ ] 创建了t_employee_system表
- [ ] 创建了t_sync_log表
- [ ] 创建了相关索引
- [ ] 没有影响现有数据
- [ ] 应用可以正常启动

完成以上检查后，数据库扩展就设置完成了，可以开始测试数据同步功能。
