package com.dfit.percode.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 自定义JWT配置类
 * 定义自定义JWT相关配置属性，与Sa-Token配置并存
 * 用于生成符合{userid, iat, exp}格式的JWT token
 *
 * <AUTHOR>
 * @since 2025-06-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "custom-jwt")
public class JwtConfig {

    /**
     * JWT签名密钥
     * 默认复用Sa-Token的密钥，确保一致性
     */
    private String secretKey = "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ";
//    private String secretKey = "iet_admin";

    /**
     * Token过期时间（秒）
     * 默认14400秒（4小时），与Sa-Token的active-timeout保持一致
     */
    private Long expiration = 14400L;

    /**
     * JWT签发者标识
     * 用于标识token的签发方
     */
    private String issuer = "permission-system";

    /**
     * Token名称
     * 用于HTTP头中的token名称
     */
    private String tokenName = "Authorization";

    /**
     * Token前缀
     * 用于Bearer Token格式
     */
    private String tokenPrefix = "Bearer";
}
