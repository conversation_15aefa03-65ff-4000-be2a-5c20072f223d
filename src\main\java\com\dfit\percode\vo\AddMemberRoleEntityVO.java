package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 添加成员角色关联实体VO类
 * 用于数据库操作，使用驼峰命名格式
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
public class AddMemberRoleEntityVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long roleId;

    private Integer orderInfo;

    private LocalDateTime createTime;

    private LocalDateTime modifyTime;
}
