package com.dfit.percode.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 用户行为拦截器
 * 用于记录用户操作日志和权限验证
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Slf4j
@Component
public class UserActionInterceptor implements HandlerInterceptor {

    /**
     * 请求处理前的拦截
     * 可以在这里进行权限验证、日志记录等操作
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @return true表示继续处理，false表示中断处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 记录请求信息
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String remoteAddr = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String authorization = request.getHeader("Authorization");

        // 记录详细的请求信息
        log.info("用户请求拦截 - URI: {}, Method: {}, IP: {}", requestURI, method, remoteAddr);
        log.debug("请求详情 - User-Agent: {}", userAgent);

        // 检查Authorization头格式
        if (authorization != null) {
            if (authorization.startsWith("Bearer ")) {
                log.debug("检测到Bearer Token格式，Token长度: {}", authorization.length() - 7);
            } else {
                log.debug("检测到非Bearer格式的Authorization头: {}", authorization.substring(0, Math.min(20, authorization.length())) + "...");
            }
        } else {
            log.debug("未检测到Authorization头");
        }

        // 记录请求开始时间（用于性能监控）
        request.setAttribute("startTime", System.currentTimeMillis());

        return true;
    }

    /**
     * 请求处理后的拦截
     * 可以在这里进行响应处理、日志记录等操作
     *
     * @param request      HTTP请求
     * @param response     HTTP响应
     * @param handler      处理器
     * @param modelAndView 模型和视图
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {
        // 计算请求处理时间
        Long startTime = (Long) request.getAttribute("startTime");
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            String requestURI = request.getRequestURI();

            // 根据处理时间记录不同级别的日志
            if (duration > 5000) {
                log.warn("慢请求警告 - URI: {}, 处理时间: {}ms", requestURI, duration);
            } else if (duration > 1000) {
                log.info("请求处理完成 - URI: {}, 处理时间: {}ms", requestURI, duration);
            } else {
                log.debug("请求处理完成 - URI: {}, 处理时间: {}ms", requestURI, duration);
            }
        }
    }

    /**
     * 请求完成后的拦截
     * 可以在这里进行资源清理等操作
     *
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @param ex       异常信息
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        if (ex != null) {
            log.error("请求处理异常 - URI: {}, Error: {}", request.getRequestURI(), ex.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
