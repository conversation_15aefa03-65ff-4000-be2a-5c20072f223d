package com.dfit.percode.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 数据操作表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-20
 */
@Getter
@Setter
@TableName("t_data_operate")
@ApiModel(value = "TDataOperate对象", description = "数据操作表")
public class TDataOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("数据操作ID")
    private Long id;

    @ApiModelProperty("模块标识符，与系统模块表中模块标识一致")
    private String moduleIdentifier;

    @ApiModelProperty("数据类型，1为条")
    private Integer dataType;

    @ApiModelProperty("操作类型，1为新增2为修改3为删除")
    private Long operateType;

    @ApiModelProperty("数据标识，与数据权限管理列表中data_identifier一致")
    private String dataIdentifier;

    @ApiModelProperty("是否删除，默认为false")
    private Boolean isDel;

    @ApiModelProperty("创建时间")
    private java.time.LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private java.time.LocalDateTime modifyTime;
}
