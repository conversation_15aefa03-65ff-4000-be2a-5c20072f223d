import java.sql.*;
import java.util.*;

public class CheckDuplicates {
    private static final String DB_URL = "****************************************";
    private static final String DB_USER = "postgres2";
    private static final String DB_PASSWORD = "123456";
    
    public static void main(String[] args) {
        System.out.println("=== 检查重复部门名称 ===");
        
        try {
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            
            // 查找重复的部门名称
            PreparedStatement stmt = conn.prepareStatement(
                "SELECT organ_name, COUNT(*) as count, " +
                "STRING_AGG(CAST(id AS TEXT), ', ') as ids, " +
                "STRING_AGG(CAST(pre_id AS TEXT), ', ') as pre_ids " +
                "FROM t_org_structure " +
                "WHERE data_source = 2 " +
                "GROUP BY organ_name " +
                "HAVING COUNT(*) > 1 " +
                "ORDER BY count DESC, organ_name"
            );
            
            ResultSet rs = stmt.executeQuery();
            
            int duplicateGroups = 0;
            int totalDuplicates = 0;
            
            System.out.println("重复的部门名称：");
            System.out.println("格式：部门名称 (重复次数) - ID列表 - 父ID列表");
            System.out.println("=" + "=".repeat(80));
            
            while (rs.next()) {
                String organName = rs.getString("organ_name");
                int count = rs.getInt("count");
                String ids = rs.getString("ids");
                String preIds = rs.getString("pre_ids");
                
                duplicateGroups++;
                totalDuplicates += count;
                
                System.out.println(String.format("%d. %s (%d次)", 
                    duplicateGroups, organName, count));
                System.out.println("   ID: " + ids);
                System.out.println("   父ID: " + preIds);
                System.out.println();
            }
            
            rs.close();
            stmt.close();
            
            System.out.println("=" + "=".repeat(80));
            System.out.println("统计结果：");
            System.out.println("重复组数: " + duplicateGroups);
            System.out.println("重复记录总数: " + totalDuplicates);
            System.out.println("需要删除的多余记录: " + (totalDuplicates - duplicateGroups));
            
            // 查看具体的重复记录详情
            if (duplicateGroups > 0) {
                System.out.println("\n=== 重复记录详细信息 ===");
                PreparedStatement detailStmt = conn.prepareStatement(
                    "SELECT o1.id, o1.organ_name, o1.pre_id, o2.organ_name as parent_name " +
                    "FROM t_org_structure o1 " +
                    "LEFT JOIN t_org_structure o2 ON o1.pre_id = o2.id " +
                    "WHERE o1.data_source = 2 " +
                    "AND o1.organ_name IN (" +
                    "  SELECT organ_name FROM t_org_structure " +
                    "  WHERE data_source = 2 " +
                    "  GROUP BY organ_name HAVING COUNT(*) > 1" +
                    ") " +
                    "ORDER BY o1.organ_name, o1.id"
                );
                
                ResultSet detailRs = detailStmt.executeQuery();
                String currentName = "";
                int groupIndex = 0;
                
                while (detailRs.next()) {
                    String organName = detailRs.getString("organ_name");
                    long id = detailRs.getLong("id");
                    long preId = detailRs.getLong("pre_id");
                    String parentName = detailRs.getString("parent_name");
                    
                    if (!organName.equals(currentName)) {
                        currentName = organName;
                        groupIndex++;
                        System.out.println("\n" + groupIndex + ". " + organName + ":");
                    }
                    
                    System.out.println(String.format("   ID: %d, 父ID: %d, 父部门: %s", 
                        id, preId, parentName != null ? parentName : "无"));
                }
                
                detailRs.close();
                detailStmt.close();
            }
            
            conn.close();
            
        } catch (SQLException e) {
            System.err.println("检查失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
