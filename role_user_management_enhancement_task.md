# 角色用户管理功能增强任务

## 任务概述
根据需求变化，修改角色用户管理功能：
1. 现有 `getRoleUserList` 接口只显示已授权用户
2. 新增 `getUnauthorizedUsers` 接口查询未授权用户
3. 前端点击"新增"按钮调用新接口，选择用户后通过 `/users/userRole` 进行授权

## 已完成步骤

### ✅ 步骤 1: 修改现有 getRoleUserList 接口
- 文件: `src/main/resources/mapper/UserMapper.xml`
- 修改 `findRoleUserListPage` 查询：使用 INNER JOIN 只显示已授权用户
- 修改 `countRoleUserList` 查询：只统计已授权用户
- 添加条件：`AND pur.is_del = false`

### ✅ 步骤 2: 创建未授权用户查询 VO 类
- `UnauthorizedUsersRequestVO` - 查询请求参数
- `UnauthorizedUserItemVO` - 用户列表项
- `UnauthorizedUsersResponseVO` - 查询响应结果

### ✅ 步骤 3: 在 UserMapper 中添加查询方法
- 文件: `src/main/java/com/dfit/percode/mapper/UserMapper.java`
- 添加 `findUnauthorizedUsersPage` 方法声明
- 添加 `countUnauthorizedUsers` 方法声明
- 文件: `src/main/resources/mapper/UserMapper.xml`
- 实现 SQL 查询：`WHERE (pur.id IS NULL OR pur.is_del = true)`

### ✅ 步骤 4: 在 UserService 中添加业务逻辑
- 文件: `src/main/java/com/dfit/percode/service/IUserService.java`
- 添加 `getUnauthorizedUsers` 方法声明
- 文件: `src/main/java/com/dfit/percode/service/impl/UserServiceImpl.java`
- 实现完整的分页查询逻辑

### ✅ 步骤 5: 在 UserController 中添加接口
- 文件: `src/main/java/com/dfit/percode/controller/UserController.java`
- 添加 `/users/getUnauthorizedUsers` 接口
- 支持分页和搜索功能

## 核心 SQL 逻辑变化

### 修改前（显示所有用户）
```sql
-- 显示所有用户，用 isAssigned 标识授权状态
LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
WHERE u.is_del = false
```

### 修改后（分离已授权和未授权）

#### 已授权用户查询（getRoleUserList）
```sql
-- 只显示已授权用户
INNER JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
WHERE u.is_del = false 
AND pur.is_del = false
```

#### 未授权用户查询（getUnauthorizedUsers）
```sql
-- 只显示未授权用户
LEFT JOIN t_perm_user_role pur ON u.id = pur.user_id AND pur.role_id = #{roleId}
WHERE u.is_del = false 
AND (pur.id IS NULL OR pur.is_del = true)
```

## 接口详情

### 1. 修改后的已授权用户接口
```
POST /users/getRoleUserList
```

**请求参数**：
```json
{
  "roleId": *********,
  "currentPage": 1,
  "pageSize": 10,
  "userName": "张三",
  "userState": false
}
```

**响应格式**：
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "userId": "1",
      "userName": "张三",
      "department": "技术部",
      "userState": false,
      "isAssigned": true,
      "canAssign": false,
      "createTime": "2024-01-01 10:00:00"
    }
  ],
  "total": 5
}
```

### 2. 新增的未授权用户接口
```
POST /users/getUnauthorizedUsers
```

**请求参数**：
```json
{
  "roleId": *********,
  "currentPage": 1,
  "pageSize": 10,
  "userName": "李四",
  "userState": false
}
```

**响应格式**：
```json
{
  "code": 200,
  "message": "SUCCESS",
  "data": [
    {
      "userId": "2",
      "userName": "李四",
      "account": "li004",
      "department": "市场部",
      "userState": false,
      "createTime": "2024-01-02 10:00:00"
    }
  ],
  "total": 15
}
```

## 前端使用流程

### 1. 显示已授权用户
```javascript
// 调用修改后的接口，只显示已授权用户
fetch('/users/getRoleUserList', {
  method: 'POST',
  body: JSON.stringify({
    roleId: currentRoleId,
    currentPage: 1,
    pageSize: 10
  })
})
```

### 2. 新增用户授权
```javascript
// 点击"新增"按钮，查询未授权用户
fetch('/users/getUnauthorizedUsers', {
  method: 'POST',
  body: JSON.stringify({
    roleId: currentRoleId,
    currentPage: 1,
    pageSize: 10
  })
})

// 选择用户后，调用授权接口
fetch('/users/userRole', {
  method: 'POST',
  body: JSON.stringify({
    userId: selectedUserId,
    roleId: currentRoleId,
    isAssigned: true  // 授权操作
  })
})
```

## 功能特性

### 1. 清晰的职责分离
- **getRoleUserList**: 专门显示已授权用户，支持取消授权
- **getUnauthorizedUsers**: 专门显示未授权用户，用于新增授权

### 2. 完整的搜索和分页
- 两个接口都支持用户名模糊搜索
- 支持用户状态筛选
- 支持创建时间范围筛选
- 完整的分页功能

### 3. 一致的数据格式
- 统一的请求参数格式
- 统一的响应数据格式
- 与现有接口保持一致

### 4. 复用现有授权逻辑
- 新增授权仍使用 `/users/userRole` 接口
- 保持现有的业务逻辑不变
- 向后兼容

## 测试验证

### 测试场景
1. **已授权用户列表**: 验证只显示已授权且未被取消的用户
2. **未授权用户列表**: 验证显示从未授权或已取消授权的用户
3. **搜索功能**: 验证两个接口的搜索功能正常
4. **分页功能**: 验证分页计算和导航正确
5. **授权流程**: 验证从未授权用户列表选择用户进行授权

### 预期结果
- 已授权和未授权用户列表互不重复
- 搜索和分页功能正常工作
- 授权操作成功后，用户从未授权列表移动到已授权列表
- 取消授权后，用户从已授权列表移动到未授权列表

## 状态
🎯 **任务完成** - 角色用户管理功能增强已完整实现，支持分离显示已授权和未授权用户，提供清晰的用户授权管理界面