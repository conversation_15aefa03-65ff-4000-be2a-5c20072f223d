# Bearer Token 支持测试文档

## 概述
本文档用于测试权限管理系统对Bearer Token格式的支持情况。

## Sa-Token配置
当前Sa-Token配置支持Bearer Token格式：
- `token-name: Authorization` - 从Authorization头读取token
- Sa-Token自动支持Bearer前缀格式

## 测试用例

### 1. 标准Bearer Token格式测试

**请求头格式：**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**测试步骤：**
1. 先调用登录接口获取token
2. 在后续请求中使用Bearer格式发送token
3. 验证权限查询接口是否正常工作

### 2. 非Bearer格式测试

**请求头格式：**
```http
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**预期结果：**
Sa-Token应该也能正常处理（向后兼容）

### 3. 拦截器日志验证

启用拦截器后，应该能在日志中看到：
- Bearer Token格式检测日志
- 请求处理时间监控
- 慢请求警告（>5秒）

## 测试API

### 登录接口
```http
POST /auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "123456"
}
```

### 权限查询接口（需要Bearer Token）
```http
GET /auth/permissions
Authorization: Bearer {token}
```

## 预期日志输出

### 成功的Bearer Token请求
```
INFO  - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 127.0.0.1
DEBUG - 检测到Bearer Token格式，Token长度: 180
DEBUG - 请求处理完成 - URI: /auth/permissions, 处理时间: 45ms
```

### 非Bearer格式请求
```
INFO  - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 127.0.0.1
DEBUG - 检测到非Bearer格式的Authorization头: eyJhbGciOiJIUzI1NiIs...
DEBUG - 请求处理完成 - URI: /auth/permissions, 处理时间: 42ms
```

### 无Authorization头请求
```
INFO  - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 127.0.0.1
DEBUG - 未检测到Authorization头
WARN  - 权限查询失败：Token无效或已过期
```

## 验证清单

- [ ] Bearer Token格式正常工作
- [ ] 非Bearer格式向后兼容
- [ ] 拦截器正常记录请求日志
- [ ] 性能监控正常工作
- [ ] 慢请求警告功能正常
- [ ] Sa-Token JWT验证正常

## 注意事项

1. **前端建议使用标准Bearer格式**：`Authorization: Bearer {token}`
2. **Sa-Token自动处理Bearer前缀**，无需额外配置
3. **拦截器会记录详细的请求信息**，便于调试和监控
4. **性能监控会标记慢请求**，有助于性能优化

## 故障排除

如果Bearer Token不工作，检查：
1. Sa-Token配置是否正确
2. 拦截器是否正常启用
3. Token是否有效且未过期
4. 请求头格式是否正确
