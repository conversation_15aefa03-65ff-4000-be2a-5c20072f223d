/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:52:51
*/


-- ----------------------------
-- Table structure for t_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_user";
CREATE TABLE "public"."t_user" (
  "id" int8 NOT NULL,
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "is_del" bool,
  "origin_id" varchar(255) COLLATE "pg_catalog"."default",
  "organ_affiliation" int8,
  "account" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "is_disable" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_user"."id" IS '人员信息id';
COMMENT ON COLUMN "public"."t_user"."user_name" IS '用户名称';
COMMENT ON COLUMN "public"."t_user"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."t_user"."origin_id" IS '原始关联id，用于外部系统对接';
COMMENT ON COLUMN "public"."t_user"."organ_affiliation" IS '组织归属，对应组织架构表中的id';
COMMENT ON COLUMN "public"."t_user"."account" IS '账户';
COMMENT ON COLUMN "public"."t_user"."password" IS '密码';
COMMENT ON COLUMN "public"."t_user"."is_disable" IS '是否停用';
COMMENT ON TABLE "public"."t_user" IS '人员信息表，与组织架构绑定';

-- ----------------------------
-- Records of t_user
-- ----------------------------
INSERT INTO "public"."t_user" VALUES (1932639748158001152, '测试用户001', 'f', NULL, NULL, '测试用户001', '123456', 'f', '2025-06-11 11:23:17.337668', '2025-06-11 18:11:21.214319');
INSERT INTO "public"."t_user" VALUES (6002, '李技术总监', 'f', 'li_cto', NULL, NULL, 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-11 18:23:35.418619');
INSERT INTO "public"."t_user" VALUES (6001, '张总经理', 'f', 'zhang_ceo', 5001, 'zhang001', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:52.152498');
INSERT INTO "public"."t_user" VALUES (1936370058246885376, 'a', 'f', NULL, 5008, 'a', 'a', 'f', '2025-06-21 18:26:12.559204', '2025-06-21 18:26:19.118878');
INSERT INTO "public"."t_user" VALUES (1934773648393113600, '测试人员', 'f', NULL, 5008, 'admintest', '123456', 'f', '2025-06-17 08:42:38.791092', '2025-06-17 08:43:01.147842');
INSERT INTO "public"."t_user" VALUES (1936613097838088192, 'ttt', 'f', NULL, 5007, 'abc', 'abc', 'f', '2025-06-22 10:31:57.713584', '2025-06-22 10:32:08.667714');
INSERT INTO "public"."t_user" VALUES (1936613452760092672, 'abc', 'f', NULL, 5008, 'xxx', 'ccc', 'f', '2025-06-22 10:33:22.333885', '2025-06-22 10:33:22.333892');
INSERT INTO "public"."t_user" VALUES (1936614969823072256, '555', 'f', NULL, 5009, 'ddd', 'ddd', 'f', '2025-06-22 10:39:24.029588', '2025-06-22 10:48:02.042139');
INSERT INTO "public"."t_user" VALUES (1936631880975781888, 'a', 'f', NULL, 5009, 'aa', 'a', 'f', '2025-06-22 11:46:35.962764', '2025-06-22 11:46:35.96277');
INSERT INTO "public"."t_user" VALUES (1935318005881901056, '123', 'f', NULL, 5012, '123123', '123456', 'f', '2025-06-18 20:45:43.729772', '2025-06-20 13:46:53.960769');
INSERT INTO "public"."t_user" VALUES (1935155281398992896, 'sdf', 'f', NULL, NULL, 'asdf', 'asdf', 'f', '2025-06-18 09:59:07.189611', '2025-06-20 13:46:57.308734');
INSERT INTO "public"."t_user" VALUES (1935946418258841600, '小韩', 'f', NULL, 5008, '456', '456', 'f', '2025-06-20 14:22:48.91593', '2025-06-20 14:22:59.743791');
INSERT INTO "public"."t_user" VALUES (6003, '王产品总监', 'f', 'wang_cpo', 5003, 'wang.cpo', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:05.422033');
INSERT INTO "public"."t_user" VALUES (6010, '王运维工程师', 'f', 'wang_ops', 5010, 'wang.ops', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:07.503522');
INSERT INTO "public"."t_user" VALUES (6006, '孙测试经理', 'f', 'sun_qa_mgr', 5009, 'sun.qa', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:09.561032');
INSERT INTO "public"."t_user" VALUES (6009, '郑测试工程师', 'f', 'zheng_qa', 5009, 'zheng.qa', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:11.336351');
INSERT INTO "public"."t_user" VALUES (6005, '钱后端经理', 'f', 'qian_be_mgr', 5008, 'qian.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:13.314671');
INSERT INTO "public"."t_user" VALUES (1934460205056266240, 'testa', 'f', NULL, 5008, 'z', '1', 'f', '2025-06-16 11:57:08.075555', '2025-06-21 07:06:15.757996');
INSERT INTO "public"."t_user" VALUES (1934845562293719040, '徐小青', 'f', NULL, 5011, '徐小青', '123456', 'f', '2025-06-17 13:28:24.400219', '2025-06-21 07:06:17.596095');
INSERT INTO "public"."t_user" VALUES (1935148278794555392, 'test', 'f', NULL, 5007, 'test12345', '123456', 'f', '2025-06-18 09:31:17.638376', '2025-06-21 07:06:19.347844');
INSERT INTO "public"."t_user" VALUES (1935937134821249024, '张茂才', 'f', NULL, 5008, '789', '456', 'f', '2025-06-20 13:45:55.571197', '2025-06-21 07:06:21.032509');
INSERT INTO "public"."t_user" VALUES (1936006492402618368, '徐小青', 'f', NULL, 5007, 'x2005', '123456', 'f', '2025-06-20 18:21:31.70736', '2025-06-21 07:06:22.911112');
INSERT INTO "public"."t_user" VALUES (6012, '陈实习生', 'f', 'chen_intern', 5013, '123', '123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:33.677616');
INSERT INTO "public"."t_user" VALUES (6011, '冯离职员工', 'f', 'feng_former', 5007, 'feng.former', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:36.20478');
INSERT INTO "public"."t_user" VALUES (6008, '吴后端开发', 'f', 'wu_be_dev', 5008, 'wu.be', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:46.758792');
INSERT INTO "public"."t_user" VALUES (6007, '周前端开发', 'f', 'zhou_fe_dev', 5007, 'zhou.fe', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:48.397188');
INSERT INTO "public"."t_user" VALUES (6004, '赵前端经理', 'f', 'zhao_fe_mgr', 5007, 'zhao.fe', 'password123', 'f', '2025-06-11 01:58:47.958483', '2025-06-21 07:06:50.372243');
INSERT INTO "public"."t_user" VALUES (1936640367617249280, '123', 'f', NULL, 5009, '123', '123', 'f', '2025-06-22 12:20:19.335999', '2025-06-23 17:42:15.799141');

-- ----------------------------
-- Indexes structure for table t_user
-- ----------------------------
CREATE INDEX "idx_user_organ_affiliation" ON "public"."t_user" USING btree (
  "organ_affiliation" "pg_catalog"."int8_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table t_user
-- ----------------------------
ALTER TABLE "public"."t_user" ADD CONSTRAINT "t_user_pk" PRIMARY KEY ("id");
