/*
 Navicat Premium Dump SQL

 Source Server         : ************_5432
 Source Server Type    : PostgreSQL
 Source Server Version : 90224 (90224)
 Source Host           : ************:5432
 Source Catalog        : rbac
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90224 (90224)
 File Encoding         : 65001

 Date: 25/06/2025 12:56:34
*/


-- ----------------------------
-- Table structure for t_menu_module
-- ----------------------------
DROP TABLE IF EXISTS "public"."t_menu_module";
CREATE TABLE "public"."t_menu_module" (
  "id" int8 NOT NULL,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_identifier" varchar(255) COLLATE "pg_catalog"."default",
  "order_info" int4,
  "is_del" bool,
  "create_time" timestamp(6),
  "modify_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."t_menu_module"."module_name" IS '模块名称';
COMMENT ON COLUMN "public"."t_menu_module"."module_identifier" IS '模块标志，用于区分模块的内部标识';
COMMENT ON COLUMN "public"."t_menu_module"."order_info" IS '显示序号';
COMMENT ON COLUMN "public"."t_menu_module"."is_del" IS '是否删除';
COMMENT ON TABLE "public"."t_menu_module" IS '菜单模块信息表，用于标识菜单管理列表中新建模块存储信息和标识';

-- ----------------------------
-- Records of t_menu_module
-- ----------------------------
INSERT INTO "public"."t_menu_module" VALUES (8001, '系统管理', 'system_management', 1, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8002, '用户管理', 'user_management', 2, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8003, '权限管理', 'permission_management', 3, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8004, '开发工具', 'development_tools', 4, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8005, '运维监控', 'operations_monitoring', 5, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (8006, '数据分析', 'data_analytics', 6, 'f', '2025-06-11 01:58:48.453698', '2025-06-11 01:58:48.453698');
INSERT INTO "public"."t_menu_module" VALUES (1932638701989531648, 'test', 'testzz', NULL, 'f', '2025-06-11 11:19:07.887076', '2025-06-11 11:19:47.294778');
INSERT INTO "public"."t_menu_module" VALUES (1935155422495379456, 'sdf', 'asdf', NULL, 'f', '2025-06-18 09:59:40.814591', '2025-06-20 13:48:17.62555');
INSERT INTO "public"."t_menu_module" VALUES (1935937883076694016, '测试添加', '', NULL, 'f', '2025-06-20 13:48:53.95583', '2025-06-20 14:25:30.910526');
INSERT INTO "public"."t_menu_module" VALUES (1935937949317337088, '测试添加', '', NULL, 'f', '2025-06-20 13:49:09.749487', '2025-06-20 14:25:30.910526');
INSERT INTO "public"."t_menu_module" VALUES (1935947752047513600, '知识库', 'kn1', NULL, 'f', '2025-06-20 14:28:06.899952', '2025-06-21 06:44:17.739607');
INSERT INTO "public"."t_menu_module" VALUES (1935947944100499456, '知识库', 'kn1', NULL, 'f', '2025-06-20 14:28:52.689595', '2025-06-21 06:44:17.739607');
INSERT INTO "public"."t_menu_module" VALUES (1935947458819526656, '知识库', 'kn', NULL, 'f', '2025-06-20 14:26:56.988993', '2025-06-21 06:44:19.966359');
INSERT INTO "public"."t_menu_module" VALUES (8008, '项目管理', 'project_management', 8, 'f', '2025-06-11 01:58:48.453698', '2025-06-21 06:54:27.048481');
INSERT INTO "public"."t_menu_module" VALUES (8007, '财务管理', 'finance_management', 7, 'f', '2025-06-11 01:58:48.453698', '2025-06-21 06:54:30.545849');
INSERT INTO "public"."t_menu_module" VALUES (1936193819141869568, '知识库', 'knowledge', NULL, 'f', '2025-06-21 06:45:53.869788', '2025-06-21 07:07:24.826142');
INSERT INTO "public"."t_menu_module" VALUES (1936602745549230080, '知识库', 'knowledge', NULL, 'f', '2025-06-22 09:50:49.520522', '2025-06-22 09:50:49.520522');
INSERT INTO "public"."t_menu_module" VALUES (1936366625624297472, 'zzc', 'z', NULL, 'f', '2025-06-21 18:12:34.147587', '2025-06-22 13:30:13.720309');
INSERT INTO "public"."t_menu_module" VALUES (1936366810022678528, 'zzc', 'z', NULL, 'f', '2025-06-21 18:13:18.111792', '2025-06-22 13:30:13.720309');

-- ----------------------------
-- Primary Key structure for table t_menu_module
-- ----------------------------
ALTER TABLE "public"."t_menu_module" ADD CONSTRAINT "t_menu_module_pk" PRIMARY KEY ("id");
