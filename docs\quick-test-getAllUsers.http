### 快速测试 /users/getAllUsers 接口修复
### 验证根部门查询修复后的效果

### 变量定义
@baseUrl = http://localhost:8080

### 1. 测试修复后的 getAllUsers 接口
POST {{baseUrl}}/users/getAllUsers
Content-Type: application/json

### 2. 验证数据库中的根部门数据
### 请在数据库中执行以下SQL来验证数据：

/*
-- 1. 检查根部门（pre_id = 0）
SELECT id, organ_name, pre_id, is_del 
FROM t_org_structure 
WHERE (pre_id = 0 OR pre_id IS NULL) AND is_del = false
ORDER BY order_info;

-- 预期结果：应该返回 5001 科技集团总公司

-- 2. 检查所有部门数据
SELECT id, organ_name, pre_id, order_info, is_del 
FROM t_org_structure 
WHERE is_del = false 
ORDER BY pre_id, order_info;

-- 预期结果：应该返回13个部门

-- 3. 检查用户数据
SELECT id, user_name, organ_affiliation, is_del 
FROM t_user 
WHERE is_del = false 
ORDER BY organ_affiliation, id;

-- 预期结果：应该返回12个用户

-- 4. 检查用户和部门的关联关系
SELECT 
    u.id as user_id,
    u.user_name,
    u.organ_affiliation,
    o.organ_name
FROM t_user u
LEFT JOIN t_org_structure o ON u.organ_affiliation = o.id
WHERE u.is_del = false
ORDER BY u.organ_affiliation, u.id;

-- 预期结果：所有用户都应该有对应的部门名称

-- 5. 测试优化后的查询SQL
SELECT 
    o.id AS orgId, 
    o.organ_name AS orgName, 
    o.pre_id AS parentId, 
    o.order_info AS orgOrder, 
    u.id AS userId, 
    u.user_name AS userName 
FROM t_org_structure o 
LEFT JOIN t_user u ON o.id = u.organ_affiliation AND u.is_del = false 
WHERE o.is_del = false 
ORDER BY o.order_info, u.user_name;

-- 预期结果：应该返回部门和用户的完整关联数据
*/

### 3. 如果接口还是返回空，检查应用日志
### 查看控制台输出，应该能看到类似这样的日志：
### "开始获取所有用户（组织架构树形结构）- 优化版本"
### "获取所有用户完成，耗时: XXXms"

### 4. 预期的成功响应格式
/*
{
  "code": 200,
  "message": "SUCCESS", 
  "data": [
    {
      "id": 5001,
      "organName": "科技集团总公司",
      "children": [
        {
          "id": 6001,
          "userName": "张总经理"
        },
        {
          "id": 5002,
          "organName": "技术研发中心",
          "children": [
            {
              "id": 6002,
              "userName": "李技术总监"
            },
            {
              "id": 5007,
              "organName": "前端开发部",
              "children": [
                {
                  "id": 6004,
                  "userName": "赵前端经理"
                },
                {
                  "id": 6007,
                  "userName": "周前端开发"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
*/

### 5. 如果还是有问题，可以尝试重启应用
### 确保代码修改生效

### 6. 性能测试
### 修复后接口应该在几秒内返回结果，不再是50+秒
