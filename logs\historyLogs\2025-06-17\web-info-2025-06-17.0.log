2025-06-17 08:54:18.973 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 22424 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 08:54:18.981 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 08:54:19.008 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 08:54:21.869 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 08:54:21.871 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 08:54:21.897 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 08:54:21.901 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 08:54:21.902 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 08:54:21.911 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 08:54:21.918 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 08:54:21.919 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 08:54:21.933 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-17 08:54:21.955 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 08:54:21.958 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 08:54:21.980 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-17 08:54:22.718 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 08:54:22.729 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 08:54:22.731 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 08:54:22.731 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 08:54:23.202 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 08:54:23.202 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4131 ms
2025-06-17 08:54:23.305 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 08:54:23.490 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 08:54:24.343 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 08:54:24.463 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 08:54:24.813 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 08:54:24.983 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 08:54:25.347 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 08:54:25.371 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 08:54:27.643 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 08:54:30.210 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 08:54:30.224 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 08:54:30.879 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.776 seconds (JVM running for 16.13)
2025-06-17 08:54:46.783 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 08:54:46.783 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 08:54:46.787 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-17 08:54:47.354 [http-nio-8285-exec-1] WARN  o.s.w.s.m.support.DefaultHandlerExceptionResolver - Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Unexpected character ('}' (code 125)): expected a value; nested exception is com.fasterxml.jackson.core.JsonParseException: Unexpected character ('}' (code 125)): expected a value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 3, column: 2]]
2025-06-17 08:55:10.101 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-17 08:55:10.102 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: null
2025-06-17 08:55:10.443 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-17 08:55:10.444 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-17 08:55:10.445 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 341ms，根部门数量: 1
2025-06-17 09:17:48.429 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:17:48.459 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 09:18:02.222 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 40064 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 09:18:02.225 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 09:18:02.234 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 09:18:03.713 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:18:03.716 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:18:03.743 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 09:18:03.749 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:18:03.750 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:18:03.766 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 09:18:03.775 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:18:03.776 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 09:18:03.790 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-17 09:18:03.817 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:18:03.819 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 09:18:03.841 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-17 09:18:04.784 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 09:18:04.827 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 09:18:04.827 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 09:18:04.828 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 09:18:05.145 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 09:18:05.146 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2850 ms
2025-06-17 09:18:05.244 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 09:18:05.432 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 09:18:06.293 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 09:18:06.444 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 09:18:06.803 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 09:18:06.979 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 09:18:07.374 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 09:18:07.386 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:18:09.732 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 09:18:11.647 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 09:18:11.661 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 09:18:12.251 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.835 seconds (JVM running for 13.34)
2025-06-17 09:18:21.432 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 09:18:21.432 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 09:18:21.437 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 5 ms
2025-06-17 09:18:36.152 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始检查用户重复，检查类型: null
2025-06-17 09:21:08.333 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:21:08.340 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 09:21:16.504 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 34856 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 09:21:16.506 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 09:21:16.513 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 09:21:17.991 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:21:17.993 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:21:18.015 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 09:21:18.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:21:18.018 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:21:18.028 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 09:21:18.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:21:18.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 09:21:18.048 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-17 09:21:18.062 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:21:18.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 09:21:18.080 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-17 09:21:18.732 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 09:21:18.745 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 09:21:18.745 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 09:21:18.745 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 09:21:18.948 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 09:21:18.948 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2371 ms
2025-06-17 09:21:19.026 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 09:21:19.140 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 09:21:19.883 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 09:21:19.930 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 09:21:20.050 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 09:21:20.192 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 09:21:20.487 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 09:21:20.499 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:21:23.130 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 09:21:25.178 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 09:21:25.193 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 09:21:25.815 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 10.043 seconds (JVM running for 12.185)
2025-06-17 09:21:32.547 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 09:21:32.547 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 09:21:32.549 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-17 09:21:41.820 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始检查用户重复，检查类型: null
2025-06-17 09:21:41.975 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户重复检查完成，结果: 可用
2025-06-17 09:23:40.001 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始检查用户重复，检查类型: null
2025-06-17 09:23:40.114 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 用户重复检查完成，结果: 可用
2025-06-17 09:39:33.541 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:39:33.626 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 09:40:10.049 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 21288 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 09:40:10.051 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 09:40:10.058 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 09:40:11.960 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:40:11.963 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:40:11.988 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 09:40:11.994 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:40:11.995 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 09:40:12.008 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 09:40:12.017 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:40:12.017 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 09:40:12.032 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 JPA repository interfaces.
2025-06-17 09:40:12.061 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 09:40:12.063 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 09:40:12.085 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-17 09:40:13.065 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 09:40:13.078 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 09:40:13.079 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 09:40:13.079 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 09:40:13.679 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 09:40:13.679 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3522 ms
2025-06-17 09:40:13.798 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 09:40:14.124 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 09:40:15.599 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 09:40:15.912 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 09:40:16.283 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 09:40:16.455 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 09:40:17.024 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 09:40:17.043 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 09:40:19.742 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 09:40:22.586 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 09:40:22.638 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 09:40:23.363 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.733 seconds (JVM running for 19.451)
2025-06-17 09:44:12.791 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 09:44:12.791 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 09:44:12.794 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-17 09:44:13.131 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-17 09:44:13.132 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: null
2025-06-17 09:44:13.339 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-17 09:44:13.340 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-17 09:44:13.341 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 208ms，根部门数量: 1
2025-06-17 09:51:31.696 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取纯部门树结构（高性能版本）
2025-06-17 09:51:31.815 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回部门数据条数: 17
2025-06-17 09:51:31.817 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 纯部门树结构获取完成，耗时: 121ms
2025-06-17 09:55:38.126 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树（优化版本）
2025-06-17 09:55:38.127 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: null, 包含已删除: false, 最大层级: 0
2025-06-17 09:55:38.268 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 批量查询所有部门成功，数量: 17
2025-06-17 09:55:38.269 [http-nio-8285-exec-5] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功（优化版本），根部门数量: 5, 耗时: 142ms
2025-06-17 10:01:09.520 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:01:09.526 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 10:08:20.297 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 38300 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 10:08:20.299 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 10:08:20.331 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 10:08:21.773 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:08:21.779 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:08:21.809 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 21 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 10:08:21.813 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:08:21.814 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:08:21.825 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 10:08:21.835 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:08:21.836 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 10:08:21.851 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 JPA repository interfaces.
2025-06-17 10:08:21.869 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:08:21.870 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 10:08:21.889 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-06-17 10:08:23.541 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 10:08:23.567 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 10:08:23.569 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 10:08:23.570 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 10:08:24.882 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 10:08:24.882 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4443 ms
2025-06-17 10:08:24.961 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 10:08:25.291 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 10:08:26.032 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 10:08:26.086 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 10:08:26.205 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 10:08:26.381 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 10:08:26.693 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 10:08:26.707 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:08:29.350 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 10:08:31.036 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 10:08:31.051 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 10:08:31.661 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.461 seconds (JVM running for 17.048)
2025-06-17 10:12:30.565 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:12:30.571 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 10:12:35.943 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 24348 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 10:12:35.944 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 10:12:35.950 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 10:12:37.074 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:12:37.076 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:12:37.098 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 10:12:37.101 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:12:37.102 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:12:37.111 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 10:12:37.118 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:12:37.119 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 10:12:37.133 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-17 10:12:37.148 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:12:37.150 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 10:12:37.167 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-17 10:12:37.919 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 10:12:37.936 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 10:12:37.937 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 10:12:37.937 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 10:12:38.195 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 10:12:38.195 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2204 ms
2025-06-17 10:12:38.273 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 10:12:38.403 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 10:12:39.233 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 10:12:39.287 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 10:12:39.448 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 10:12:39.595 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 10:12:39.873 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 10:12:39.886 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:12:41.966 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 10:12:43.440 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 10:12:43.451 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 10:12:44.044 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 8.82 seconds (JVM running for 10.735)
2025-06-17 10:12:47.850 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 10:12:47.850 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 10:12:47.854 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-17 10:12:55.051 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取纯部门树结构（高性能版本）
2025-06-17 10:12:55.168 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回部门数据条数: 17
2025-06-17 10:12:55.174 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 纯部门树结构获取完成，耗时: 122ms
2025-06-17 10:13:36.078 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 123456789, 角色ID: 123456789
2025-06-17 10:13:36.170 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条原始数据
2025-06-17 10:13:36.170 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个部门节点
2025-06-17 10:14:09.717 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5001, 角色ID: 7001
2025-06-17 10:14:09.786 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到18条原始数据
2025-06-17 10:14:09.787 [http-nio-8285-exec-10] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回1个部门节点
2025-06-17 10:15:41.512 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-17 10:15:41.512 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: null
2025-06-17 10:15:41.669 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-17 10:15:41.670 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-17 10:15:41.670 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 157ms，根部门数量: 1
2025-06-17 10:25:19.481 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:25:19.486 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 10:25:29.777 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 44668 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 10:25:29.779 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 10:25:29.787 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 10:25:31.422 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:25:31.426 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:25:31.457 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 10:25:31.460 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:25:31.461 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:25:31.471 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 9 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 10:25:31.481 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:25:31.481 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 10:25:31.498 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 JPA repository interfaces.
2025-06-17 10:25:31.517 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:25:31.518 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 10:25:31.536 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-17 10:25:32.161 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 10:25:32.170 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 10:25:32.171 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 10:25:32.171 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 10:25:32.362 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 10:25:32.362 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2512 ms
2025-06-17 10:25:32.443 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 10:25:32.552 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 10:25:33.265 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 10:25:33.313 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 10:25:33.433 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 10:25:33.572 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 10:25:33.936 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 10:25:33.947 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:25:36.452 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 10:25:37.954 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 10:25:37.965 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 10:25:38.650 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 9.773 seconds (JVM running for 12.257)
2025-06-17 10:25:49.045 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 10:25:49.045 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 10:25:49.047 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-17 10:25:49.269 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5001, 角色ID: 7001
2025-06-17 10:25:49.393 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:25:49.393 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:01.951 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5002, 角色ID: 7001
2025-06-17 10:26:02.017 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:26:02.017 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:23.344 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5001, 角色ID: 7010
2025-06-17 10:26:23.422 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到1条直属用户数据
2025-06-17 10:26:23.423 [http-nio-8285-exec-6] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回1个用户节点
2025-06-17 10:26:28.288 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5002, 角色ID: 7010
2025-06-17 10:26:28.368 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:26:28.368 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:32.998 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5003, 角色ID: 7010
2025-06-17 10:26:33.062 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到1条直属用户数据
2025-06-17 10:26:33.062 [http-nio-8285-exec-5] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回1个用户节点
2025-06-17 10:26:37.946 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5004, 角色ID: 7010
2025-06-17 10:26:38.021 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:26:38.021 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:43.036 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5005, 角色ID: 7010
2025-06-17 10:26:43.100 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:26:43.101 [http-nio-8285-exec-7] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:48.026 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5006, 角色ID: 7010
2025-06-17 10:26:48.090 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到0条直属用户数据
2025-06-17 10:26:48.090 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回0个用户节点
2025-06-17 10:26:58.160 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5007, 角色ID: 7010
2025-06-17 10:26:58.221 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到3条直属用户数据
2025-06-17 10:26:58.222 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回3个用户节点
2025-06-17 10:37:43.122 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 开始获取部门结构树（优化版本）
2025-06-17 10:37:43.123 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 排除部门ID: null, 包含已删除: false, 最大层级: 0
2025-06-17 10:37:43.290 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 批量查询所有部门成功，数量: 17
2025-06-17 10:37:43.292 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.TOrgStructureServiceImpl - 部门结构树获取成功（优化版本），根部门数量: 5, 耗时: 169ms
2025-06-17 10:38:33.600 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-17 10:38:33.600 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: null
2025-06-17 10:38:33.703 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-17 10:38:33.704 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-17 10:38:33.704 [http-nio-8285-exec-2] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 104ms，根部门数量: 1
2025-06-17 10:39:04.078 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取未授权用户树形结构
2025-06-17 10:39:04.079 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: null
2025-06-17 10:39:04.185 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - RECURSIVE 查询返回 22 条记录
2025-06-17 10:39:04.185 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建未授权用户部门树完成，根部门数量: 1
2025-06-17 10:39:04.185 [http-nio-8285-exec-8] INFO  com.dfit.percode.service.impl.UserServiceImpl - 未授权用户树形结构构建完成，耗时: 106ms，根部门数量: 1
2025-06-17 10:39:56.065 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:39:56.105 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-17 10:49:02.012 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 20728 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-17 10:49:02.014 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-17 10:49:02.024 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-17 10:49:03.649 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:49:03.653 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:49:03.683 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-17 10:49:03.689 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:49:03.690 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-17 10:49:03.710 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-17 10:49:03.736 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:49:03.737 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 10:49:03.765 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 JPA repository interfaces.
2025-06-17 10:49:03.787 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 10:49:03.789 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 10:49:03.807 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-06-17 10:49:04.990 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-17 10:49:05.003 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-17 10:49:05.004 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-17 10:49:05.004 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-17 10:49:05.405 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-17 10:49:05.405 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3306 ms
2025-06-17 10:49:05.492 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-17 10:49:05.770 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-17 10:49:06.683 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-17 10:49:06.746 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-17 10:49:06.885 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-17 10:49:07.070 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-17 10:49:07.413 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-17 10:49:07.427 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 10:49:10.345 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-17 10:49:13.358 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-17 10:49:13.374 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-17 10:49:14.028 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 13.129 seconds (JVM running for 15.67)
2025-06-17 10:49:16.518 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-17 10:49:16.519 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-17 10:49:16.520 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-17 10:49:42.020 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始懒加载获取部门未授权用户，部门ID: 5007, 角色ID: 7001
2025-06-17 10:49:42.135 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 查询到3条直属用户数据
2025-06-17 10:49:42.136 [http-nio-8285-exec-9] INFO  com.dfit.percode.service.impl.UserServiceImpl - 懒加载获取部门未授权用户完成，返回3个用户节点
2025-06-17 19:56:58.067 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-17 19:56:59.548 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
