2025-06-22 14:14:32.415 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 36368 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-22 14:14:32.490 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-22 14:14:32.494 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-22 14:14:35.539 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:14:35.543 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:14:35.576 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Elasticsearch repository interfaces.
2025-06-22 14:14:35.583 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:14:35.585 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:14:35.602 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-22 14:14:35.613 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:14:35.613 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 14:14:35.640 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 JPA repository interfaces.
2025-06-22 14:14:35.692 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:14:35.694 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-22 14:14:35.733 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
2025-06-22 14:14:37.274 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-22 14:14:37.292 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-22 14:14:37.293 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 14:14:37.294 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-22 14:14:37.751 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 14:14:37.751 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4881 ms
2025-06-22 14:14:37.920 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-22 14:14:38.240 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-22 14:14:39.211 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 14:14:39.353 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-22 14:14:39.677 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-22 14:14:40.035 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-22 14:14:40.589 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-22 14:14:40.612 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:14:43.397 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 14:14:46.875 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-22 14:14:46.914 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-22 14:14:48.020 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 17.152 seconds (JVM running for 23.157)
2025-06-22 14:19:25.506 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 14:19:25.506 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 14:19:25.509 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-22 14:28:02.647 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取部门用户树形结构（层级懒加载）
2025-06-22 14:28:02.648 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 1936612757591953408, 父部门ID: null
2025-06-22 14:28:02.940 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 层级查询返回 1 条记录
2025-06-22 14:28:02.941 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建简化树形结构完成，节点数量: 1
2025-06-22 14:28:02.941 [http-nio-8285-exec-3] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门用户树形结构构建完成，耗时: 292ms，节点数量: 1
2025-06-22 14:36:44.472 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取部门用户树形结构（层级懒加载）
2025-06-22 14:36:44.472 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 1936612757591953408, 父部门ID: null
2025-06-22 14:36:44.618 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 层级查询返回 1 条记录
2025-06-22 14:36:44.618 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建简化树形结构完成，节点数量: 1
2025-06-22 14:36:44.619 [http-nio-8285-exec-4] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门用户树形结构构建完成，耗时: 147ms，节点数量: 1
2025-06-22 14:39:20.303 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:39:20.315 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-22 14:39:35.856 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 31040 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-22 14:39:35.858 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-22 14:39:35.875 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-22 14:39:37.912 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:39:37.915 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:39:37.956 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 32 ms. Found 0 Elasticsearch repository interfaces.
2025-06-22 14:39:37.961 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:39:37.963 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:39:37.980 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-22 14:39:38.031 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:39:38.032 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 14:39:38.058 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 JPA repository interfaces.
2025-06-22 14:39:38.083 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:39:38.085 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-22 14:39:38.117 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-06-22 14:39:39.148 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-22 14:39:39.160 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-22 14:39:39.160 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 14:39:39.161 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-22 14:39:39.430 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 14:39:39.430 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3490 ms
2025-06-22 14:39:39.545 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-22 14:39:40.065 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-22 14:39:41.030 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 14:39:41.223 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-22 14:39:41.451 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-22 14:39:41.611 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-22 14:39:42.120 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-22 14:39:42.143 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:39:44.868 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 14:39:48.489 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-22 14:39:48.504 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-22 14:39:49.477 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 14.474 seconds (JVM running for 17.039)
2025-06-22 14:39:53.732 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 14:39:53.733 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 14:39:53.737 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 4 ms
2025-06-22 14:39:54.136 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取部门用户树形结构（层级懒加载）
2025-06-22 14:39:54.136 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 1936612757591953408, 父部门ID: null
2025-06-22 14:39:54.419 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 层级查询返回 1 条记录
2025-06-22 14:39:54.419 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - === 调试：查询结果原始数据 ===
2025-06-22 14:39:54.420 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 第一条记录的所有字段:
2025-06-22 14:39:54.420 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   字段名: 'organname', 值: '宁波钢铁', 类型: String
2025-06-22 14:39:54.420 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   字段名: 'id', 值: '5001', 类型: Long
2025-06-22 14:39:54.420 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - === 调试信息结束 ===
2025-06-22 14:39:54.421 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - === 调试：处理数据行 ===
2025-06-22 14:39:54.421 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 尝试获取字段值:
2025-06-22 14:39:54.421 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   id: 5001
2025-06-22 14:39:54.421 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   organName: null
2025-06-22 14:39:54.421 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   organname: 宁波钢铁
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   organ_name: null
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   userName: null
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   username: null
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   isDisable: null
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl -   isdisable: null
2025-06-22 14:39:54.422 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 最终设置的值: id=5001, organName=宁波钢铁, userName=null, isDisable=null
2025-06-22 14:39:54.423 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建简化树形结构完成，节点数量: 1
2025-06-22 14:39:54.423 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门用户树形结构构建完成，耗时: 285ms，节点数量: 1
2025-06-22 14:41:54.388 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:41:54.393 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-22 14:42:04.839 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 19116 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-22 14:42:04.841 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-22 14:42:04.853 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-22 14:42:06.898 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:42:06.903 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:42:06.943 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 31 ms. Found 0 Elasticsearch repository interfaces.
2025-06-22 14:42:06.949 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:42:06.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-22 14:42:06.977 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-22 14:42:06.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:42:06.992 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 14:42:07.014 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 14 ms. Found 0 JPA repository interfaces.
2025-06-22 14:42:07.037 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 14:42:07.039 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-22 14:42:07.073 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-06-22 14:42:07.941 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-22 14:42:07.951 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-22 14:42:07.952 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 14:42:07.952 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-22 14:42:08.180 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 14:42:08.180 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3264 ms
2025-06-22 14:42:08.267 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-22 14:42:08.399 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-22 14:42:09.157 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 14:42:09.222 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-22 14:42:09.432 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-22 14:42:09.613 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-22 14:42:10.147 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-22 14:42:10.213 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 14:42:12.682 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 14:42:14.907 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-22 14:42:14.939 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-22 14:42:15.921 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 11.953 seconds (JVM running for 14.506)
2025-06-22 14:42:20.513 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 14:42:20.513 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 14:42:20.516 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 14:42:20.695 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 开始获取部门用户树形结构（层级懒加载）
2025-06-22 14:42:20.696 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 角色ID: 1936612757591953408, 父部门ID: null
2025-06-22 14:42:20.947 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 层级查询返回 1 条记录
2025-06-22 14:42:20.948 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 构建简化树形结构完成，节点数量: 1
2025-06-22 14:42:20.949 [http-nio-8285-exec-1] INFO  com.dfit.percode.service.impl.UserServiceImpl - 部门用户树形结构构建完成，耗时: 252ms，节点数量: 1
2025-06-22 15:11:59.177 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 15:11:59.183 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-22 15:12:15.142 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 11 on DESKTOP-UKI346C with PID 34616 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-06-22 15:12:15.143 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-06-22 15:12:15.145 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-06-22 15:12:16.958 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 15:12:16.960 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-06-22 15:12:16.997 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Elasticsearch repository interfaces.
2025-06-22 15:12:17.003 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 15:12:17.004 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-06-22 15:12:17.021 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-06-22 15:12:17.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 15:12:17.035 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-22 15:12:17.054 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 JPA repository interfaces.
2025-06-22 15:12:17.074 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-22 15:12:17.076 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-22 15:12:17.101 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-22 15:12:18.134 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-06-22 15:12:18.148 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-06-22 15:12:18.149 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-22 15:12:18.149 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-06-22 15:12:18.525 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-22 15:12:18.525 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3314 ms
2025-06-22 15:12:18.714 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-22 15:12:18.963 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-22 15:12:19.787 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-22 15:12:19.838 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-06-22 15:12:19.969 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-06-22 15:12:20.126 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-06-22 15:12:20.473 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-06-22 15:12:20.486 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-22 15:12:23.066 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-22 15:12:25.374 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-06-22 15:12:25.389 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-06-22 15:12:26.634 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 12.684 seconds (JVM running for 15.101)
2025-06-22 15:15:41.913 [http-nio-8285-exec-2] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-22 15:15:41.915 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-22 15:15:41.918 [http-nio-8285-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-06-22 15:16:00.577 [http-nio-8285-exec-6] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1930307870503604200
2025-06-22 15:16:19.829 [http-nio-8285-exec-7] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1935949462509850600
2025-06-22 15:16:40.474 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1935949462509850624
2025-06-22 15:16:40.574 [http-nio-8285-exec-8] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单详情获取成功，菜单名称: 测试
2025-06-22 15:16:46.440 [http-nio-8285-exec-10] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1935949462509850600
2025-06-22 15:16:53.416 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 开始获取菜单详情，菜单ID: 1935949462509850624
2025-06-22 15:16:53.486 [http-nio-8285-exec-2] INFO  c.dfit.percode.service.impl.MenuModuleServiceImpl - 菜单详情获取成功，菜单名称: 测试
2025-06-22 21:00:00.004 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 开始执行孤儿记录关联任务 ===
2025-06-22 21:00:00.695 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 开始关联孤儿记录
2025-06-22 21:00:00.774 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工岗位孤儿记录: 0 条
2025-06-22 21:00:00.842 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工职称孤儿记录: 0 条
2025-06-22 21:00:00.906 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 关联员工系统标识孤儿记录: 0 条
2025-06-22 21:00:00.906 [scheduling-1] INFO  com.dfit.percode.sync.service.DataSyncService - 孤儿记录关联汇总: 岗位表=0 条, 职称表=0 条, 系统表=0 条, 总计=0 条
2025-06-22 21:00:00.972 [scheduling-1] INFO  com.dfit.percode.sync.task.DataSyncScheduledTask - === 孤儿记录关联任务执行成功 ===
