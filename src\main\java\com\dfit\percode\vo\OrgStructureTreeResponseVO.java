package com.dfit.percode.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 部门结构树响应VO类
 * 用于移动部门时显示部门树形结构
 * 
 * <AUTHOR>
 * @since 2025-01-20
 */
@Data
@ApiModel(value = "OrgStructureTreeResponseVO", description = "部门结构树响应数据")
public class OrgStructureTreeResponseVO {
    
    @ApiModelProperty(value = "部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    
    @ApiModelProperty(value = "部门名称")
    private String organName;
    
    @ApiModelProperty(value = "父部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long preId;
    
    @ApiModelProperty(value = "排序序号")
    private Integer orderInfo;
    
    @ApiModelProperty(value = "部门层级（从1开始）")
    private Integer level;
    
    @ApiModelProperty(value = "完整路径（如：总公司/技术部/研发组）")
    private String fullPath;
    
    @ApiModelProperty(value = "是否可选择（用于移动部门时排除自身和子部门）")
    private Boolean selectable = true;
    
    @ApiModelProperty(value = "子部门列表")
    private List<OrgStructureTreeResponseVO> children;
    
    @ApiModelProperty(value = "子部门数量")
    private Integer childCount = 0;
}
