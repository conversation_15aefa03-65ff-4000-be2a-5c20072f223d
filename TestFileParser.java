import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TestFileParser {
    public static void main(String[] args) {
        String filePath = "organization-sync-temp/department_sync_test.sql";
        
        System.out.println("测试文件解析...");
        System.out.println("文件路径: " + filePath);
        
        try {
            List<String> lines = Files.readAllLines(Paths.get(filePath), StandardCharsets.UTF_8);
            System.out.println("文件总行数: " + lines.size());
            
            Pattern pattern = Pattern.compile(
                "INSERT INTO `department_sync_test` VALUES \\(" +
                "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)', '([^']*)', " +
                "([^,]+), '([^']*)', '([^']*)', '([^']*)', '([^']*)'\\)"
            );
            
            int insertCount = 0;
            int validCount = 0;
            int deletedCount = 0;
            
            for (int i = 0; i < Math.min(lines.size(), 60); i++) {
                String line = lines.get(i);
                if (line.trim().startsWith("INSERT INTO `department_sync_test`")) {
                    insertCount++;
                    System.out.println("\n第" + insertCount + "条INSERT语句:");
                    System.out.println(line.substring(0, Math.min(100, line.length())) + "...");
                    
                    Matcher matcher = pattern.matcher(line);
                    if (matcher.find()) {
                        String orgCode = matcher.group(2);
                        String orgName = matcher.group(3);
                        String fullName = matcher.group(6);
                        String userPredef14 = matcher.group(8);
                        
                        System.out.println("  orgCode: " + orgCode);
                        System.out.println("  orgName: " + orgName);
                        System.out.println("  fullName: " + fullName);
                        System.out.println("  userPredef14: " + userPredef14);
                        
                        if ("D".equals(userPredef14)) {
                            deletedCount++;
                            System.out.println("  状态: 已删除");
                        } else if (fullName != null && !fullName.trim().isEmpty()) {
                            validCount++;
                            System.out.println("  状态: 有效");
                        } else {
                            System.out.println("  状态: 无效（fullName为空）");
                        }
                    } else {
                        System.out.println("  正则匹配失败");
                    }
                }
            }
            
            System.out.println("\n前60行统计:");
            System.out.println("INSERT语句数: " + insertCount);
            System.out.println("有效记录数: " + validCount);
            System.out.println("删除记录数: " + deletedCount);
            
        } catch (IOException e) {
            System.err.println("文件读取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
