package com.dfit.percode.service;

import com.dfit.percode.entity.TOrgStructure;
import com.dfit.percode.vo.SearchOrgStructureRequestVO;
import com.dfit.percode.vo.SearchOrgStructureResponseVO;
import com.dfit.percode.vo.AddOrgStructureRequestVO;
import com.dfit.percode.vo.AddOrgStructureResponseVO;
import com.dfit.percode.vo.UpdateOrgStructureRequestVO;
import com.dfit.percode.vo.UpdateOrgStructureResponseVO;
import com.dfit.percode.vo.DeleteOrgStructureRequestVO;
import com.dfit.percode.vo.DeleteOrgStructureResponseVO;
import com.dfit.percode.vo.OrgStructureTreeRequestVO;
import com.dfit.percode.vo.OrgStructureTreeResponseVO;
import com.dfit.percode.vo.MoveOrgStructureRequestVO;
import com.dfit.percode.vo.MoveOrgStructureResponseVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dfit.percode.userPerm.RoleEntity;
import com.dfit.percode.userPerm.UserTree;

import java.util.List;

/**
 * <p>
 * 组织架构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface ITOrgStructureService extends IService<TOrgStructure> {

    List<UserTree> choseUser();

    List<RoleEntity> roleSelected();

    /**
     * 搜索部门
     * 支持按组织名称模糊搜索、父部门ID精确匹配、是否包含已删除部门
     *
     * @param request 搜索请求参数
     * @return 搜索结果列表
     */
    List<SearchOrgStructureResponseVO> searchOrgStructure(SearchOrgStructureRequestVO request);

    /**
     * 新增部门
     * 创建新的组织架构节点，支持父子层级关系
     *
     * @param request 新增部门请求参数
     * @return 新增部门的详细信息
     */
    AddOrgStructureResponseVO addOrgStructure(AddOrgStructureRequestVO request);

    /**
     * 修改部门
     * 主要用于部门重命名和排序调整
     *
     * @param request 修改部门请求参数
     * @return 修改后的部门详细信息
     */
    UpdateOrgStructureResponseVO updateOrgStructure(UpdateOrgStructureRequestVO request);

    /**
     * 删除部门
     * 支持级联删除子部门，逻辑删除方式
     *
     * @param request 删除部门请求参数
     * @return 删除操作的详细信息
     */
    DeleteOrgStructureResponseVO deleteOrgStructure(DeleteOrgStructureRequestVO request);

    /**
     * 获取部门结构树
     * 用于移动部门时显示可选择的部门树形结构
     *
     * @param request 获取部门树请求参数
     * @return 部门树形结构列表
     */
    List<OrgStructureTreeResponseVO> getOrgStructureTree(OrgStructureTreeRequestVO request);

    /**
     * 移动部门
     * 将指定部门移动到新的父部门下，并重新排序
     *
     * @param request 移动部门请求参数
     * @return 移动操作的详细结果信息
     */
    MoveOrgStructureResponseVO moveOrgStructure(MoveOrgStructureRequestVO request);

}
