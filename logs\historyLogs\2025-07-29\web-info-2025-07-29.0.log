2025-07-29 00:12:38.069 [SpringApplicationShutdownHook] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 00:12:38.229 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-07-29 00:13:23.827 [main] INFO  com.dfit.percode.KmapApplication - Starting KmapApplication using Java 1.8.0_442 on DESKTOP-UKI346C with PID 33976 (G:\fushun\permissionCode\target\classes started by Dell in G:\fushun\permissionCode)
2025-07-29 00:13:23.831 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-07-29 00:13:23.831 [main] INFO  com.dfit.percode.KmapApplication - The following 1 profile is active: "dev"
2025-07-29 00:13:26.847 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 00:13:26.854 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
2025-07-29 00:13:26.917 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 0 Elasticsearch repository interfaces.
2025-07-29 00:13:26.927 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 00:13:26.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
2025-07-29 00:13:26.950 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 19 ms. Found 0 Reactive Elasticsearch repository interfaces.
2025-07-29 00:13:26.970 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 00:13:26.971 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-29 00:13:27.007 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 JPA repository interfaces.
2025-07-29 00:13:27.044 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-29 00:13:27.047 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-29 00:13:27.084 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-07-29 00:13:28.780 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8285 (http)
2025-07-29 00:13:28.812 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8285"]
2025-07-29 00:13:28.815 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-29 00:13:28.815 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.68]
2025-07-29 00:13:28.973 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-29 00:13:28.973 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4910 ms
2025-07-29 00:13:29.166 [main] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-29 00:13:29.735 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-29 00:13:31.332 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-29 00:13:31.546 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.12.Final
2025-07-29 00:13:32.443 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-29 00:13:33.023 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQL92Dialect
2025-07-29 00:13:34.011 [main] INFO  o.h.e.t.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-29 00:13:34.076 [main] INFO  o.s.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-29 00:13:34.123 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 超级管理员功能已启用
2025-07-29 00:13:34.123 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户ID数量: 2
2025-07-29 00:13:34.129 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员登录账号数量: 2
2025-07-29 00:13:34.130 [main] INFO  com.dfit.percode.config.SuperAdminConfig - 配置的超级管理员用户名数量: 2
2025-07-29 00:13:40.797 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-29 00:13:51.465 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8285"]
2025-07-29 00:13:51.512 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8285 (http) with context path ''
2025-07-29 00:13:53.246 [main] INFO  com.dfit.percode.KmapApplication - Started KmapApplication in 31.703 seconds (JVM running for 38.626)
2025-07-29 00:14:23.998 [http-nio-8285-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-29 00:14:23.998 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-07-29 00:14:24.001 [http-nio-8285-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
2025-07-29 00:14:24.099 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 用户请求拦截 - URI: /auth/permissions, Method: GET, IP: 127.0.0.1
2025-07-29 00:14:24.924 [http-nio-8285-exec-1] INFO  com.dfit.percode.interceptor.HybridAuthInterceptor -  超级管理员访问 - 用户ID: 1938155631131365376, URI: /auth/permissions, Method: GET, IP: 127.0.0.1
2025-07-29 00:14:25.073 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 查询用户权限请求，模块标识: sd, 权限类型: menu
2025-07-29 00:14:25.074 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 开始查询用户权限，用户ID: 1938155631131365376, 模块标识: sd, 权限类型: menu
2025-07-29 00:14:25.074 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 检测到超级管理员，返回全量权限，用户ID: 1938155631131365376, 权限类型: menu
2025-07-29 00:14:25.074 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员权限，用户ID: 1938155631131365376, 模块标识: sd
2025-07-29 00:14:25.074 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.SuperAdminPermissionService - 开始查询超级管理员模块级菜单权限，模块标识: sd
2025-07-29 00:14:25.669 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.SuperAdminPermissionService - 查询到模块总数: 4
2025-07-29 00:14:26.081 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员模块级菜单权限查询完成，模块数量: 1，耗时: 1007ms
2025-07-29 00:14:26.147 [http-nio-8285-exec-1] INFO  c.dfit.percode.service.SuperAdminPermissionService - 超级管理员权限查询完成，用户ID: 1938155631131365376, 根菜单数量: 1, 按钮数量: 3, 耗时: 1073ms
2025-07-29 00:14:26.148 [http-nio-8285-exec-1] INFO  c.d.percode.service.impl.UserPermissionServiceImpl - 超级管理员权限查询完成，用户ID: 1938155631131365376, 耗时: 1074ms
2025-07-29 00:14:26.148 [http-nio-8285-exec-1] INFO  com.dfit.percode.controller.AuthController - 用户权限查询成功（混合认证），用户ID: 1938155631131365376, 认证方式: SuperAdmin, 权限类型: menu, 菜单数量: 1, 按钮数量: 3, 数据权限数量: 0, 耗时: 1074ms
2025-07-29 00:14:27.846 [http-nio-8285-exec-1] INFO  com.dfit.percode.listener.UserActionInterceptor - 请求处理完成 - URI: /auth/permissions, 处理时间: 3747ms
